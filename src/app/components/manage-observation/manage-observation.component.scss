.custom-card {
    border-radius: 10px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}
.card-body{
    height: 70vh;
    overflow: auto;
}

.table-responsive {
    overflow-x: auto;
    max-width: 100%;
}

.custom-table {
    width: 100%;
    table-layout: fixed; /* Changed to fixed for better column width control */
    border-collapse: collapse;
    border-radius: 10px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.table-header th {
    font-size: 12px;
    background-color: #f5f7fa;
    color: black;
    font-weight: bold;
    text-align: center;
    padding: 10px 8px;
}

td {
    font-size: 12px;
    text-align: center; /* Center align by default */
    vertical-align: middle;
    word-wrap: break-word; /* Allow text to wrap */
    overflow-wrap: break-word;
    padding: 12px 5px; /* Reduced horizontal padding */
    max-width: 200px; /* Limit maximum width */
}

/* Left align cells with details-container */
td .details-container {
    text-align: left;
}

/* Column width definitions */
th.col-id {
    width: 60px;
}

th.col-actions {
    width: 120px;
}

th.col-user-details {
    width: 180px;
}

th.col-plant-details {
    width: 250px;
}

th.col-images {
    width: 100px;
}

th.col-fixed-images {
    width: 100px;
}

th.col-fixed-desc {
    width: 180px;
}

th.col-assigned-to {
    width: 180px;
}

/* Fix for observation text that grows infinitely */
.observation-cell {
    max-width: 400px;
    white-space: normal;
    word-wrap: break-word;
    overflow-wrap: break-word;
}

.pre-wrap {
    white-space: pre-wrap;
}

/* Admin details styling */
.details-container {
    display: table;
    width: 100%;
    text-align: left;
    padding: 5px;
    border-spacing: 0 8px;
}

.details-row {
    display: table-row;
    width: 100%;
}

.details-label {
    display: table-cell;
    font-weight: 500;
    width: 80px;
    text-align: left;
    padding-right: 10px;
    color: #777;
    vertical-align: top;
    white-space: nowrap;
}

.details-value {
    display: table-cell;
    word-break: break-word;
    overflow-wrap: break-word;
    text-align: left;
    padding-bottom: 8px;
    border-bottom: 1px dashed #eee;
    vertical-align: top;
    font-weight: 600;
    color: #222;
}

/* Action buttons styling */
.actions {
    min-width: 100px;
}

.actions .btn {
    margin: 2px;
    min-width: 32px;
    width: 32px;
    height: 32px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.actions .btn-group {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 5px;
}

.img-thumbnail {
    width: 80px;
    height: 80px;
    object-fit: cover;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

::ng-deep .carousel-item img {
    max-width: 100%;
    height: auto;
    object-fit: contain;
    margin: 0 auto;
    display: block;
}

.image-preview-container {
    display: inline-block;
    position: relative;
    margin-right: 8px;
    margin-bottom: 8px;
    border: 1px solid #dee2e6;
    padding: 4px;
    border-radius: 4px;
}

.existing-images-preview,
.new-images-preview {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 8px;
}

.existing-images-preview .image-preview-container,
.new-images-preview .image-preview-container {
    flex: 0 0 auto;
}

/* Badge styling */
.badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 500;
    margin-bottom: 4px;
    white-space: normal;
    text-align: center;
    line-height: 1.2;
    color: white; /* Make text color white for better contrast */
}

/* Filter button styling */
.filter-button {
    width: 35px;
    cursor: pointer;
}

/* Remove button styling */
.remove-btn {
    line-height: 1;
}

/* Pagination styling */
.card-footer {
    overflow: hidden;
}

/* Carousel styling */
app-carousel {
    display: block;
    width: 100%;
    overflow: hidden;
    position: relative;
}

::ng-deep .carousel {
    max-width: 100%;
    overflow: hidden;
    position: relative;
}

::ng-deep .carousel-inner {
    max-width: 70%;
    margin: 0 auto;
}

::ng-deep .carousel-control-prev,
::ng-deep .carousel-control-next {
    width: 24px;
    height: 24px;
    top: 50%;
    transform: translateY(-50%);
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: 50%;
    opacity: 0.7;
    position: absolute;
    left: 0;
    right: auto;
    z-index: 10;
}

::ng-deep .carousel-control-next {
    left: auto;
    right: 0;
}

::ng-deep .carousel-control-prev-icon,
::ng-deep .carousel-control-next-icon {
    width: 12px;
    height: 12px;
}

::ng-deep .carousel-indicators {
    margin-bottom: 0;
    position: relative;
    bottom: 0;
}

::ng-deep .carousel-indicators [data-bs-target] {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: #aaa;
    margin: 0 3px;
}

::ng-deep .carousel-indicators .active {
    background-color: #555;
}

/* Ensure carousel stays within its container */
td.text-center app-carousel {
    max-width: 100%;
    overflow: hidden;
}

/* Ensure carousel caption doesn't overflow */
::ng-deep .carousel-caption {
    position: static;
    padding: 5px 0;
    margin: 0;
    color: #333;
}

/* Style for bold text in table cells to match value-text */
td b, td strong {
    font-weight: 600;
    color: #222;
}
