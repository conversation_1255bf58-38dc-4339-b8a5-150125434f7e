<app-toast-message></app-toast-message>
<app-tab [tabs]="tabs" [selectedTabIndex]="selectedTabIndex" (tabSelected)="onTabSelected($event)">
</app-tab>

<!-- Find it, Fix it (Tab 0) -->
<div *ngIf="selectedTabIndex==0" class="card custom-card" id="findit-fixit-list">
    <div class="card-header">
        <div class="row align-items-center">
            <div class="col">
                <h6 class="mb-0">Find it, Fix it</h6>
            </div>
            <div class="col text-end d-flex align-items-center justify-content-end">
                <div ngbDropdown class="d-inline-block me-2">
                    <button type="button" class="btn btn-sm adani-btn dropdown-toggle" id="downloadObsExcelDropdown0"
                        ngbDropdownToggle [disabled]="isDownloadingExcel || listLoading">
                        <span *ngIf="!isDownloadingExcel"> <i class="bi bi-file-earmark-excel me-1"></i> Download Excel
                        </span>
                        <span *ngIf="isDownloadingExcel"> <span class="spinner-border spinner-border-sm me-1"
                                role="status" aria-hidden="true"></span> Downloading {{ downloadType === 'current' ?
                            'Page' : 'All' }}... </span>
                    </button>
                    <ul ngbDropdownMenu aria-labelledby="downloadObsExcelDropdown0">
                        <li> <button type="button" ngbDropdownItem (click)="downloadExcel('current')"
                                [disabled]="isDownloadingExcel || listLoading || (getCurrentListData()?.length ?? 0) === 0">
                                <i class="bi bi-download me-1"></i> Download Current Page ({{
                                getCurrentListData()?.length ?? 0 }}) </button> </li>
                        <li> <button type="button" ngbDropdownItem (click)="downloadExcel('all')"
                                [disabled]="isDownloadingExcel || listLoading || totalItems === 0"> <i
                                    class="bi bi-cloud-download me-1"></i> Download All Filtered ({{ totalItems }})
                            </button> </li>
                    </ul>
                </div>
                <img src="../../../assets/svg/filter.svg" class="filter-button ms-3" (click)="openFilterModal()"
                    alt="Filter" />
            </div>
        </div>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered custom-table data-detail-table">
                <thead class="table-header text-center">
                    <tr>
                        <th scope="col" class="col-actions">Actions</th>
                        <th scope="col" class="col-user-details">User Details</th>
                        <th scope="col" class="col-plant-details">Plant Details</th>
                        <th scope="col" class="col-images">Images</th>
                        <th scope="col" class="col-fixed-images">Fixed Images</th>
                        <th scope="col" class="col-fixed-desc">Fixed Description</th>
                    </tr>
                </thead>
                <tbody>
                    <tr *ngIf="listLoading">
                        <td colspan="6" class="text-center p-4">
                            <span class="spinner-border spinner-border-sm me-2"></span> Loading...
                        </td>
                    </tr>
                    <tr *ngIf="!listLoading && (!findItFixItList || findItFixItList.length === 0)">
                        <td colspan="6" class="text-center p-4 text-muted">No 'Find it, Fix it' records found.</td>
                    </tr>
                    <tr *ngFor="let item of findItFixItList">
                        <td class="actions text-center">
                            <div class="d-flex justify-content-center">
                                <div class="btn-group" role="group" aria-label="Observation Actions">
                                    <button type="button" class="btn btn-sm btn-warning" title="Edit" (click)="openEditModal(item)">
                                        <i class="bi bi-pencil"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-info" title="Details" (click)="openDetailsModal(item)">
                                        <i class="bi bi-eye"></i>
                                    </button>
                                    <button type="button" *ngIf="!item.rewardGranted" class="btn btn-sm btn-success" title="Reward"
                                        (click)="openRewardConfirmation(item)">
                                        <i class="bi bi-gift"></i>
                                    </button>
                                </div>
                            </div>
                        </td>
                        <td>
                            <div class="details-container">
                                <div class="details-row">
                                    <span class="details-label">Name:</span>
                                    <span class="details-value">{{item.admin?.firstName}} {{item.admin?.lastName || ''}}</span>
                                </div>
                                <div class="details-row">
                                    <span class="details-label">Contact:</span>
                                    <span class="details-value">{{item.admin?.contactNumber || 'N/A'}}</span>
                                </div>
                                <div class="details-row">
                                    <span class="details-label">Email:</span>
                                    <span class="details-value">{{item.admin?.email || 'N/A'}}</span>
                                </div>
                            </div>
                        </td>
                        <td>
                            <div class="details-container">
                                <div class="details-row">
                                    <span class="details-label">Plant:</span>
                                    <span class="details-value">{{item.plant?.name || 'N/A'}}</span>
                                </div>
                                <div class="details-row">
                                    <span class="details-label">Zone:</span>
                                    <span class="details-value">{{item.zone?.zoneName || 'N/A'}}</span>
                                </div>
                                <div class="details-row">
                                    <span class="details-label">Area:</span>
                                    <span class="details-value">{{item.qrCode?.zoneArea || item.zoneArea || 'N/A'}}</span>
                                </div>
                                <div class="details-row">
                                    <span class="details-label">Type:</span>
                                    <span class="details-value">
                                        <span *ngIf="item.type == 0" class="badge bg-success">Safe Act</span>
                                        <span *ngIf="item.type == 1" class="badge bg-danger">Unsafe Act</span>
                                        <span *ngIf="item.type == 2" class="badge bg-info">Unsafe Conditions</span>
                                    </span>
                                </div>
                                <div class="details-row">
                                    <span class="details-label">Title:</span>
                                    <span class="details-value">{{ item.title }}</span>
                                </div>
                                <div class="details-row" *ngIf="item.relatesTos && item.relatesTos.length > 0">
                                    <span class="details-label">Relates To:</span>
                                    <span class="details-value">
                                        <ul class="list-unstyled mb-0 ps-0">
                                            <li *ngFor="let rel of item.relatesTos">- {{ $any(rel).title }}</li>
                                        </ul>
                                    </span>
                                </div>
                                <div class="details-row" *ngIf="item.thirdPartyCompanyName">
                                    <span class="details-label">3rd Party:</span>
                                    <span class="details-value">{{ item.thirdPartyCompanyName }}</span>
                                </div>
                            </div>
                        </td>
                        <td class="text-center">
                            <app-carousel [images]="item.images ?? []"></app-carousel>
                        </td>
                        <td class="text-center">
                            <app-carousel [images]="item.fixImages ?? []"></app-carousel>
                        </td>
                        <td class="observation-cell">{{ item.fixedItDescription || 'N/A' }}</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
    <div class="card-footer text-muted text-center"> <app-pagination [currentPage]="currentPage"
            [totalItems]="totalItems" [itemsPerPage]="itemsPerPage"
            (pageChange)="onPageChange($event)"></app-pagination> </div>
</div>

<!-- Open Observations (Tab 1) -->
<div *ngIf="selectedTabIndex==1" class="card custom-card" id="open-observation-list">
    <div class="card-header">
        <div class="row align-items-center">
            <div class="col">
                <h6 class="mb-0">Open Observations</h6>
            </div>
            <div class="col text-end d-flex align-items-center justify-content-end">
                <div ngbDropdown class="d-inline-block me-2">
                    <button type="button" class="btn btn-sm adani-btn dropdown-toggle" id="downloadObsExcelDropdown1"
                        ngbDropdownToggle [disabled]="isDownloadingExcel || listLoading">
                        <span *ngIf="!isDownloadingExcel"> <i class="bi bi-file-earmark-excel me-1"></i> Download Excel
                        </span>
                        <span *ngIf="isDownloadingExcel"> <span class="spinner-border spinner-border-sm me-1"></span>
                            Downloading {{ downloadType === 'current' ? 'Page' : 'All' }}... </span>
                    </button>
                    <ul ngbDropdownMenu aria-labelledby="downloadObsExcelDropdown1">
                        <li> <button type="button" ngbDropdownItem (click)="downloadExcel('current')"
                                [disabled]="isDownloadingExcel || listLoading || (getCurrentListData()?.length ?? 0) === 0">
                                <i class="bi bi-download me-1"></i> Download Current Page ({{
                                getCurrentListData()?.length ?? 0 }}) </button> </li>
                        <li> <button type="button" ngbDropdownItem (click)="downloadExcel('all')"
                                [disabled]="isDownloadingExcel || listLoading || totalItems === 0"> <i
                                    class="bi bi-cloud-download me-1"></i> Download All Filtered ({{ totalItems }})
                            </button> </li>
                    </ul>
                </div>
                <img src="../../../assets/svg/filter.svg" class="filter-button ms-3" (click)="openFilterModal()"
                    alt="Filter" />
            </div>
        </div>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered custom-table">
                <thead class="table-header text-center">
                    <tr>
                        <th scope="col" class="col-actions">Actions</th>
                        <th scope="col" class="col-user-details">User Details</th>
                        <th scope="col" class="col-assigned-to">Assigned To</th>
                        <th scope="col" class="col-plant-details">Plant Details</th>
                        <th scope="col" class="col-images">Images</th>
                    </tr>
                </thead>
                <tbody>
                    <tr *ngIf="listLoading">
                        <td colspan="5" class="text-center p-4">
                            <span class="spinner-border spinner-border-sm me-2"></span> Loading...
                        </td>
                    </tr>
                    <tr *ngIf="!listLoading && (!openObservationList || openObservationList.length === 0)">
                        <td colspan="5" class="text-center p-4 text-muted">No open observations found.</td>
                    </tr>
                    <tr *ngFor="let item of openObservationList; let i = index">
                        <td class="actions text-center">
                            <button type="button" class="btn adani-btn w-100 mb-2"
                                (click)="openUpdateStatusConfirmation(item, ObservationStatus.Closed)"
                                title="Close Observation">Close Observation</button>
                            <div class="d-flex justify-content-center">
                                <div class="btn-group" role="group" aria-label="Observation Actions">
                                    <button type="button" *ngIf="adminrole == 'superadmin'" class="btn btn-sm btn-warning" title="Edit"
                                        (click)="openEditModal(item)">
                                        <i class="bi bi-pencil"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-info" title="Details" (click)="openDetailsModal(item)">
                                        <i class="bi bi-eye"></i>
                                    </button>
                                </div>
                            </div>
                        </td>
                        <td>
                            <div class="details-container" *ngIf="item.admin">
                                <div class="details-row">
                                    <span class="details-label">Name:</span>
                                    <span class="details-value">{{ item.admin.firstName }} {{ item.admin.lastName }}</span>
                                </div>
                                <div class="details-row">
                                    <span class="details-label">Contact:</span>
                                    <span class="details-value">{{ item.admin.contactNumber || 'N/A'}}</span>
                                </div>
                                <div class="details-row">
                                    <span class="details-label">Email:</span>
                                    <span class="details-value">{{ item.admin.email || 'N/A'}}</span>
                                </div>
                            </div>
                            <div class="details-container" *ngIf="!item.admin && item.fullName">
                                <div class="details-row">
                                    <span class="details-label">Name:</span>
                                    <span class="details-value">{{ item.fullName }}</span>
                                </div>
                                <div class="details-row">
                                    <span class="details-label">Contact:</span>
                                    <span class="details-value">{{ item.contactNumber || 'N/A'}}</span>
                                </div>
                                <div class="details-row">
                                    <span class="details-label">Email:</span>
                                    <span class="details-value">{{ item.email || 'N/A'}}</span>
                                </div>
                            </div>
                            <span *ngIf="!item.admin && !item.fullName">N/A</span>
                        </td>
                        <td>
                            <div class="details-container" *ngIf="item.assignToAdmin">
                                <div class="details-row">
                                    <span class="details-label">Name:</span>
                                    <span class="details-value">{{item.assignToAdmin.firstName}} {{item.assignToAdmin.lastName}}</span>
                                </div>
                                <div class="details-row">
                                    <span class="details-label">Contact:</span>
                                    <span class="details-value">{{item.assignToAdmin.contactNumber || 'N/A'}}</span>
                                </div>
                                <div class="details-row">
                                    <span class="details-label">Email:</span>
                                    <span class="details-value">{{item.assignToAdmin.email || 'N/A'}}</span>
                                </div>
                            </div>
                            <span *ngIf="!item.assignToAdmin">N/A</span>
                        </td>
                        <td>
                            <div class="details-container">
                                <div class="details-row">
                                    <span class="details-label">Plant:</span>
                                    <span class="details-value">{{ item.plant?.name || 'N/A'}}</span>
                                </div>
                                <div class="details-row">
                                    <span class="details-label">Zone:</span>
                                    <span class="details-value">{{ item.zone?.zoneName || 'N/A'}}</span>
                                </div>
                                <div class="details-row">
                                    <span class="details-label">Area:</span>
                                    <span class="details-value">{{ item.qrCode?.zoneArea || item.zoneArea || 'N/A'}}</span>
                                </div>
                                <div class="details-row">
                                    <span class="details-label">Type:</span>
                                    <span class="details-value">
                                        <span *ngIf="item.type == 0" class="badge bg-success">Safe Act</span>
                                        <span *ngIf="item.type == 1" class="badge bg-danger">Unsafe Act</span>
                                        <span *ngIf="item.type == 2" class="badge bg-info">Unsafe Conditions</span>
                                    </span>
                                </div>
                                <div class="details-row">
                                    <span class="details-label">Title:</span>
                                    <span class="details-value">{{ item.title }}</span>
                                </div>
                                <div class="details-row" *ngIf="item.relatesTos && item.relatesTos.length > 0">
                                    <span class="details-label">Relates To:</span>
                                    <span class="details-value">
                                        <ul class="list-unstyled mb-0 ps-0">
                                            <li *ngFor="let rel of item.relatesTos">- {{ $any(rel).title }}</li>
                                        </ul>
                                    </span>
                                </div>
                                <div class="details-row" *ngIf="item.thirdPartyCompanyName">
                                    <span class="details-label">3rd Party:</span>
                                    <span class="details-value">{{ item.thirdPartyCompanyName }}</span>
                                </div>
                            </div>
                        </td>
                        <td class="text-center">
                            <app-carousel [images]="item.images ?? []"></app-carousel>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
    <div class="card-footer text-muted text-center"> <app-pagination [currentPage]="currentPage"
            [totalItems]="totalItems" [itemsPerPage]="itemsPerPage"
            (pageChange)="onPageChange($event)"></app-pagination> </div>
</div>

<!-- Closed Observations (Tab 2) -->
<div *ngIf="selectedTabIndex==2" class="card custom-card" id="closed-observation-list">
    <div class="card-header">
        <div class="row align-items-center">
            <div class="col">
                <h6 class="mb-0">Closed Observations</h6>
            </div>
            <div class="col text-end d-flex align-items-center justify-content-end">
                <div ngbDropdown class="d-inline-block me-2">
                    <button type="button" class="btn btn-sm adani-btn dropdown-toggle" id="downloadObsExcelDropdown2"
                        ngbDropdownToggle [disabled]="isDownloadingExcel || listLoading">
                        <span *ngIf="!isDownloadingExcel"> <i class="bi bi-file-earmark-excel me-1"></i> Download Excel
                        </span>
                        <span *ngIf="isDownloadingExcel"> <span class="spinner-border spinner-border-sm me-1"></span>
                            Downloading {{ downloadType === 'current' ? 'Page' : 'All' }}... </span>
                    </button>
                    <ul ngbDropdownMenu aria-labelledby="downloadObsExcelDropdown2">
                        <li> <button type="button" ngbDropdownItem (click)="downloadExcel('current')"
                                [disabled]="isDownloadingExcel || listLoading || (getCurrentListData()?.length ?? 0) === 0">
                                <i class="bi bi-download me-1"></i> Download Current Page ({{
                                getCurrentListData()?.length ?? 0 }}) </button> </li>
                        <li> <button type="button" ngbDropdownItem (click)="downloadExcel('all')"
                                [disabled]="isDownloadingExcel || listLoading || totalItems === 0"> <i
                                    class="bi bi-cloud-download me-1"></i> Download All Filtered ({{ totalItems }})
                            </button> </li>
                    </ul>
                </div>
                <img src="../../../assets/svg/filter.svg" class="filter-button ms-3" (click)="openFilterModal()"
                    alt="Filter" />
            </div>
        </div>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered custom-table">
                <thead class="table-header text-center">
                    <tr>
                        <th scope="col" class="col-actions">Actions</th>
                        <th scope="col" class="col-user-details">User Details</th>
                        <th scope="col" class="col-assigned-to">Assigned To</th>
                        <th scope="col" class="col-plant-details">Plant Details</th>
                        <th scope="col" class="col-images">Images</th>
                        <th scope="col" class="col-fixed-images">Fixed Images</th>
                    </tr>
                </thead>
                <tbody>
                    <tr *ngIf="listLoading">
                        <td colspan="6" class="text-center p-4">
                            <span class="spinner-border spinner-border-sm me-2"></span> Loading...
                        </td>
                    </tr>
                    <tr *ngIf="!listLoading && (!closeObservationList || closeObservationList.length === 0)">
                        <td colspan="6" class="text-center p-4 text-muted">No closed observations found.</td>
                    </tr>
                    <tr *ngFor="let item of closeObservationList; let i = index">
                        <td class="actions text-center">
                            <button type="button" class="btn adani-btn w-100 mb-2"
                                (click)="openUpdateStatusConfirmation(item, ObservationStatus.Open)"
                                title="Open Observation">Open Observation</button>
                            <div class="d-flex justify-content-center">
                                <div class="btn-group" role="group" aria-label="Observation Actions">
                                    <button type="button" *ngIf="adminrole == 'superadmin'" class="btn btn-sm btn-warning" title="Edit"
                                        (click)="openEditModal(item)">
                                        <i class="bi bi-pencil"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-info" title="Details" (click)="openDetailsModal(item)">
                                        <i class="bi bi-eye"></i>
                                    </button>
                                </div>
                            </div>
                        </td>
                        <td>
                            <div class="details-container" *ngIf="item.admin">
                                <div class="details-row">
                                    <span class="details-label">Name:</span>
                                    <span class="details-value">{{ item.admin.firstName }} {{ item.admin.lastName }}</span>
                                </div>
                                <div class="details-row">
                                    <span class="details-label">Contact:</span>
                                    <span class="details-value">{{ item.admin.contactNumber || 'N/A'}}</span>
                                </div>
                                <div class="details-row">
                                    <span class="details-label">Email:</span>
                                    <span class="details-value">{{ item.admin.email || 'N/A'}}</span>
                                </div>
                            </div>
                            <div class="details-container" *ngIf="!item.admin && item.fullName">
                                <div class="details-row">
                                    <span class="details-label">Name:</span>
                                    <span class="details-value">{{ item.fullName }}</span>
                                </div>
                                <div class="details-row">
                                    <span class="details-label">Contact:</span>
                                    <span class="details-value">{{ item.contactNumber || 'N/A'}}</span>
                                </div>
                                <div class="details-row">
                                    <span class="details-label">Email:</span>
                                    <span class="details-value">{{ item.email || 'N/A'}}</span>
                                </div>
                            </div>
                            <span *ngIf="!item.admin && !item.fullName">N/A</span>
                        </td>
                        <td>
                            <div class="details-container" *ngIf="item.assignToAdmin">
                                <div class="details-row">
                                    <span class="details-label">Name:</span>
                                    <span class="details-value">{{item.assignToAdmin.firstName}} {{item.assignToAdmin.lastName}}</span>
                                </div>
                                <div class="details-row">
                                    <span class="details-label">Contact:</span>
                                    <span class="details-value">{{item.assignToAdmin.contactNumber || 'N/A'}}</span>
                                </div>
                                <div class="details-row">
                                    <span class="details-label">Email:</span>
                                    <span class="details-value">{{item.assignToAdmin.email || 'N/A'}}</span>
                                </div>
                            </div>
                            <span *ngIf="!item.assignToAdmin">N/A</span>
                        </td>
                        <td>
                            <div class="details-container">
                                <div class="details-row">
                                    <span class="details-label">Plant:</span>
                                    <span class="details-value">{{ item.plant?.name || 'N/A'}}</span>
                                </div>
                                <div class="details-row">
                                    <span class="details-label">Zone:</span>
                                    <span class="details-value">{{ item.zone?.zoneName || 'N/A'}}</span>
                                </div>
                                <div class="details-row">
                                    <span class="details-label">Area:</span>
                                    <span class="details-value">{{ item.qrCode?.zoneArea || item.zoneArea || 'N/A'}}</span>
                                </div>
                                <div class="details-row">
                                    <span class="details-label">Type:</span>
                                    <span class="details-value">
                                        <span *ngIf="item.type == 0" class="badge bg-success">Safe Act</span>
                                        <span *ngIf="item.type == 1" class="badge bg-danger">Unsafe Act</span>
                                        <span *ngIf="item.type == 2" class="badge bg-info">Unsafe Conditions</span>
                                    </span>
                                </div>
                                <div class="details-row">
                                    <span class="details-label">Title:</span>
                                    <span class="details-value">{{ item.title }}</span>
                                </div>
                                <div class="details-row" *ngIf="item.relatesTos && item.relatesTos.length > 0">
                                    <span class="details-label">Relates To:</span>
                                    <span class="details-value">
                                        <ul class="list-unstyled mb-0 ps-0">
                                            <li *ngFor="let rel of item.relatesTos">- {{ $any(rel).title }}</li>
                                        </ul>
                                    </span>
                                </div>
                                <div class="details-row" *ngIf="item.thirdPartyCompanyName">
                                    <span class="details-label">3rd Party:</span>
                                    <span class="details-value">{{ item.thirdPartyCompanyName }}</span>
                                </div>
                            </div>
                        </td>
                        <td class="text-center">
                            <app-carousel [images]="item.images ?? []"></app-carousel>
                        </td>
                        <td class="text-center">
                            <app-carousel [images]="item.fixImages ?? []"></app-carousel>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
    <div class="card-footer text-muted text-center"> <app-pagination [currentPage]="currentPage"
            [totalItems]="totalItems" [itemsPerPage]="itemsPerPage"
            (pageChange)="onPageChange($event)"></app-pagination> </div>
</div>

<!-- Filter Offcanvas -->
<app-offcanvas [title]="'Filter Observations'" *ngIf="isFilterModalOpen" (onClickCross)="closeFilterModal()">
    <div class="filter-container p-3">
        <form #filterForm="ngForm" (ngSubmit)="applyFilters()" novalidate>
            <div class="row g-3">
                <div class="col-12">
                    <label class="form-label" for="filterFirstName">Reporter First Name</label>
                    <input type="text" id="filterFirstName" class="form-control" placeholder="First Name"
                        [(ngModel)]="filters.firstName" name="firstName" maxlength="30" pattern="^[a-zA-Z\s]*$"
                        #firstName="ngModel"
                        [ngClass]="{'is-invalid': firstName.invalid && (firstName.dirty || firstName.touched)}"
                        (blur)="trimInputField(filters, 'firstName')">
                    <div *ngIf="firstName.invalid && (firstName.dirty || firstName.touched)" class="invalid-feedback">
                        <div *ngIf="firstName.errors?.['maxlength']">First name cannot exceed 30 characters.</div>
                        <div *ngIf="firstName.errors?.['pattern']">First name should contain only alphabets.</div>
                    </div>
                </div>

                <div class="col-12">
                    <label class="form-label" for="filterLastName">Reporter Last Name</label>
                    <input type="text" id="filterLastName" class="form-control" placeholder="Last Name"
                        [(ngModel)]="filters.lastName" name="lastName" maxlength="30" pattern="^[a-zA-Z\s]*$"
                        #lastName="ngModel"
                        [ngClass]="{'is-invalid': lastName.invalid && (lastName.dirty || lastName.touched)}"
                        (blur)="trimInputField(filters, 'lastName')">
                    <div *ngIf="lastName.invalid && (lastName.dirty || lastName.touched)" class="invalid-feedback">
                        <div *ngIf="lastName.errors?.['maxlength']">Last name cannot exceed 30 characters.</div>
                        <div *ngIf="lastName.errors?.['pattern']">Last name should contain only alphabets.</div>
                    </div>
                </div>

                <div class="col-12">
                    <label class="form-label" for="filterEmail">Reporter Email</label>
                    <input type="email" id="filterEmail" class="form-control" placeholder="Email"
                        [(ngModel)]="filters.email" name="email" email pattern="^[a-zA-Z0-9._%+-]+@adani\.com$"
                        #email="ngModel" [ngClass]="{'is-invalid': email.invalid && (email.dirty || email.touched)}"
                        (blur)="trimInputField(filters, 'email')">
                    <div *ngIf="email.invalid && (email.dirty || email.touched)" class="invalid-feedback">
                        <div *ngIf="email.errors?.['email']">Please enter a valid email address.</div>
                        <div *ngIf="email.errors?.['pattern']">Email must be an adani.com address.</div>
                    </div>
                </div>

                <div class="col-12">
                    <label class="form-label" for="filterContactNumber">Reporter Contact</label>
                    <input type="text" id="filterContactNumber" class="form-control" placeholder="Contact"
                        [(ngModel)]="filters.contactNumber" name="contactNumber" pattern="^[0-9]{10}$" maxlength="10"
                        #contactNumber="ngModel"
                        [ngClass]="{'is-invalid': contactNumber.invalid && (contactNumber.dirty || contactNumber.touched)}"
                        (blur)="trimInputField(filters, 'contactNumber')">
                    <div *ngIf="contactNumber.invalid && (contactNumber.dirty || contactNumber.touched)"
                        class="invalid-feedback">
                        <div *ngIf="contactNumber.errors?.['pattern']">Please enter a valid 10-digit number.</div>
                    </div>
                </div>

                <div class="col-12">
                    <label class="form-label">Date Range (Reported)</label>
                    <div class="input-group">
                        <input type="date" class="form-control" [(ngModel)]="filters.startDate" name="startDate"
                            #startDate="ngModel"
                            [ngClass]="{'is-invalid': startDate.invalid && (startDate.dirty || startDate.touched)}">
                        <span class="input-group-text">to</span>
                        <input type="date" class="form-control" [(ngModel)]="filters.endDate" name="endDate"
                            #endDate="ngModel"
                            [ngClass]="{'is-invalid': endDate.invalid && (endDate.dirty || endDate.touched)}">
                    </div>
                    <div *ngIf="endDate.value && startDate.value && endDate.value < startDate.value"
                        class="text-danger small mt-1">
                        End date cannot be earlier than start date.
                    </div>
                </div>

                <div class="col-12">
                    <label class="form-label" for="filterPlant">Select Plant</label>
                    <select id="filterPlant" class="form-select" [(ngModel)]="filters.plantId" name="plantId">
                        <option [ngValue]="null">All Plants</option>
                        <option *ngFor="let plant of plants" [value]="plant.id">{{ plant.name }}</option>
                    </select>
                </div>

                <div class="col-12">
                    <label class="form-label" for="filterEnabled">Enabled Status</label>
                    <select id="filterEnabled" class="form-select" [(ngModel)]="filters.enabled" name="enabled">
                        <option [ngValue]="null">Any</option>
                        <option [ngValue]="true">Yes</option>
                        <option [ngValue]="false">No</option>
                    </select>
                </div>

                <div class="col-12">
                    <label class="form-label" for="filterSortField">Sort By</label>
                    <select id="filterSortField" class="form-select" [(ngModel)]="filters.sortField" name="sortField"
                        required #sortField="ngModel"
                        [ngClass]="{'is-invalid': sortField.invalid && (sortField.dirty || sortField.touched)}">
                        <option [ngValue]="null" selected>ID Desc</option>
                        <option *ngFor="let field of availableSortFields" [value]="field.value">{{ field.label }}
                        </option>
                    </select>
                    <div *ngIf="sortField.invalid && (sortField.dirty || sortField.touched)" class="invalid-feedback">
                        <div *ngIf="sortField.errors?.['required']">Sort field is required.</div>
                    </div>

                    <label class="form-label mt-2" for="filterSortDir">Sort Direction</label>
                    <select id="filterSortDir" class="form-select" [(ngModel)]="filters.sortDirection"
                        name="sortDirection" required #sortDirection="ngModel"
                        [ngClass]="{'is-invalid': sortDirection.invalid && (sortDirection.dirty || sortDirection.touched)}">
                        <option value="DESC">Descending</option>
                        <option value="ASC">Ascending</option>
                    </select>
                    <div *ngIf="sortDirection.invalid && (sortDirection.dirty || sortDirection.touched)"
                        class="invalid-feedback">
                        <div *ngIf="sortDirection.errors?.['required']">Sort direction is required.</div>
                    </div>
                </div>

                <div class="col-12 mt-4 d-grid gap-2">
                    <button type="submit" class="btn adani-btn"
                        [disabled]="filterForm.invalid || (endDate.value && startDate.value && endDate.value < startDate.value)">
                        <i class="bi bi-search me-1"></i> Search
                    </button>
                    <button type="button" class="btn btn-secondary" (click)="resetFilters()">
                        <i class="bi bi-arrow-clockwise me-1"></i> Reset Filters
                    </button>
                </div>
            </div>
        </form>
    </div>
</app-offcanvas>

<!-- Edit Offcanvas (Reactive Forms) -->
<app-offcanvas [title]="'Edit Observation'" [width]="'500'" *ngIf="isEditModalOpen" (onClickCross)="closeEditModal()">
    <div class="edit-container p-3">
        <div *ngIf="!editUserForm && isEditModalOpen" class="text-center p-5">
            <div class="spinner-border spinner-border-sm"></div> Initializing Form...
        </div>

        <form *ngIf="editUserForm" [formGroup]="editUserForm" (ngSubmit)="submitEditForm()">
            <div class="row g-3">
                <!-- Row 1: Switches -->
                <div class="col-md-6">
                    <div class="form-check form-switch"><input class="form-check-input" type="checkbox" role="switch"
                            id="editObsEnabled" formControlName="enabled"><label class="form-check-label"
                            for="editObsEnabled">Enabled</label></div>
                </div>
                <div class="col-md-6">
                    <div class="form-check form-switch"><input class="form-check-input" type="checkbox" role="switch"
                            id="editObsIsFixedIt" formControlName="isFixedIt"><label class="form-check-label"
                            for="editObsIsFixedIt">Fixed It</label></div>
                </div>

                <!-- Row 2: Reporter Info -->
                <h6 class="col-12 mt-3 text-primary border-top pt-2">Reporter Details (Read-only)</h6>
                <ng-container *ngIf="selectedObservationForEdit?.adminId">
                    <div class="col-md-6"><label class="form-label">First Name</label><input type="text"
                            class="form-control form-control-sm bg-light"
                            [value]="selectedObservationForEdit?.admin?.firstName" readonly disabled></div>
                    <div class="col-md-6"><label class="form-label">Last Name</label><input type="text"
                            class="form-control form-control-sm bg-light"
                            [value]="selectedObservationForEdit?.admin?.lastName" readonly disabled></div>
                    <div class="col-md-6"><label class="form-label">Contact</label><input type="text"
                            class="form-control form-control-sm bg-light"
                            [value]="selectedObservationForEdit?.admin?.contactNumber" readonly disabled></div>
                    <div class="col-md-6"><label class="form-label">Email</label><input type="email"
                            class="form-control form-control-sm bg-light"
                            [value]="selectedObservationForEdit?.admin?.email" readonly disabled></div>
                </ng-container>
                <ng-container *ngIf="!selectedObservationForEdit?.adminId">
                    <div class="col-12 text-muted small mb-2">Non-Admin Reporter Details</div>
                    <div class="col-md-4">
                        <label class="form-label" for="editFullName">Full Name</label>
                        <input type="text" id="editFullName" class="form-control" formControlName="fullName"
                            [ngClass]="{'is-invalid': editUserForm.get('fullName')?.invalid && (editUserForm.get('fullName')?.touched || editUserForm.get('fullName')?.dirty)}"
                            (blur)="trimInputField(editUserForm.getRawValue(), 'fullName'); editUserForm.get('fullName')?.setValue(editUserForm.getRawValue().fullName)">
                        <div *ngIf="editUserForm.get('fullName')?.invalid && (editUserForm.get('fullName')?.touched || editUserForm.get('fullName')?.dirty)"
                            class="text-danger small mt-1">
                            <div *ngIf="editUserForm.get('fullName')?.errors?.['pattern']">Name should contain only
                                alphabets.</div>
                            <div *ngIf="editUserForm.get('fullName')?.errors?.['maxlength']">Name cannot exceed 50
                                characters.</div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <label class="form-label" for="editGuestEmail">Email</label>
                        <input type="email" id="editGuestEmail" class="form-control" formControlName="email"
                            [ngClass]="{'is-invalid': editUserForm.get('email')?.invalid && (editUserForm.get('email')?.touched || editUserForm.get('email')?.dirty)}"
                            (blur)="trimInputField(editUserForm.getRawValue(), 'email'); editUserForm.get('email')?.setValue(editUserForm.getRawValue().email)">
                        <div *ngIf="editUserForm.get('email')?.invalid && (editUserForm.get('email')?.touched || editUserForm.get('email')?.dirty)"
                            class="text-danger small mt-1">
                            <div *ngIf="editUserForm.get('email')?.errors?.['email']">Please enter a valid email
                                address.</div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <label class="form-label" for="editGuestContact">Contact</label>
                        <input type="text" id="editGuestContact" class="form-control" formControlName="contactNumber"
                            maxlength="10"
                            [ngClass]="{'is-invalid': editUserForm.get('contactNumber')?.invalid && (editUserForm.get('contactNumber')?.touched || editUserForm.get('contactNumber')?.dirty)}"
                            (blur)="trimInputField(editUserForm.getRawValue(), 'contactNumber'); editUserForm.get('contactNumber')?.setValue(editUserForm.getRawValue().contactNumber)">
                        <div *ngIf="editUserForm.get('contactNumber')?.invalid && (editUserForm.get('contactNumber')?.touched || editUserForm.get('contactNumber')?.dirty)"
                            class="text-danger small mt-1">
                            <div *ngIf="editUserForm.get('contactNumber')?.errors?.['pattern']">Please enter a valid
                                10-digit number.</div>
                        </div>
                    </div>
                </ng-container>

                <!-- Row 4: Location -->
                <h6 class="col-12 mt-3 text-primary border-top pt-2">Location</h6>
                <div class="col-md-4">
                    <label class="form-label" for="editPlantId">Plant <span class="text-danger">*</span></label>
                    <select id="editPlantId" class="form-select" formControlName="plantId" required
                        (change)="onEditPlantSelectionChange($event)">
                        <option [ngValue]="null" disabled>-- Select Plant --</option>
                        <option *ngFor="let plant of plants" [value]="plant.id">{{ plant.name }}</option>
                    </select>
                    <div *ngIf="editUserForm.get('plantId')?.invalid && (editUserForm.get('plantId')?.touched || editUserForm.get('plantId')?.dirty)"
                        class="text-danger small mt-1">Plant is required.</div>
                </div>
                <div class="col-md-4">
                    <label class="form-label" for="editZoneId">Zone</label>
                    <select id="editZoneId" class="form-select" formControlName="zoneId"
                        (change)="onEditZoneChange($event)">
                        <option [ngValue]="null">-- Select Zone --</option>
                        <option *ngFor="let zone of editAvailableZones" [value]="zone.id">{{ $any(zone).zoneName }}
                        </option>
                    </select>
                </div>
                <div class="col-md-4">
                    <label class="form-label" for="editQrCodeId">QR Code (Zone Area)</label>
                    <select id="editQrCodeId" class="form-select" formControlName="qrCodeId"
                        [disabled]="!editUserForm.get('zoneId')?.value">
                        <option [ngValue]="null"> {{ !editUserForm.get('zoneId')?.value ? 'Select Zone First' : '--
                            Select QR Area --' }} </option>
                        <option *ngFor="let qr of availableQrCodes" [value]="qr.id">{{ qr.zoneArea || ('QR ID ' + qr.id)
                            }}</option>
                    </select>
                </div>

                <!-- Row 5: Observation Details -->
                <h6 class="col-12 mt-3 text-primary border-top pt-2">Observation Details</h6>
                <div class="col-md-4">
                    <label class="form-label" for="editObsType">Observation Type</label>
                    <select id="editObsType" class="form-select" formControlName="type">
                        <option [ngValue]="null">-- Select Type --</option>
                        <option *ngFor="let type of observationTypes" [value]="type.value">{{ type.label }}</option>
                    </select>
                </div>
                <div class="col-12">
                    <label class="form-label" for="editObsTitle">Observation Title/Desc <span
                            class="text-danger">*</span></label>
                    <textarea id="editObsTitle" class="form-control" rows="3" placeholder="Title/Description"
                        formControlName="title" required maxlength="200"
                        (blur)="trimInputField(editUserForm.getRawValue(), 'title'); editUserForm.get('title')?.setValue(editUserForm.getRawValue().title)"></textarea>
                    <div class="d-flex justify-content-between mt-1">
                        <div *ngIf="editUserForm.get('title')?.invalid && (editUserForm.get('title')?.touched || editUserForm.get('title')?.dirty)"
                            class="text-danger small">
                            <div *ngIf="editUserForm.get('title')?.errors?.['required']">Title/Description is required.
                            </div>
                            <div *ngIf="editUserForm.get('title')?.errors?.['maxlength']">Title/Description cannot
                                exceed 200 characters.</div>
                        </div>
                        <small class="text-muted ms-auto">{{ editUserForm.get('title')?.value?.length || 0 }}/200
                            characters</small>
                    </div>
                </div>

                <!-- Row 6: Conditional Fields -->
                <ng-container *ngIf="editUserForm.get('isFixedIt')?.value">
                    <div class="col-12">
                        <label class="form-label" for="editFixedDesc">Fixed It Description <span
                                class="text-danger">*</span></label>
                        <textarea id="editFixedDesc" class="form-control" rows="3" placeholder="Details of fix"
                            formControlName="fixedItDescription" maxlength="200"
                            [ngClass]="{'is-invalid': editUserForm.get('fixedItDescription')?.invalid && (editUserForm.get('fixedItDescription')?.touched || editUserForm.get('fixedItDescription')?.dirty)}"
                            (blur)="trimInputField(editUserForm.getRawValue(), 'fixedItDescription'); editUserForm.get('fixedItDescription')?.setValue(editUserForm.getRawValue().fixedItDescription)"></textarea>
                        <div class="d-flex justify-content-between mt-1">
                            <div *ngIf="editUserForm.get('fixedItDescription')?.invalid && (editUserForm.get('fixedItDescription')?.touched || editUserForm.get('fixedItDescription')?.dirty)"
                                class="text-danger small">
                                <div *ngIf="editUserForm.get('fixedItDescription')?.errors?.['required']">Fixed It
                                    Description is required when Fixed It is enabled.</div>
                                <div *ngIf="editUserForm.get('fixedItDescription')?.errors?.['maxlength']">Description
                                    cannot exceed 200 characters.</div>
                            </div>
                            <small class="text-muted ms-auto">{{ editUserForm.get('fixedItDescription')?.value?.length
                                || 0 }}/200 characters</small>
                        </div>
                    </div>
                </ng-container>
                <ng-container *ngIf="!selectedObservationForEdit?.adminId">
                    <div class="col-md-6">
                        <label class="form-label" for="editCounselingName">Person(s) Counselled</label>
                        <input type="text" id="editCounselingName" class="form-control" placeholder="Name(s)"
                            formControlName="counselingPersonName"
                            [ngClass]="{'is-invalid': editUserForm.get('counselingPersonName')?.invalid && (editUserForm.get('counselingPersonName')?.touched || editUserForm.get('counselingPersonName')?.dirty)}"
                            (blur)="trimInputField(editUserForm.getRawValue(), 'counselingPersonName'); editUserForm.get('counselingPersonName')?.setValue(editUserForm.getRawValue().counselingPersonName)">
                        <div *ngIf="editUserForm.get('counselingPersonName')?.invalid && (editUserForm.get('counselingPersonName')?.touched || editUserForm.get('counselingPersonName')?.dirty)"
                            class="text-danger small mt-1">
                            <div *ngIf="editUserForm.get('counselingPersonName')?.errors?.['pattern']">Name should
                                contain only alphabets.</div>
                            <div *ngIf="editUserForm.get('counselingPersonName')?.errors?.['maxlength']">Name cannot
                                exceed 50 characters.</div>
                        </div>
                    </div>
                    <div class="col-md-6" *ngIf="editUserForm.get('isFixedIt')?.value">
                        <label class="form-label" for="editThirdPartyName">Third Party Company</label>
                        <!-- Corrected Typo -->
                        <input type="text" id="editThirdPartyName" class="form-control" placeholder="Company Name"
                            formControlName="thirdPartyCompanyName"
                            [ngClass]="{'is-invalid': editUserForm.get('thirdPartyCompanyName')?.invalid && (editUserForm.get('thirdPartyCompanyName')?.touched || editUserForm.get('thirdPartyCompanyName')?.dirty)}"
                            (blur)="trimInputField(editUserForm.getRawValue(), 'thirdPartyCompanyName'); editUserForm.get('thirdPartyCompanyName')?.setValue(editUserForm.getRawValue().thirdPartyCompanyName)">
                        <div *ngIf="editUserForm.get('thirdPartyCompanyName')?.invalid && (editUserForm.get('thirdPartyCompanyName')?.touched || editUserForm.get('thirdPartyCompanyName')?.dirty)"
                            class="text-danger small mt-1">
                            <div *ngIf="editUserForm.get('thirdPartyCompanyName')?.errors?.['maxlength']">Company name
                                cannot exceed 100 characters.</div>
                        </div>
                    </div>
                </ng-container>

                <!-- Row 7: Image Management -->
                <h6 class="col-12 mt-3 text-primary border-top pt-2">Manage Media</h6>
                <div class="col-md-6 border-end">
                    <label for="newObsImages" class="form-label small">Add New Observation Images</label>
                    <input class="form-control form-control-sm" type="file" id="newObsImages" multiple accept="image/*"
                        (change)="handleNewImageChange($event)">
                    <div *ngIf="(editNewImagePreviews?.length ?? 0) > 0" class="mt-2 new-images-preview">
                        <div *ngFor="let previewUrl of editNewImagePreviews; let i = index"
                            class="image-preview-container position-relative me-2 mb-2 border p-1">
                            <img [src]="previewUrl" alt="New Observation Image Preview {{i+1}}" class="img-thumbnail">
                            <button type="button"
                                class="btn btn-xs btn-outline-danger position-absolute top-0 end-0 p-0 px-1 remove-btn"
                                aria-label="Remove" (click)="removeNewImage(i)"><i
                                    class="bi bi-x-lg small"></i></button>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <label for="newFixImages" class="form-label small">Add New Fixed Images</label>
                    <input class="form-control form-control-sm" type="file" id="newFixImages" multiple accept="image/*"
                        (change)="handleNewFixImageChange($event)">
                    <div *ngIf="(editNewFixImagePreviews?.length ?? 0) > 0" class="mt-2 new-images-preview">
                        <div *ngFor="let previewUrl of editNewFixImagePreviews; let i = index"
                            class="image-preview-container position-relative me-2 mb-2 border p-1">
                            <img [src]="previewUrl" alt="New Fixed Image Preview {{i+1}}" class="img-thumbnail">
                            <button type="button"
                                class="btn btn-xs btn-outline-danger position-absolute top-0 end-0 p-0 px-1 remove-btn"
                                aria-label="Remove" (click)="removeNewFixImage(i)"></button>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="col-12 mt-4 d-flex justify-content-end gap-2">
                    <button type="button" class="btn btn-secondary" (click)="closeEditModal()">Cancel</button>
                    <button type="submit" class="btn adani-btn" [disabled]="editUserForm.invalid || editLoading">
                        <span *ngIf="!editLoading"><i class="bi bi-save me-1"></i> Save Changes</span>
                        <span *ngIf="editLoading" class="spinner-border spinner-border-sm"></span><span
                            *ngIf="editLoading"> Saving...</span>
                    </button>
                </div>
            </div> <!-- End row -->
        </form>
    </div>
</app-offcanvas>
<!-- ********** END: Edit Offcanvas ********** -->


<!-- Details Offcanvas -->
<app-offcanvas [title]="'Observation Details'" [width]="'600px'" *ngIf="isDetailsModalOpen"
    (onClickCross)="closeDetailsModal()">
    <div class="details-container p-3">
        <div *ngIf="!selectedObservationForDetails && isDetailsModalOpen" class="text-center p-5">
            <div class="spinner-border spinner-border-sm"></div> Loading...
        </div>
        <div *ngIf="selectedObservationForDetails" class="read-only-details">
            <dl class="row mb-3">
                <dt class="col-sm-4">ID</dt>
                <dd class="col-sm-8">{{ selectedObservationForDetails.id }}</dd>
                <dt class="col-sm-4">Status</dt>
                <dd class="col-sm-8"><span
                        *ngIf="selectedObservationForDetails.status == ObservationStatus.Open">Open</span><span
                        *ngIf="selectedObservationForDetails.status == ObservationStatus.Closed">Closed</span></dd>
                <dt class="col-sm-4">Enabled?</dt>
                <dd class="col-sm-8">{{ selectedObservationForDetails.enabled ? 'Yes' : 'No' }}</dd>
                <dt class="col-sm-4">Fixed It?</dt>
                <dd class="col-sm-8">{{ selectedObservationForDetails.isFixedIt ? 'Yes' : 'No' }}</dd>
                <dt class="col-sm-4">Reward Granted?</dt>
                <dd class="col-sm-8">{{ selectedObservationForDetails.rewardGranted ? 'Yes' : 'No' }}</dd>
                <dt class="col-sm-4">Reported Date</dt>
                <dd class="col-sm-8">{{ selectedObservationForDetails.createdTimestamp | date:'medium' }}</dd>
            </dl>
            <h6 class="text-primary border-top pt-2 mb-2">Reporter Details</h6>
            <dl class="row mb-3">
                <ng-container *ngIf="selectedObservationForDetails.admin">
                    <dt class="col-sm-4">Name</dt>
                    <dd class="col-sm-8">{{ selectedObservationForDetails.admin.firstName }} {{
                        selectedObservationForDetails.admin.lastName }}</dd>
                    <dt class="col-sm-4">Contact</dt>
                    <dd class="col-sm-8">{{ selectedObservationForDetails.admin.contactNumber || 'N/A' }}</dd>
                    <dt class="col-sm-4">Email</dt>
                    <dd class="col-sm-8">{{ selectedObservationForDetails.admin.email || 'N/A' }}</dd>
                </ng-container>
                <ng-container *ngIf="!selectedObservationForDetails.admin">
                    <dt class="col-sm-4">Name</dt>
                    <dd class="col-sm-8">{{ selectedObservationForDetails.fullName || 'Guest' }}</dd>
                    <dt class="col-sm-4">Contact</dt>
                    <dd class="col-sm-8">{{ selectedObservationForDetails.contactNumber || 'N/A' }}</dd>
                    <dt class="col-sm-4">Email</dt>
                    <dd class="col-sm-8">{{ selectedObservationForDetails.email || 'N/A' }}</dd>
                </ng-container>
            </dl>
            <h6 class="text-primary border-top pt-2 mb-2">Assignment</h6>
            <dl class="row mb-3">
                <dt class="col-sm-4">Assigned To</dt>
                <dd class="col-sm-8">{{ selectedObservationForDetails.assignToAdmin ?
                    (selectedObservationForDetails.assignToAdmin.firstName + ' ' +
                    selectedObservationForDetails.assignToAdmin.lastName) : 'N/A' }}</dd>
            </dl>
            <h6 class="text-primary border-top pt-2 mb-2">Location</h6>
            <dl class="row mb-3">
                <dt class="col-sm-4">Plant</dt>
                <dd class="col-sm-8">{{ selectedObservationForDetails.plant?.name || 'N/A' }}</dd>
                <dt class="col-sm-4">Zone</dt>
                <dd class="col-sm-8">{{ selectedObservationForDetails.zone?.zoneName || 'N/A' }}</dd>
                <dt class="col-sm-4">Area (QR)</dt>
                <dd class="col-sm-8">{{ selectedObservationForDetails.qrCode?.zoneArea ||
                    selectedObservationForDetails.zoneArea || 'N/A' }}</dd>
            </dl>
            <h6 class="text-primary border-top pt-2 mb-2">Observation Details</h6>
            <dl class="row mb-3">
                <dt class="col-sm-4">Type</dt>
                <dd class="col-sm-8"><span *ngIf="selectedObservationForDetails.type === 0"
                        class="badge bg-success">Safe Act</span><span *ngIf="selectedObservationForDetails.type === 1"
                        class="badge bg-danger">Unsafe Act</span><span *ngIf="selectedObservationForDetails.type === 2"
                        class="badge bg-info">Unsafe Conditions</span><span
                        *ngIf="selectedObservationForDetails.type == null">N/A</span></dd>
                <dt class="col-sm-4">Title/Desc</dt>
                <dd class="col-sm-8 pre-wrap">{{ selectedObservationForDetails.title || 'N/A' }}</dd>
                <dt class="col-sm-4">Relates To</dt>
                <dd class="col-sm-8"><span
                        *ngIf="!selectedObservationForDetails.relatesTos || selectedObservationForDetails.relatesTos.length === 0">N/A</span><span
                        *ngFor="let rel of selectedObservationForDetails.relatesTos; let i = index">{{ rel.title }}<span
                            *ngIf="i < selectedObservationForDetails.relatesTos!.length - 1">, </span></span></dd>
                <ng-container *ngIf="selectedObservationForDetails.isFixedIt">
                    <dt class="col-sm-4">Fixed Description</dt>
                    <dd class="col-sm-8 pre-wrap">{{ selectedObservationForDetails.fixedItDescription || 'N/A' }}</dd>
                </ng-container>
                <ng-container *ngIf="!selectedObservationForDetails.admin?.id">
                    <dt class="col-sm-4">Person Counselled</dt>
                    <dd class="col-sm-8">{{ selectedObservationForDetails.counselingPersonName || 'N/A' }}</dd>
                    <ng-container *ngIf="selectedObservationForDetails.isFixedIt">
                        <dt class="col-sm-4">3rd Party Co.</dt>
                        <dd class="col-sm-8">{{ selectedObservationForDetails.thirdPartyCompanyName || 'N/A' }}</dd>
                    </ng-container>
                </ng-container>
            </dl>
            <h6 class="mt-3 text-primary border-top pt-2 mb-2">Media</h6>
            <div class="row">
                <div class="col-md-6">
                    <label class="form-label d-block">Observation Images</label>
                    <app-carousel [images]="selectedObservationForDetails.images ?? []"></app-carousel>
                    <p *ngIf="!selectedObservationForDetails.images || selectedObservationForDetails.images.length === 0"
                        class="text-muted small mt-2">No observation images.</p>
                </div>
                <div class="col-md-6">
                    <label class="form-label d-block">Fixed Images</label>
                    <app-carousel [images]="selectedObservationForDetails.fixImages ?? []"></app-carousel>
                    <p *ngIf="!selectedObservationForDetails.fixImages || selectedObservationForDetails.fixImages.length === 0"
                        class="text-muted small mt-2">No fixed images.</p>
                </div>
            </div>
            <div class="col-12 mt-4 text-end border-top pt-3">
                <button type="button" class="btn btn-secondary" (click)="closeDetailsModal()">Close</button>
            </div>
        </div>
    </div>
</app-offcanvas>
<!-- ********** END: Details Offcanvas ********** -->

<!-- Confirmation Modals -->
<div class="modal fade" #deleteConfirmationModalElement id="deleteConfirmationModal" tabindex="-1"
    aria-labelledby="deleteConfirmationModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="deleteConfirmationModalLabel"><i
                        class="bi bi-exclamation-triangle-fill me-2"></i> Confirm Deletion</h5> <button type="button"
                    class="btn-close btn-close-white" aria-label="Close" (click)="closeDeleteConfirmation()"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete Observation ID <strong>{{ itemToDelete?.id }}</strong>?</p>
                <p class="small text-muted" *ngIf="itemToDelete">Title: {{ itemToDelete.title }}</p>
                <p class="text-danger fw-bold">This action might be irreversible.</p>
            </div>
            <div class="modal-footer justify-content-center"> <button type="button" class="btn btn-secondary"
                    (click)="closeDeleteConfirmation()"><i class="bi bi-x-lg me-1"></i> Cancel</button> <button
                    type="button" class="btn btn-danger" (click)="confirmDelete()"><i class="bi bi-trash-fill me-1"></i>
                    Yes, Delete</button> </div>
        </div>
    </div>
</div>
<div class="modal fade" #updateStatusConfirmationModalElement id="updateStatusConfirmationModal" tabindex="-1"
    aria-labelledby="updateStatusConfirmationModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header text-white" [ngClass]="confirmButtonClass">
                <h5 class="modal-title" id="updateStatusConfirmationModalLabel"><i [ngClass]="confirmIconClass"
                        class="me-2"></i> {{ modalTitle }}</h5> <button type="button" class="btn-close btn-close-white"
                    aria-label="Close" (click)="closeUpdateStatusConfirmation()"></button>
            </div>
            <div class="modal-body">
                <p>{{ modalMessage }}</p>
                <p class="small text-muted" *ngIf="itemToUpdateStatus">Title: {{ itemToUpdateStatus.title }}</p>
            </div>
            <div class="modal-footer justify-content-center"> <button type="button" class="btn btn-secondary"
                    (click)="closeUpdateStatusConfirmation()"><i class="bi bi-x-lg me-1"></i> Cancel</button> <button
                    type="button" class="btn" [ngClass]="confirmButtonClass.replace('bg-', 'btn-')"
                    (click)="confirmUpdateStatus()"><i [ngClass]="confirmIconClass" class="me-1"></i> {{
                    confirmButtonText }}</button> </div>
        </div>
    </div>
</div>
<div class="modal fade" #rewardConfirmationModalElement id="rewardConfirmationModal" tabindex="-1"
    aria-labelledby="rewardConfirmationModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title" id="rewardConfirmationModalLabel"><i class="bi bi-gift-fill me-2"></i> Confirm
                    Reward</h5> <button type="button" class="btn-close btn-close-white" aria-label="Close"
                    (click)="closeRewardConfirmation()"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to grant a reward for Observation ID <strong>{{ itemToReward?.id }}</strong>?
                </p>
                <p class="small text-muted" *ngIf="itemToReward">Title: {{ itemToReward.title }}</p>
                <p class="text-success">This will mark the observation as rewarded.</p>
            </div>
            <div class="modal-footer justify-content-center"> <button type="button" class="btn btn-secondary"
                    (click)="closeRewardConfirmation()"><i class="bi bi-x-lg me-1"></i> Cancel</button> <button
                    type="button" class="btn btn-success" (click)="confirmReward()"><i
                        class="bi bi-check-circle-fill me-1"></i> Yes, Grant Reward</button> </div>
        </div>
    </div>
</div>