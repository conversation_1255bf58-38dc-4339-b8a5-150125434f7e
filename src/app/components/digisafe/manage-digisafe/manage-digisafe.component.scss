/* Basic Card Styling */
.card {
  background-color: #fff;
  border-radius: 10px;
  border: 1px solid rgba(0, 0, 0, 0.125);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  min-width: 0;
  word-wrap: break-word;
  margin-top: 10px;
  overflow: hidden; /* Prevent card content breaking out */
}

.card-header {
  padding: 0.75rem 1.25rem;
  margin-bottom: 0;
  background-color: rgba(0, 0, 0, 0.03);
  border-bottom: 1px solid rgba(0, 0, 0, 0.125);
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
}

.card-body {
  /* Consider if height constraint is necessary, adjust if needed */
  height: 70vh;
  overflow-y: auto; /* Primarily vertical scroll for card body */
  overflow-x: hidden; /* Prevent horizontal scroll here, let table-responsive handle it */
  padding: 1rem; /* Add some padding */
}

/* Responsive Table Container */
.table-responsive {
  overflow-x: auto; /* Enable horizontal scrolling ONLY when table content overflows */
  width: 100%;
  max-width: 100%;
  /* white-space: nowrap; <-- REMOVED/COMMENTED - This often forces columns wide */
}

/* Custom Table Styling */
.custom-table {
  width: 100%;
  min-width: 800px; /* Optional: Set a minimum width for the table to encourage scrolling sooner */
  border-collapse: collapse;
  margin-top: 10px; /* Remove margin-top if card-body has padding */
  border: 1px solid #ddd;
  /* Removed border-radius and overflow: hidden here, let wrapper handle */
}

.custom-table th,
.custom-table td {
  padding: 10px 12px;
  text-align: left;
  font-size: 12px; /* Adjusted for better readability */
  vertical-align: top; /* Important for complex cell content */
  border-bottom: 1px solid #ddd;
  border-right: 1px solid #ddd;
  /* CRITICAL: Allow content within cells to wrap */
  white-space: normal;
}

.custom-table th {
  color: black;
  background-color: #f5f7fa;
  font-weight: bold;
  white-space: nowrap; /* Keep headers on one line if possible */
}

/* Remove right border from last cell/header in a row */
.custom-table tr td:last-child,
.custom-table tr th:last-child {
  border-right: none;
}

/* Remove bottom border from last row */
.custom-table tbody tr:last-child td {
  border-bottom: none;
}

/* Center text alignment helpers (applied in HTML) */
.text-center {
  text-align: center !important;
}

/* --- Safety Report Details Cell Specific Styles --- */
.safety-report-details-cell {
  /* No specific width needed here, let table layout handle */
}

.safety-report-details-content {
   /* Container for the columns */
}

.column-layout {
  display: flex;
  flex-wrap: nowrap; /* Prevent columns themselves from wrapping */
  width: 100%; /* Take full width of the cell */
}

.column {
  flex: 1 1 50%; /* Allow columns to share space, basis 50% */
  padding-right: 10px;
  min-width: 200px; /* Prevent columns from becoming too narrow */
}

.column:last-child {
  padding-right: 0;
  padding-left: 10px;
}

/* Vertical Line Separator */
.vl {
  border-left: 1px solid #ddd;
  /* height: auto; Let flex height work */
  margin: 0 10px;
  flex-shrink: 0; /* Prevent line from shrinking */
}

/* --- Detail Row Styling (Applies inside Safety Report & Updated By) --- */
.detail-row {
  margin-bottom: 6px;
  display: flex;
  align-items: baseline; /* Align label and value nicely */
  line-height: 1.4; /* Improve readability */
}

.detail-label {
  flex: 0 0 auto; /* Don't grow, don't shrink, use content size */
  font-weight: bold;
  margin-right: 5px;
  white-space: nowrap; /* Keep label itself on one line */
  display: inline-block; /* Ensure proper spacing */
}

/* Target the VALUE span specifically for wrapping */
.detail-row span:not(.detail-label) {
  word-break: break-word; /* Older browsers */
  overflow-wrap: break-word; /* Standard */
  white-space: normal; /* Ensure wrapping is allowed */
  min-width: 0; /* Helps flexbox calculate wrapping in tight spaces */
}


/* --- Updated By Cell Specific Styles --- */
.updated-by-cell {
   min-width: 200px; /* Give 'Updated By' some minimum space */
}

.updated-by-details .detail-label {
   /* Adjust if a fixed label width is desired */
   /* width: 60px; */
}


/* --- Created Date Cell --- */
.created-date-cell span {
    white-space: nowrap; /* Prevent date from wrapping */
}


/* --- Responsive Adjustments --- */
/* Stack columns in Safety Details on medium/small screens */
@media (max-width: 992px) { /* Adjust breakpoint as needed (e.g., 768px) */
  .column-layout {
      flex-direction: column; /* Stack columns vertically */
      width: 100%;
  }

  .vl {
      display: none; /* Hide vertical line when stacked */
  }

  .column {
      padding-right: 0; /* Remove horizontal padding */
      padding-left: 0;
      width: 100%; /* Ensure columns take full width when stacked */
      min-width: unset; /* Reset min-width */
      flex-basis: auto; /* Reset flex-basis */
  }

   .column:last-child {
     padding-left: 0; /* Ensure no extra padding */
     margin-top: 15px; /* Add space between stacked columns */
  }

  /* Optional: Reduce padding on smaller screens for all cells */
  .custom-table th,
  .custom-table td {
       padding: 8px 10px;
  }
}

/* Further adjustments for very small screens if needed */
@media (max-width: 576px) {
    .custom-table {
        min-width: 100%; /* Allow table to shrink fully on very small screens */
    }
    .detail-label {
        white-space: normal; /* Allow labels to wrap on very small screens */
    }
     .custom-table th,
     .custom-table td {
       padding: 6px 8px;
    }
}