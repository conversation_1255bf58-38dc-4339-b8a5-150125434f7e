<div class="container-fluid">
  <div class="row">
    <div class="col-lg-12">
      <div class="card">
        <div class="card-header">
          <h6 class="mb-0">Unfilled Reports</h6>
        </div>
        <div class="card-body">
          <div class="table-container">
            <div class="table-responsive">
              <table class="table table-bordered table-hover">
                <thead class="table-header">
                  <tr class="text-center">
                    <th>ID</th>
                    <th>Name</th>
                    <th>Email</th>
                    <th>First Name</th>
                    <th>Last Name</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngIf="listLoading">
                    <td colspan="5" class="text-center p-4">
                      <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                      Loading data...
                    </td>
                  </tr>
                  <tr *ngIf="!listLoading && (!dataSource || dataSource.length === 0)">
                    <td colspan="5" class="text-center p-4 text-muted">
                      No data found.
                    </td>
                  </tr>
                  <tr *ngFor="let element of dataSource">
                    <td>{{element.id}}</td>
                    <td>{{element.name}}</td>
                    <td>{{element.email}}</td>
                    <td>{{element.firstName}}</td>
                    <td>{{element.lastName}}</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
        <div class="card-footer text-muted text-center">
          <app-pagination [currentPage]="currentPage" [totalItems]="totalItems" [itemsPerPage]="itemsPerPage"
            (pageChange)="onPageChange($event)"></app-pagination>
        </div>
      </div>
    </div>
  </div>
</div>