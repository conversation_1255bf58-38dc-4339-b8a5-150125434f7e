import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DigisafeService } from '../../../services/digisafe/digisafe.service';
import { PaginationComponent } from '../../../shared/pagination/pagination.component';
import { createAxiosConfig } from '../../../core/utilities/axios-param-config';
import { AuthService } from '../../../services/auth.service';

interface UnfilledReport {
  id: number;
  name: string;
  plantAdminId: number;
  email: string;
  firstName: string;
  lastName: string;
  businessUnitId: number;
}

@Component({
  selector: 'app-unfilled-report',
  standalone: true,
  imports: [CommonModule, PaginationComponent],
  templateUrl: './unfilled-report.component.html',
  styleUrl: './unfilled-report.component.scss'
})
export class UnfilledReportComponent implements OnInit {
  displayedColumns: string[] = ['id', 'name', 'email', 'firstName', 'lastName'];
  dataSource: UnfilledReport[] = [];
  currentPage = 1;
  itemsPerPage = 10;
  totalItems = 0;
  listLoading = false;

  constructor(private digisafeService: DigisafeService, private authService: AuthService) { }

  ngOnInit(): void {
    this.getUnfilledReportData(this.currentPage);
  }

  async getUnfilledReportData(page: number): Promise<void> {
    this.listLoading = true;
    const businessUnitId = this.authService.getBusinessUnitId();
    const data = {
      page: page,
      limit: this.itemsPerPage,
      businessUnitId: businessUnitId
    };
    const params = createAxiosConfig(data);

    try {
      const response = await this.digisafeService.getUnfilledReport(params);
      this.dataSource = response?.data ?? [];
      this.totalItems = response?.total ?? 0;
    } catch (error: any) {
      console.error('Error fetching unfilled report:', error);
      this.dataSource = [];
      this.totalItems = 0;
    } finally {
      this.listLoading = false;
    }
  }

  onPageChange(page: number): void {
    if (this.currentPage === page || this.listLoading) return;
    this.currentPage = page;
    this.getUnfilledReportData(this.currentPage);
  }
}