/* custom-grid.component.css */

/* Basic Card Styling (same as before) */
.card {
    background-color: #fff;
    border-radius: 10px;
    border: 1px solid rgba(0, 0, 0, 0.125);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    min-width: 0;
    word-wrap: break-word;
    margin-top: 10px;
  }

  .card-header {
    padding: 0.75rem 1.25rem;
    margin-bottom: 0;
    background-color: rgba(0, 0, 0, 0.03);
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
  }

  .card-body {
    padding: 1.25rem;
  }


  /* Responsive Table Container */
  .table-responsive {
    overflow-x: auto; /* Enable horizontal scrolling ONLY when table content overflows */
    width: 100%;
    max-width: 100%;
  }

  /* Custom Table Styling */
  .custom-table {
    width: 100%;
    min-width: 800px; /* Optional: Set a minimum width for the table to encourage scrolling sooner */
    border-collapse: collapse;
    margin-top: 10px; /* Remove margin-top if card-body has padding */
    border: 1px solid #ddd;
  }

  .custom-table th,
  .custom-table td {
    padding: 10px 12px;
    text-align: left;
    font-size: 12px; /* Adjusted for better readability */
    vertical-align: top; /* Important for complex cell content */
    border-bottom: 1px solid #ddd;
    border-right: 1px solid #ddd;
    white-space: normal; /* Allow content within cells to wrap */
  }

  .custom-table th {
    color: black;
    background-color: #f5f7fa;
    font-weight: bold;
    white-space: nowrap; /* Keep headers on one line if possible */
  }

  /* Styling for the total column */
  .total-column {
    background-color: #e6f7ff; /* Light blue background */
    font-weight: bold;
    border-left: 2px solid #1890ff; /* Blue left border to make it stand out */
  }

  /* Remove right border from last cell/header in a row */
  .custom-table tr td:last-child,
  .custom-table tr th:last-child {
    border-right: none;
  }

  /* Remove bottom border from last row */
  .custom-table tbody tr:last-child td {
    border-bottom: none;
  }

  /* Center text alignment helpers (applied in HTML) */
  .text-center {
    text-align: center !important;
  }

  /* Responsive Adjustments */
  @media (max-width: 992px) {
    .custom-table th,
    .custom-table td {
         padding: 8px 10px;
    }
  }

  @media (max-width: 576px) {
      .custom-table {
          min-width: 100%;
      }
       .custom-table th,
       .custom-table td {
         padding: 6px 8px;
      }
  }

  /* Filter Offcanvas Styling */
  .filter-container {
    /* Add padding or other styles if needed */
  }

  .filter-container .form-label {
    margin-bottom: 0.25rem; /* Adjust spacing below labels */
  }

  .filter-container .form-control-sm,
  .filter-container .form-select-sm {
    font-size: 0.875rem; /* Smaller font size for form elements */
    padding: 0.25rem 0.5rem; /* Adjust padding */
  }

  .filter-container .form-check-label {
    font-size: 0.875rem; /* Smaller font size for checkbox label */
  }

  .filter-container .form-text {
    font-size: 0.75rem; /* Smaller font size for helper text */
  }

  .filter-container .btn-sm {
    padding: 0.25rem 0.5rem; /* Adjust button padding */
    font-size: 0.875rem; /* Smaller font size for buttons */
  }

  .filter-container .adani-btn {
    /* Add specific styling for adani-btn if needed */
  }