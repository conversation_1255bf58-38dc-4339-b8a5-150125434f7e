<!-- Tabs -->
<app-tab [tabs]="tabs" [selectedTabIndex]="selectedTabIndex" (tabSelected)="onTabSelected($event)"></app-tab>

<!-- Common Header Template -->
<ng-template #tableHeader let-title="title">
    <div class="row align-items-center">
        <div class="col d-flex align-items-center">
            <h6 class="mb-0">{{ title }}</h6> <!-- Use passed title -->
        </div>
        <div class="col text-end d-flex align-items-center justify-content-end">

            <!-- *** UPDATED: Download Excel Button with Dropdown *** -->
            <div ngbDropdown class="d-inline-block me-2"> <!-- Adjusted margin -->
                <button type="button" class="btn btn-sm adani-btn dropdown-toggle" id="downloadTourExcelDropdown" ngbDropdownToggle
                    [disabled]="isDownloadingExcel || listLoading">
                    <span *ngIf="!isDownloadingExcel">
                        <i class="bi bi-file-earmark-excel me-1"></i> Download Excel
                    </span>
                    <span *ngIf="isDownloadingExcel">
                        <span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
                        Downloading {{ downloadType === 'current' ? 'Page' : (downloadType === 'all' ? 'All' : '') }}...
                    </span>
                </button>
                <ul ngbDropdownMenu aria-labelledby="downloadTourExcelDropdown">
                    <li>
                        <button ngbDropdownItem (click)="downloadExcel('current')" [disabled]="isDownloadingExcel || listLoading || (getCurrentListData()?.length ?? 0) === 0">
                            <i class="bi bi-download me-1"></i> Download Current Page ({{ getCurrentListData()?.length ?? 0 }})
                        </button>
                    </li>
                    <li>
                        <button ngbDropdownItem (click)="downloadExcel('all')" [disabled]="isDownloadingExcel || listLoading || totalItems === 0">
                            <i class="bi bi-cloud-download me-1"></i> Download All Filtered ({{ totalItems }})
                        </button>
                    </li>
                </ul>
            </div>
            <!-- *** END: Download Excel Button Update *** -->

            <img src="../../../assets/svg/filter.svg" class="filter-button" (click)="openFilterModal()" alt="Filter"
                style="width: 35px; cursor: pointer;" title="Open Filters"/>
        </div>
    </div>
</ng-template>


<!-- Ongoing Tours List -->
<div *ngIf="selectedTabIndex == 0" class="card custom-card" id="ongoing-tour-list">
    <div class="card-header">
        <!-- Use common header template -->
        <ng-container *ngTemplateOutlet="tableHeader; context: { title: 'List of Ongoing Tours' }"></ng-container>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered table-hover custom-table">
                <thead class="table-header text-center">
                    <tr>
                        <th scope="col" style="width: 15%;">Actions</th>
                        <th scope="col" style="width: 40%;">Tour Started By</th>
                        <th scope="col" style="width: 45%;">Tour Details</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- Loading State -->
                    <tr *ngIf="listLoading">
                        <td colspan="3" class="text-center p-4">
                            <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                            Loading ongoing tours...
                        </td>
                    </tr>
                    <!-- No Data State -->
                    <tr *ngIf="!listLoading && ongoingToursList.length === 0">
                        <td colspan="3" class="text-center p-4 text-muted">
                            No ongoing tours found matching the criteria.
                        </td>
                    </tr>
                    <!-- Data Rows -->
                    <tr *ngFor="let tour of ongoingToursList">
                        <td class="actions text-center align-middle">
                             <button class="btn btn-sm adani-btn " (click)="openTourPointModal(tourPointModal, tour)" title="View Scan Points">
                                <i class="bi bi-geo-alt"></i> Tour Points
                            </button>
                        </td>
                        <td class="align-middle">
                            <div class="details-container">
                                <p class="label-value"><strong>ID:</strong> <span class="value-text">{{tour.adminId}}</span></p>
                                <p class="label-value"><strong>Name:</strong> <span class="value-text">{{tour.admin?.firstName}} {{tour.admin?.lastName}}</span></p>
                                <p class="label-value"><strong>Email:</strong> <span class="value-text">{{tour.admin?.email ?? 'N/A'}}</span></p>
                            </div>
                        </td>
                        <td class="align-middle">
                            <div class="details-container">
                                <!-- Use 'medium' pipe for clearer date/time -->
                                <p class="label-value"><strong>Started:</strong> <span class="value-text">{{tour.startTime | date:'medium'}}</span></p>
                                <p class="label-value"><strong>Last Scan:</strong> <span class="value-text">{{tour.lastQrScannedTime | date:'medium'}}</span></p>
                                <p class="label-value"><strong>Scan Points:</strong> <span class="value-text">{{tour.qrPointTotal ?? 0}}</span></p>
                                <p class="label-value"><strong>Plant:</strong> <span class="value-text">{{tour.plant?.name ?? 'N/A'}}</span></p>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
    <div class="card-footer text-muted text-center">
        <app-pagination [currentPage]="currentPage" [totalItems]="totalItems" [itemsPerPage]="itemsPerPage"
            (pageChange)="onPageChange($event)"></app-pagination>
    </div>
</div>

<!-- Completed Tours List -->
<div *ngIf="selectedTabIndex == 1" class="card custom-card" id="completed-tour-list">
     <div class="card-header">
         <!-- Use common header template -->
        <ng-container *ngTemplateOutlet="tableHeader; context: { title: 'List of Completed Tours' }"></ng-container>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered table-hover custom-table">
                <thead class="table-header text-center">
                    <tr>
                        <th scope="col" style="width: 15%;">Scan Points</th>
                        <th scope="col" style="width: 35%;">Tour By</th>
                        <th scope="col" style="width: 30%;">Tour Details</th>
                        <th scope="col" style="width: 20%;">Timing</th>
                    </tr>
                </thead>
                 <tbody>
                     <!-- Loading State -->
                     <tr *ngIf="listLoading">
                        <td colspan="4" class="text-center p-4">
                            <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                            Loading completed tours...
                        </td>
                    </tr>
                    <!-- No Data State -->
                    <tr *ngIf="!listLoading && completedToursLists.length === 0">
                        <td colspan="4" class="text-center p-4 text-muted">
                            No completed tours found matching the criteria.
                        </td>
                    </tr>
                    <!-- Data Rows -->
                     <tr *ngFor="let tour of completedToursLists">
                         <td class="actions text-center align-middle">
                             <button class="btn btn-sm adani-btn" (click)="openTourPointModal(tourPointModal, tour)" title="View Scan Points">
                                <i class="bi bi-geo-alt"></i> View Points
                             </button>
                         </td>
                         <td class="align-middle">
                            <div class="details-container">
                                <p class="label-value"><strong>ID:</strong> <span class="value-text">{{tour.adminId}}</span></p>
                                <p class="label-value"><strong>Name:</strong> <span class="value-text">{{tour.admin?.firstName}} {{tour.admin?.lastName}}</span></p>
                                <p class="label-value"><strong>Email:</strong> <span class="value-text">{{tour.admin?.email ?? 'N/A'}}</span></p>
                            </div>
                         </td>
                         <td class="align-middle">
                            <div class="details-container">
                                <p class="label-value"><strong>Started:</strong> <span class="value-text">{{tour.startTime | date:'mediumDate'}}</span></p> <!-- Date only -->
                                <p class="label-value"><strong>Last Scan:</strong> <span class="value-text">{{tour.lastQrScannedTime | date:'shortTime'}}</span></p> <!-- Time only -->
                                <p class="label-value"><strong>Scan Points:</strong> <span class="value-text">{{tour.qrPointTotal ?? 0}}</span></p>
                                <p class="label-value"><strong>Plant:</strong> <span class="value-text">{{tour.plant?.name ?? 'N/A'}}</span></p>
                            </div>
                         </td>
                         <td class="align-middle">
                            <div class="details-container">
                                <p class="label-value"><strong>Ended:</strong> <span class="value-text">{{tour.endTime | date:'shortTime'}}</span></p> <!-- Time only -->
                                <p class="label-value"><strong>Duration:</strong> <span class="value-text">{{tour.tourDurationInMinus ? tour.tourDurationInMinus + ' mins' : 'N/A'}}</span></p>
                            </div>
                         </td>
                     </tr>
                 </tbody>
            </table>
        </div>
    </div>
     <div class="card-footer text-muted text-center">
        <app-pagination [currentPage]="currentPage" [totalItems]="totalItems" [itemsPerPage]="itemsPerPage"
            (pageChange)="onPageChange($event)"></app-pagination>
    </div>
</div>

<!-- Filter Offcanvas -->
<app-offcanvas [title]="'Filter Tours'" *ngIf="isFilterModalOpen" (onClickCross)="closeModal()">
    <div class="filter-container p-3">
        <form #filterForm="ngForm" (ngSubmit)="applyFilters()" novalidate>
            <div class="row g-3">

                <div class="col-12">
                    <label class="form-label" for="filterFirstName">User First Name</label>
                    <input type="text" id="filterFirstName" class="form-control" placeholder="Filter by first name"
                           [(ngModel)]="filters.firstName" name="firstName" maxlength="30" pattern="^[a-zA-Z\s]*$"
                           #firstName="ngModel" [ngClass]="{'is-invalid': firstName.invalid && (firstName.dirty || firstName.touched)}"
                           (blur)="trimInputField(filters, 'firstName')">
                    <div *ngIf="firstName.invalid && (firstName.dirty || firstName.touched)" class="invalid-feedback">
                        <div *ngIf="firstName.errors?.['maxlength']">First name cannot exceed 30 characters.</div>
                        <div *ngIf="firstName.errors?.['pattern']">First name should contain only alphabets.</div>
                    </div>
                    <small *ngIf="filters && filters.firstName" class="text-muted">
                        {{ filters.firstName.length }}/30 characters
                    </small>
                </div>

                <div class="col-12">
                    <label class="form-label" for="filterLastName">User Last Name</label>
                    <input type="text" id="filterLastName" class="form-control" placeholder="Filter by last name"
                           [(ngModel)]="filters.lastName" name="lastName" maxlength="30" pattern="^[a-zA-Z\s]*$"
                           #lastName="ngModel" [ngClass]="{'is-invalid': lastName.invalid && (lastName.dirty || lastName.touched)}"
                           (blur)="trimInputField(filters, 'lastName')">
                    <div *ngIf="lastName.invalid && (lastName.dirty || lastName.touched)" class="invalid-feedback">
                        <div *ngIf="lastName.errors?.['maxlength']">Last name cannot exceed 30 characters.</div>
                        <div *ngIf="lastName.errors?.['pattern']">Last name should contain only alphabets.</div>
                    </div>
                    <small *ngIf="filters && filters.lastName" class="text-muted">
                        {{ filters.lastName.length }}/30 characters
                    </small>
                </div>

                <div class="col-12">
                    <label class="form-label" for="filterEmail">User Email</label>
                    <input type="email" id="filterEmail" class="form-control" placeholder="Filter by email with &#64;adani.com domain"
                           [(ngModel)]="filters.email" name="email" email pattern="[a-zA-Z0-9._%+-]+&#64;adani\.com$"
                           #email="ngModel" [ngClass]="{'is-invalid': email.invalid && (email.dirty || email.touched)}"
                           (blur)="trimInputField(filters, 'email')">
                    <div *ngIf="email.invalid && (email.dirty || email.touched)" class="invalid-feedback">
                        <div *ngIf="email.errors?.['email']">Please enter a valid email address.</div>
                        <div *ngIf="email.errors?.['pattern']">Email must use the &#64;adani.com domain.</div>
                    </div>
                    <small *ngIf="filters && filters.email" class="text-muted">
                        {{ filters.email.length }} characters
                    </small>
                </div>

                <div class="col-12">
                    <label class="form-label" for="filterContactNumber">User Contact Number</label>
                    <input type="text" id="filterContactNumber" class="form-control" placeholder="Filter by contact #"
                           [(ngModel)]="filters.contactNumber" name="contactNumber" pattern="^[0-9]{10}$" maxlength="10"
                           #contactNumber="ngModel" [ngClass]="{'is-invalid': contactNumber.invalid && (contactNumber.dirty || contactNumber.touched)}"
                           (blur)="trimInputField(filters, 'contactNumber')">
                    <div *ngIf="contactNumber.invalid && (contactNumber.dirty || contactNumber.touched)" class="invalid-feedback">
                        <div *ngIf="contactNumber.errors?.['pattern']">Please enter a valid 10-digit number.</div>
                    </div>
                    <small *ngIf="filters && filters.contactNumber" class="text-muted">
                        {{ filters.contactNumber.length }}/10 characters
                    </small>
                </div>

                <div class="col-12">
                    <label class="form-label" for="filterStartDate">Start Date Range</label>
                    <div class="input-group">
                        <input type="date" class="form-control" id="filterStartDate" aria-label="Start Date"
                               [(ngModel)]="filters.startDate" name="startDate" #startDate="ngModel"
                               [ngClass]="{'is-invalid': startDate.invalid && (startDate.dirty || startDate.touched)}" />
                        <span class="input-group-text">to</span>
                        <input type="date" class="form-control" id="filterEndDate" aria-label="End Date"
                               [(ngModel)]="filters.endDate" name="endDate" #endDate="ngModel"
                               [ngClass]="{'is-invalid': endDate.invalid && (endDate.dirty || endDate.touched)}" />
                    </div>
                    <small class="text-muted">Filters tours that *started* within this date range.</small>
                    <div *ngIf="endDate.value && startDate.value && endDate.value < startDate.value" class="text-danger small mt-1">
                        End date cannot be earlier than start date.
                    </div>
                </div>

                <div class="col-12">
                    <label class="form-label" for="filterPlant">
                         {{ currentUserRole === componentRoles.PLANT_ADMIN ? 'Select Your Plant' : 'Select Plant' }}
                    </label>
                    <select id="filterPlant" class="form-select"
                            [(ngModel)]="filters.plantId" name="plantId">
                            <!-- No longer disabled -->
                        <!-- Show "All Plants" only if Super Admin -->
                        <option [ngValue]="null" *ngIf="currentUserRole !== componentRoles.PLANT_ADMIN">All Plants</option>
                        <!-- Add default selection prompt for Plant Admin -->
                        <option [ngValue]="null" *ngIf="currentUserRole === componentRoles.PLANT_ADMIN">-- Select Your Plant --</option>
                        <!-- availablePlants is filtered correctly in TS -->
                        <option *ngFor="let plant of availablePlants" [value]="plant.id">{{ plant.name }}</option>
                        <!-- Message if Plant Admin has no plants -->
                        <option [ngValue]="null" *ngIf="currentUserRole === componentRoles.PLANT_ADMIN && availablePlants.length === 0" disabled>No plants assigned or available</option>
                    </select>
                </div>

                <div class="col-12">
                    <label class="form-label" for="filterSortByTour">Sort By</label>
                    <select id="filterSortByTour" class="form-select" [(ngModel)]="filters.sortField" name="sortField" required
                            #sortField="ngModel" [ngClass]="{'is-invalid': sortField.invalid && (sortField.dirty || sortField.touched)}">
                        <option value="id">Default (ID)</option> <!-- Default clarity -->
                         <option *ngFor="let field of availableSortFields" [value]="field.value">{{ field.label }}</option>
                    </select>
                    <div *ngIf="sortField.invalid && (sortField.dirty || sortField.touched)" class="invalid-feedback">
                        <div *ngIf="sortField.errors?.['required']">Sort field is required.</div>
                    </div>

                    <label class="form-label mt-2" for="filterSortDirTour">Sort Direction</label>
                    <select id="filterSortDirTour" class="form-select" [(ngModel)]="filters.sortDirection" name="sortDirection" required
                            #sortDirection="ngModel" [ngClass]="{'is-invalid': sortDirection.invalid && (sortDirection.dirty || sortDirection.touched)}">
                        <option value="DESC">Descending</option>
                        <option value="ASC">Ascending</option>
                    </select>
                    <div *ngIf="sortDirection.invalid && (sortDirection.dirty || sortDirection.touched)" class="invalid-feedback">
                        <div *ngIf="sortDirection.errors?.['required']">Sort direction is required.</div>
                    </div>
                </div>

                <div class="col-12 mt-4 d-grid gap-2">
                    <button type="submit" class="btn adani-btn" [disabled]="filterForm.invalid || (endDate.value && startDate.value && endDate.value < startDate.value)">
                        <i class="bi bi-search me-1"></i> Apply Filters
                    </button>
                    <button type="button" class="btn btn-secondary" (click)="resetFilters()">
                        <i class="bi bi-arrow-clockwise me-1"></i> Reset Filters
                    </button>
                </div>
            </div>
        </form>
    </div>
</app-offcanvas>


<!-- Tour Point Details Modal (ng-bootstrap) -->
<ng-template #tourPointModal let-modal>
  <div class="modal-header">
    <h4 class="modal-title" id="tour-point-modal-title">
        Tour Scan Points (Tour ID: {{ selectedTourForDetails?.id }}) <br>
        <small class="text-muted">User: {{ selectedTourForDetails?.admin?.firstName }} {{ selectedTourForDetails?.admin?.lastName }} (ID: {{ selectedTourForDetails?.adminId }})</small>
    </h4>
    <button type="button" class="btn-close" aria-label="Close" (click)="closeDetailsModal()"></button>
  </div>
  <div class="modal-body">
    <div *ngIf="detailsLoading" class="text-center p-4">
      <span class="spinner-border text-primary" role="status" aria-hidden="true"></span>
      <p class="mt-2 mb-0">Loading scan point details...</p>
    </div>

    <div *ngIf="!detailsLoading && tourScanPoints.length === 0" class="alert alert-info text-center">
        No scan points found for this tour.
    </div>

    <div *ngIf="!detailsLoading && tourScanPoints.length > 0" class="table-responsive">
      <table class="table table-sm table-bordered table-striped table-hover">
        <thead class="table-light">
          <tr>
            <th>Scan Point Details</th>
            <th>QR Code Scanned</th>
          </tr>
        </thead>
        <tbody>
          <!-- Use TourScanPoint interface -->
          <tr *ngFor="let point of tourScanPoints">
            <td class="align-middle">
                <div class="details-container">
                    <p class="label-value"><strong>Scan Time:</strong> <span class="value-text">{{ point.createdTimestamp | date:'medium' }}</span></p>
                    <p class="label-value"><strong>Distance:</strong> <span class="value-text">{{ point.scanDistanceInMeter | number:'1.0-1' }} m</span></p>
                    <p class="label-value"><strong>Steps:</strong> <span class="value-text">{{ point.steps ?? 'N/A' }}</span></p>
                </div>
            </td>
            <td class="align-middle">
                <div class="details-container">
                    <!-- Access nested QR data safely -->
                    <p class="label-value"><strong>Zone Area:</strong> <span class="value-text">{{ point.qrCode?.zoneArea || 'N/A' }}</span></p>
                    <p class="label-value"><strong>Zone:</strong> <span class="value-text">{{ point.qrCode?.zone?.zoneName || 'N/A' }}</span></p>
                    <!-- Try plant info from QR first, then from the scan point itself -->
                    <p class="label-value"><strong>Plant:</strong> <span class="value-text">{{ point.qrCode?.plant?.name || point.plant?.name || 'N/A' }}</span></p>
                    <p class="label-value"><strong>QR ID:</strong> <span class="value-text">{{ point.qrCodeId || 'N/A' }}</span></p> <!-- Show QR ID -->
                </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
  <div class="modal-footer justify-content-between">
    <!-- Show pagination only if needed -->
    <div *ngIf="!detailsLoading && tourDetailsPagination.totalItems > tourDetailsPagination.itemsPerPage">
        <app-pagination
            [currentPage]="tourDetailsPagination.currentPage"
            [totalItems]="tourDetailsPagination.totalItems"
            [itemsPerPage]="tourDetailsPagination.itemsPerPage"
            (pageChange)="onDetailsPageChange($event)">
        </app-pagination>
    </div>
    <!-- Show total count if pagination isn't needed but there are items -->
     <div *ngIf="!detailsLoading && tourDetailsPagination.totalItems <= tourDetailsPagination.itemsPerPage && tourDetailsPagination.totalItems > 0">
        <span class="text-muted small">Total Scan Points: {{ tourDetailsPagination.totalItems }}</span>
    </div>
    <!-- Placeholder if no items and not loading -->
    <div *ngIf="!detailsLoading && tourDetailsPagination.totalItems === 0">
         <span class="text-muted small"></span> <!-- Empty span to maintain layout -->
    </div>
    <button type="button" class="btn btn-outline-secondary btn-sm" (click)="closeDetailsModal()">Close</button>
  </div>
</ng-template>
<!-- END: Tour Point Details Modal -->


<!-- Force Complete Confirmation Modal (standard Bootstrap JS) -->
<div #forceCompleteConfirmationModal class="modal fade" id="forceCompleteConfirmModal" tabindex="-1" aria-labelledby="forceCompleteConfirmModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered modal-sm">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="forceCompleteConfirmModalLabel">Confirm Action</h5>
        <button type="button" class="btn-close" aria-label="Close" (click)="cancelForceComplete()"></button>
      </div>
      <div class="modal-body text-center">
        <p>Are you sure you want to <strong>{{ confirmationActionText }}</strong> this tour?</p>
        <p class="text-muted small">(Tour ID: {{ tourToForceComplete?.id }})</p>
      </div>
      <div class="modal-footer justify-content-center">
         <button type="button" class="btn btn-secondary btn-sm" (click)="cancelForceComplete()">
            Cancel
         </button>
         <button type="button" class="btn btn-danger btn-sm" (click)="confirmForceComplete()">
            Yes, {{ confirmationActionText }}
         </button>
      </div>
    </div>
  </div>
</div>
<!-- END: Force Complete Confirmation Modal -->


<!-- Toast Message Component -->
<app-toast-message></app-toast-message>