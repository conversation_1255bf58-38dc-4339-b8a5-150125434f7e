import { Component, OnInit, ViewChild, ElementRef, AfterViewInit, OnDestroy } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { TabComponent } from "../../shared/tab/tab.component";
import { OffcanvasComponent } from "../../shared/offcanvas/offcanvas.component";
import { CommonModule } from '@angular/common';
import { createAxiosConfig } from '../../core/utilities/axios-param-config';
import { TourManagementService } from '../../services/tour-management/tour-management.service';
import { PaginationComponent } from "../../shared/pagination/pagination.component";
import { PlantManagementService } from '../../services/plant-management/plant-management.service';
import { ToastMessageComponent } from "../../shared/toast-message/toast-message.component";
import { UpdateService } from '../../services/update/update.service';
import { <PERSON><PERSON>Moda<PERSON>, NgbModalModule, NgbModalRef, NgbDropdownModule } from '@ng-bootstrap/ng-bootstrap';
import * as XLSX from 'xlsx'; // Import the XLSX library

// --- Define ROLES constant (adjust values if needed) ---
const ROLES = {
    SUPER_ADMIN: 'super_admin',
    PLANT_ADMIN: 'plant_admin',
};

// --- Interfaces ---
interface TourFilter {
    firstName?: string | null; lastName?: string | null; email?: string | null;
    contactNumber?: string | null; startDate?: string | null; endDate?: string | null;
    plantId?: number | null; // User selected plant (from filtered list if Plant Admin)
    enabled?: string | null; // Keep if still relevant for Tours, otherwise remove
    sortField?: string;
    sortDirection?: 'ASC' | 'DESC';
}

interface Tour {
    id: number; adminId: number; plantId: number; startDate: string | null;
    startTime: string | null; // ISO String
    endTime: string | null; // ISO String
    lastQrScannedTime: string | null; // ISO String
    qrPointTotal: number; tourDurationInMinus: number | null; status: number; // 0=Ongoing, 1=Completed
    admin: { id: number; firstName: string; lastName: string; email: string; contactNumber?: string | null; };
    plant: { id: number; name: string; }; enabled?: boolean;
    createdTimestamp?: string; // ISO String
    updatedTimestamp?: string; // ISO String
}

interface SelectOption { // Simplified for Plant dropdown
    id: number;
    name: string;
}

// TourScanPoint interface (assuming it remains the same)
interface TourScanPoint {
    id: number; enabled: boolean; isDeleted: boolean; steps: number; adminId: number;
    plantId: number; tourId: number; qrCodeId: number; scanDurationInMinus: number | null;
    scanDistanceInMeter: number; priority: number | null; status: number;
    createdTimestamp: string; updatedTimestamp: string;
    qrCode: { id: number; enabled: boolean; isDeleted: boolean; noOfScan: number; type: number;
        uuid: string | null; isFlag: boolean; lastScanDate: string; lastScanById: number;
        lastScanByAdminId: number; qrCode: string; qrCodeData: any | null; qrCodePdf: any | null;
        lat: string | number | null; long: string | number | null; status: number;
        beaconStatus: number | null; plantId: number; segmentId: any | null; locationId: any | null;
        locationTypeId: any | null; qrTypeId: number; zoneId: number; zoneArea: string;
        qrDetail: any | null; remarks: any | null; fromDate: any | null; toDate: any | null;
        priority: any | null; createdBy: any | null; createdTimestamp: string; updatedBy: any | null;
        updatedTimestamp: string;
        zone: { id: number; enabled: boolean; isDeleted: boolean; plantId: number; zoneName: string;
            zoneArea: string; priority: any | null; createdBy: any | null; createdTimestamp: string;
            updatedBy: any | null; updatedTimestamp: string;
        } | null;
        plant?: { id: number; name: string; };
    } | null;
    plant: { id: number; enabled: boolean; isDeleted: boolean; name: string; } | null;
    tour: { id: number; } | null;
}

declare var bootstrap: any; // For Bootstrap JS components like Modal

@Component({
    selector: 'app-tour-management',
    standalone: true,
    imports: [
        CommonModule,
        FormsModule,
        TabComponent,
        OffcanvasComponent,
        PaginationComponent,
        NgbModalModule,     // For Tour Point Details Modal
        NgbDropdownModule,  // For the download button
        ToastMessageComponent
    ],
    templateUrl: './tour-management.component.html',
    styleUrl: './tour-management.component.scss'
})
export class TourManagementComponent implements OnInit, AfterViewInit, OnDestroy {
    // --- Expose ROLES constant to the template ---
    public componentRoles = ROLES;

    // --- ViewChild References ---
    @ViewChild(ToastMessageComponent) toast!: ToastMessageComponent;
    @ViewChild('forceCompleteConfirmationModal') confirmationModalElementRef!: ElementRef;

    // --- State Variables ---
    isFilterModalOpen = false;
    listLoading = false;
    detailsLoading = false;
    isDownloadingExcel = false;
    downloadType: 'current' | 'all' | null = null;

    // --- Role-Based Access Control ---
    currentUserRole: string = '';
    loggedInAdminId: number | null = null;
    loggedInPlantIds: number[] = []; // Store all assigned plant IDs for Plant Admin

    // --- Data Lists ---
    ongoingToursList: Tour[] = [];
    completedToursLists: Tour[] = [];

    // --- Pagination (Main List) ---
    currentPage = 1;
    itemsPerPage = 10;
    totalItems = 0;

    // --- Tabs ---
    selectedTabIndex = 0;
    tabs = [
        { title: 'Ongoing Tour', status: 0, listKey: 'ongoingToursList' as const },
        { title: 'Completed Tour', status: 1, listKey: 'completedToursLists' as const },
    ];

    // --- Filters ---
    filters: TourFilter = {
        firstName: null, lastName: null, email: null, contactNumber: null,
        startDate: null, endDate: null, plantId: null, // plantId selected by user
        enabled: null, // Keep or remove based on relevance
        sortField: 'id', sortDirection: 'DESC'
    };
    availablePlants: SelectOption[] = []; // Filtered based on role
    availableSortFields = [
        { value: 'id', label: 'Tour ID' },
        { value: 'admin.firstName', label: 'User Name' },
        { value: 'plant.name', label: 'Plant Name' },
        { value: 'startTime', label: 'Start Time' },
        { value: 'endTime', label: 'End Time' },
        { value: 'createdTimestamp', label: 'Created Date' }
    ];

    // --- Tour Point Details Modal State ---
    selectedTourForDetails: Tour | null = null;
    tourScanPoints: TourScanPoint[] = [];
    tourDetailsPagination = { currentPage: 1, itemsPerPage: 5, totalItems: 0 };
    private detailsModalRef: NgbModalRef | null = null;

    // --- Confirmation Modal State ---
    confirmationModalInstance: any;
    tourToForceComplete: Tour | null = null;
    confirmationActionText: string = 'force complete';

    constructor(
        readonly tourManagementService: TourManagementService,
        readonly plantService: PlantManagementService,
        readonly updateService: UpdateService,
        private modalService: NgbModal
    ) { }

    ngOnInit() {
        // --- Set role and plant IDs first ---
        this.setCurrentUserRoleAndDetailsById();

        // REMOVE: No pre-filling needed based on single plant ID

        // Load initial data
        this.getPlants(); // Fetches plants based on role
        this.loadToursForCurrentTab(); // Fetches tours based on initial filters
    }

    ngAfterViewInit(): void {
        this.initializeConfirmationModal();
    }

    ngOnDestroy(): void {
        this.confirmationModalInstance?.dispose();
        this.detailsModalRef?.close();
    }

    // --- Role Setup Method ---
    private setCurrentUserRoleAndDetailsById(): void {
        try {
            const userString = localStorage.getItem('user');
            if (!userString || userString.trim() === "" || ['null', 'undefined', '""', '" "'].includes(userString.trim().toLowerCase())) {
                console.error("User data not found or is invalid in localStorage.");
                this.currentUserRole = '';
                this.loggedInAdminId = null;
                this.loggedInPlantIds = [];
                this.toast?.showErrorToast("User session invalid. Please log in again.");
                return;
            }

            const currentUser = JSON.parse(userString);
            console.log('Current User Parsed for Role Check (Tour Mgmt):', currentUser);

            this.loggedInAdminId = currentUser?.id ?? null;

            // Store the entire array of plant IDs, ensuring they are numbers
            this.loggedInPlantIds = (Array.isArray(currentUser?.plantIds) && currentUser.plantIds.length > 0)
                                      ? currentUser.plantIds.map((id: any) => Number(id)).filter((id: number) => !isNaN(id))
                                      : [];

            const roleId = currentUser?.adminsRoleId;

            if (roleId === 1 || roleId === 6) {
                this.currentUserRole = this.componentRoles.SUPER_ADMIN;
            } else if (roleId === 2) {
                this.currentUserRole = this.componentRoles.PLANT_ADMIN;
                if (this.loggedInPlantIds.length === 0) {
                    console.error(`Plant Admin (Role ID ${roleId}, User ID ${this.loggedInAdminId}) has no plants assigned! Access may be incorrect.`);
                    this.toast?.showErrorToast("User configuration error: Plant Admin has no assigned plants.");
                    // Optionally revoke role: this.currentUserRole = '';
                }
            } else {
                console.error(`Invalid or missing adminsRoleId (${roleId}) for User ID ${this.loggedInAdminId}.`);
                this.currentUserRole = '';
                this.toast?.showErrorToast("User configuration error: Invalid role.");
            }

            console.log(`User Role Determined: ${this.currentUserRole || 'None'}, Plant IDs: [${this.loggedInPlantIds.join(', ')}]`);

        } catch (error) {
            console.error("Error parsing user data from localStorage:", error);
            this.currentUserRole = '';
            this.loggedInAdminId = null;
            this.loggedInPlantIds = [];
            this.toast?.showErrorToast("Error reading user session. Please log in again.");
        }
    }

    // --- Initialize Confirmation Modal ---
    initializeConfirmationModal(): void {
        if (typeof bootstrap !== 'undefined' && this.confirmationModalElementRef?.nativeElement) {
             if (!this.confirmationModalInstance) {
                try {
                    this.confirmationModalInstance = new bootstrap.Modal(this.confirmationModalElementRef.nativeElement);
                    console.log("Bootstrap confirmation modal initialized.");
                } catch (e) {
                    console.error("Error initializing Bootstrap confirmation modal:", e);
                    this.toast?.showErrorToast("Failed to initialize confirmation dialog.");
                }
            }
        } else if (!this.confirmationModalElementRef?.nativeElement) {
             console.warn("Confirmation modal element (#forceCompleteConfirmationModal) not found in ngAfterViewInit.");
        } else if (typeof bootstrap === 'undefined'){
             console.error("Bootstrap JS library not found.");
             this.toast?.showErrorToast("Required library missing for confirmation.");
        }
    }

    // --- Data Fetching for Plant Dropdown ---
    async getPlants() {
        const data = { sort: 'name,ASC', filter: ['enabled||eq||true'], limit: 1000 };
        const param = createAxiosConfig(data);
        try {
            console.log("Fetching plants for Tour Management...");
            const response = await this.plantService.getPlants(param);
            // Map to the structure needed by the dropdown
            let allEnabledPlants: SelectOption[] = response?.data?.map((p: any) => ({ id: p.id, name: p.name })) ?? response?.map((p: any) => ({ id: p.id, name: p.name })) ?? [];
            console.log("Fetched all enabled plants:", allEnabledPlants.length);

            // Filter plants based on role
            if (this.currentUserRole === this.componentRoles.PLANT_ADMIN && this.loggedInPlantIds.length > 0) {
                this.availablePlants = allEnabledPlants.filter(plant =>
                    this.loggedInPlantIds.includes(plant.id)
                );
                console.log(`Plant Admin: Filtered ${this.availablePlants.length} plants from assigned IDs: [${this.loggedInPlantIds.join(', ')}]`);
                if (this.availablePlants.length !== this.loggedInPlantIds.length) {
                     console.warn(`Some assigned plants might be disabled or not found.`);
                }
            } else if (this.currentUserRole === this.componentRoles.SUPER_ADMIN) {
                this.availablePlants = allEnabledPlants;
                 console.log(`Super Admin: Showing all ${this.availablePlants.length} enabled plants.`);
            } else {
                 this.availablePlants = []; // No role or Plant Admin with no plants
                 console.log("No plants available for current user role/assignment.");
            }

        } catch (error) {
            console.error("Error fetching plants:", error);
            this.availablePlants = [];
            this.toast?.showErrorToast("Failed to load plants for filtering.");
        }
    }

    // --- Filter Modal Methods ---
    openFilterModal() { this.isFilterModalOpen = true; }
    closeModal() { this.isFilterModalOpen = false; }

    applyFilters() {
        // Trim string values to prevent whitespace-only searches
        if (this.filters.firstName) {
            this.filters.firstName = this.filters.firstName.trim();
        }
        if (this.filters.lastName) {
            this.filters.lastName = this.filters.lastName.trim();
        }
        if (this.filters.email) {
            this.filters.email = this.filters.email.trim();
        }
        if (this.filters.contactNumber) {
            this.filters.contactNumber = this.filters.contactNumber.trim();
        }

        // Check for date range validity
        if (this.filters.startDate && this.filters.endDate) {
            const startDate = new Date(this.filters.startDate);
            const endDate = new Date(this.filters.endDate);
            if (endDate < startDate) {
                this.toast?.showErrorToast("End date cannot be earlier than start date.");
                return;
            }
        }

        // Validate first name contains only alphabets if provided
        if (this.filters.firstName && !this.isValidName(this.filters.firstName)) {
            this.toast?.showErrorToast("First name should contain only alphabets.");
            return;
        }

        // Validate last name contains only alphabets if provided
        if (this.filters.lastName && !this.isValidName(this.filters.lastName)) {
            this.toast?.showErrorToast("Last name should contain only alphabets.");
            return;
        }

        // Validate email format if provided
        if (this.filters.email) {
            // First check basic email format
            const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
            if (!emailRegex.test(this.filters.email)) {
                this.toast?.showErrorToast("Please enter a valid email address.");
                return;
            }

            // Then check for adani.com domain
            if (!this.filters.email.toLowerCase().endsWith('@adani.com')) {
                this.toast?.showErrorToast("Email must use the @adani.com domain.");
                return;
            }
        }

        // Validate contact number if provided
        if (this.filters.contactNumber && !this.isValidContactNumber(this.filters.contactNumber)) {
            this.toast?.showErrorToast("Contact number must be 10 digits.");
            return;
        }

        this.currentPage = 1; // Reset page on filter apply
        this.loadToursForCurrentTab();
        this.closeModal();
    }

    resetFilters() {
        this.filters = {
            firstName: null, lastName: null, email: null, contactNumber: null,
            startDate: null, endDate: null,
            plantId: null, // Reset plant filter
            enabled: null,
            sortField: 'id', sortDirection: 'DESC'
        };
        // REMOVE: No need to re-apply plant filter based on role here

        this.currentPage = 1;
        this.loadToursForCurrentTab();
        // Keep filter modal open after reset?
        // this.closeModal();
    }

    // --- Validation Helper Methods ---
    private isValidName(name: string): boolean {
        const nameRegex = /^[a-zA-Z\s]*$/;
        return nameRegex.test(name);
    }

    private isValidEmail(email: string): boolean {
        // First check basic email format
        const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
        if (!emailRegex.test(email)) {
            return false;
        }

        // Then check for adani.com domain
        return email.toLowerCase().endsWith('@adani.com');
    }

    private isValidContactNumber(contactNumber: string): boolean {
        const contactRegex = /^[0-9]{10}$/;
        return contactRegex.test(contactNumber);
    }

    // --- Helper to trim input fields ---
    trimInputField(formData: any, fieldName: string): void {
        if (formData && formData[fieldName] && typeof formData[fieldName] === 'string') {
            formData[fieldName] = formData[fieldName].trim();
        }
    }

    // --- Tour Point Details Modal Logic (using ng-bootstrap) ---
    async openTourPointModal(modalContent: any, tour: Tour) {
         if (this.detailsModalRef) { return; }
         if (!tour || !tour.id) { console.error("Invalid tour data."); this.toast?.showErrorToast("Could not load details."); return; }

         this.selectedTourForDetails = tour;
         this.tourScanPoints = [];
         this.tourDetailsPagination = { currentPage: 1, itemsPerPage: 5, totalItems: 0 };

         this.detailsModalRef = this.modalService.open(modalContent, {
             ariaLabelledBy: 'tour-point-modal-title', size: 'xl', centered: true, scrollable: true
         });
         this.detailsModalRef.result.then(
             (result: any) => { console.log(`Details Modal closed: ${result}`); this.clearDetailsModalState(); },
             (reason:any) => { console.log(`Details Modal dismissed: ${reason}`); this.clearDetailsModalState(); }
         );
         await this.fetchTourScanPoints(tour.id, this.tourDetailsPagination.currentPage);
    }

    async fetchTourScanPoints(tourId: number, page: number) {
        if (!tourId) return;
        this.detailsLoading = true;
        const data = {
            page: page, limit: this.tourDetailsPagination.itemsPerPage, sort: 'createdTimestamp,DESC',
            filter: [`tourId||eq||${tourId}`], join: ['qrCode.zone', 'plant', 'qrCode.plant'] // Adjust joins as needed
        };
        try {
            const param = createAxiosConfig(data);
            const response = await this.tourManagementService.getTourScanPoint(param); // Ensure this service method exists
            this.tourScanPoints = response?.data ?? [];
            this.tourDetailsPagination.totalItems = response?.total ?? 0;
        } catch (error) {
            console.error(`Error fetching scan points for tour ${tourId}:`, error);
            this.tourScanPoints = []; this.tourDetailsPagination.totalItems = 0;
            this.toast?.showErrorToast("Failed to load scan point details.");
        } finally { this.detailsLoading = false; }
    }

    onDetailsPageChange(page: number) {
        if (this.selectedTourForDetails && page > 0 && page !== this.tourDetailsPagination.currentPage) {
            this.tourDetailsPagination.currentPage = page;
            this.fetchTourScanPoints(this.selectedTourForDetails.id, page);
        }
    }

    clearDetailsModalState() {
        this.selectedTourForDetails = null; this.tourScanPoints = [];
        this.tourDetailsPagination = { currentPage: 1, itemsPerPage: 5, totalItems: 0 };
        this.detailsModalRef = null;
    }

    closeDetailsModal() { this.detailsModalRef?.close('Close click'); }

    // --- Force Complete Confirmation Modal Logic (using standard Bootstrap JS) ---
    requestForceCompleteTour(tour: Tour): void {
         if (!tour || tour.id === undefined) {
            console.error("Cannot force complete: Invalid tour data provided.");
            this.toast?.showErrorToast("Invalid tour data.");
            return;
         }
         this.tourToForceComplete = tour;

         if (!this.confirmationModalInstance && this.confirmationModalElementRef?.nativeElement) {
            this.initializeConfirmationModal();
         }

         if (this.confirmationModalInstance && typeof this.confirmationModalInstance.show === 'function') {
             console.log(`Requesting confirmation to force complete tour ID: ${tour.id}`);
             this.confirmationActionText = 'force complete';
             this.confirmationModalInstance.show();
         } else {
              console.error("Confirmation modal instance not available or initialized correctly.");
              this.toast?.showErrorToast("Could not open confirmation dialog. Check console.");
         }
    }

    async confirmForceComplete(): Promise<void> {
        if (!this.tourToForceComplete || this.tourToForceComplete.id === undefined) {
            console.error("Confirmation failed: No tour context stored.");
            this.toast?.showErrorToast("An error occurred confirming the action.");
            this.hideConfirmationModal();
            return;
        }

        const tourId = this.tourToForceComplete.id;
        console.log(`Confirmation received. Force completing tour ID: ${tourId}`);
        this.hideConfirmationModal();

        const updatePayload = {
            tableName: 'tours', // VERIFY TABLE NAME
            id: tourId,
            data: {
                status: 1, // Completed
                endTime: new Date().toISOString()
            }
            // createdBy: this.loggedInAdminId // Add if UpdateService requires it
        };

        try {
            await this.updateService.update(updatePayload);
            this.toast?.showSuccessToast(`Tour ${tourId} marked as complete.`);
            this.loadToursForCurrentTab(); // Refresh list
        } catch (error) {
            console.error(`Error force completing tour ${tourId}:`, error);
             const errorMsg = (error as any)?.response?.data?.message || "Failed to complete tour.";
             this.toast?.showErrorToast(errorMsg);
        } finally {
             this.tourToForceComplete = null;
        }
    }

    cancelForceComplete(): void {
        console.log("Force complete cancelled by user.");
        this.hideConfirmationModal();
        this.tourToForceComplete = null;
    }

    private hideConfirmationModal(): void {
         if (this.confirmationModalInstance && typeof this.confirmationModalInstance.hide === 'function') {
            this.confirmationModalInstance.hide();
         } else {
             console.warn("Attempted to hide confirmation modal, but instance was not available.");
         }
    }

    // --- Centralized Data Fetching for Main Tour Lists ---
    async fetchTours(page: number, status: number, currentFilters: TourFilter): Promise<{ data: Tour[], total: number } | null> {
        this.listLoading = true;
        const filterParams: string[] = [`status||eq||${status}`];

        // --- Apply Role-Based Plant Filtering ---
        if (this.currentUserRole === this.componentRoles.PLANT_ADMIN) {
            if (this.loggedInPlantIds.length > 0) {
                if (currentFilters.plantId !== null && currentFilters.plantId !== undefined) {
                    // Plant Admin selected a specific plant from their assigned list
                    // Use 'eq' for the single selected plant ID based on current filter structure
                    filterParams.push(`plantId||eq||${currentFilters.plantId}`);
                    console.log("Plant Admin: Filtering tours by selected plant ID (single select):", currentFilters.plantId);
                } else {
                    // Plant Admin selected "All My Plants" (or default) - filter by all their assigned plants
                    filterParams.push(`plantId||$in||${this.loggedInPlantIds.join(',')}`);
                    console.log("Plant Admin: Filtering tours by assigned plant IDs:", this.loggedInPlantIds);
                }
            } else {
                // Plant Admin has no plants assigned - apply a filter that returns no data
                console.warn("Plant Admin has no assigned plants. Applying impossible filter.");
                filterParams.push(`plantId||eq||-1`);
            }
        } else if (this.currentUserRole === this.componentRoles.SUPER_ADMIN) {
            // Super Admin: Only filter if they explicitly selected a plant
            if (currentFilters.plantId !== null && currentFilters.plantId !== undefined) {
                filterParams.push(`plantId||eq||${currentFilters.plantId}`);
                console.log("Super Admin: Filtering tours by selected plant ID:", currentFilters.plantId);
            } else {
                 console.log("Super Admin: No specific plant selected, showing all.");
                 // No plant filter added
            }
        } else {
            // No role or unknown role - potentially restrict or show all? Showing all for now.
            console.warn("Unknown user role, not applying specific plant filters.");
            // No plant filter added by default
        }
        // --- End Role-Based Plant Filtering ---

        // Apply other filters
        if (currentFilters.enabled !== null && currentFilters.enabled !== undefined && currentFilters.enabled !== '') filterParams.push(`enabled||eq||${currentFilters.enabled}`);

        // Trim string values before applying filters
        if (currentFilters.firstName) {
            const trimmedFirstName = currentFilters.firstName.trim();
            if (trimmedFirstName) {
                filterParams.push(`admin.firstName||$contL||${trimmedFirstName}`);
            }
        }

        if (currentFilters.lastName) {
            const trimmedLastName = currentFilters.lastName.trim();
            if (trimmedLastName) {
                filterParams.push(`admin.lastName||$contL||${trimmedLastName}`);
            }
        }

        if (currentFilters.email) {
            const trimmedEmail = currentFilters.email.trim();
            if (trimmedEmail) {
                filterParams.push(`admin.email||$contL||${trimmedEmail}`);
            }
        }

        if (currentFilters.contactNumber) {
            const trimmedContactNumber = currentFilters.contactNumber.trim();
            if (trimmedContactNumber) {
                filterParams.push(`admin.contactNumber||$contL||${trimmedContactNumber}`);
            }
        }
        if (currentFilters.startDate) filterParams.push(`startTime||gte||${currentFilters.startDate}T00:00:00.000Z`);
        if (currentFilters.endDate) {
             const endDate = new Date(currentFilters.endDate);
             endDate.setUTCHours(23, 59, 59, 999);
             filterParams.push(`startTime||lte||${endDate.toISOString()}`);
        }

        const data = {
            page: page, limit: this.itemsPerPage,
            sort: `${currentFilters.sortField || 'id'},${currentFilters.sortDirection || 'DESC'}`,
            filter: filterParams,
            join: ['admin', 'plant'] // Ensure needed joins
        };
        console.log("API Request Params (fetchTours):", JSON.stringify(data, null, 2));

        try {
            const param = createAxiosConfig(data);
            const response = await this.tourManagementService.getTours(param);
            return { data: response?.data ?? [], total: response?.total ?? 0 };
        } catch (error) {
            console.error("Error fetching tours:", error);
            this.toast?.showErrorToast("Failed to load tours list.");
            return null;
        } finally { this.listLoading = false; }
    }

    // --- Helper to load data into the correct list based on tab ---
    async loadToursForCurrentTab() {
        const currentTab = this.tabs[this.selectedTabIndex];
        if (!currentTab) return;

        console.log(`Loading data for tab: ${currentTab.title}`);
        const result = await this.fetchTours(this.currentPage, currentTab.status, this.filters);

        // Assign to correct list property dynamically
        const listKey = currentTab.listKey;
         if (listKey) {
            // Reset lists before loading new data
            this.ongoingToursList = [];
            this.completedToursLists = [];
            this.totalItems = 0;

            if (result) {
                 console.log(`Fetched ${result.data.length} records for ${listKey}, total: ${result.total}`);
                this.totalItems = result.total;
                this[listKey] = result.data; // Dynamic assignment
            }
        } else {
            console.error("Invalid listKey for tab:", currentTab);
        }
    }

    // --- Tab and Pagination Handlers ---
    onTabSelected(index: number) {
        if (this.selectedTabIndex === index) return;
        console.log(`Tab selected: ${index}`);
        this.selectedTabIndex = index;
        this.currentPage = 1;
        this.loadToursForCurrentTab();
    }
    onPageChange(page: number) {
        if (this.currentPage === page) return;
         console.log(`Page changed to: ${page}`);
        this.currentPage = page;
        this.loadToursForCurrentTab();
    }

    // --- Table Sorting ---
    getSortClass(key: string): string {
        return this.filters.sortField === key ? `sort-${this.filters.sortDirection?.toLowerCase()}` : 'sort-none';
    }
    sortBy(field: string) {
         if (this.listLoading) return;
         console.log(`Sorting by: ${field}`);
        if (this.filters.sortField === field) {
            this.filters.sortDirection = this.filters.sortDirection === 'ASC' ? 'DESC' : 'ASC';
        } else {
            this.filters.sortField = field;
            this.filters.sortDirection = 'DESC';
        }
        this.currentPage = 1;
        this.loadToursForCurrentTab();
    }

    // --- Helper to get current list data for download ---
    getCurrentListData(): Tour[] | undefined {
        const currentTab = this.tabs[this.selectedTabIndex];
        if (currentTab && currentTab.listKey) {
            if (Array.isArray(this[currentTab.listKey])) {
                return this[currentTab.listKey];
            }
        }
        console.warn("Could not get list data for download for tab index:", this.selectedTabIndex);
        return undefined;
    }

    // --- Method to Fetch ALL Filtered Tours (No Pagination) ---
    async fetchAllFilteredTours(): Promise<Tour[] | null> {
        const currentTab = this.tabs[this.selectedTabIndex];
        if (!currentTab) {
            console.error("Cannot fetch all data: Invalid tab selected.");
            return null;
        }

        const status = currentTab.status;
        const filterParams: string[] = [`status||eq||${status}`];

        // Append dynamic filters from this.filters
        if (this.filters.plantId !== null && this.filters.plantId !== undefined) {
            filterParams.push(`plantId||eq||${this.filters.plantId}`);
        }
        if (this.filters.enabled !== null && this.filters.enabled !== undefined && this.filters.enabled !== '') filterParams.push(`enabled||eq||${this.filters.enabled}`);

        // Trim string values before applying filters
        if (this.filters.firstName) {
            const trimmedFirstName = this.filters.firstName.trim();
            if (trimmedFirstName) {
                filterParams.push(`admin.firstName||$contL||${trimmedFirstName}`);
            }
        }

        if (this.filters.lastName) {
            const trimmedLastName = this.filters.lastName.trim();
            if (trimmedLastName) {
                filterParams.push(`admin.lastName||$contL||${trimmedLastName}`);
            }
        }

        if (this.filters.email) {
            const trimmedEmail = this.filters.email.trim();
            if (trimmedEmail) {
                filterParams.push(`admin.email||$contL||${trimmedEmail}`);
            }
        }

        if (this.filters.contactNumber) {
            const trimmedContactNumber = this.filters.contactNumber.trim();
            if (trimmedContactNumber) {
                filterParams.push(`admin.contactNumber||$contL||${trimmedContactNumber}`);
            }
        }
        if (this.filters.startDate) filterParams.push(`startTime||gte||${this.filters.startDate}T00:00:00.000Z`);
        if (this.filters.endDate) {
             const endDate = new Date(this.filters.endDate);
             endDate.setUTCHours(23, 59, 59, 999);
             filterParams.push(`startTime||lte||${endDate.toISOString()}`);
        }

        const data = {
            limit: 10000, // Adjust limit as needed
            sort: `${this.filters.sortField || 'id'},${this.filters.sortDirection || 'DESC'}`,
            filter: filterParams,
            join: ['admin', 'plant'] // Ensure needed joins
        };
        console.log('API Request Params (Tour Download - All Data):', JSON.stringify(data, null, 2));

        try {
            const param = createAxiosConfig(data);
            const response = await this.tourManagementService.getTours(param);

            if (response && Array.isArray(response.data)) {
                return response.data;
            } else if (response && Array.isArray(response)) {
                 return response;
            } else {
                console.warn("Received unexpected response structure when fetching all tours:", response);
                this.toast?.showErrorToast("Could not retrieve data in the expected format for download.");
                return null;
            }
        } catch (error) {
            console.error("Error fetching all tours for download:", error);
             const errorMsg = (error as any)?.response?.data?.message || "Failed to retrieve full data for download.";
            this.toast?.showErrorToast(errorMsg);
            return null;
        }
    }

    // --- Excel Download Logic ---
    async downloadExcel(type: 'current' | 'all') {
        const currentTab = this.tabs[this.selectedTabIndex];
        if (!currentTab || this.isDownloadingExcel) {
            if (this.isDownloadingExcel) console.warn("Download already in progress.");
            else console.error("No valid tab selected for download.");
            return;
        }

        this.isDownloadingExcel = true;
        this.downloadType = type;
        this.toast?.showSuccessToast(`Preparing download for ${type === 'current' ? 'current page' : 'all filtered'} tours...`);

        let dataToExport: Tour[] | null = null;

        try {
            // 1. Get Data based on type
            if (type === 'all') {
                dataToExport = await this.fetchAllFilteredTours();
            } else { // type === 'current'
                dataToExport = this.getCurrentListData() ?? null;
                if (dataToExport === null){
                    console.error("Could not find data list for current tab:", currentTab.title);
                    // Toast likely already shown
                }
            }

            // 2. Check data
            if (dataToExport === null) { // Check for null after fetch attempt
                return;
            }
            if (!dataToExport || dataToExport.length === 0) {
                this.toast?.showErrorToast(`No tours available to download for ${type === 'current' ? 'the current page' : 'the current filters'}.`);
                return;
            }

            console.log(`Fetched ${dataToExport.length} tours for Excel export (${type}).`);

            // 3. Transform data for Excel Sheet
            const dataForExcel = dataToExport.map(tour => ({
                'Tour ID': tour.id,
                'User ID': tour.adminId,
                'User Name': `${tour.admin?.firstName ?? ''} ${tour.admin?.lastName ?? ''}`.trim(),
                'User Email': tour.admin?.email ?? '',
                'Plant': tour.plant?.name ?? tour.plantId ?? '',
                'Start Date': tour.startTime ? new Date(tour.startTime).toLocaleDateString() : '',
                'Start Time': tour.startTime ? new Date(tour.startTime).toLocaleTimeString() : '',
                'End Date': tour.endTime ? new Date(tour.endTime).toLocaleDateString() : '',
                'End Time': tour.endTime ? new Date(tour.endTime).toLocaleTimeString() : '',
                'Duration (Minutes)': tour.tourDurationInMinus ?? '',
                'Last Scan Time': tour.lastQrScannedTime ? new Date(tour.lastQrScannedTime).toLocaleString() : '',
                'Total Scan Points': tour.qrPointTotal ?? 0,
                'Status': tour.status === 0 ? 'Ongoing' : (tour.status === 1 ? 'Completed' : 'Unknown'),
                'Created At': tour.createdTimestamp ? new Date(tour.createdTimestamp).toLocaleString() : '',
            }));

            // 4. Create Worksheet and Workbook
            const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(dataForExcel);
            const wb: XLSX.WorkBook = XLSX.utils.book_new();
            const safeSheetName = currentTab.title.replace(/[/\\?*[\]]/g, '').substring(0, 30); // Sanitize
            XLSX.utils.book_append_sheet(wb, ws, safeSheetName || 'Tours');

            // 5. Generate File Name
            const dateStr = new Date().toISOString().slice(0, 10);
            const typeStr = type === 'current' ? `Page${this.currentPage}` : 'All';
            const fileName = `Tours_${safeSheetName}_${typeStr}_${dateStr}.xlsx`;

            // 6. Trigger Download
            XLSX.writeFile(wb, fileName);

            this.toast?.showSuccessToast(`Excel file download started (${type}).`);

        } catch (error) {
            console.error(`Error generating Excel file (${type}):`, error);
            this.toast?.showErrorToast(`An error occurred while generating the Excel file (${type}).`);
        } finally {
            this.isDownloadingExcel = false; // Reset flag
            this.downloadType = null; // Reset type
        }
    }

} // End of TourManagementComponent class