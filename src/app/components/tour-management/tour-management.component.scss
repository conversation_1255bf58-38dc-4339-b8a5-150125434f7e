.card-body{
    height: 70vh;
    overflow: auto;
}
.table-header th {
    font-size: 12px;
    background-color: #0B74B0 !important;
    color: white !important;
    text-align: center;
}
td{
    font-size: 12px;
}
i.edit{
    font-size: 12px;
}
.actions{
    text-align: center !important;
    vertical-align: middle !important;
}
.table-container{
    width: 100%;
    overflow-x: auto;
}
.table-responsive {
    overflow-x: auto;
    white-space: nowrap;
    max-width: 100%;
}

/* Styling for table details */
.details-container {
    display: table;
    width: 100%;
    text-align: left;
    padding: 5px;
    border-spacing: 0 8px;
}
/* Styling for labels */
p strong {
    font-weight: 500;
    color: #777;
    margin-right: 8px;
    font-size: 12px;
    min-width: 110px;
    display: inline-block;
    vertical-align: top;
}

.value-text {
    font-weight: 600;
    color: #222;
    font-size: 12px;
    display: inline-block;
    word-break: break-word;
    overflow-wrap: break-word;
    flex: 1;
    letter-spacing: normal;
    line-height: 1.5;
}

/* Improved layout for details */
.align-middle p, .label-value {
    margin-bottom: 8px;
    line-height: 1.5;
    display: flex;
    align-items: baseline;
    padding: 2px 0;
    border-bottom: 1px dashed #eee;
    padding-bottom: 8px;
}

/* For the last item in a cell */
.align-middle p.mb-0, .label-value:last-child {
    margin-bottom: 0;
    border-bottom: none;
}

/* Style for bold text in table cells to match value-text */
td b,
td strong,
td .details-container p span:not(strong) {
    font-weight: 600;
    color: #222;
    font-size: 12px;
    letter-spacing: normal;
    line-height: 1.5;
}

/* Add bottom border to table rows */
.table-bordered tbody tr {
    border-bottom: 1px dashed #eee;
}

/* Styling for modal tables */
.modal-body table {
    width: 100%;
    border-collapse: collapse;
}

.modal-body table th {
    font-weight: 600;
    background-color: #f5f7fa;
    color: #333;
    text-align: center;
    padding: 8px;
}

.modal-body table td {
    font-weight: 500;
    padding: 8px;
    border-bottom: 1px dashed #eee;
}

/* Ensure consistent styling for details in modals */
.modal-body .details-container p {
    margin-bottom: 8px;
    line-height: 1.5;
    display: flex;
    align-items: baseline;
    padding: 2px 0;
    border-bottom: 1px dashed #eee;
    padding-bottom: 8px;
}

.modal-body .details-container p:last-child {
    margin-bottom: 0;
    border-bottom: none;
}