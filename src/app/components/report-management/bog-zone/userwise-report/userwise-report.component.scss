/* custom-table.component.css */

.custom-table {
  width: 100%;
  border-collapse: collapse;
  border-radius: 10px;
  margin-top: 10px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.card-body {
  height: 70vh;
  overflow: auto;
}

.custom-table th,
.custom-table td {
  padding: 10px 12px;
  text-align: left;
  font-size: 12px;
  vertical-align: middle;
}

.custom-table thead th {
  font-size: 12px;
  background-color: #0B74B0 !important;
  color: white !important;
  text-align: center;
  font-weight: bold;
}

/* Add bottom border to table rows */
.custom-table tbody tr {
  border-bottom: 1px dotted #eee;
}

/* Details container styling */
.details-container {
  display: table;
  width: 100%;
  text-align: left;
  padding: 5px;
  border-spacing: 0 8px;
}

.label-value {
  margin-bottom: 8px;
  line-height: 1.5;
  display: flex;
  align-items: baseline;
  padding: 2px 0;
  border-bottom: 1px dotted #eee;
  padding-bottom: 8px;
}

.label-value:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.label-value strong {
  font-weight: 500;
  color: #777;
  margin-right: 8px;
  min-width: 110px;
  font-size: 12px;
  display: inline-block;
  vertical-align: top;
}

.value-text {
  font-weight: 600;
  color: #222;
  font-size: 12px;
  display: inline-block;
  word-break: break-word;
  overflow-wrap: break-word;
  flex: 1;
  letter-spacing: normal;
  line-height: 1.5;
}

/* User details styling */
.user-details-cell {
  padding: 5px;
}

/* Filter button styling */
.filter-button {
  width: 35px;
  cursor: pointer;
}


/* Tooltip Styling */
.tooltip-container {
  position: relative;
  display: inline-block;
}

.info-icon-container {
  display: inline-block;
  position: absolute;
  top: 0;
  right: 0;
  padding: 5px;
  cursor: pointer;
}

.info-icon-container i.el-icon-info {
  color: #999;
  font-size: 16px;
}

.tooltip-content {
  display: none;
  position: absolute;
  z-index: 1;
  top: 100%;
  right: 0;
  background-color: white;
  color: #333;
  border: 1px solid #ccc;
  padding: 15px;
  border-radius: 4px;
  box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.2);
  width: 250px;
  text-align: left;
}

.tooltip-content::before {
  content: "";
  position: absolute;
  bottom: 100%;
  right: 10px;
  border-width: 0 6px 6px 6px;
  border-style: solid;
  border-color: transparent transparent white transparent;
}

.tooltip-container:hover .tooltip-content {
  display: block;
}

.qr-details-header {
  font-weight: bold;
  margin-bottom: 8px;
}

.tooltip-content ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.tooltip-content li {
  margin-bottom: 5px;
  display: flex;
  align-items: center;
}

.tooltip-content li i {
  margin-right: 8px;
  color: #007bff;
}