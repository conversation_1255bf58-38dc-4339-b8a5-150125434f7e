<app-toast-message></app-toast-message>
<div class="card custom-card">
    <div class="card-header">
      <div class="row align-items-center">
        <div class="col">
          <h6 class="mb-0">User wise report</h6>
        </div>
        <div class="col text-end d-flex align-items-center justify-content-end">
          <div ngbDropdown class="d-inline-block me-2">
            <button type="button" class="btn btn-sm adani-btn dropdown-toggle" id="downloadZoneUserReportExcelDropdown" ngbDropdownToggle
                [disabled]="isDownloadingExcel || listLoading">
                <span *ngIf="!isDownloadingExcel">
                    <i class="bi bi-file-earmark-excel me-1"></i> Download Excel
                </span>
                <span *ngIf="isDownloadingExcel">
                    <div class="spinner-border spinner-border-sm me-1"></div>
                    Downloading {{ downloadType === 'current' ? 'Page' : (downloadType === 'all' ? 'All' : '') }}...
                </span>
            </button>
            <ul ngbDropdownMenu aria-labelledby="downloadZoneUserReportExcelDropdown">
                <li>
                    <button type="button" ngbDropdownItem (click)="downloadExcel('current')" [disabled]="isDownloadingExcel || listLoading || (list?.length ?? 0) === 0">
                        <i class="bi bi-download me-1"></i> Download Current Page ({{ list?.length ?? 0 }})
                    </button>
                </li>
                <li>
                    <button type="button" ngbDropdownItem (click)="downloadExcel('all')" [disabled]="isDownloadingExcel || listLoading || totalItems === 0">
                        <i class="bi bi-cloud-download me-1"></i> Download All Filtered ({{ totalItems }})
                    </button>
                </li>
            </ul>
        </div>
          <button type="button" class="btn btn-sm ms-3" (click)="openFilterModal()" aria-label="Filter">
            <img src="../../../assets/svg/filter.svg" class="filter-button" alt="" />
          </button>
      </div>
      </div>
    </div>
    <div class="card-body">
      <div class="table-responsive">
        <table class="table table-bordered table-hover custom-table">
          <thead class="table-header">
            <tr>
              <th scope="col" class="text-center">User Id</th>
              <th scope="col" class="text-center">User Details</th>
              <th scope="col" class="text-center">Plant</th>
              <th scope="col" class="text-center">Zone Area</th>
              <th scope="col" class="text-center">Number of Time Zone Scanned</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngIf="listLoading">
              <td colspan="5" class="text-center p-4"> <!-- Colspan = 5 -->
                  <div class="spinner-border spinner-border-sm me-2"></div>
                  Loading report...
              </td>
          </tr>
          <tr *ngIf="!listLoading && list.length === 0">
            <td colspan="5" class="text-center p-4 text-muted"> <!-- Colspan = 5 -->
                No user wise zone report data found matching the criteria.
            </td>
        </tr>
            <tr *ngFor="let item of list">
              <td class="text-center">{{ item.adminId }}</td>
              <td class="user-details-cell">
                <div class="details-container">
                  <p class="label-value">
                    <strong>Name:</strong>
                    <span class="value-text">{{ item.firstName }} {{ item.lastName }}</span>
                  </p>
                  <p class="label-value">
                    <strong>Email:</strong>
                    <span class="value-text">{{ item.email }}</span>
                  </p>
                  <p class="label-value">
                    <strong>Contact:</strong>
                    <span class="value-text">{{ item.contactNumber }}</span>
                  </p>
                </div>
              </td>
              <td class="text-center">{{ item.name }}</td>
              <td class="text-center">{{ item.zoneArea }}</td>
              <td class="text-center">{{ item.noOfTimesCovered }}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
    <div class="card-footer text-muted text-center">
      <app-pagination [currentPage]="currentPage" [totalItems]="totalItems" [itemsPerPage]="itemsPerPage"
      (pageChange)="onPageChange($event)"></app-pagination>
    </div>
  </div>
<!-- Offcanvas for Filters -->
<app-offcanvas [title]="'Filter UserWise Zone Report'" *ngIf="isFilterModalOpen" (onClickCross)="closeModal()">
  <!-- Use the Reactive Form -->
  <div class="container">
    <!-- Bind the form group and handle submission -->
    <form [formGroup]="filterForm" (ngSubmit)="applyFilters()">
      <div class="row g-3"> <!-- Use g-3 for spacing -->

        <!-- From Date -->
        <div class="col-12">
          <label for="fromDate" class="form-label">From Date <span class="text-danger">*</span></label>
          <input
            type="date"
            id="fromDate"
            class="form-control"
            placeholder="From Date"
            formControlName="startDate"
            [class.is-invalid]="filterForm.get('startDate')?.invalid && filterForm.get('startDate')?.touched">
           <!-- Validation message -->
           <div *ngIf="filterForm.get('startDate')?.invalid && filterForm.get('startDate')?.touched" class="invalid-feedback">
               <small *ngIf="filterForm.get('startDate')?.errors?.['required']">From Date is required.</small>
           </div>
         </div>

         <!-- To Date -->
         <div class="col-12">
           <label for="toDate" class="form-label">To Date <span class="text-danger">*</span></label>
           <input
             type="date"
             id="toDate"
             class="form-control"
             placeholder="To Date"
             formControlName="endDate"
             [class.is-invalid]="filterForm.get('endDate')?.invalid && filterForm.get('endDate')?.touched">
              <div *ngIf="filterForm.get('endDate')?.invalid && filterForm.get('endDate')?.touched" class="invalid-feedback">
                <small *ngIf="filterForm.get('endDate')?.errors?.['required']">To Date is required.</small>
            </div>
             <!-- Date range validation message -->
             <div *ngIf="filterForm.get('endDate')?.valid && filterForm.get('startDate')?.valid && (filterForm.value.endDate ?? '') < (filterForm.value.startDate ?? '') && filterForm.get('endDate')?.touched"
                  class="text-danger small mt-1">
                  To Date cannot be before From Date.
             </div>
         </div>

        <!-- Cluster Select -->
        <div class="col-12">
          <label for="clusterSelect" class="form-label">Select Cluster</label>
          <div class="d-flex align-items-center"> <!-- Flex container for select + spinner -->
              <select id="clusterSelect" class="form-select" formControlName="clusterId" [attr.disabled]="isLoadingClusters ? true : null">
                  <option [ngValue]="null">
                      {{ isLoadingClusters ? 'Loading...' : 'Select Cluster' }}
                  </option>
                  <option *ngFor="let cluster of clusterOptions" [value]="cluster.id">
                      {{ cluster.title }}
                  </option>
              </select>
               <!-- Loading Spinner for Clusters -->
              <div *ngIf="isLoadingClusters" class="spinner-border spinner-border-sm text-secondary ms-2">
                  <span class="visually-hidden">Loading...</span>
              </div>
          </div>
      </div>

        <!-- Plant Select (Using ng-select) -->
        <div class="col-12">
          <label for="plantSelectNg" class="form-label">Plant(s)</label>
           <!-- Replace the previous select/spinner container with ng-select -->
           <ng-select
                id="plantSelectNg"
                [items]="plantOptions"
                bindLabel="name"
                bindValue="id"
                [multiple]="true"
                [placeholder]="calculatePlantPlaceholder()"
                formControlName="plantIds"
                [loading]="isLoadingPlants"
                [searchable]="true"
                [closeOnSelect]="false"
                [disabled]="isLoadingPlants || !filterForm.get('clusterId')?.value">
                 <!-- Optional: Customize display for not found -->
                 <ng-template ng-notfound-text>
                     <span>{{ calculateNotFoundText() }}</span>
                 </ng-template>

           </ng-select>
           <!-- The loading spinner is now built into ng-select ([loading]) -->
           <!-- The text hint can remain -->
           <small class="form-text text-muted" *ngIf="!isLoadingPlants && plantOptions.length > 0">Type to search or select multiple.</small>
        </div>


        <!-- Submit Button -->
        <div class="col-12 mt-4">
          <button type="submit" class="btn adani-btn w-100" [disabled]="filterForm.invalid || (filterForm.value.endDate && filterForm.value.startDate && (filterForm.value.endDate ?? '') < (filterForm.value.startDate ?? ''))">
            <i class="bi bi-search"></i> Search
          </button>
        </div>
      </div>
    </form>
  </div>
</app-offcanvas>
