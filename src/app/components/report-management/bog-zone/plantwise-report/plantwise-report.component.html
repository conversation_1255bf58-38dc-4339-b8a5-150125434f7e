<app-toast-message></app-toast-message>
<div class="card custom-card">
  <div class="card-header">
    <div class="row align-items-center">
      <div class="col">
        <h6 class="mb-0">Plant wise report</h6>
      </div>
      <div class="col text-end d-flex align-items-center justify-content-end">
        <div ngbDropdown class="d-inline-block me-2">
          <button type="button" class="btn btn-sm adani-btn dropdown-toggle" id="downloadPlantZoneExcelDropdown"
            ngbDropdownToggle [disabled]="isDownloadingExcel || isLoadingReport">
            <span *ngIf="!isDownloadingExcel">
              <i class="bi bi-file-earmark-excel me-1"></i> Download Excel
            </span>
            <span *ngIf="isDownloadingExcel">
              <span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
              Downloading {{ downloadType === 'current' ? 'Page' : (downloadType === 'all' ? 'All' : '') }}...
            </span>
          </button>
          <ul ngbDropdownMenu aria-labelledby="downloadPlantZoneExcelDropdown">
            <li>
              <button ngbDropdownItem (click)="downloadExcel('current')"
                [disabled]="isDownloadingExcel || isLoadingReport || (list?.length ?? 0) === 0">
                <i class="bi bi-download me-1"></i> Download Current Page ({{ list?.length ?? 0 }})
              </button>
            </li>
            <li>
              <button ngbDropdownItem (click)="downloadExcel('all')"
                [disabled]="isDownloadingExcel || isLoadingReport || totalItems === 0">
                <i class="bi bi-cloud-download me-1"></i> Download All Filtered ({{ totalItems }})
              </button>
            </li>
          </ul>
        </div>
        <img src="../../../assets/svg/filter.svg" class="filter-button ms-3" (click)="openFilterModal()" alt=""
          style="width: 35px;" />
      </div>
    </div>
  </div>
  <div class="card-body">
    <div class="table-responsive">
      <table class="table custom-table">
        <thead class="table-header">
          <tr>
            <th scope="col" class="text-center">Plant</th>
            <th scope="col" class="text-center">Zone Area</th>
            <th scope="col" class="text-center">Total Numbers of Scans</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngIf="isLoadingReport">
            <td colspan="3" class="text-center p-4"> <!-- Colspan = 3 -->
              <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
              Loading report...
            </td>
          </tr>
          <tr *ngIf="!isLoadingReport && list.length === 0">
            <td colspan="3" class="text-center p-4 text-muted"> <!-- Colspan = 3 -->
              No plant wise zone report data found matching the criteria.
            </td>
          </tr>
          <tr *ngFor="let item of list">
            <td class="text-center">{{ item.name }}</td>
            <td class="text-center">{{ item.zoneArea }}</td>
            <td class="text-center">{{ item.noOfTimesCovered }}</td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
  <div class="card-footer text-muted text-center">
    <app-pagination [currentPage]="currentPage" [totalItems]="totalItems" [itemsPerPage]="itemsPerPage"
      (pageChange)="onPageChange($event)"></app-pagination>
  </div>
</div>
<app-offcanvas [title]="'Filter PlantWise zone report'" *ngIf="isFilterModalOpen" (onClickCross)="closeModal()">
  <div class="container">
    <form [formGroup]="filterForm" (ngSubmit)="applyFilters()">
      <div class="row g-2">
        <div class="col-12">
          <label class="form-label">From Date</label>
          <input type="date" id="fromDate" class="form-control" placeholder="From Date" formControlName="startDate" />
        </div>
        <div class="col-12">
          <label class="form-label">To Date</label>
          <input type="date" id="toDate" class="form-control" placeholder="To Date" formControlName="endDate" />
        </div>

        <!-- Cluster Select -->
        <div class="col-12">
          <label for="clusterSelect" class="form-label">Select Cluster</label>
          <div class="d-flex align-items-center"> <!-- Flex container for select + spinner -->
            <select id="clusterSelect" class="form-select" formControlName="clusterId"
              [attr.disabled]="isLoadingClusters ? true : null">
              <option [ngValue]="null">
                {{ isLoadingClusters ? 'Loading...' : 'Select Cluster' }}
              </option>
              <option *ngFor="let cluster of clusterOptions" [value]="cluster.id">
                {{ cluster.title }}
              </option>
            </select>
            <!-- Loading Spinner for Clusters -->
            <div *ngIf="isLoadingClusters" class="spinner-border spinner-border-sm text-secondary ms-2" role="status">
              <span class="visually-hidden">Loading...</span>
            </div>
          </div>
        </div>
        <!-- Plant Select (Using ng-select) -->
        <div class="col-12">
          <label for="plantSelectNg" class="form-label">Plant(s)</label>
          <!-- Replace the previous select/spinner container with ng-select -->
          <ng-select id="plantSelectNg" [items]="plantOptions" bindLabel="name" bindValue="id"
            [multiple]="true" [placeholder]="calculatePlantPlaceholder()" formControlName="plantIds"
            [loading]="isLoadingPlants" [searchable]="true" [closeOnSelect]="false"
            [disabled]="isLoadingPlants || !filterForm.get('clusterId')?.value">
            <!-- Optional: Customize display for not found -->

            <!-- Optional: Customize loading text -->
            <!-- <ng-template ng-loadingtext-tmp>
                    <span>Loading plants...</span>
                </ng-template> -->
          </ng-select>
          <!-- The loading spinner is now built into ng-select ([loading]) -->
          <!-- The text hint can remain -->
          <small class="form-text text-muted" *ngIf="!isLoadingPlants && plantOptions.length > 0">Type to search or
            select multiple.</small>
        </div>
        <div class="col-12">
          <label class="form-label">&nbsp;</label>
          <button id="searchButton" class="btn adani-btn w-100 mt-2">
            <i class="bi bi-search"></i> Search
          </button>
        </div>
      </div>
    </form>
  </div>
</app-offcanvas>