/* custom-table.component.css */

/* Basic Card Styling */
.card {
    background-color: #fff;
    border-radius: 10px; /* Match el-table style */
    border: 1px solid rgba(0, 0, 0, 0.125);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); /* Match el-table style */
    display: flex;
    flex-direction: column;
    min-width: 0;
    word-wrap: break-word;
    margin-top: 10px; /* Added margin top for spacing */
  }
  
  .card-body{
    height: 70vh;
    overflow: auto;
}
  .card-header {
    padding: 0.75rem 1.25rem;
    margin-bottom: 0;
    background-color: rgba(0, 0, 0, 0.03);
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
    border-top-left-radius: 10px; /* Match card border-radius */
    border-top-right-radius: 10px; /* Match card border-radius */
  }
  
  .card-body {
    padding: 1.25rem;
  }
  
  
  /* Custom Table Styling */
  .custom-table {
    width: 100%;
    border-collapse: collapse;
    border-radius: 0;
    margin-top: 0;
    box-shadow: none;
    overflow: visible;
    border: 1px solid #ddd; /* Table outer border */
  }
  
  .custom-table th,
  .custom-table td {
    padding: 10px 12px; /* Adjusted padding */
    font-size: 12px;
    border: 1px solid #ddd; /* Added cell border for visibility */
    text-align: center; /* Match el-table align="center" */
    vertical-align: middle;
  }
  
  .custom-table thead th {
    color: #333; /* Match header-cell-style color */
    background-color: #f0f0f0; /* Match header-cell-style backgroundColor */
    border-bottom: 1px solid #ddd; /* Separator for header rows, adjust as needed */
  }
  
  /* Specific styling for the first header row THs (Plant and QR Codes) */
  .custom-table thead tr:first-child th {
    background-color: #f5f7fa; /* Match :header-cell-style background */
    color: black;        /* Match :header-cell-style color */
  }
  
  /* Specific styling for the second header row THs (QR counts) */
  .custom-table thead tr:nth-child(2) th {
    background-color: #f5f7fa; /* Match :header-cell-style background */
    color: black;        /* Match :header-cell-style color */
  }
  
  
  .custom-table tbody tr {
    border-bottom: 1px solid #ddd; /* Row borders */
  }
  
  .custom-table tbody tr:last-child {
    border-bottom: none; /* Remove border from last row */
  }
  
  
  /* Responsive Table (optional) */
  .table-responsive {
    overflow-x: auto;
  }