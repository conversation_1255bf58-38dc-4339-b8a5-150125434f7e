import { CommonModule } from '@angular/common';
import { Component, OnInit, ViewChild, inject } from '@angular/core'; // Removed ChangeDetectorRef, OnDestroy, ElementRef, AfterViewInit if not used
import { FormsModule, NgForm } from '@angular/forms'; // Keep FormsModule
// import { Subject } from 'rxjs'; // Removed if not used
import { ReportManagementService } from '../../../../services/report-management/report-management.service';
import { PaginationComponent } from "../../../../shared/pagination/pagination.component";
import { OffcanvasComponent } from "../../../../shared/offcanvas/offcanvas.component";
import { NgSelectModule } from '@ng-select/ng-select'; // Keep NgSelectModule
import { PlantManagementService } from '../../../../services/plant-management/plant-management.service'; // Import Plant service
import { createAxiosConfig } from '../../../../core/utilities/axios-param-config'; // If you use this helper
import { ClusterService } from '../../../../services/master-management/cluster/cluster.service'; // Keep if used elsewhere
import { ToastMessageComponent } from '../../../../shared/toast-message/toast-message.component'; // Adjust path if needed
import { NgbDropdownModule } from '@ng-bootstrap/ng-bootstrap'; // For dropdown button
import * as XLSX from 'xlsx'; // For Excel generation
// import { Modal } from 'bootstrap'; // Removed if not used

// Define ROLES constant
const ROLES = {
    SUPER_ADMIN: 'super_admin',
    PLANT_ADMIN: 'plant_admin',
};

// --- Interfaces (adjust properties based on your actual data) ---
interface Cluster {
  id: number;
  name: string;
}

interface Plant {
  id: number;
  name: string;
  clusterId?: number;
}

interface RAGReportItem {
  name: string; // Plant Name
  redZoneCount?: number;
  yellowZoneCount?: number;
  greenZoneCount?: number;
  plantId?: number;
}

@Component({
  selector: 'app-plantwise-report',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    NgSelectModule,
    PaginationComponent,
    OffcanvasComponent,
    NgbDropdownModule,
    ToastMessageComponent
  ],
  templateUrl: './plantwise-report.component.html',
  styleUrl: './plantwise-report.component.scss'
})
export class PlantwiseReportComponent implements OnInit {
  @ViewChild(ToastMessageComponent) toast!: ToastMessageComponent;
  isEditUserModalOpen: boolean = false; // Assuming this is for the filter offcanvas
  list: RAGReportItem[] = []; // Use specific type
  currentPage = 1;
  itemsPerPage = 20;
  totalItems = 0;

  plantsList: Plant[] = []; // This will be filtered by role for the dropdown
  isLoadingPlants: boolean = false;
  isLoadingReport: boolean = false;
  isDownloadingExcel = false;
  downloadType: 'current' | 'all' | null = null;

  selectedPlantIds: number[] = [];
  currentFilterPlantIds: number[] = [];

  // --- Role-Based Access Control Properties ---
  currentUserRole: string = '';
  loggedInAdminId: number | null = null;
  loggedInPlantIds: number[] = [];

  // --- Inject Services ---
  private reportService = inject(ReportManagementService);
  private plantService = inject(PlantManagementService);
  // ClusterService is not used directly in methods, remove inject if not needed elsewhere
  // private clusterService = inject(ClusterService);

  constructor(
    // Keep constructor empty if using inject()
  ) { }

  ngOnInit() {
    this.setCurrentUserRoleAndDetailsById(); // Set role first
    // Fetch plants based on role for the filter dropdown
    this.fetchPlantsForFilter();
    // Fetch the initial report (will apply role filter)
    this.getReport(this.currentPage);
  }

   private setCurrentUserRoleAndDetailsById(): void {
        try {
            const userString = localStorage.getItem('user');
            if (!userString || userString.trim() === "" || ['null', 'undefined', '""', '" "'].includes(userString.trim().toLowerCase())) {
                this.currentUserRole = ''; this.loggedInAdminId = null; this.loggedInPlantIds = [];
                this.toast?.showErrorToast("User session invalid."); return;
            }
            const currentUser = JSON.parse(userString);
            this.loggedInAdminId = currentUser?.id ?? null;
            this.loggedInPlantIds = (Array.isArray(currentUser?.plantIds) && currentUser.plantIds.length > 0)
                ? currentUser.plantIds.map((id: any) => Number(id)).filter((id: number) => !isNaN(id)) : [];
            const roleId = currentUser?.adminsRoleId;
            if (roleId === 1 || roleId === 6) { this.currentUserRole = ROLES.SUPER_ADMIN; }
            else if (roleId === 2) {
                this.currentUserRole = ROLES.PLANT_ADMIN;
                if (this.loggedInPlantIds.length === 0) { this.toast?.showErrorToast("Plant Admin has no assigned plants."); }
            } else { this.currentUserRole = ''; this.toast?.showErrorToast("Invalid user role."); }
            console.log(`Role: ${this.currentUserRole}, UserID: ${this.loggedInAdminId}, Plants: [${this.loggedInPlantIds.join(', ')}]`);
        } catch (error) {
            console.error("Error parsing user data:", error);
            this.currentUserRole = ''; this.loggedInAdminId = null; this.loggedInPlantIds = [];
            this.toast?.showErrorToast("Error reading user session.");
        }
    }

  getCurrentListData(): RAGReportItem[] | undefined {
    return this.list;
  }

  async fetchAllFilteredRAGReports(): Promise<RAGReportItem[] | null> {
    let plantIdsToSend: number[] | null = null;

    if (this.currentUserRole === ROLES.PLANT_ADMIN) {
        if (this.loggedInPlantIds.length > 0) {
            if (this.currentFilterPlantIds.length > 0) {
                 plantIdsToSend = this.currentFilterPlantIds.filter(id => this.loggedInPlantIds.includes(id));
                 if(plantIdsToSend.length === 0) { plantIdsToSend = this.loggedInPlantIds; }
            } else { plantIdsToSend = this.loggedInPlantIds; }
        } else { return []; } // Return empty if no plants assigned
    } else if (this.currentUserRole === ROLES.SUPER_ADMIN) {
        plantIdsToSend = this.currentFilterPlantIds.length > 0 ? this.currentFilterPlantIds : null;
    } else { return null; }

    const payload = {
        "plantIds": plantIdsToSend,
        "pageSize": 10000,
        "pageIndex": 1
    };
    const cleanedPayload: { [key: string]: any } = {};
    for (const key in payload) {
        const value = (payload as any)[key];
        if (value !== null && value !== undefined) {
            if (Array.isArray(value) && value.length > 0) cleanedPayload[key] = value;
            else if (!Array.isArray(value)) cleanedPayload[key] = value;
        }
    }
    console.log('Request Payload for ALL RAG Report Data:', cleanedPayload);
    this.isLoadingReport = true; // Indicate loading
    try {
        const res = await this.reportService.ragPlantWiseReport(cleanedPayload);
        return res?.data ?? [];
    } catch (error) {
        console.error("Error fetching all RAG reports for download:", error);
        this.toast?.showErrorToast("Failed to retrieve full data for download.");
        return null;
    } finally {
        this.isLoadingReport = false; // Stop loading
    }
  }

  async downloadExcel(type: 'current' | 'all') {
    if (this.isDownloadingExcel || this.isLoadingReport) return;

    this.isDownloadingExcel = true;
    this.downloadType = type;
    this.toast?.showSuccessToast(`Preparing download for ${type === 'current' ? 'current page' : 'all filtered'} reports...`);

    let dataToExport: RAGReportItem[] | null = null;

    try {
        if (type === 'all') { dataToExport = await this.fetchAllFilteredRAGReports(); }
        else { dataToExport = this.getCurrentListData() ?? null; if (dataToExport === undefined) { dataToExport = null; } }

        if (dataToExport === null) { this.toast?.showErrorToast("Failed to retrieve data for download."); return; }
        if (!dataToExport || dataToExport.length === 0) { this.toast?.showErrorToast(`No reports available to download.`); return; }

        console.log(`Fetched ${dataToExport.length} RAG reports for Excel export (${type}).`);

        const dataForExcel = dataToExport.map(item => ({
            'Plant': item.name, 'Red QR Count': item.redZoneCount ?? 0, 'Yellow QR Count': item.yellowZoneCount ?? 0, 'Green QR Count': item.greenZoneCount ?? 0,
        }));

        const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(dataForExcel);
        const wb: XLSX.WorkBook = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(wb, ws, 'PlantwiseRAGReport');

        const dateStr = new Date().toISOString().slice(0, 10);
        const typeStr = type === 'current' ? `Page${this.currentPage}` : 'All';
        const fileName = `PlantwiseRAGReport_${typeStr}_${dateStr}.xlsx`;

        XLSX.writeFile(wb, fileName);
        this.toast?.showSuccessToast(`Excel file download started (${type}).`);

    } catch (error) {
        console.error(`Error generating Excel file (${type}):`, error);
        this.toast?.showErrorToast(`An error occurred while generating the Excel file (${type}).`);
    } finally {
        this.isDownloadingExcel = false; this.downloadType = null;
    }
  }

  closeModal() { this.isEditUserModalOpen = false; } // Keep for filter modal

  openFilterModal() {
     this.selectedPlantIds = [...this.currentFilterPlantIds];
     // Plants list (`this.plantsList`) is already filtered by role
     this.isEditUserModalOpen = true; // Open the filter offcanvas
  }

  async fetchPlantsForFilter() {
    this.isLoadingPlants = true;
    const data = { page: 1, limit: 1000, sort: 'name,ASC', filter: ['isDeleted||eq||false', 'enabled||eq||true'] };
    const param = createAxiosConfig(data);
    let allEnabledPlants: Plant[] = [];
    try {
      const plants = await this.plantService.getPlants(param);
      allEnabledPlants = plants.data?.map((p: any) => ({ id: p.id, name: p.name })) || [];
    } catch (error) {
      console.error("Error fetching plants:", error);
      this.plantsList = [];
      this.toast?.showErrorToast("Failed to load plants for filter.");
      this.isLoadingPlants = false;
      return;
    }

    // Filter based on role
    if (this.currentUserRole === ROLES.PLANT_ADMIN && this.loggedInPlantIds.length > 0) {
        this.plantsList = allEnabledPlants.filter(plant => this.loggedInPlantIds.includes(plant.id));
    } else if (this.currentUserRole === ROLES.SUPER_ADMIN) {
        this.plantsList = allEnabledPlants;
    } else {
        this.plantsList = []; // No access or no plants assigned
    }
    this.isLoadingPlants = false;
  }

  async getReport(page: number) {
    this.isLoadingReport = true;
    this.currentPage = page;
    this.list = []; // Clear list

    let plantIdsToSend: number[] | null = null;

    // Determine plant IDs based on role and *applied* filter
    if (this.currentUserRole === ROLES.PLANT_ADMIN) {
        if (this.loggedInPlantIds.length > 0) {
            if (this.currentFilterPlantIds.length > 0) {
                 plantIdsToSend = this.currentFilterPlantIds.filter(id => this.loggedInPlantIds.includes(id));
                 if(plantIdsToSend.length === 0) { plantIdsToSend = this.loggedInPlantIds; }
            } else { plantIdsToSend = this.loggedInPlantIds; }
        } else {
            console.warn("Plant Admin has no plants, skipping report fetch.");
            this.totalItems = 0; this.isLoadingReport = false; return;
        }
    } else if (this.currentUserRole === ROLES.SUPER_ADMIN) {
        plantIdsToSend = this.currentFilterPlantIds.length > 0 ? this.currentFilterPlantIds : null;
    } else {
         console.error("Unknown user role, cannot fetch report.");
         this.totalItems = 0; this.isLoadingReport = false; return;
    }

    const payload = {
      "plantIds": plantIdsToSend,
      "pageSize": this.itemsPerPage,
      "pageIndex": this.currentPage
    };
    const cleanedPayload: { [key: string]: any } = {};
    for (const key in payload) {
        const value = (payload as any)[key];
        if (value !== null && value !== undefined) {
            if (Array.isArray(value) && value.length > 0) cleanedPayload[key] = value;
            else if (!Array.isArray(value)) cleanedPayload[key] = value;
        }
    }

    console.log("Fetching report with payload:", cleanedPayload);

    try {
      const res = await this.reportService.ragPlantWiseReport(cleanedPayload);
      this.totalItems = res?.total ?? 0;
      this.list = res?.data ?? [];
      console.log("Report data received:", this.list);
    } catch (error) {
      console.error("Error fetching report:", error);
      this.list = []; this.totalItems = 0;
      this.toast?.showErrorToast("Failed to load RAG report.");
    } finally {
      this.isLoadingReport = false;
    }
  }

  applyFilter() {
    // Apply the selection from the modal to the active filter
    this.currentFilterPlantIds = [...this.selectedPlantIds];
    this.currentPage = 1;
    this.getReport(this.currentPage);
    this.closeModal();
  }

  onPageChange(page: number) {
    if (this.currentPage === page || this.isLoadingReport) return;
    this.getReport(page);
  }

  getBadgeClass(status: string): string {
    if (!status) return 'bg-light text-dark';
    switch (status.toLowerCase()) {
      case 'red': return 'bg-danger';
      case 'yellow': return 'bg-warning text-dark';
      case 'green': return 'bg-success';
      default: return 'bg-secondary';
    }
  }
}