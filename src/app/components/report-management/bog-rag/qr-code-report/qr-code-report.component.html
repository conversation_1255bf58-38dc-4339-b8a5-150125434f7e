<app-toast-message></app-toast-message>
<div class="card custom-card">
    <div class="card-header d-flex align-items-center justify-content-between">
      <div>
        <h6 class="mb-0">QR Code report</h6>
      </div>
      <div class="d-flex align-items-center">
           <!-- *** REPLACE the existing simple Download button with this Dropdown *** -->
           <div ngbDropdown class="d-inline-block me-2">
            <button type="button" class="btn btn-sm adani-btn dropdown-toggle" id="downloadQRCodeExcelDropdown" ngbDropdownToggle
                [disabled]="isDownloadingExcel || isLoadingReport">
                <span *ngIf="!isDownloadingExcel">
                    <i class="bi bi-file-earmark-excel me-1"></i> Download Excel
                </span>
                <span *ngIf="isDownloadingExcel">
                    <span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
                    Downloading {{ downloadType === 'current' ? 'Page' : (downloadType === 'all' ? 'All' : '') }}...
                </span>
            </button>
            <ul ngbDropdownMenu aria-labelledby="downloadQRCodeExcelDropdown">
                <li>
                    <button ngbDropdownItem (click)="downloadExcel('current')" [disabled]="isDownloadingExcel || isLoadingReport || (list?.length ?? 0) === 0">
                        <i class="bi bi-download me-1"></i> Download Current Page ({{ list?.length ?? 0 }})
                    </button>
                </li>
                <li>
                    <button ngbDropdownItem (click)="downloadExcel('all')" [disabled]="isDownloadingExcel || isLoadingReport || totalItems === 0">
                        <i class="bi bi-cloud-download me-1"></i> Download All Filtered ({{ totalItems }})
                    </button>
                </li>
            </ul>
        </div>
        <!-- *** END REPLACEMENT *** -->
          <img src="../../../assets/svg/filter.svg" class="filter-button ms-3" (click)="openFilterModal()" alt="" style="width: 35px;" />
      </div>
      </div>
    <div class="card-body">
      <div class="table-responsive">
        <table class="table custom-table">
          <thead class="table-header">
            <tr>
              <th scope="col">Plant</th>
              <th scope="col">RAG</th>
              <th scope="col">Zone Area</th>
              <th scope="col">Last Scanned On</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngIf="isLoadingReport">
              <td colspan="4" class="text-center p-4"> <!-- Colspan = 4 -->
                <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                Loading RAG report...
              </td>
            </tr>
            <tr *ngIf="!isLoadingReport && list.length === 0">
              <td colspan="4" class="text-center p-4 text-muted"> <!-- Colspan = 4 -->
                No RAG report data found matching the criteria.
              </td>
            </tr>
            <tr *ngFor="let item of list">
              <td>{{ item.name }}</td>
              <td>{{ item.RAG }}</td>
              <td>{{ item.zoneArea }}</td>
              <td>{{ formatDate(item.lastScanDate) }}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
    <div class="card-footer text-muted text-center">
      <app-pagination [currentPage]="currentPage" [totalItems]="totalItems" [itemsPerPage]="itemsPerPage"
      (pageChange)="onPageChange($event)"></app-pagination>
    </div>
  </div>
<!-- Filter Offcanvas -->
<app-offcanvas [title]="'Filter QR Code Report'" *ngIf="isEditUserModalOpen" (onClickCross)="closeModal()">
  <div class="container">
    <div class="row g-3"> <!-- Increased gap slightly -->

      <!-- Cluster Selection -->
      <div class="col-12">
          <label for="clusterSelectNg" class="form-label">Cluster</label>
          <ng-select
              id="clusterSelectNg"
              [items]="clustersList"
              bindLabel="title"
              bindValue="id"                 
              [(ngModel)]="selectedClusterId"
              (change)="onClusterChange($event)"  
              [loading]="isLoadingClusters"
              [searchable]="true"
              [clearable]="true"              
              placeholder="Select Cluster"
              >
          </ng-select>
           <div *ngIf="!isLoadingClusters && clustersList.length === 0" class="text-muted small mt-1">
                Could not load clusters.
           </div>
      </div>

      <!-- Plant Selection (Dependent on Cluster) -->
      <div class="col-12">
        <label for="plantSelectNg" class="form-label">Plant(s)</label>
         <ng-select
              id="plantSelectNg"
              [items]="availablePlantsForFilter"  
              bindLabel="name"
              bindValue="id"
              placeholder="Select Plant(s)"
              [multiple]="true"
              [(ngModel)]="selectedPlantIds"    
              [loading]="isLoadingPlants"
              [searchable]="true"
              [closeOnSelect]="false"
              [disabled]="!selectedClusterId || isLoadingClusters || isLoadingPlants">
         </ng-select>
          <div *ngIf="selectedClusterId && !isLoadingPlants && availablePlantsForFilter.length === 0" class="text-muted small mt-1">
                No plants found for the selected cluster or failed to load.
           </div>
           <div *ngIf="!selectedClusterId" class="text-muted small mt-1">
               Please select a cluster first.
           </div>
      </div>

      <!-- Search Button -->
      <div class="col-12 mt-3">
        <button type="button" (click)="applyFilter()" class="btn adani-btn w-100" [disabled]="isLoadingClusters || isLoadingPlants">
          <i class="bi bi-search"></i> Search
        </button>
      </div>

    </div>
  </div>
</app-offcanvas>
