import { CommonModule } from '@angular/common';
import { Component, OnInit, ViewChild, inject } from '@angular/core'; // Removed unused imports like ChangeDetectorRef, OnDestroy, ElementRef, AfterViewInit
import { FormsModule } from '@angular/forms'; // Import FormsModule
import { ReportManagementService } from '../../../../services/report-management/report-management.service';
import { PaginationComponent } from "../../../../shared/pagination/pagination.component";
import { OffcanvasComponent } from "../../../../shared/offcanvas/offcanvas.component";
import { NgSelectModule } from '@ng-select/ng-select'; // Import NgSelectModule
import { PlantManagementService } from '../../../../services/plant-management/plant-management.service'; // Import Plant service
import { createAxiosConfig } from '../../../../core/utilities/axios-param-config'; // If you use this helper
import { ClusterService } from '../../../../services/master-management/cluster/cluster.service';
import { ToastMessageComponent } from '../../../../shared/toast-message/toast-message.component'; // Adjust path if needed
import { NgbDropdownModule } from '@ng-bootstrap/ng-bootstrap'; // For dropdown button
import * as XLSX from 'xlsx'; // For Excel generation
// Removed Modal import as it's not used

// Define ROLES constant
const ROLES = {
    SUPER_ADMIN: 'super_admin',
    PLANT_ADMIN: 'plant_admin',
};

// Interfaces (adjust properties based on your actual data)
interface Cluster {
  id: number;
  name: string; // Assuming name, adjust if it's title
}

interface Plant {
  id: number;
  name: string;
  clusterId?: number; // Optional: If plant data includes cluster info
}

interface QRCodeReportItem {
  name: string; // Plant Name
  RAG: string; // Assuming RAG is a string like 'Red', 'Yellow', 'Green'
  zoneArea: string;
  lastScanDate: string | Date; // Keep flexible
  plantId?: number; // Added for potential use
}

@Component({
  selector: 'app-qr-code-report',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    NgSelectModule,
    PaginationComponent,
    OffcanvasComponent,
    NgbDropdownModule,
    ToastMessageComponent
  ],
  templateUrl: './qr-code-report.component.html',
  styleUrl: './qr-code-report.component.scss'
})
export class QrCodeReportComponent implements OnInit {
  @ViewChild(ToastMessageComponent) toast!: ToastMessageComponent;
  isEditUserModalOpen: boolean = false; // Use for filter offcanvas
  list: QRCodeReportItem[] = []; // Use specific type
  currentPage = 1;
  itemsPerPage = 20;
  totalItems = 0;
  isLoadingReport: boolean = false;

  clustersList: Cluster[] = [];
  availablePlantsForFilter: Plant[] = []; // Filtered plants for the dropdown
  isLoadingClusters: boolean = false;
  isLoadingPlants: boolean = false;
  isDownloadingExcel = false;
  downloadType: 'current' | 'all' | null = null;

  selectedClusterId: number | null = null;
  selectedPlantIds: number[] = [];
  currentFilterPlantIds: number[] = [];

  // --- Role-Based Access Control Properties ---
  currentUserRole: string = '';
  loggedInAdminId: number | null = null;
  loggedInPlantIds: number[] = [];

  // --- Inject Services ---
  private reportService = inject(ReportManagementService);
  private plantService = inject(PlantManagementService);
  private clusterService = inject(ClusterService);

  constructor(
    // Keep constructor empty if using inject()
  ) { }

  ngOnInit() {
    this.setCurrentUserRoleAndDetailsById(); // Set role first
    // Fetch clusters (needed for filter regardless of role initially)
    this.fetchClustersForFilter();
    // Fetch plants based on role for the filter dropdown
    this.fetchPlantsForFilter(null); // Fetch initial plants (all accessible)
    // Fetch the initial report (will apply role filter)
    this.getReport(this.currentPage);
  }

   private setCurrentUserRoleAndDetailsById(): void {
        try {
            const userString = localStorage.getItem('user');
            if (!userString || userString.trim() === "" || ['null', 'undefined', '""', '" "'].includes(userString.trim().toLowerCase())) {
                this.currentUserRole = ''; this.loggedInAdminId = null; this.loggedInPlantIds = [];
                this.toast?.showErrorToast("User session invalid."); return;
            }
            const currentUser = JSON.parse(userString);
            this.loggedInAdminId = currentUser?.id ?? null;
            this.loggedInPlantIds = (Array.isArray(currentUser?.plantIds) && currentUser.plantIds.length > 0)
                ? currentUser.plantIds.map((id: any) => Number(id)).filter((id: number) => !isNaN(id)) : [];
            const roleId = currentUser?.adminsRoleId;
            if (roleId === 1 || roleId === 6) { this.currentUserRole = ROLES.SUPER_ADMIN; }
            else if (roleId === 2) {
                this.currentUserRole = ROLES.PLANT_ADMIN;
                if (this.loggedInPlantIds.length === 0) { this.toast?.showErrorToast("Plant Admin has no assigned plants."); }
            } else { this.currentUserRole = ''; this.toast?.showErrorToast("Invalid user role."); }
            console.log(`Role: ${this.currentUserRole}, UserID: ${this.loggedInAdminId}, Plants: [${this.loggedInPlantIds.join(', ')}]`);
        } catch (error) {
            console.error("Error parsing user data:", error);
            this.currentUserRole = ''; this.loggedInAdminId = null; this.loggedInPlantIds = [];
            this.toast?.showErrorToast("Error reading user session.");
        }
    }

  getCurrentListData(): QRCodeReportItem[] | undefined {
    return this.list;
  }

  async fetchAllFilteredQRCodeReports(): Promise<QRCodeReportItem[] | null> {
    let plantIdsToSend: number[] | null = null;

    // Apply Role-Based Plant Filter Logic
    if (this.currentUserRole === ROLES.PLANT_ADMIN) {
        if (this.loggedInPlantIds.length > 0) {
            if (this.currentFilterPlantIds.length > 0) {
                 plantIdsToSend = this.currentFilterPlantIds.filter(id => this.loggedInPlantIds.includes(id));
                 if(plantIdsToSend.length === 0) { plantIdsToSend = this.loggedInPlantIds; }
            } else { plantIdsToSend = this.loggedInPlantIds; }
        } else { return []; }
    } else if (this.currentUserRole === ROLES.SUPER_ADMIN) {
        plantIdsToSend = this.currentFilterPlantIds.length > 0 ? this.currentFilterPlantIds : null;
    } else { return null; }

    const payload = {
        "plantIds": plantIdsToSend,
        "pageSize": 10000,
        "pageIndex": 1
    };
    const cleanedPayload: { [key: string]: any } = {};
    for (const key in payload) {
        const value = (payload as any)[key];
        if (value !== null && value !== undefined) {
            if (Array.isArray(value) && value.length > 0) cleanedPayload[key] = value;
            else if (!Array.isArray(value)) cleanedPayload[key] = value;
        }
    }
    console.log('Request Payload for ALL QR Code Report Data:', cleanedPayload);
    this.isLoadingReport = true;
    try {
        const res = await this.reportService.ragQRCodeReport(cleanedPayload);
        return res?.data ?? [];
    } catch (error) {
        console.error("Error fetching all QR code reports for download:", error);
        this.toast?.showErrorToast("Failed to retrieve full data for download.");
        return null;
    } finally {
        this.isLoadingReport = false;
    }
  }

  async downloadExcel(type: 'current' | 'all') {
    if (this.isDownloadingExcel || this.isLoadingReport) return;

    this.isDownloadingExcel = true;
    this.downloadType = type;
    this.toast?.showSuccessToast(`Preparing download for ${type === 'current' ? 'current page' : 'all filtered'} reports...`);

    let dataToExport: QRCodeReportItem[] | null = null;

    try {
        if (type === 'all') { dataToExport = await this.fetchAllFilteredQRCodeReports(); }
        else { dataToExport = this.getCurrentListData() ?? null; if (dataToExport === undefined) { dataToExport = null; } }

        if (dataToExport === null) { this.toast?.showErrorToast("Failed to retrieve data for download."); return; }
        if (!dataToExport || dataToExport.length === 0) { this.toast?.showErrorToast(`No reports available to download.`); return; }

        console.log(`Fetched ${dataToExport.length} QR code reports for Excel export (${type}).`);

        const dataForExcel = dataToExport.map(item => ({
            'Plant': item.name, 'RAG Status': item.RAG, 'Zone Area': item.zoneArea,
            'Last Scanned On': this.formatDate(item.lastScanDate),
        }));

        const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(dataForExcel);
        const wb: XLSX.WorkBook = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(wb, ws, 'QRCodeReport');

        const dateStr = new Date().toISOString().slice(0, 10);
        const typeStr = type === 'current' ? `Page${this.currentPage}` : 'All';
        const fileName = `QRCodeReport_${typeStr}_${dateStr}.xlsx`;

        XLSX.writeFile(wb, fileName);
        this.toast?.showSuccessToast(`Excel file download started (${type}).`);

    } catch (error) {
        console.error(`Error generating Excel file (${type}):`, error);
        this.toast?.showErrorToast(`An error occurred while generating the Excel file (${type}).`);
    } finally {
        this.isDownloadingExcel = false; this.downloadType = null;
    }
  }

  closeModal() { this.isEditUserModalOpen = false; }

  openFilterModal() {
     this.selectedPlantIds = [...this.currentFilterPlantIds];
     // Re-fetch plants based on the current cluster and user role
     this.fetchPlantsForFilter(this.selectedClusterId, false);
     this.isEditUserModalOpen = true;
  }

  async fetchClustersForFilter() {
    this.isLoadingClusters = true;
    try {
      const filter = ['isDeleted||eq||false', 'enabled||eq||true'];
      const params = createAxiosConfig({ page: 1, limit: 1000, sort: 'title,ASC', filter: filter });
      const res = await this.clusterService.getCluster(params);
      // No role-based filtering needed for clusters themselves usually
      this.clustersList = res?.data ?? [];
    } catch (error) {
      console.error("Error fetching clusters:", error);
      this.clustersList = [];
    } finally {
      this.isLoadingClusters = false;
    }
  }

  onClusterChange(selectedCluster: Cluster | null): void {
    this.selectedPlantIds = [];
    this.availablePlantsForFilter = [];
    const clusterId = selectedCluster?.id ?? null;
    this.selectedClusterId = clusterId;
    this.fetchPlantsForFilter(clusterId, true);
  }

  async fetchPlantsForFilter(clusterId: number | null, resetSelection: boolean = true) {
    this.isLoadingPlants = true;
    if (resetSelection) this.selectedPlantIds = [];
    this.availablePlantsForFilter = [];

    const plantFilters = ['isDeleted||eq||false', 'enabled||eq||true'];
    if (clusterId) { plantFilters.push(`clusterId||eq||${clusterId}`); }

    // Apply role-based filtering for the dropdown options
    if (this.currentUserRole === ROLES.PLANT_ADMIN && this.loggedInPlantIds.length > 0) {
        plantFilters.push(`id||$in||${this.loggedInPlantIds.join(',')}`);
    } else if (this.currentUserRole === ROLES.PLANT_ADMIN && this.loggedInPlantIds.length === 0) {
        this.isLoadingPlants = false; return; // No plants to show
    }
    // Super Admin sees all (optionally filtered by cluster)

    try {
      const plantParams = createAxiosConfig({ page: 1, limit: 1000, sort: 'name,ASC', filter: plantFilters });
      const res = await this.plantService.getPlants(plantParams);
      this.availablePlantsForFilter = res?.data ?? [];

      if (!resetSelection) {
           const validPlantIds = this.availablePlantsForFilter.map(p => p.id);
           this.selectedPlantIds = this.selectedPlantIds.filter(id => validPlantIds.includes(id));
      }
    } catch (error) {
      console.error(`Error fetching plants for cluster ${clusterId}:`, error);
      this.availablePlantsForFilter = []; this.selectedPlantIds = [];
    } finally {
      this.isLoadingPlants = false;
    }
  }

  async getReport(page: number) {
    this.isLoadingReport = true;
    this.currentPage = page;
    this.list = [];

    let plantIdsToSend: number[] | null = null;

    // Determine plant IDs based on role and *applied* filter
    if (this.currentUserRole === ROLES.PLANT_ADMIN) {
        if (this.loggedInPlantIds.length > 0) {
            if (this.currentFilterPlantIds.length > 0) {
                 plantIdsToSend = this.currentFilterPlantIds.filter(id => this.loggedInPlantIds.includes(id));
                 if(plantIdsToSend.length === 0) { plantIdsToSend = this.loggedInPlantIds; }
            } else { plantIdsToSend = this.loggedInPlantIds; }
        } else {
            console.warn("Plant Admin has no plants, skipping report fetch.");
            this.totalItems = 0; this.isLoadingReport = false; return;
        }
    } else if (this.currentUserRole === ROLES.SUPER_ADMIN) {
        plantIdsToSend = this.currentFilterPlantIds.length > 0 ? this.currentFilterPlantIds : null;
    } else {
         console.error("Unknown user role, cannot fetch report.");
         this.totalItems = 0; this.isLoadingReport = false; return;
    }

    const payload = {
      "plantIds": plantIdsToSend,
      "pageSize": this.itemsPerPage,
      "pageIndex": this.currentPage
    };
    const cleanedPayload: { [key: string]: any } = {};
    for (const key in payload) {
        const value = (payload as any)[key];
        if (value !== null && value !== undefined) {
            if (Array.isArray(value) && value.length > 0) cleanedPayload[key] = value;
            else if (!Array.isArray(value)) cleanedPayload[key] = value;
        }
    }

    console.log("Fetching QR Code report with payload:", cleanedPayload);

    try {
      const res = await this.reportService.ragQRCodeReport(cleanedPayload);
      this.totalItems = res?.total ?? 0;
      this.list = res?.data ?? [];
      console.log("QR Code report data received:", this.list);
    } catch (error) {
      console.error("Error fetching QR code report:", error);
      this.list = []; this.totalItems = 0;
      this.toast?.showErrorToast("Failed to load RAG QR Code report.");
    } finally {
      this.isLoadingReport = false;
    }
  }

  applyFilter() {
    // Apply the selection from the modal to the active filter
    this.currentFilterPlantIds = [...this.selectedPlantIds];
    this.currentPage = 1;
    this.getReport(this.currentPage);
    this.closeModal();
  }

  onPageChange(page: number) {
    if (this.currentPage === page || this.isLoadingReport) return;
    this.getReport(page);
  }

  getBadgeClass(status: string): string {
    if (!status) return 'bg-light text-dark';
    switch (status.toLowerCase()) {
      case 'red': return 'bg-danger';
      case 'yellow': return 'bg-warning text-dark';
      case 'green': return 'bg-success';
      default: return 'bg-secondary';
    }
  }

  formatDate(dateValue: string | Date | null | undefined): string {
    if (!dateValue) return 'N/A';
    try {
      const date = new Date(dateValue);
      const options: Intl.DateTimeFormatOptions = {
        day: '2-digit',
        month: 'short',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: true
      };
      return date.toLocaleDateString('en-GB', options).replace(',', ',');
    } catch (error) {
      console.error('Error formatting date:', error);
      return 'Invalid Date';
    }
  }
}