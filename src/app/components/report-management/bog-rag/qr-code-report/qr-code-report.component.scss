/* custom-table.component.css */

/* Basic Card Styling */
.card {
    background-color: #fff;
    border-radius: 10px; /* Match el-table style */
    border: 1px solid rgba(0, 0, 0, 0.125);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); /* Match el-table style */
    display: flex;
    flex-direction: column;
    min-width: 0;
    word-wrap: break-word;
    margin-top: 10px; /* Added margin top for spacing */
  }
  
  .card-body{
    height: 70vh;
    overflow: auto;
}
  
  .card-header {
    padding: 0.75rem 1.25rem;
    margin-bottom: 0;
    background-color: rgba(0, 0, 0, 0.03);
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
    border-top-left-radius: 10px; /* Match card border-radius */
    border-top-right-radius: 10px; /* Match card border-radius */
  }
  
  .card-body {
    padding: 1.25rem;
  }
  
  
  /* Custom Table Styling */
  .custom-table {
    width: 100%;
    border-collapse: collapse;
    border-radius: 10px; /* Match el-table style */
    margin-top: 10px; /* Match el-table style */
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); /* Match el-table style */
    overflow: hidden; /* To ensure border-radius and box-shadow work together */
    border: 1px solid #ddd; /* Table border, if you want one */
  }
  
  .custom-table th,
  .custom-table td {
    padding: 10px 12px; /* Adjusted padding */
    text-align: center; /* Match el-table align="center" */
    font-size: 12px;
    vertical-align: middle;
    border-bottom: 1px solid #ddd; /* Row separator border */
    border-right: 1px solid #ddd;  /* Column separator border */
  }
  
  .custom-table th {
    color: black; /* Match header-cell-style color */
    background-color: #f5f7fa; /* Match header-cell-style background */
    font-weight: bold; /* Match header-cell-style fontWeight */
  }
  
  /* Remove right border from the last column cells */
  .custom-table tr td:last-child,
  .custom-table tr th:last-child {
    border-right: none;
  }
  /* Remove bottom border from the last row cells */
  .custom-table tbody tr:last-child td,
  .custom-table tbody tr:last-child th {
    border-bottom: none;
  }
  
  /* Responsive Table (optional) */
  .table-responsive {
    overflow-x: auto;
  }