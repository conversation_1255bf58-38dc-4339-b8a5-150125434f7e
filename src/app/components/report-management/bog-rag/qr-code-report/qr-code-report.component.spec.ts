import { ComponentFixture, TestBed } from '@angular/core/testing';

import { QrCodeReportComponent } from './qr-code-report.component';

describe('QrCodeReportComponent', () => {
  let component: QrCodeReportComponent;
  let fixture: ComponentFixture<QrCodeReportComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [QrCodeReportComponent]
    })
    .compileComponents();

    fixture = TestBed.createComponent(QrCodeReportComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
