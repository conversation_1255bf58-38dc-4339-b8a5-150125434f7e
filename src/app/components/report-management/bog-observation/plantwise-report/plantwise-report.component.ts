import { CommonModule } from '@angular/common';
import { Component, OnInit, ViewChild, ChangeDetectorRef, <PERSON><PERSON><PERSON><PERSON>, ElementRef, inject, AfterViewInit } from '@angular/core'; // Added inject, AfterViewInit
import { FormsModule, NgForm } from '@angular/forms';
import { Subject } from 'rxjs'; // Optional: For potential future takeUntil
import { PaginationComponent } from "../../../../shared/pagination/pagination.component";
import { OffcanvasComponent } from "../../../../shared/offcanvas/offcanvas.component";
import { NgSelectModule } from '@ng-select/ng-select'; // Import NgSelectModule
import { PlantManagementService } from '../../../../services/plant-management/plant-management.service'; // Import Plant service
import { createAxiosConfig } from '../../../../core/utilities/axios-param-config'; // If you use this helper
import { ClusterService } from '../../../../services/master-management/cluster/cluster.service';
import { ReportManagementService } from '../../../../services/report-management/report-management.service'; // Import ReportManagementService
import { ToastMessageComponent } from '../../../../shared/toast-message/toast-message.component'; // Adjust path if needed
import { NgbDropdownModule } from '@ng-bootstrap/ng-bootstrap'; // For dropdown button
import * as XLSX from 'xlsx'; // For Excel generation
import { Modal } from 'bootstrap'; // Import Modal

// Define ROLES constant
const ROLES = {
    SUPER_ADMIN: 'super_admin',
    PLANT_ADMIN: 'plant_admin',
};

// --- Interfaces & Enums ---

interface Cluster {
  id: number;
  name: string; // Assuming name, adjust if it's title
}

interface Plant {
  id: number;
  name: string;
  clusterId?: number;
}

interface PlantObservationReportItem {
  plantId: number;
  name: string; // Plant Name
  openObservation: number;
  closedObservation: number;
  fifiObservation: number; // Assuming FiFi means FindItFixIt
  safeActObservation: number;
  unsafeActObservation: number;
  unsafeConditionObservation: number;
  observationOpenFrom15Days: number;
  observationOpenFrom30Days: number;
}

@Component({
  selector: 'app-plantwise-report',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    NgSelectModule,
    PaginationComponent,
    OffcanvasComponent,
    NgbDropdownModule,
    ToastMessageComponent
    // TabComponent is not used in template, remove if not needed
  ],
  templateUrl: './plantwise-report.component.html',
  styleUrl: './plantwise-report.component.scss'
})
export class PlantwiseReportComponent implements OnInit { // Removed AfterViewInit, OnDestroy if not used
  @ViewChild(ToastMessageComponent) toast!: ToastMessageComponent;
  isEditUserModalOpen: boolean = false; // Assuming this is for the filter offcanvas
  list: PlantObservationReportItem[] = []; // Use specific type
  currentPage = 1;
  itemsPerPage = 20;
  totalItems = 0;
  isLoadingReport: boolean = false;

  clustersList: Cluster[] = [];
  availablePlantsForFilter: Plant[] = []; // Filtered plants for the dropdown
  isLoadingClusters: boolean = false;
  isLoadingPlants: boolean = false;

  startDate: string | null = null;
  endDate: string | null = null;
  selectedClusterId: number | null = null;
  selectedPlantIds: number[] = []; // Use number[] for multi-select model
  isDownloadingExcel = false;
  downloadType: 'current' | 'all' | null = null;

  currentFilterStartDate: string | null = null;
  currentFilterEndDate: string | null = null;
  currentFilterPlantIds: number[] = [];

  // --- Role-Based Access Control Properties ---
  currentUserRole: string = '';
  loggedInAdminId: number | null = null;
  loggedInPlantIds: number[] = [];

  // --- Inject Services ---
  private reportService = inject(ReportManagementService);
  private plantService = inject(PlantManagementService);
  private clusterService = inject(ClusterService);

  constructor(
    // Keep constructor empty if using inject(), or inject services here if preferred
  ) { }

  ngOnInit() {
    this.setCurrentUserRoleAndDetailsById(); // Set role first
    this.setDefaultDates();
    this.currentFilterStartDate = this.startDate;
    this.currentFilterEndDate = this.endDate;
    this.currentFilterPlantIds = []; // Initialize with no plants selected by default

    this.getReport(this.currentPage); // Load initial report (will apply role filter)
    this.fetchClustersForFilter();
    // Plants are loaded dynamically based on cluster or role
  }

   private setCurrentUserRoleAndDetailsById(): void {
        try {
            const userString = localStorage.getItem('user');
            if (!userString || userString.trim() === "" || ['null', 'undefined', '""', '" "'].includes(userString.trim().toLowerCase())) {
                this.currentUserRole = ''; this.loggedInAdminId = null; this.loggedInPlantIds = [];
                this.toast?.showErrorToast("User session invalid."); return;
            }
            const currentUser = JSON.parse(userString);
            this.loggedInAdminId = currentUser?.id ?? null;
            this.loggedInPlantIds = (Array.isArray(currentUser?.plantIds) && currentUser.plantIds.length > 0)
                ? currentUser.plantIds.map((id: any) => Number(id)).filter((id: number) => !isNaN(id)) : [];
            const roleId = currentUser?.adminsRoleId;
            if (roleId === 1 || roleId === 6) { this.currentUserRole = ROLES.SUPER_ADMIN; }
            else if (roleId === 2) {
                this.currentUserRole = ROLES.PLANT_ADMIN;
                if (this.loggedInPlantIds.length === 0) { this.toast?.showErrorToast("Plant Admin has no assigned plants."); }
            } else { this.currentUserRole = ''; this.toast?.showErrorToast("Invalid user role."); }
            console.log(`Role: ${this.currentUserRole}, UserID: ${this.loggedInAdminId}, Plants: [${this.loggedInPlantIds.join(', ')}]`);
        } catch (error) {
            console.error("Error parsing user data:", error);
            this.currentUserRole = ''; this.loggedInAdminId = null; this.loggedInPlantIds = [];
            this.toast?.showErrorToast("Error reading user session.");
        }
    }

  getCurrentListData(): PlantObservationReportItem[] | undefined {
    return this.list;
  }

  async fetchAllFilteredObservationReports(): Promise<PlantObservationReportItem[] | null> {
    let plantIdsToSend: number[] | null = null;

    // Apply Role-Based Plant Filter Logic
    if (this.currentUserRole === ROLES.PLANT_ADMIN) {
        if (this.loggedInPlantIds.length > 0) {
            // Use *currently applied* filters for export
            if (this.currentFilterPlantIds.length > 0) {
                 // Export only the selected plants (subset of their allowed plants)
                 plantIdsToSend = this.currentFilterPlantIds.filter(id => this.loggedInPlantIds.includes(id));
                 if(plantIdsToSend.length === 0) { // Handle case where selection became invalid
                      plantIdsToSend = this.loggedInPlantIds; // Fallback to all assigned? Or return error?
                      console.warn("Selected plants for export invalid for Plant Admin, exporting all assigned plants.");
                 }
            } else {
                plantIdsToSend = this.loggedInPlantIds; // Export all assigned plants if none specific selected
            }
        } else {
            console.log("Plant Admin has no plants, returning empty data for export.");
            return []; // Return empty array directly
        }
    } else if (this.currentUserRole === ROLES.SUPER_ADMIN) {
        // Super Admin uses the selection from the filter
        plantIdsToSend = this.currentFilterPlantIds.length > 0 ? this.currentFilterPlantIds : null;
    } else {
         console.error("Unknown user role, cannot determine plants for export.");
         return null; // Or return empty array
    }
    // End Role-Based Plant Filter Logic

    const payload: any = {
        "startDate": this.currentFilterStartDate,
        "endDate": this.currentFilterEndDate,
        ...(plantIdsToSend && { plantIds: plantIdsToSend }),
        "pageSize": 10000,
        "pageIndex": 1
    };
    if (!payload.startDate) delete payload.startDate;
    if (!payload.endDate) delete payload.endDate;

    console.log('Request Payload for ALL Observation Report Data:', payload);
    this.isLoadingReport = true; // Indicate loading for fetch all
    try {
        const res = await this.reportService.observationReport(payload);
        return res?.data ?? [];
    } catch (error) {
        console.error("Error fetching all observation reports for download:", error);
        this.toast?.showErrorToast("Failed to retrieve full data for download.");
        return null;
    } finally {
        this.isLoadingReport = false; // Stop loading
    }
  }

  async downloadExcel(type: 'current' | 'all') {
    if (this.isDownloadingExcel || this.isLoadingReport) return;

    this.isDownloadingExcel = true;
    this.downloadType = type;
    this.toast?.showSuccessToast(`Preparing download for ${type === 'current' ? 'current page' : 'all filtered'} reports...`);

    let dataToExport: PlantObservationReportItem[] | null = null;

    try {
        if (type === 'all') {
            dataToExport = await this.fetchAllFilteredObservationReports();
        } else {
            dataToExport = this.getCurrentListData() ?? null;
            if (dataToExport === undefined) { dataToExport = null; }
        }

        if (dataToExport === null) { this.toast?.showErrorToast("Failed to retrieve data for download."); return; }
        if (!dataToExport || dataToExport.length === 0) { this.toast?.showErrorToast(`No reports available to download.`); return; }

        console.log(`Fetched ${dataToExport.length} observation reports for Excel export (${type}).`);

        const dataForExcel = dataToExport.map(item => ({
            'Plant ID': item.plantId, 'Plant Name': item.name, 'Open Obs': item.openObservation ?? 0, 'Closed Obs': item.closedObservation ?? 0,
            'FindItFixIt Obs': item.fifiObservation ?? 0, 'Safe Act Obs': item.safeActObservation ?? 0, 'Unsafe Act Obs': item.unsafeActObservation ?? 0,
            'Unsafe Condition Obs': item.unsafeConditionObservation ?? 0, 'Open > 15 Days': item.observationOpenFrom15Days ?? 0, 'Open > 30 Days': item.observationOpenFrom30Days ?? 0,
        }));

        const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(dataForExcel);
        const wb: XLSX.WorkBook = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(wb, ws, 'PlantwiseObsReport');

        const dateStr = new Date().toISOString().slice(0, 10);
        const typeStr = type === 'current' ? `Page${this.currentPage}` : 'All';
        const fileName = `PlantwiseObservationReport_${typeStr}_${dateStr}.xlsx`;

        XLSX.writeFile(wb, fileName);
        this.toast?.showSuccessToast(`Excel file download started (${type}).`);

    } catch (error) {
        console.error(`Error generating Excel file (${type}):`, error);
        this.toast?.showErrorToast(`An error occurred while generating the Excel file (${type}).`);
    } finally {
        this.isDownloadingExcel = false; this.downloadType = null;
    }
  }

  setDefaultDates(): void {
     const today = new Date();
     const endDate = new Date();
     const startDate = new Date();
     startDate.setDate(today.getDate() - 30);
     this.startDate = this.formatDateForInput(startDate);
     this.endDate = this.formatDateForInput(endDate);
  }

  formatDateForInput(date: Date): string {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
  }

  closeModal() { this.isEditUserModalOpen = false; } // Renamed for filter modal

  openFilterModal() {
     this.startDate = this.currentFilterStartDate;
     this.endDate = this.currentFilterEndDate;
     this.selectedClusterId = this.clustersList.find(c => c.id === this.selectedClusterId)?.id ?? null;
     // Restore selected plants - IMPORTANT: Available plants depend on selected cluster AND role
     this.selectedPlantIds = [...this.currentFilterPlantIds]; // Restore the IDs

     // Re-fetch plants based on the restored cluster and user role
     this.fetchPlantsForFilter(this.selectedClusterId, false); // false = don't reset selectedPlantIds

     this.isEditUserModalOpen = true; // Open the filter offcanvas
  }

  async fetchClustersForFilter() {
    this.isLoadingClusters = true;
    try {
      // Super admin sees all clusters, Plant admin should only see clusters relevant to their plants?
      // This might require backend filtering or frontend filtering after getting all clusters.
      // For now, assume all active clusters are fetched. Adjust filter if needed.
      const filter = ['isDeleted||eq||false', 'enabled||eq||true'];
      const params = createAxiosConfig({ page: 1, limit: 1000, sort: 'title,ASC', filter: filter }); // Assuming 'title' field for cluster name
      const res = await this.clusterService.getCluster(params);
      // Plant Admins should only see clusters that contain at least one of their plants
      if(this.currentUserRole === ROLES.PLANT_ADMIN && this.loggedInPlantIds.length > 0) {
          // This requires fetching all plants associated with each cluster or having plantIds on cluster object
          // Simpler approach: Fetch all clusters and filter plants later.
          this.clustersList = res?.data ?? []; // Adjust based on response structure
      } else {
          this.clustersList = res?.data ?? [];
      }

    } catch (error) {
      console.error("Error fetching clusters:", error);
      this.clustersList = [];
    } finally {
      this.isLoadingClusters = false;
    }
  }

  onClusterChange(selectedCluster: Cluster | null): void {
    this.selectedPlantIds = [];
    this.availablePlantsForFilter = [];
    const clusterId = selectedCluster?.id ?? null;
    this.selectedClusterId = clusterId; // Store the ID
    this.fetchPlantsForFilter(clusterId, true); // Fetch plants for this cluster
  }


  async fetchPlantsForFilter(clusterId: number | null, resetSelection: boolean = true) {
    this.isLoadingPlants = true;
    if (resetSelection) this.selectedPlantIds = [];
    this.availablePlantsForFilter = [];

    // Base filters: enabled and not deleted
    const plantFilters = ['isDeleted||eq||false', 'enabled||eq||true'];

    // Add cluster filter if a cluster is selected
    if (clusterId) {
        plantFilters.push(`clusterId||eq||${clusterId}`);
    }

    // Add role-based filter: Plant Admins only see their assigned plants
    if (this.currentUserRole === ROLES.PLANT_ADMIN && this.loggedInPlantIds.length > 0) {
        plantFilters.push(`id||$in||${this.loggedInPlantIds.join(',')}`);
    } else if (this.currentUserRole === ROLES.PLANT_ADMIN && this.loggedInPlantIds.length === 0) {
        // Plant admin with no plants assigned should see an empty list
        this.isLoadingPlants = false;
        return;
    }
    // Super Admin sees all plants (optionally filtered by cluster if selected)

    try {
      const plantParams = createAxiosConfig({
        page: 1, limit: 1000, sort: 'name,ASC',
        filter: plantFilters
      });
      const res = await this.plantService.getPlants(plantParams);
      this.availablePlantsForFilter = res?.data ?? [];

      // If not resetting, ensure previously selected plants are still valid within the new list
      if (!resetSelection) {
           const validPlantIds = this.availablePlantsForFilter.map(p => p.id);
           this.selectedPlantIds = this.selectedPlantIds.filter(id => validPlantIds.includes(id));
      }

    } catch (error) {
      console.error(`Error fetching plants for cluster ${clusterId}:`, error);
      this.availablePlantsForFilter = [];
      this.selectedPlantIds = [];
    } finally {
      this.isLoadingPlants = false;
    }
  }

  async getReport(page: number) {
    this.isLoadingReport = true;
    this.currentPage = page;
    this.list = []; // Clear list before fetching

    let plantIdsToSend: number[] | null = null;

    // Determine which plant IDs to send based on role and filter selection
    if (this.currentUserRole === ROLES.PLANT_ADMIN) {
        if (this.loggedInPlantIds.length > 0) {
             // Use the *applied* filters (currentFilterPlantIds)
            if (this.currentFilterPlantIds.length > 0) {
                // Send only the selected plants that the admin has access to
                 plantIdsToSend = this.currentFilterPlantIds.filter(id => this.loggedInPlantIds.includes(id));
                 if (plantIdsToSend.length === 0) { // If selection is invalid for the admin
                      plantIdsToSend = this.loggedInPlantIds; // Fallback to all assigned
                 }
            } else {
                // If no specific plants selected in filter, send all assigned plant IDs
                plantIdsToSend = this.loggedInPlantIds;
            }
        } else {
            // Plant Admin with no plants, effectively send no data request or a blocking filter
            console.warn("Plant Admin has no plants, skipping report fetch.");
            this.totalItems = 0;
            this.isLoadingReport = false;
            return; // Exit early
        }
    } else if (this.currentUserRole === ROLES.SUPER_ADMIN) {
        // Super Admin uses the applied filter selection
        plantIdsToSend = this.currentFilterPlantIds.length > 0 ? this.currentFilterPlantIds : null;
    } else {
         console.error("Unknown user role, cannot fetch report.");
         this.totalItems = 0;
         this.isLoadingReport = false;
         return; // Exit for unknown roles
    }


    const payload: any = {
      "startDate": this.currentFilterStartDate,
      "endDate": this.currentFilterEndDate,
      ...(plantIdsToSend && { plantIds: plantIdsToSend }), // Conditionally add plantIds
      "pageSize": this.itemsPerPage,
      "pageIndex": this.currentPage
    };
    if (!payload.startDate) delete payload.startDate;
    if (!payload.endDate) delete payload.endDate;

    console.log("Fetching Observation report with payload:", payload);

    try {
      const res = await this.reportService.observationReport(payload);
      this.totalItems = res?.total ?? 0;
      this.list = res?.data ?? [];
      console.log("Observation report data:", this.list);
    } catch (error) {
      console.error("Error fetching observation report:", error);
      this.list = []; this.totalItems = 0;
      this.toast?.showErrorToast("Failed to load observation report.");
    } finally {
      this.isLoadingReport = false;
    }
  }

  applyFilter() {
    if (this.startDate && this.endDate && this.startDate > this.endDate) {
        this.toast?.showErrorToast('Start date cannot be after end date.');
        return;
    }

    // Apply filters from modal state to current filter state
    this.currentFilterStartDate = this.startDate;
    this.currentFilterEndDate = this.endDate;
    // Ensure plant IDs selected are valid for the user's role
     if (this.currentUserRole === ROLES.PLANT_ADMIN) {
          this.currentFilterPlantIds = this.selectedPlantIds.filter(id => this.loggedInPlantIds.includes(id));
     } else {
          this.currentFilterPlantIds = [...this.selectedPlantIds];
     }

    this.currentPage = 1;
    this.getReport(this.currentPage);
    this.closeModal(); // Close filter modal
  }

  onPageChange(page: number) {
    if (this.currentPage === page || this.isLoadingReport) return;
    this.getReport(page);
  }

   formatDisplayDate(dateString: string | null): string {
       if (!dateString) return 'N/A';
       try {
           const date = new Date(dateString);
           return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
       } catch (e) {
           return 'Invalid Date';
       }
   }
}