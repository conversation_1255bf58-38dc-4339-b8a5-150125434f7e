<app-toast-message></app-toast-message>
<div class="card custom-card">
    <div class="card-header d-flex align-items-center justify-content-between">
      <div>
        <h6 class="mb-0">Plant wise report</h6>
      </div>
      <div class="d-flex align-items-center">
          <!-- *** REPLACE the existing simple Download button with this Dropdown *** -->
          <div ngbDropdown class="d-inline-block me-2">
            <button type="button" class="btn btn-sm adani-btn dropdown-toggle" id="downloadPlantObsExcelDropdown" ngbDropdownToggle
                [disabled]="isDownloadingExcel || isLoadingReport">
                <span *ngIf="!isDownloadingExcel">
                    <i class="bi bi-file-earmark-excel me-1"></i> Download Excel
                </span>
                <span *ngIf="isDownloadingExcel">
                    <span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
                    Downloading {{ downloadType === 'current' ? 'Page' : (downloadType === 'all' ? 'All' : '') }}...
                </span>
            </button>
            <ul ngbDropdownMenu aria-labelledby="downloadPlantObsExcelDropdown">
                <li>
                    <button ngbDropdownItem (click)="downloadExcel('current')" [disabled]="isDownloadingExcel || isLoadingReport || (list?.length ?? 0) === 0">
                        <i class="bi bi-download me-1"></i> Download Current Page ({{ list?.length ?? 0 }})
                    </button>
                </li>
                <li>
                    <button ngbDropdownItem (click)="downloadExcel('all')" [disabled]="isDownloadingExcel || isLoadingReport || totalItems === 0">
                        <i class="bi bi-cloud-download me-1"></i> Download All Filtered ({{ totalItems }})
                    </button>
                </li>
            </ul>
        </div>
          <img src="../../../assets/svg/filter.svg" class="filter-button ms-3" (click)="openFilterModal()" alt="" style="width: 35px;" />
      </div>
      </div>
    <div class="card-body">
      <div class="table-responsive">
        <table class="table custom-table">
          <thead class="table-header">
            <tr>
              <th scope="col">Plant Id</th>
              <th scope="col">Plant</th>
              <th scope="col">Open</th>
              <th scope="col">Closed</th>
              <th scope="col">FiFi</th>
              <th scope="col">Safe Act</th>
              <th scope="col">Unsafe Act</th>
              <th scope="col">Unsafe Condition</th>
              <th scope="col">Open From 15 Days</th>
              <th scope="col">Open From 30 Days</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngIf="isLoadingReport">
              <td colspan="10" class="text-center p-4"> <!-- Colspan = 4 -->
                <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                Loading Plant wise observation report...
              </td>
            </tr>
            <tr *ngIf="!isLoadingReport && list.length === 0">
              <td colspan="10" class="text-center p-4 text-muted"> <!-- Colspan = 4 -->
                No plant wise observation report data found matching the criteria.
              </td>
            </tr>
            <tr *ngFor="let item of list">
              <td>{{ item.plantId }}</td>
              <td>{{ item.name }}</td>
              <td>{{ item.openObservation }}</td>
              <td>{{ item.closedObservation }}</td>
              <td>{{ item.fifiObservation }}</td>
              <td>{{ item.safeActObservation }}</td>
              <td>{{ item.unsafeActObservation }}</td>
              <td>{{ item.unsafeConditionObservation }}</td>
              <td>{{ item.observationOpenFrom15Days }}</td>
              <td>{{ item.observationOpenFrom30Days }}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
    <div class="card-footer text-muted text-center">
      <app-pagination [currentPage]="currentPage" [totalItems]="totalItems" [itemsPerPage]="itemsPerPage"
      (pageChange)="onPageChange($event)"></app-pagination>
    </div>
  </div>
  <!-- Filter Offcanvas -->
<app-offcanvas [title]="'Filter PlantWise Observations'" *ngIf="isEditUserModalOpen" (onClickCross)="closeModal()">
  <div class="container">
    <div class="row g-3"> <!-- Use g-3 for slightly more spacing -->

      <!-- From Date -->
      <div class="col-md-6"> <!-- Use columns for better layout on medium screens -->
        <label for="fromDate" class="form-label">From Date</label>
        <input type="date" id="fromDate" class="form-control" [(ngModel)]="startDate">
      </div>

      <!-- To Date -->
      <div class="col-md-6">
        <label for="toDate" class="form-label">To Date</label>
        <input type="date" id="toDate" class="form-control" [(ngModel)]="endDate">
      </div>

      <!-- Cluster Selection -->
      <div class="col-12">
          <label for="clusterSelectNgObs" class="form-label">Cluster</label>
          <ng-select
              id="clusterSelectNgObs"
              [items]="clustersList"
              bindLabel="title"
              bindValue="id"
              [(ngModel)]="selectedClusterId"
              (change)="onClusterChange($event)"
              [loading]="isLoadingClusters"
              [searchable]="true"
              [clearable]="true"
              placeholder="Select Cluster (Optional)">
          </ng-select>
           <div *ngIf="!isLoadingClusters && clustersList.length === 0" class="text-muted small mt-1">
                Could not load clusters.
           </div>
      </div>

      <!-- Plant Selection (Dependent on Cluster) -->
      <div class="col-12">
        <label for="plantSelectNgObs" class="form-label">Plant(s)</label>
         <ng-select
              id="plantSelectNgObs"
              [items]="availablePlantsForFilter"
              bindLabel="name"
              bindValue="id"
              placeholder="Select Plant(s) (Optional)"
              [multiple]="true"
              [(ngModel)]="selectedPlantIds"
              [loading]="isLoadingPlants"
              [searchable]="true"
              [closeOnSelect]="false"
               [disabled]="isLoadingClusters || isLoadingPlants">
              <!-- Note: Removed disable based on cluster selection to allow selecting plants *without* a cluster filter if desired -->
               <!-- Add back [disabled]="!selectedClusterId || isLoadingClusters || isLoadingPlants" if plants *must* be filtered by cluster -->
         </ng-select>
          <div *ngIf="selectedClusterId && !isLoadingPlants && availablePlantsForFilter.length === 0" class="text-muted small mt-1">
                No plants found for the selected cluster or failed to load.
           </div>
      </div>

      <!-- Search Button -->
      <div class="col-12 mt-3">
        <button type="button" (click)="applyFilter()" class="btn adani-btn w-100" [disabled]="isLoadingClusters || isLoadingPlants">
          <i class="bi bi-search"></i> Search
        </button>
      </div>

    </div>
  </div>
</app-offcanvas>
