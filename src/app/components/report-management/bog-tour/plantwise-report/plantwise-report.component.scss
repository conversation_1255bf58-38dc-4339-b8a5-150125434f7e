/* custom-table.component.css */

.custom-table {
  width: 100%;
  border-collapse: collapse;
  border-radius: 10px;
  margin-top: 10px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}
.card-body{
  height: 70vh;
  overflow: auto;
}

.custom-table th,
.custom-table td {
  padding: 10px 12px;
  border: 1px solid #dee2e6;
  text-align: left;
  font-size: 12px;
  vertical-align: middle;
}

.custom-table thead th {
  color: black;
  background-color: #f5f7fa;
  font-weight: bold;
  text-align: center;
}

.custom-table tbody tr:nth-child(even) {
  background-color: #f9f9f9;
}

/* Info Group Styling */
.info-group {
  margin-bottom: 6px;
  display: flex; /* Use flexbox for label and value alignment */
  align-items: baseline; /* Align labels and values at the baseline */
}

.info-label {
  font-size: 12px;
  margin-right: 8px; /* Add some space between label and value */
  min-width: 120px; /* Ensure labels have consistent width */
  display: inline-block; /* For min-width to work properly */
}


/* Tooltip Styling */
.tooltip-container {
  position: relative;
  display: inline-block;
}

.info-icon-container {
  display: inline-block;
  position: absolute;
  top: 0;
  right: 0;
  padding: 5px;
  cursor: pointer;
}

.info-icon-container i.el-icon-info {
  color: #999;
  font-size: 16px;
}

.tooltip-content {
  display: none;
  position: absolute;
  z-index: 1;
  top: 100%;
  right: 0;
  background-color: white;
  color: #333;
  border: 1px solid #ccc;
  padding: 15px;
  border-radius: 4px;
  box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.2);
  width: 250px;
  text-align: left;
}

.tooltip-content::before {
  content: "";
  position: absolute;
  bottom: 100%;
  right: 10px;
  border-width: 0 6px 6px 6px;
  border-style: solid;
  border-color: transparent transparent white transparent;
}

.tooltip-container:hover .tooltip-content {
  display: block;
}

.qr-details-header {
  font-weight: bold;
  margin-bottom: 8px;
}

.tooltip-content ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.tooltip-content li {
  margin-bottom: 5px;
  display: flex;
  align-items: center;
}

.tooltip-content li i {
  margin-right: 8px;
  color: #007bff;
}