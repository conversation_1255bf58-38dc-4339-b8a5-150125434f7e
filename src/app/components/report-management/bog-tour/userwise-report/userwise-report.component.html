<app-toast-message></app-toast-message>
<div class="card custom-card">
    <div class="card-header">
        <div class="row align-items-center">
            <div class="col">
                <h6 class="mb-0">Userwise report</h6>
            </div>
            <div class="col text-end d-flex align-items-center justify-content-end">
                <div ngbDropdown class="d-inline-block me-2">
                    <button type="button" class="btn btn-sm adani-btn dropdown-toggle" id="downloadUserReportExcelDropdown" ngbDropdownToggle
                        [disabled]="isDownloadingExcel || listLoading">
                        <span *ngIf="!isDownloadingExcel">
                            <i class="bi bi-file-earmark-excel me-1"></i> Download Excel
                        </span>
                        <span *ngIf="isDownloadingExcel">
                            <span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
                            Downloading {{ downloadType === 'current' ? 'Page' : (downloadType === 'all' ? 'All' : '') }}...
                        </span>
                    </button>
                    <ul ngbDropdownMenu aria-labelledby="downloadUserReportExcelDropdown">
                        <li>
                            <button ngbDropdownItem (click)="downloadExcel('current')" [disabled]="isDownloadingExcel || listLoading || (list?.length ?? 0) === 0">
                                <i class="bi bi-download me-1"></i> Download Current Page ({{ list?.length ?? 0 }})
                            </button>
                        </li>
                        <li>
                            <button ngbDropdownItem (click)="downloadExcel('all')" [disabled]="isDownloadingExcel || listLoading || totalItems === 0">
                                <i class="bi bi-cloud-download me-1"></i> Download All Filtered ({{ totalItems }})
                            </button>
                        </li>
                    </ul>
                </div>
                <img src="../../../assets/svg/filter.svg" class="filter-button ms-3" (click)="openFilterModal()" alt="" style="width: 35px;" />
            </div>
        </div>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered table-hover custom-table">
                <thead class="table-header">
                    <tr>
                        <th scope="col" class="text-center">User Details</th>
                        <th scope="col" class="text-center">Safety and Conditions</th>
                        <th scope="col" class="text-center">Additional Data</th>
                        <th scope="col" class="text-center">Difference / Time Spent / Last QR Scanned</th>
                    </tr>
                </thead>
                <tbody>
                    <tr *ngIf="listLoading">
                        <td colspan="4" class="text-center p-4">
                            <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                            Loading userwise report...
                        </td>
                    </tr>
                    <tr *ngIf="!listLoading && list.length === 0">
                        <td colspan="4" class="text-center p-4 text-muted">
                            No userwise tour report data found matching the criteria.
                        </td>
                    </tr>
                    <tr *ngFor="let item of list">
                        <td>
                            <div class="details-container">
                                <p class="label-value"><strong>User ID:</strong> <span class="value-text">{{ item.adminId }}</span></p>
                                <p class="label-value"><strong>Plant Name:</strong> <span class="value-text">{{ item.name }}</span></p>
                                <p class="label-value"><strong>User Name:</strong> <span class="value-text">{{ item.firstName }} {{ item.lastName }}</span></p>
                                <p class="label-value"><strong>Email:</strong> <span class="value-text">{{ item.email }}</span></p>
                                <p class="label-value"><strong>Contact:</strong> <span class="value-text">{{ item.contactNumber }}</span></p>
                            </div>
                        </td>

                        <td>
                            <div class="details-container">
                                <p class="label-value"><strong>Safe Act:</strong> <span class="value-text">{{ item.safeObservationCount }}</span></p>
                                <p class="label-value"><strong>Unsafe Act:</strong> <span class="value-text">{{ item.unSafeObservationCount }}</span></p>
                                <p class="label-value"><strong>Unsafe Condition:</strong> <span class="value-text">{{ item.unSafeConditionObservationCount }}</span></p>
                            </div>
                        </td>

                        <td>
                            <div class="details-container">
                                <p class="label-value">
                                    <strong>QR Scanned:</strong>
                                    <span class="value-text">
                                        {{ item.noOfQrScanned }}
                                        <i class="bi bi-info-circle info-icon"
                                           [ngbTooltip]="qrDetailsTooltip"
                                           [openDelay]="300"
                                           placement="auto"
                                           container="body"
                                           tooltipClass="qr-details-tooltip"></i>
                                    </span>
                                    <ng-template #qrDetailsTooltip>
                                        <div class="qr-tooltip-content">
                                            <p class="qr-details-header">
                                                <strong>QR Details:</strong>
                                            </p>
                                            <ul class="qr-details-list">
                                                <li>
                                                    <i class="bi bi-clock-fill"></i>
                                                    <b>Temporary QR Scan:</b> {{ item.temporaryQrCount }}
                                                </li>
                                                <li>
                                                    <i class="bi bi-check-circle-fill"></i>
                                                    <b>Permanent QR Scan:</b> {{ item.parmanentQrCount }}
                                                </li>
                                                <li>
                                                    <i class="bi bi-x-circle-fill"></i>
                                                    <b>Ignored QR:</b> {{ item.ignoreQrCount }}
                                                </li>
                                                <li>
                                                    <i class="bi bi-person-fill"></i>
                                                    <b>DJP Assign Count:</b> {{ item.djpAssignCount }}
                                                </li>
                                                <li>
                                                    <i class="bi bi-flag-fill"></i>
                                                    <b>DJP Scan Count:</b> {{ item.djpScanCount }}
                                                </li>
                                            </ul>
                                        </div>
                                    </ng-template>
                                </p>
                                <p class="label-value"><strong>Zone Covered:</strong> <span class="value-text">{{ item.noOfZonesCovered }}</span></p>
                                <p class="label-value"><strong>Total Observation:</strong> <span class="value-text">{{ item.observationCount }}</span></p>
                                <p class="label-value"><strong>Fi Fi:</strong> <span class="value-text">{{ item.fifiCount }}</span></p>
                            </div>
                        </td>

                        <td>
                            <div class="details-container">
                                <p class="label-value">
                                    <strong>Start at:</strong>
                                    <span class="value-text">{{ formatDisplayDate(item.startTime) }}</span>
                                </p>
                                <p class="label-value">
                                    <strong>Last QR Scanned:</strong>
                                    <span class="value-text">
                                        {{ formatDisplayDate(item.lastQrScannedTime) }}
                                    </span>
                                </p>
                                <p class="label-value">
                                    <strong>End at:</strong>
                                    <span class="value-text">{{ formatDisplayDate(item.endTime) }}</span>
                                </p>
                                <p class="label-value">
                                    <strong>Difference:</strong>
                                    <span class="value-text" *ngIf="convertDifferenceToHours(item.difference) !== null">
                                        {{ convertDifferenceToHours(item.difference) }} Hours
                                    </span>
                                    <span class="value-text" *ngIf="convertDifferenceToHours(item.difference) === null">
                                        Time difference not available
                                    </span>
                                </p>
                                <p class="label-value">
                                    <strong>Time Spent:</strong>
                                    <span class="value-text">
                                        {{ item.timeSpendCalculation !== null && item.timeSpendCalculation !== undefined ? (convertDifferenceToHours(item.timeSpendCalculation) + ' Hours') : '0 Hours' }}
                                    </span>
                                </p>
                            </div>
                        </td>
                </tr>
                </tbody>
            </table>
        </div>
    </div>
    <div class="card-footer text-muted text-center">
        <app-pagination [currentPage]="currentPage" [totalItems]="totalItems" [itemsPerPage]="itemsPerPage"
        (pageChange)="onPageChange($event)"></app-pagination>
      </div>
</div>
<!-- Filter Offcanvas -->
<app-offcanvas [title]="'Filter UserWise Tour Report'" *ngIf="isEditUserModalOpen" (onClickCross)="closeModal()">
    <div class="filter-container p-3"> <!-- Added padding -->
        <!-- Use ngForm's validity to control the submit button -->
        <form #filterForm="ngForm" (ngSubmit)="applyFilters()">
            <div class="row g-3"> <!-- Use g-3 for spacing -->

                <div class="col-12">
                    <label class="form-label" for="fromDate">From Date <span class="text-danger">*</span></label>
                    <input type="date" id="fromDate" class="form-control"
                           [(ngModel)]="filters.startDate" name="startDate"
                           required
                           #startDateModel="ngModel"
                           [class.is-invalid]="startDateModel.invalid && (startDateModel.dirty || startDateModel.touched || filterForm.submitted)" />
                    <!-- Validation Message -->
                    <div *ngIf="startDateModel.invalid && (startDateModel.dirty || startDateModel.touched || filterForm.submitted)"
                         class="invalid-feedback">
                        From Date is required.
                    </div>
                </div>

                <div class="col-12">
                    <label class="form-label" for="toDate">To Date <span class="text-danger">*</span></label>
                    <input type="date" id="toDate" class="form-control"
                           [(ngModel)]="filters.endDate" name="endDate"
                           required
                           #endDateModel="ngModel"
                           [class.is-invalid]="endDateModel.invalid && (endDateModel.dirty || endDateModel.touched || filterForm.submitted)" />
                    <!-- Validation Message -->
                    <div *ngIf="endDateModel.invalid && (endDateModel.dirty || endDateModel.touched || filterForm.submitted)"
                         class="invalid-feedback">
                        To Date is required.
                    </div>
                     <!-- Optional: Add validation to ensure To Date is not before From Date -->
                     <div *ngIf="endDateModel.valid && startDateModel.valid && (filters.endDate ?? 0) < (filters.startDate ?? 0) && (endDateModel.dirty || endDateModel.touched)"
                         class="text-danger small mt-1">
                         To Date cannot be before From Date.
                     </div>
                </div>

                <div class="col-12">
                    <label class="form-label" for="filterPlant">Select Plant(s)</label>
                    <ng-select
                        [items]="availablePlants"
                        bindLabel="name"
                        bindValue="id"
                        [multiple]="true"
                        placeholder="Select one or more plants"
                        [(ngModel)]="filters.plantIds"
                        name="plantIds"
                        [closeOnSelect]="false"
                        [searchable]="true"
                        [clearSearchOnAdd]="true"
                        id="filterPlant"
                        (change)="onPlantFilterChange()">
                        <ng-template ng-header-tmp>
                            <div class="form-check mb-1 ms-2">
                                <input class="form-check-input" type="checkbox"
                                       id="selectAllPlantsCheckbox"
                                       [checked]="isAllPlantsSelected"
                                       (change)="toggleSelectAllPlants($event)">
                                <label class="form-check-label small" for="selectAllPlantsCheckbox">
                                    Select All / Deselect All
                                </label>
                            </div>
                        </ng-template>
                    </ng-select>
                </div>

                <div class="col-12">
                    <label class="form-label" for="adminRoleSelect">Admin Role</label>
                    <select id="adminRoleSelect" class="form-select"
                            [(ngModel)]="filters.adminRoleId" name="adminRoleId"
                            (change)="onRoleFilterChange()">
                        <option [ngValue]="null">All Roles</option>
                        <option *ngFor="let role of availableAdminRoles" [value]="role.id">
                            {{ role.name }}
                        </option>
                    </select>
                </div>

                <div class="col-12">
                    <label class="form-label" for="userSelect">
                        Select User(s)
                        <span *ngIf="filters.userId && filters.userId.length > 0" class="badge bg-primary ms-2">
                            {{ filters.userId.length }} selected
                        </span>
                    </label>
                    <ng-select
                        [items]="availableUsers"
                        bindLabel="firstName"
                        bindValue="id"
                        [multiple]="true"
                        placeholder="Select one or more users"
                        [(ngModel)]="filters.userId"
                        name="userId"
                        [closeOnSelect]="false"
                        [searchable]="true"
                        [searchFn]="customUserSearchFn"
                        [clearSearchOnAdd]="true"
                        id="userSelect"
                        (change)="updateSelectAllUsersState()"
                        (paste)="handlePaste($event)">
                        <!-- Custom option template to show full name and email -->
                        <ng-template ng-option-tmp let-item="item">
                            <div class="d-flex flex-column">
                                <span>{{ item.firstName }} {{ item.lastName }}</span>
                                <small class="text-muted">{{ item.email }}</small>
                            </div>
                        </ng-template>

                        <!-- Custom label template for selected items -->
                        <ng-template ng-label-tmp let-item="item">
                            <span>{{ item.firstName }} {{ item.lastName }}</span>
                        </ng-template>

                        <ng-template ng-header-tmp>
                            <div class="form-check mb-1 ms-2">
                                <input class="form-check-input" type="checkbox"
                                       id="selectAllUsersCheckbox"
                                       [checked]="isAllUsersSelected"
                                       (change)="toggleSelectAllUsers($event)">
                                <label class="form-check-label small" for="selectAllUsersCheckbox">
                                    Select All / Deselect All
                                </label>
                            </div>
                        </ng-template>
                        <ng-template ng-footer-tmp>
                            <div class="small text-muted px-2 py-1">
                                <i class="bi bi-info-circle me-1"></i> Tip: You can paste emails to automatically select users
                            </div>
                            <div *ngIf="filters.userId && filters.userId.length > 0" class="small text-primary px-2 py-1 border-top">
                                <i class="bi bi-check-circle-fill me-1"></i> {{ filters.userId.length }} users selected
                            </div>
                        </ng-template>
                    </ng-select>
                </div>

                <div class="col-12">
                    <label class="form-label" for="tourSelect">Tour</label>
                    <select id="tourSelect" class="form-select"
                            [(ngModel)]="filters.validTour" name="validTour">
                        <option [ngValue]="false">All Tour</option>
                        <option [ngValue]="true">Valid Tour</option>
                    </select>
                </div>

                <div class="col-12 mt-4 d-grid gap-2">
                    <!-- Disable button if the form is invalid -->
                    <button type="submit" class="btn adani-btn" [disabled]="!filterForm.valid || (filters.endDate && filters.startDate && filters.endDate < filters.startDate)">
                        <i class="bi bi-search me-1"></i> Search
                    </button>
                    <button type="button" class="btn btn-secondary" (click)="resetFilters()">
                        <i class="bi bi-arrow-clockwise me-1"></i> Reset Filters
                    </button>
                </div>
            </div>
        </form>
    </div>
</app-offcanvas>
