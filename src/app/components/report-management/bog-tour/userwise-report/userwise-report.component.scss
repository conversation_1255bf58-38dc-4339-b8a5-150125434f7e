/* Card styling */
.card {
  background-color: #fff;
  border-radius: 10px;
  border: 1px solid rgba(0, 0, 0, 0.125);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  min-width: 0;
  word-wrap: break-word;
  margin-top: 10px;
  overflow: hidden;
}

.card-body {
  height: 70vh;
  overflow: auto;
  padding: 1.25rem;
}

.card-header {
  padding: 0.75rem 1.25rem;
  margin-bottom: 0;
  background-color: rgba(0, 0, 0, 0.03);
  border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}

/* Table styling */
.custom-table {
  width: 100%;
  border-collapse: collapse;
  border-radius: 10px;
  margin-top: 10px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.custom-table th,
.custom-table td {
  padding: 10px 12px;
  text-align: left;
  font-size: 12px;
  vertical-align: middle;
}

.custom-table thead th {
  font-size: 12px;
  background-color: #0B74B0 !important;
  color: white !important;
  text-align: center;
  font-weight: bold;
}

/* Add bottom border to table rows */
.custom-table tbody tr {
  border-bottom: 1px dotted #eee;
}

/* Details container styling */
.details-container {
  display: table;
  width: 100%;
  text-align: left;
  padding: 5px;
  border-spacing: 0 8px;
}

.label-value {
  margin-bottom: 8px;
  line-height: 1.5;
  display: flex;
  align-items: baseline;
  padding: 2px 0;
  border-bottom: 1px dotted #eee;
  padding-bottom: 8px;
}

.label-value:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.label-value strong {
  font-weight: 500;
  color: #777;
  margin-right: 8px;
  min-width: 110px;
  font-size: 12px;
  display: inline-block;
  vertical-align: top;
}

.value-text {
  font-weight: 600;
  color: #222;
  font-size: 12px;
  display: inline-block;
  word-break: break-word;
  overflow-wrap: break-word;
  flex: 1;
  letter-spacing: normal;
  line-height: 1.5;
}

/* Info Group Styling - updated to match qrcode-management */
.info-group {
  margin-bottom: 8px;
  line-height: 1.5;
  display: flex;
  align-items: baseline;
  padding: 2px 0;
  border-bottom: 1px dotted #eee;
  padding-bottom: 8px;
}

.info-group:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.info-label {
  font-weight: 500;
  color: #777;
  margin-right: 8px;
  min-width: 120px;
  font-size: 12px;
  display: inline-block;
  vertical-align: top;
}

/* Style for values */
.info-group span:not(.info-label) {
  font-weight: 600;
  color: #222;
  font-size: 12px;
  display: inline-block;
  word-break: break-word;
  overflow-wrap: break-word;
  flex: 1;
  letter-spacing: normal;
  line-height: 1.5;
}


/* Tooltip Styling */
.info-icon {
  color: #0b74b0;
  font-size: 14px;
  margin-left: 5px;
  cursor: pointer;
}

/* Custom tooltip styles that will be applied globally */
::ng-deep .qr-details-tooltip {
  max-width: 300px !important;
  background-color: white !important;
  color: #333 !important;
  border: 1px solid #ccc !important;
  padding: 0 !important;
  border-radius: 4px !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2) !important;
  opacity: 1 !important;

  .tooltip-inner {
    max-width: 300px;
    padding: 10px;
    text-align: left;
    background-color: white;
    color: #333;
  }

  .arrow::before {
    border-top-color: white !important;
    border-bottom-color: white !important;
  }
}

/* QR Details Tooltip Content Styling */
.qr-tooltip-content {
  width: 100%;

  .qr-details-header {
    font-weight: 600;
    margin-bottom: 8px;
    color: #222;
    font-size: 12px;
  }

  .qr-details-list {
    list-style: none;
    padding: 0;
    margin: 0;

    li {
      margin-bottom: 5px;
      display: flex;
      align-items: center;
      padding: 2px 0;
      border-bottom: 1px dotted #eee;
      padding-bottom: 5px;
    }

    li:last-child {
      border-bottom: none;
      margin-bottom: 0;
    }

    li i {
      margin-right: 8px;
      color: #0b74b0;
    }

    li b {
      font-weight: 500;
      color: #777;
      margin-right: 5px;
      min-width: 110px;
      display: inline-block;
    }
  }
}