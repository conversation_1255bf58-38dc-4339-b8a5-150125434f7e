import { Component, ElementRef, ViewChild } from '@angular/core';
import { FormsModule, NgForm, ReactiveFormsModule } from '@angular/forms';
import { OffcanvasComponent } from '../../shared/offcanvas/offcanvas.component';
import { CommonModule } from '@angular/common';
import { ExcelExportService } from '../../services/excel-export/excel-export.service';
import { createAxiosConfig } from '../../core/utilities/axios-param-config';
import { AdminService } from '../../services/admin/admin.service';
import { ToastMessageComponent } from '../../shared/toast-message/toast-message.component';
import { ZoneService } from '../../services/zone/zone.service';
import { AddDjpService } from '../../services/add-djp/add-djp.service';
import { TabComponent } from '../../shared/tab/tab.component';
import { PaginationComponent } from '../../shared/pagination/pagination.component';
import { MatTooltip } from '@angular/material/tooltip';
import { NgbDateStruct, NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { Modal } from 'bootstrap';
import { NgSelectModule } from '@ng-select/ng-select';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import * as XLSX from 'xlsx';
import { PlantManagementService } from '../../services/plant-management/plant-management.service';
import { DjpInfoDialogComponent } from './djp-info-dialog/djp-info-dialog.component';
import { MatDialog } from '@angular/material/dialog';

interface AddDjpItemData {
  id: number | null;
  djpTitle: string;
  zoneId: number | null;
  jobDescription: string;
  criticalityOptions: SimpleOption[];
  adminId: number | null;
  startDate: string | null | undefined;
  status: number;
  startTime: string | null | undefined;
  endDate: string | null | undefined;
  endTime: string | null | undefined;
  plantId: number | null;
  zone: Zone | null;
  type: number | null;
  risk: number | null;
  fromDate?: string | Date | null | undefined;
  toDate?: string | Date | null | undefined;
  selected?: boolean;
}

interface SimpleOption {
  value: number;
  label: string;
}

interface AdminUser { id: number; firstName: string; lastName: string; email: string, plantIds: any, fullName: string }

interface Zone {
  id: number;
  zoneName: string;
  plantId?: number;
}

interface DjpFilter {
  djpTitle?: string | null;
  plantId?: number | string | null;
  enabled?: string | null;
  sortField?: string | null;
  risk: string;
  sortDirection?: 'ASC' | 'DESC';
  endDate: string | null;
  startDate: string | null;
}

interface DjpFeedback {
  djpQrCodeId?: number | string | null;
  plantId?: number | string | null;
}

interface MultiUploadSuperAdmin {
  plantId: number | null;
  zoneId: number | null;
}

interface Plant { id: number; name: string; }

@Component({
  selector: 'app-add-djp',
  imports: [
    CommonModule,
    FormsModule,
    OffcanvasComponent,
    TabComponent,
    PaginationComponent,
    MatTooltip,
    ReactiveFormsModule,
    NgbModule,
    ToastMessageComponent,
    NgSelectModule,
    MatSnackBarModule
  ],
  templateUrl: './add-djp.component.html',
  styleUrl: './add-djp.component.scss'
})
export class AddDjpComponent {

  isFilterModalOpen: boolean = false;
  isAddEditModalOpen: boolean = false;
  isUploadModelOpen: boolean = false;
  @ViewChild('djpForm') djpForm!: NgForm;
  @ViewChild('djpmultiUploadForm') djpmultiUploadForm!: NgForm;
  @ViewChild('fileInput') fileInput!: ElementRef<HTMLInputElement>;
  @ViewChild(ToastMessageComponent) toast!: ToastMessageComponent;
  @ViewChild('confirmAddNewDjp') confirmAddNewDjp!: ElementRef;
  @ViewChild('uploadDjp') uploadDjp!: ElementRef;
  @ViewChild('uploadCancelDjp') uploadCancelDjp!: ElementRef;
  selectedDjpData: AddDjpItemData | null = null;
  zone: any[] = ['zone 1', 'zone 2', 'zone 3', 'zone 4', 'zone 5']
  criticality: any[] = ['zone 1', 'zone 2', 'zone 3', 'zone 4', 'zone 5']
  criticalityOptions: SimpleOption[] = [{ value: 1, label: 'Low' }, { value: 2, label: 'Medium' }, { value: 3, label: 'High' }, { value: 4, label: 'Critical' }];
  isDragging = false;
  invalidFile: { name: string; error: string } | null = null;
  selectedFile: File | null = null;
  availablePlantAdmins: AdminUser[] = [];
  getAllPlantUsers: AdminUser[] = [];
  editPlantUser: AdminUser[] = [];
  availableZones: Zone[] = [];
  loggedInAdminId: number | null = null;
  loggedInPlantId: number | null = null;
  multiUploadSuperAdmin!: MultiUploadSuperAdmin;
  allowedFileTypes = [
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/vnd.ms-excel',
  ];
  djpResponse: AddDjpItemData[] = [];
  djpHistoryResponse: AddDjpItemData[] = [];
  listLoading = false;
  listLoadingValidRecords = false;
  selectedTabIndex = 0;
  selectedAddDjpTabIndex = 0;
  currentPage = 1;
  currentPageValidRecords = 1;
  currentPageHistory = 1;
  itemsPerPage = 10;
  itemsPerPageHistory = 10;
  itemsPerPageValidRecords = 10;
  totalItems = 0;
  totalItemsValidRecords = 0;
  totalItemsHistory = 0;
  selectedDate: NgbDateStruct | undefined;
  confirmAddDjpModalInstance: Modal | null = null;
  uploadDjpModalInstance: Modal | null = null;
  cancelDjpModalInstance: Modal | null = null;
  searchText: string = '';
  searchTextHistory: string = '';
  filteredDjpResponse: any[] = [];
  validRecordsBulkUpload: any[] = [];
  filtervalidRecords: any[] = [];
  filterIDsArray: any[] = [];
  historyDjpResponse: any[] = [];
  searchTextResponse: any[] = [];
  searchTextResponseHistory: any[] = [];
  isDownloadingExcel = false;
  isDownloadingExcelHistory = false;
  downloadType: 'current' | 'all' | null = null;
  status: number = 1;
  startDateTimeInPast = false;
  endDateTimeInPast = false;
  endDateBeforeStart = false;
  endDateHourGap = false;
  availablePlants: Plant[] = [];
  isSuperAdminLogin: boolean = false;
  isEditMode: boolean = false;
  superAdminPlant: any;
  superAdminZone: any;
  defaultLoggedInPlantId: any;
  editLoading: boolean = false;
  tabs = [
    { title: 'DJP Information', status: 0, listKey: 'djpInfo' as const },
    { title: 'DJP History', status: 1, listKey: 'djphistory' as const },
  ];

  addDJPtabs = [
    { title: 'Add DJP', status: 0, listKey: 'adddjp' as const },
    { title: 'Multi Upload', status: 1, listKey: 'multiupload' as const },
  ];
  filters: DjpFilter = {
    djpTitle: null,
    enabled: null,
    plantId: null,
    sortField: 'id',
    risk: '',
    sortDirection: 'DESC',
    endDate: null,
    startDate: null
  };

  feedbackFilters: DjpFeedback = {
    djpQrCodeId: null,
    plantId: null
  };

  availableSortFields = [
    { value: 'id', label: 'ID' },
    { value: 'djpTitle', label: 'Description/Title' },
    { value: 'enabled', label: 'Status' },
    { value: 'createdAt', label: 'Created Date' }
  ];

  constructor(
    private excelExportService: ExcelExportService,
    private snackBar: MatSnackBar,
    private adminService: AdminService,
    private zoneService: ZoneService,
    private addDjpService: AddDjpService,
    private plantService: PlantManagementService,
    public dialog: MatDialog,
  ) {
    const userString = localStorage.getItem('user');
    if (!userString || userString.trim() === "" || ['null', 'undefined', '""', '" "'].includes(userString.trim().toLowerCase())) {
      this.toast?.showErrorToast("User session invalid."); return;
    }
    const currentUser = JSON.parse(userString);
    this.loggedInAdminId = currentUser?.id ?? null;
    this.loggedInPlantId = currentUser?.plant[0].id;
    this.defaultLoggedInPlantId = currentUser?.plant[0].id;
    if (currentUser) {
      this.isSuperAdminLogin = currentUser?.adminsRole?.name === 'superadmin' ? true : false;
    }
  }

  ngOnInit() {
    // this.getDjpData(this.currentPage);
    this.listLoading = true;
    this.loadPlantAdmins()
    this.loadZones()
    this.getPlants();

    window.addEventListener('dragover', function (e) {
      e.preventDefault();
    });
    window.addEventListener('drop', function (e) {
      e.preventDefault();
    });
  }

  async getPlants() {
    const data = { sort: 'name,ASC', filter: ['enabled||eq||true'], limit: 1000 };
    let allEnabledPlants: Plant[] = [];
    try {
      const param = createAxiosConfig(data);
      const response = await this.plantService.getPlants(param);
      allEnabledPlants = response?.data ?? response ?? [];
      this.availablePlants = allEnabledPlants;
    } catch (error) {
      console.error("Error fetching plants:", error);
      this.availablePlants = [];
      this.toast?.showErrorToast('Failed to load plants for filter.');
      return;
    }
  }

  async onPlantSelectionChange(selectedPlantId: number | null): Promise<void> {
    this.availableZones = [];
    this.availablePlantAdmins = [];
    if (this.selectedDjpData) {
      this.selectedDjpData.adminId = null;
    }
    if (selectedPlantId !== null) {
      await this.loadZonesForFilter(selectedPlantId);
    } else {
    }
  }

  async loadZonesForFilter(plantId: number): Promise<void> {
    await this.loadZonesSuperAdmin(plantId, 'filter');
  }

  ngAfterViewInit(): void {
    if (this.confirmAddNewDjp) {
      this.confirmAddDjpModalInstance = new Modal(this.confirmAddNewDjp.nativeElement);
    } else { console.error("confirmation modal element not found!"); }
    if (this.uploadDjp) {
      this.uploadDjpModalInstance = new Modal(this.uploadDjp.nativeElement);
    }
    else { console.error("confirmation modal element not found!"); }
    if (this.uploadCancelDjp) {
      this.cancelDjpModalInstance = new Modal(this.uploadCancelDjp.nativeElement);
    }
    else { console.error("Cancel confirmation modal element not found!"); }
  }

  ngOnDestroy(): void {
    this.confirmAddDjpModalInstance?.dispose();
    this.uploadDjpModalInstance?.dispose();
    this.cancelDjpModalInstance?.dispose();

    window.removeEventListener('dragover', this.preventDefault, false);
    window.removeEventListener('drop', this.preventDefault, false);
  }

  private preventDefault(e: Event) {
    e.preventDefault();
  }

  async getDjpData(pageNumber: number) {
    const filterParams: string[] = [];
    if (!this.isSuperAdminLogin) {
      if (this.loggedInPlantId) {
        filterParams.push(`plantId||eq||${this.loggedInPlantId}`);
      }
      if (this.filters.djpTitle) {
        filterParams.push(`djpTitle||$contL||${this.filters.djpTitle}`);
      }
      if (this.filters.startDate) {
        filterParams.push(`createdTimestamp||$gte||${this.filters.startDate}`);
      }
      if (this.filters.endDate) {
        filterParams.push(`createdTimestamp||$lte||${this.filters.endDate}`);
      }
      filterParams.push(`status||eq||${1}`);
    }
    else {
      if (this.isSuperAdminLogin) {
        const today = new Date();
        const startDateObj = new Date(today);
        startDateObj.setDate(today.getDate() - 30);

        const endDateObj = new Date(today);
        endDateObj.setDate(today.getDate() + 30);

        const startDate = startDateObj.toISOString().slice(0, 10);
        const endDate = endDateObj.toISOString().slice(0, 10);

        if (this.filters.plantId) {
          filterParams.push(`plantId||eq||${this.loggedInPlantId}`);
        }
        if (this.filters.djpTitle) {
          filterParams.push(`djpTitle||$contL||${this.filters.djpTitle}`);
        }
        if (this.filters.startDate === null || this.filters.startDate === "") {
          filterParams.push(`createdTimestamp||$gte||${startDate}`);
        }
        else {
          filterParams.push(`createdTimestamp||$gte||${this.filters.startDate}`);
        }
        if (this.filters.endDate === null || this.filters.endDate === "") {
          filterParams.push(`createdTimestamp||$lte||${endDate}`);
        }
        else {
          filterParams.push(`createdTimestamp||$lte||${this.filters.endDate}`);
        }
        filterParams.push(`status||eq||${1}`);
      }
    }
    const data = {
      page: pageNumber,
      limit: this.itemsPerPage,
      sort: `${this.filters.sortField || 'id'},${this.filters.sortDirection || 'DESC'}`,
      filter: filterParams
    };
    try {

      const param = createAxiosConfig(data);
      const response = await this.addDjpService.getDjp(param);
      this.djpResponse = response.data;
      this.filteredDjpResponse = [...this.djpResponse];
      this.totalItems = response.total ?? 0;

      this.manageDjpInfo(this.djpResponse);
    }
    catch (error) {
      console.error(`Error fetching get djp data`, error);
      this.toast?.showErrorToast('Network Error : failed to load djp list');
      this.totalItems = 0;
      this.listLoading = false;
    }
  }

  formatDate(date: Date | string | undefined): string {
    if (!date) return '';
    const d = new Date(date);

    // IST offset in milliseconds
    const IST_OFFSET = 5.5 * 60 * 60 * 1000;
    const istTime = d.getTime() + IST_OFFSET;
    const local = new Date(istTime);

    // Month abbreviations
    const months = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];

    // Pad function for day
    const pad = (num: number) => num.toString().padStart(2, '0');

    return `${pad(local.getUTCDate())}/${months[local.getUTCMonth()]}/${local.getUTCFullYear()} ${pad(local.getUTCHours())}:${pad(local.getUTCMinutes())}`;
  }


  manageDjpInfo(djpInfo: any) {
    const IST_OFFSET = 5.5 * 60 * 60 * 1000;
    const now = new Date();
    const nowIST = new Date(now.getTime() + IST_OFFSET);

    this.filteredDjpResponse = djpInfo.map((item: any) => {
      const itemDate = new Date(item.fromDate);
      const itemDateIST = new Date(itemDate.getTime() + IST_OFFSET);

      // Compare up to the minute
      const isSameMinute =
        itemDateIST.getUTCFullYear() === nowIST.getUTCFullYear() &&
        itemDateIST.getUTCMonth() === nowIST.getUTCMonth() &&
        itemDateIST.getUTCDate() === nowIST.getUTCDate() &&
        itemDateIST.getUTCHours() === nowIST.getUTCHours() &&
        itemDateIST.getUTCMinutes() === nowIST.getUTCMinutes();

      let color_code = '';
      if (itemDateIST > nowIST) {
        color_code = '#EFA300';
      } else if (isSameMinute) {
        color_code = '#2B8900';
      } else {
        color_code = '#2B8900';
      }
      return {
        ...item,
        djpId: item.djpId,
        risk: this.getCriticalityLabel(item.risk),
        criticality: item.risk,
        fromDate: this.formatDate(item.fromDate),
        plantUser: this.getAssignedPlantUser(item.adminId, 'fullname'),
        userEmail: this.getAssignedPlantUser(item.adminId, 'emailID'),
        plantName: this.getPlantName(item.plantId),
        toDate: this.formatDate(item.toDate),
        color_code
      };
    });

    this.searchTextResponse = [...this.filteredDjpResponse];
    this.listLoading = false;
  }

  getAssignedPlantUser(userID: number, value: string) {
    if (value === 'fullname') {
      const userName = this.getAllPlantUsers.find((item: any) => item.id === userID)
      return userName ? `${userName.firstName}, ${userName.lastName}` : 'NA'
    }
    else {
      const userName = this.getAllPlantUsers.find((item: any) => item.id === userID)
      return userName ? userName?.email : 'NA'
    }
  }

  getBoxColor(status: string): string {
    switch (status) {
      case 'In-Progress':
        return '#2B8900';
      case 'Pending':
        return '#EFA300';
      default:
        return '#EFA300';
    }
  }


  getTooltipText(color: string): string {
    if (color === '#2B8900') {
      return 'In-Progress';
    } else if (color === '#EFA300') {
      return 'Pending';
    }
    return '';
  }


  async openDjpInfo(djpInfo: any) {
    const filterParams: string[] = [];
    if (djpInfo) {
      if (djpInfo.id) {
        filterParams.push(`djpQrCodeId||eq||${djpInfo.id}`);
      }
      if (djpInfo.plantId) {
        filterParams.push(`plantId||eq||${djpInfo.plantId}`);
      }
    }
    const data = {
      filter: filterParams
    };
    try {
      const param = createAxiosConfig(data);
      const feedbackResponse = await this.addDjpService.feedbackDjp(param);
      this.openDjpDialog(feedbackResponse);
    }
    catch (error) {
      this.totalItems = 0;
      this.toast?.showErrorToast('Failed to load observation of djp.');
      console.error(`Error fetching observation djp data`, error);
    }
  }

  private openDjpDialog(feedbackResponse: any): void {
    this.dialog.open(DjpInfoDialogComponent, {
      width: '720px',
      height: '460px',
      data: {
        djpFeedbackData: feedbackResponse,
      }
    });
  }


  getPlantName(plantid: number) {
    const plant = this.availablePlants?.find((item: any) => item.id === plantid)
    return plant ? plant.name : 'NA';
  }

  async getDjpDataHistory(pageNumber: number) {
    const filterParams: string[] = [];
    if (!this.isSuperAdminLogin) {
      if (this.loggedInPlantId) {
        filterParams.push(`plantId||eq||${this.loggedInPlantId}`);
      }
      if (this.filters.djpTitle) {
        filterParams.push(`djpTitle||$contL||${this.filters.djpTitle}`);
      }
      if (this.filters.startDate) {
        filterParams.push(`createdTimestamp||$gte||${this.filters.startDate}`);
      }
      if (this.filters.endDate) {
        filterParams.push(`createdTimestamp||$lte||${this.filters.endDate}`);
      }
      filterParams.push(`status||eq||${3} || ${4}`);
    }
    else {
      const today = new Date();
      const startDateObj = new Date(today);
      startDateObj.setDate(today.getDate() - 8);

      const endDateObj = new Date(today);
      endDateObj.setDate(today.getDate() + 8);

      const startDate = startDateObj.toISOString().slice(0, 10);
      const endDate = endDateObj.toISOString().slice(0, 10);
      if (this.filters.plantId) {
        filterParams.push(`plantId||eq||${this.loggedInPlantId}`);
      }
      if (this.filters.djpTitle) {
        filterParams.push(`djpTitle||$contL||${this.filters.djpTitle}`);
      }
      if (this.filters.startDate === null || this.filters.startDate === "") {
        filterParams.push(`createdTimestamp||$gte||${startDate}`);
      }
      else {
        filterParams.push(`createdTimestamp||$gte||${this.filters.startDate}`);
      }
      if (this.filters.endDate === null || this.filters.endDate === "") {
        filterParams.push(`createdTimestamp||$lte||${endDate}`);
      }
      else {
        filterParams.push(`createdTimestamp||$lte||${this.filters.endDate}`);
      }
      filterParams.push(`status||eq||${3} || ${4}`);

    }
    const data = {
      page: pageNumber,
      limit: this.itemsPerPageHistory,
      sort: `${this.filters.sortField || 'id'},${this.filters.sortDirection || 'DESC'}`,
      filter: filterParams
    };
    try {
      const param = createAxiosConfig(data);
      const response = await this.addDjpService.getDjp(param);
      this.djpHistoryResponse = response.data;
      this.historyDjpResponse = [...this.djpHistoryResponse];
      this.totalItemsHistory = response.total ?? 0;
      this.manageDjpHistory(this.djpHistoryResponse);
    }
    catch (error) {
      console.error(`Error fetching get djp data`, error);
      this.totalItemsHistory = 0;
    }
  }

  manageDjpHistory(djpHistory: any) {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    this.historyDjpResponse = djpHistory.map((item: any) => ({
      ...item,
      djpId: item.djpId,
      risk: this.getCriticalityLabel(item.risk),
      criticality: item.risk,
      fromDate: this.formatDate(item.fromDate),
      plantUser: this.getAssignedPlantUser(item.adminId, 'fullname'),
      userEmail: this.getAssignedPlantUser(item.adminId, 'emailID'),
      plantName: this.getPlantName(item.plantId),
      toDate: this.formatDate(item.toDate),
    }));
    this.searchTextResponseHistory = [...this.historyDjpResponse]
  }

  async loadZonesSuperAdmin(plantId: number, target: 'filter' | 'edit'): Promise<void> {
    const targetListKey = 'availableZones';
    this.loggedInPlantId = Number(plantId);
    this[targetListKey] = [];
    const data = { sort: 'zoneName,ASC', filter: [`plantId||eq||${plantId}`, 'enabled||eq||true'], limit: 1000 };
    try {
      const param = createAxiosConfig(data);
      const response = await this.zoneService.getZone(param);
      this[targetListKey] = response?.data ?? response ?? [];
    } catch (error) {
      console.error(`Error fetching zones for plant ${plantId}:`, error);
      this[targetListKey] = [];
      this.toast?.showErrorToast(`Failed to load zones for the selected plant.`);

    } finally {
      // this.zoneLoading = false; // Optional: clear loading state
    }
    // if (!this.isEditMode || !this.isEditModePlantChange) {
    this.loadPlantAdmins()
    // }
  }

  editDjpRecord(editData: any) {
    this.editLoading = true;
    this.loadZonesSuperAdmin(editData.plantId, 'edit')
    this.loadPlantAdminsEdit(editData.plantId, editData.adminId)
    this.isEditMode = true;
    const normalizedEditData = {
      ...editData,
      risk: this.getCriticalityValue(editData.criticality),
      fromDate: this.formatDateISO(editData.fromDate),
      toDate: this.formatDateISO(editData.toDate)
    };
    this.selectedDjpData = { ...normalizedEditData };
    this.openDJPModal('Edit');
    // this.editLoading = false;
  }

  async loadPlantAdminsEdit(plantId: number, adminId: number): Promise<void> {
    const data = { sort: 'firstName,ASC', filter: ['enabled||eq||true', 'status||eq||1', 'adminsRoleId||eq||3'], limit: 10000 };
    const param = createAxiosConfig(data);
    try {
      const response = await this.adminService.getAdmin(param);
      this.availablePlantAdmins = response?.data ?? response ?? [];
      this.editPlantUser = this.availablePlantAdmins;
      const filteredAvailablePlantAdmins = this.availablePlantAdmins.filter(item => {
        return item.plantIds[0] === plantId && item.id === adminId
      })

      this.availablePlantAdmins = filteredAvailablePlantAdmins.map(admin => ({
        ...admin,
        email: `${admin.email}`,
        fullName: `${admin.firstName}, ${admin.lastName}`
      }));
      this.editLoading = false;
    } catch (error) {
      console.error("Error fetching plant admins:", error); this.availablePlantAdmins = [];
      this.toast?.showErrorToast('Failed to load plant admins.');
      this.listLoading = false;
    }
  }

  async loadZones(): Promise<void> {
    this.availableZones = [];
    const data = { sort: 'zoneName,DESC', filter: [`plantId||eq||${this.loggedInPlantId}`, 'enabled||eq||true'], limit: 1000 };
    try {
      const param = createAxiosConfig(data);
      const response = await this.zoneService.getZone(param);
      this.availableZones = response?.data ?? response ?? [];
    } catch (error) {
      console.error(`Error fetching zones for plant ${this.loggedInPlantId}:`, error);
      this.availableZones = [];
      this.toast?.showErrorToast(`Failed to load zones for the selected plant.`);
    } finally {
    }
  }

  async loadPlantAdmins(): Promise<void> {
    const data = { sort: 'firstName,ASC', filter: ['enabled||eq||true', 'status||eq||1', 'adminsRoleId||eq||3'], limit: 10000 };
    const param = createAxiosConfig(data);
    try {
      const response = await this.adminService.getAdmin(param);
      if (!response || (!response.data && !Array.isArray(response))) {
        throw new Error('Invalid API response structure');
      }
      else {
        this.availablePlantAdmins = response?.data ?? response ?? [];
        this.getAllPlantUsers = this.availablePlantAdmins;
        const filteredAvailablePlantAdmins = this.availablePlantAdmins.filter(item => {
          return item.plantIds[0] === this.loggedInPlantId
        })

        this.availablePlantAdmins = filteredAvailablePlantAdmins.map(admin => ({
          ...admin,
          email: `${admin.email}`,
          fullName: `${admin.firstName}, ${admin.lastName}`
        }));
        this.currentPage = 1;
        this.getDjpData(this.currentPage)
      }
    } catch (error) {
      console.error("Error fetching plant users:", error); this.availablePlantAdmins = [];
      this.listLoading = false;
      this.toast?.showErrorToast('Failed to load plant users.');
    }
  }

  getCriticalityLabel(value: number | null | undefined): string {
    if (value === null || value === undefined) return '';
    const option = this.criticalityOptions.find(opt => opt.value === value);
    return option?.label ?? '';
  }

  getCriticalityValue(value: number | null | undefined): any {
    if (value === null || value === undefined) return '';
    const option = this.criticalityOptions.find(opt => opt.value === value);
    return option?.value ?? 0;
  }

  onDateChange(dateValue: string, field: 'fromDate' | 'toDate') {
    const formattedDate = dateValue.replace('T', ' ') + ':00';
    if (this.selectedDjpData) {
      this.selectedDjpData[field] = formattedDate;
    }
  }

  getTomorrowDateTimeLocal(): string {
    const now = new Date();
    now.setDate(now.getDate() + 1);
    now.setSeconds(0, 0);
    const year = String(now.getFullYear()).padStart(2, '0');
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');
    return `${year}-${month}-${day}T${hours}:${minutes}`;
  }

  getSixMonthsLaterDateTimeLocal(): string {
    const now = new Date();
    now.setDate(now.getDate() + 1);
    now.setMonth(now.getMonth() + 6);
    now.setSeconds(0, 0);
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');
    return `${year}-${month}-${day}T${hours}:${minutes}`;
  }

  getMinEndDateTimeLocal(): string {
    if (this.selectedDjpData && this.selectedDjpData.fromDate) {
      let fromDate: Date;
      if (typeof this.selectedDjpData.fromDate === 'string') {
        fromDate = new Date(this.selectedDjpData.fromDate.replace(' ', 'T'));
      } else {
        fromDate = new Date(this.selectedDjpData.fromDate);
      }

      fromDate.setMinutes(fromDate.getMinutes() + 60);

      const year = fromDate.getFullYear();
      const month = String(fromDate.getMonth() + 1).padStart(2, '0');
      const day = String(fromDate.getDate()).padStart(2, '0');
      const hours = String(fromDate.getHours()).padStart(2, '0');
      const minutes = String(fromDate.getMinutes()).padStart(2, '0');

      return `${year}-${month}-${day}T${hours}:${minutes}`;
    }

    // Fallback
    return this.getTomorrowDateTimeLocal();
  }



  toDateTimeLocalString(date: Date): string {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    return `${year}-${month}-${day}T${hours}:${minutes}`;
  }

  searchTableData() {
    if (!this.searchText) {
      this.filteredDjpResponse = [...this.searchTextResponse];
      return;
    }
    const searchLower = this.searchText.toLowerCase();
    this.filteredDjpResponse = this.searchTextResponse.filter(emp => {
      const fromDateStr = emp.fromDate ? emp.fromDate.toString() : '';
      const toDateStr = emp.toDate ? emp.toDate.toString() : '';
      return (
        emp.djpTitle.toLowerCase().includes(searchLower) ||
        emp.jobDescription.toLowerCase().includes(searchLower) ||
        emp.zone?.zoneName.toLowerCase().includes(searchLower) ||
        emp.risk.toLowerCase().includes(searchLower) ||
        fromDateStr.toLowerCase().includes(searchLower) ||
        toDateStr.toLowerCase().includes(searchLower)
      );
    });
  }

  searchTableDataHistory() {
    if (!this.searchTextHistory) {
      this.historyDjpResponse = [...this.searchTextResponseHistory];
      return;
    }
    const searchLowerHistory = this.searchTextHistory.toLowerCase();
    this.historyDjpResponse = this.historyDjpResponse.filter(emp => {
      const fromDateStr = emp.fromDate ? emp.fromDate.toString() : '';
      const toDateStr = emp.toDate ? emp.toDate.toString() : '';
      return (
        emp.djpTitle.toLowerCase().includes(searchLowerHistory) ||
        emp.jobDescription.toLowerCase().includes(searchLowerHistory) ||
        emp.zone?.zoneName.toLowerCase().includes(searchLowerHistory) ||
        emp.risk.toLowerCase().includes(searchLowerHistory) ||
        fromDateStr.toLowerCase().includes(searchLowerHistory) ||
        toDateStr.toLowerCase().includes(searchLowerHistory)
      );
    });
  }

  openDJPModal(mode: string): void {
    if (mode === 'Add') {
      this.isEditMode = false;
      this.selectedDjpData = {
        id: null,
        djpTitle: '',
        zoneId: null,
        jobDescription: '',
        plantId: null,
        criticalityOptions: [],
        startDate: '',
        status: 1,
        adminId: null,
        startTime: '',
        endDate: '',
        endTime: '',
        zone: {
          id: 0,
          zoneName: '',
          plantId: 0
        },
        type: 0,
        risk: null,
        fromDate: '',
        toDate: ''
      };
      this.isAddEditModalOpen = true;
      this.endDateBeforeStart = false;
      this.endDateHourGap = false;
      this.endDateTimeInPast = false;
    }
    else {
      this.isAddEditModalOpen = true;
      this.endDateBeforeStart = false;
      this.endDateHourGap = false;
      this.endDateTimeInPast = false;
    }
  }

  closeAddEditModal(): void {
    this.isAddEditModalOpen = false;
    this.selectedAddDjpTabIndex = 0;
    this.resetUploadState();
  }

  combineDateAndTime(dateStr: any, timeStr: any): any {
    const [hours, minutes] = timeStr.split(':').map(Number);
    const date = new Date(dateStr);
    date.setHours(hours, minutes, 0, 0);
    const pad = (num: number) => num.toString().padStart(2, '0');
    const year = date.getFullYear();
    const month = pad(date.getMonth() + 1);
    const day = pad(date.getDate());
    const hour = pad(date.getHours());
    const minute = pad(date.getMinutes());
    const second = pad(date.getSeconds());
    return `${year}-${month}-${day} ${hour}:${minute}:${second}.000`;
  }

  customSearchFn(term: string, item: AdminUser & { fullName: string }): boolean {
    term = term.toLowerCase();
    return (
      item.fullName.toLowerCase().includes(term) ||
      item.email.toLowerCase().includes(term)
    );
  }

  toDate(val: string | Date | null | undefined): Date | null {
    if (!val) return null;
    if (val instanceof Date) return val;
    // Now val is string
    let normalized = val.replace(' ', 'T');
    if (normalized.length === 16) normalized += ':00';
    const parsed = new Date(normalized);
    return isNaN(parsed.getTime()) ? null : parsed;
  }

  openCreateSingleDjp(): void {
    this.startDateTimeInPast = false;
    this.endDateTimeInPast = false;
    this.endDateBeforeStart = false;
    this.endDateHourGap = false;
    const startDateStr = this.selectedDjpData?.fromDate;
    const endDateStr = this.selectedDjpData?.toDate;
    const now = new Date();

    if (this.djpForm && this.djpForm.invalid) {
      setTimeout(() => {
        Object.values(this.djpForm.controls).forEach(control => {
          control.markAllAsTouched();
          control.markAsDirty();
          control.updateValueAndValidity();
        });
      });
      return;
    }

    const startDate = this.toDate(startDateStr);
    const endDate = this.toDate(endDateStr);

    // Required field validation
    if (!this.isEditMode) {
      if (!startDate) {
        this.startDateTimeInPast = true;
        return;
      }
      if (!endDate) {
        this.endDateTimeInPast = true;
        return;
      }

      // Start date in past
      if (startDate < now) {
        this.startDateTimeInPast = true;
        return;
      }

      // End date in past
      if (endDate < now) {
        this.endDateTimeInPast = true;
        return;
      }

      // End date before start date
      if (endDate < startDate) {
        this.endDateBeforeStart = true;
        return;
      }

      const gapInMilliseconds = endDate.getTime() - startDate.getTime();
      const isValid = gapInMilliseconds >= 60 * 60 * 1000; // 1 hour = 3600 * 1000 ms
      if (!isValid) {
        this.endDateHourGap = true;
        return;
      }
      else {
        this.endDateHourGap = false;
      }
    }
    else {
      if (startDate && endDate) {
        if (!startDate) {
          this.startDateTimeInPast = true;
          return;
        }
        if (!endDate) {
          this.endDateTimeInPast = true;
          return;
        }

        // Start date in past
        if (startDate < now) {
          this.startDateTimeInPast = true;
          return;
        }

        // End date in past
        if (endDate < now) {
          this.endDateTimeInPast = true;
          return;
        }

        // End date before start date
        if (endDate < startDate) {
          this.endDateBeforeStart = true;
          return;
        }
        const gapInMilliseconds = endDate.getTime() - startDate.getTime();
        const isValid = gapInMilliseconds >= 60 * 60 * 1000; // 1 hour = 3600 * 1000 ms
        if (!isValid) {
          this.endDateHourGap = true;
          return;
        }
        else {
          this.endDateHourGap = false;
        }
      }
    }

    // All validations passed
    this.confirmAddDjpModalInstance?.show();
  }

  markAllAsTouched(controls: { [key: string]: any }) {
    Object.values(controls).forEach(control => {
      if (control.markAsTouched) {
        control.markAsTouched();
      }
      if (control.controls) {
        this.markAllAsTouched(control.controls);
      }
    });
  }

  closeCreateSingleDjp(): void {
    this.confirmAddDjpModalInstance?.hide();
    this.isEditMode = false;
    this.resetDjpForm();
  }

  cancelCreateSingleDjp(): void {
    this.confirmAddDjpModalInstance?.hide();
    // this.isEditMode = false;
    this.resetDjpForm();
  }

  confirmationCreateDjp() {
    this.saveSingleDjp();
    this.confirmAddDjpModalInstance?.hide();
  }

  async saveSingleDjp(): Promise<void> {
    if (this.djpForm && this.djpForm.invalid) {
      Object.values(this.djpForm.controls).forEach(control => {
        control.markAsTouched();
        return;
      });
    }
    else {
      if (!this.isEditMode) {
        const payload = {
          djpTitle: this.selectedDjpData?.djpTitle,
          zoneId: this.selectedDjpData?.zoneId,
          jobDescription: this.selectedDjpData?.jobDescription,
          adminId: this.selectedDjpData?.adminId,
          status: this.selectedDjpData?.status,
          plantId: this.isSuperAdminLogin ? this.selectedDjpData?.plantId : this.loggedInPlantId,
          type: this.selectedDjpData?.type,
          risk: this.selectedDjpData?.risk,
          toDate: this.selectedDjpData?.toDate,
          fromDate: this.selectedDjpData?.fromDate
        }
        try {
          const createDjpResponse = await this.addDjpService.createDjp(payload);
          if (createDjpResponse.responseCode === 200) {
            this.resetDjpForm();
            this.currentPage = 1;
            this.getDjpData(this.currentPage);
            this.toast?.showSuccessToast('New DJP is created!')
            this.closeAddEditModal();
          }
          else {
            const errorMsg = createDjpResponse.message || "API returned a non-success status.";
            console.error("API returned non-success status:", errorMsg);
            this.toast?.showErrorToast(errorMsg);
            this.resetDjpForm();
          }
        } catch (error: any) {
          console.error("Error saving/sending new djp:", error);
          this.toast?.showErrorToast(error?.response?.data?.message || "Failed to save New Djp.");
          this.resetDjpForm();
        }
      }
      else {
        const payload = {
          id: this.selectedDjpData?.id,
          djpTitle: this.selectedDjpData?.djpTitle,
          zoneId: this.selectedDjpData?.zoneId,
          jobDescription: this.selectedDjpData?.jobDescription,
          adminId: this.selectedDjpData?.adminId,
          status: this.selectedDjpData?.status,
          plantId: this.isSuperAdminLogin ? this.selectedDjpData?.plantId : this.loggedInPlantId,
          type: this.selectedDjpData?.type,
          risk: this.selectedDjpData?.risk,
          toDate: this.selectedDjpData?.toDate,
          fromDate: this.selectedDjpData?.fromDate
        }
        try {
          const updateDjpResponse = await this.addDjpService.updateDjp(payload);
          if (updateDjpResponse.responseCode === 200) {
            this.resetDjpForm();
            this.currentPage = 1;
            this.getDjpData(this.currentPage);
            this.toast?.showSuccessToast('DJP is updated!')
            this.closeAddEditModal();
          }
          else {
            const errorMsg = updateDjpResponse.message || "API returned a non-success status.";
            console.error("API returned non-success status:", errorMsg);
            this.toast?.showErrorToast(errorMsg);
            this.resetDjpForm();
          }
        }
        catch (error: any) {
          console.error("Error saving/sending update djp:", error);
          this.toast?.showErrorToast(error?.response?.data?.message || "Failed to update Djp.");
          this.resetDjpForm();
        }
      }
    }
    this.isEditMode = false;
  }


  parseCustomDate(dateStr: string): Date {
    const [datePart, timePart] = dateStr.split(' ');
    const [day, monthAbbr, year] = datePart.split('/');

    // Map month abbreviation to month index
    const months = ["Jan", "Feb", "Mar", "Apr", "May", "Jun",
      "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];
    const month = months.indexOf(monthAbbr);

    // Handle time part (optional)
    let hours = 0, minutes = 0;
    if (timePart) {
      [hours, minutes] = timePart.split(':').map(Number);
    }

    return new Date(+year, month, +day, hours, minutes);
  }


  formatDateISO(dateStr: string): string {
    if (!dateStr) return dateStr;
    const date = this.parseCustomDate(dateStr);
    if (isNaN(date.getTime())) return dateStr;
    const pad = (num: number) => String(num).padStart(2, '0');
    const year = date.getFullYear();
    const month = pad(date.getMonth() + 1);
    const day = pad(date.getDate());
    const hours = pad(date.getHours());
    const minutes = pad(date.getMinutes());
    return `${year}-${month}-${day}T${hours}:${minutes}`;
  }

  async onSubmitBulkUpload(): Promise<string> {
    let url = '';
    if (!this.selectedFile) {
      return url;
    }
    this.clearAllArraysAndValues();
    const fileName = this.selectedFile.name;
    const regex = new RegExp(
      `^djp-template( \\(\\d+\\))?\\.xlsx$`,
      'i'
    );
    if (!regex.test(fileName)) {
      this.toast.showErrorToast(
        'Please upload a valid file name.',
        5000
      );
      return fileName;
    }
    const fileFormData = new FormData();
    fileFormData.append('filedata', this.selectedFile, fileName);
    if (!this.isSuperAdminLogin) {
      fileFormData.append('plantId', this.loggedInPlantId?.toString() ?? '');
    }
    else {
      fileFormData.append('plantId', this.superAdminPlant?.toString() ?? '');
    }
    fileFormData.append('limit', this.totalItemsValidRecords.toString() ?? 0);
    try {
      const uploadExcelResponse = await this.addDjpService.bulkUploadExcel(fileFormData);
      if (uploadExcelResponse.responseCode === 200) {
        if (uploadExcelResponse.recods && uploadExcelResponse.recods.length > 0) {
          this.filtervalidRecords = [];
          this.validRecordsBulkUpload = [];
          this.filterIDsArray = [];
          this.totalItemsValidRecords = uploadExcelResponse.recods.total ?? 0;
          // Process each record
          uploadExcelResponse.recods.forEach((record: any, idx: number) => {
            if (record.excelRecods) {
              this.filtervalidRecords.push(record.excelRecods);


              this.validRecordsBulkUpload.push({
                ...record.excelRecods,
                id: `${idx + 1}`,
                selected: true
              });
            }
            this.validRecordsBulkUpload = this.validRecordsBulkUpload.map((item: any) => ({
              ...item,
              risk: this.getCriticalityLabel(item.risk),
              criticality: item.risk,
              fromDate: this.roundToNearestMinute(item.fromDate),
              toDate: this.roundToNearestMinute(item.toDate)
            }));

            if (record.validRecods) {
              this.filterIDsArray.push({
                ...record.validRecods,
                businessUnitId: 1,
                fromDate: this.formatDateExcel(record.validRecods.fromDate),
                toDate: this.formatDateExcel(record.validRecods.toDate),
                id: `${idx + 1}`
              });
            }
          });
          if (uploadExcelResponse.invalidRecordsLength !== 0) {
            this.downloadExcelFromBase64(uploadExcelResponse.fileData, 'invalid-bulk-upload');
            // Show success message
            this.snackBar.open('Please check the downloaded file for invalid records.', 'Close', {
              duration: 7000,
              horizontalPosition: 'right',
              verticalPosition: 'bottom',
              panelClass: ['snackbar-custom']
            });
          }
          setTimeout(() => {
            this.isUploadModelOpen = true;
          }, 2000);

          this.resetUploadState();

        } else {
          console.warn('No records found in response');
          if (uploadExcelResponse.invalidRecordsLength !== 0) {
            this.downloadExcelFromBase64(uploadExcelResponse.fileData, 'invalid-bulk-upload');
          }
          this.toast?.showErrorToast('No valid records found.');
          this.resetUploadState();
          this.totalItemsValidRecords = 0;
        }
      } else {
        const errorMsg = uploadExcelResponse.message || "API returned a non-success status.";
        console.error("API returned non-success status:", errorMsg);
        this.toast?.showErrorToast(errorMsg);
        this.resetUploadState();
      }
    } catch (uploadError) {
      console.error('Error uploading file:', uploadError);
      this.toast?.showErrorToast('An error occurred while uploading.');
      this.resetUploadState();
    }

    return url;
  }

  roundToNearestMinute(dateString: string): Date {
    const date = new Date(dateString);
    if (date.getSeconds() >= 30) {
      date.setMinutes(date.getMinutes() + 1);
    }
    date.setSeconds(0, 0);
    return date;
  }

  formatDateExcel(date: Date | string): string {
    const d = typeof date === 'string' ? new Date(date) : date;
    if (!(d instanceof Date) || isNaN(d.getTime())) {
      throw new Error('Invalid date');
    }
    const day = d.getUTCDate().toString().padStart(2, '0');
    const month = (d.getUTCMonth() + 1).toString().padStart(2, '0');
    const year = d.getUTCFullYear();
    const hours = d.getUTCHours().toString().padStart(2, '0');
    const minutes = d.getUTCMinutes().toString().padStart(2, '0');
    const seconds = d.getUTCSeconds().toString().padStart(2, '0');
    // return `${day}/${month}/${year} ${hours}:${minutes}:${seconds}`;
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  }

  mappingIDs(data: any): void {
    if (!data.recods || !Array.isArray(data.recods)) {
      console.warn('No records found in data');
      return;
    }
    this.validRecordsBulkUpload = [];
    this.filterIDsArray = [];

    data.recods.forEach((record: any, idx: number) => {
      if (record.excelRecods) {
        this.validRecordsBulkUpload.push({
          ...record.excelRecods,
          id: `${idx + 1}`,
          selected: true
        });
      }
      if (record.validRecods) {
        this.filterIDsArray.push({
          ...record.validRecods,
          id: `${idx + 1}`
        });
      }
    });

    data.recods.forEach((record: any, idx: number) => {
      if (record.excelRecods) {
        this.validRecordsBulkUpload.push({
          ...record.excelRecods,
          id: `${idx + 1}`,
          selected: true
        });
      }
      if (record.validRecods) {
        this.filterIDsArray.push({
          ...record.validRecods,
          id: `${idx + 1}`
        });
      }
    });
  }

  async confirmUploadDjp(): Promise<void> {
    try {
      const selectedDjps = this.validRecordsBulkUpload.filter(djp => djp.selected);
      const selectedIds = selectedDjps.map(djp => djp.id);
      const filteredValidRecords = this.filterIDsArray.filter(record => selectedIds.includes(record.id));
      const payload = {
        records: filteredValidRecords
      };
      const validRecordsResponse = await this.addDjpService.bulkUploadValidRecords(payload);
      if (validRecordsResponse.responseCode === 200) {
        this.toast?.showSuccessToast('All selected records have been submitted!')
        this.isUploadModelOpen = false;
        this.resetUploadState();
        this.getDjpData(1);
      }
      this.uploadDjpModalInstance?.hide();
    } catch (error) {
      console.error('Error uploading DJPs:', error);
    }
  }

  clearAllArraysAndValues(): void {
    this.validRecordsBulkUpload = [];
    this.filterIDsArray = [];
    this.filtervalidRecords = [];
  }

  downloadExcelFromBase64(base64: string, fileName: string) {
    const mimeType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
    const blob = this.base64ToBlob(base64, mimeType);
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = fileName;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }

  base64ToBlob(base64: string, mimeType: string): Blob {
    const byteCharacters = atob(base64.split(',')[1] || base64);
    const byteNumbers = new Array(byteCharacters.length);
    for (let i = 0; i < byteCharacters.length; i++) {
      byteNumbers[i] = byteCharacters.charCodeAt(i);
    }
    const byteArray = new Uint8Array(byteNumbers);
    return new Blob([byteArray], { type: mimeType });
  }

  onStartDateChange(event: Event): void {
    const input = event.target as HTMLInputElement;
    const value = input.value;
    if (!value) return;
    const selectedDate = new Date(value);
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    selectedDate.setHours(0, 0, 0, 0);
    if (selectedDate.getTime() === today.getTime()) {
      if (this.selectedDjpData) this.selectedDjpData.status = 1;
    } else {
      if (this.selectedDjpData) this.selectedDjpData.status = 1;
    }
  }

  onPageChange(page: number) {
    if (this.currentPage === page || this.listLoading) return;
    this.currentPage = page;
    this.getDjpData(this.currentPage);
  }

  onPageChangeHistory(page: number) {
    if (this.currentPageHistory === page || this.listLoading) return;
    this.currentPageHistory = page;
    this.getDjpDataHistory(this.currentPageHistory);
  }

  onPageChangeValidRecords(page: number) {
    if (this.currentPageValidRecords === page || this.listLoadingValidRecords) return;
    this.currentPageValidRecords = page;
    this.getDjpData(this.currentPageValidRecords);
  }

  onTabSelected(index: number) {
    if (this.selectedTabIndex === index) return;
    this.selectedTabIndex = index;
    if (this.selectedTabIndex === 1) {
      this.currentPageHistory = 1;
      this.filters = { djpTitle: null, enabled: null, sortField: 'id', sortDirection: 'DESC', risk: '', endDate: '', startDate: '' };
      this.getDjpDataHistory(this.currentPageHistory)
    }
    else {
      this.currentPage = 1;
      this.filters = { djpTitle: null, enabled: null, sortField: 'id', sortDirection: 'DESC', risk: '', endDate: '', startDate: '' };
      this.getDjpData(this.currentPage)
    }
  }

  onAddDjpTabSelected(index: number) {
    if (this.selectedAddDjpTabIndex === index) return;
    this.selectedAddDjpTabIndex = index;
  }

  onDragOver(event: DragEvent): void {
    event.preventDefault();
    this.isDragging = true;
  }

  onDragLeave(): void {
    this.isDragging = false;
  }

  onDrop(event: DragEvent): void {
    event.preventDefault();
    this.isDragging = false;
    if (event.dataTransfer?.files && event.dataTransfer.files.length > 0) {
      this.handleFile(event.dataTransfer.files[0]);
    }
  }

  onFileSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files?.length) {
      this.handleFile(input.files[0]);
    }
  }

  isAnyRecordSelected(): boolean {
    if (!this.validRecordsBulkUpload) return false;
    return this.validRecordsBulkUpload.some(record => record.selected);
  }

  handleFile(file: File): void {
    this.selectedFile = null;
    this.invalidFile = null;
    if (this.allowedFileTypes.includes(file.type)) {
      this.selectedFile = file;
      this.resetDjpForm();
    } else {
      this.invalidFile = {
        name: file.name,
        error: 'This document is not supported. Please select another file.',
      };
    }
  }

  removeFile(): void {
    this.selectedFile = null;
    if (this.fileInput && this.fileInput.nativeElement) {
      this.fileInput.nativeElement.value = '';
    }
  }

  removeInvalidFile(): void {
    this.invalidFile = null;
    if (this.fileInput && this.fileInput.nativeElement) {
      this.fileInput.nativeElement.value = '';
    }
  }

  uploadFiles(form: NgForm): void {
    if (form.valid) {
      this.onSubmitBulkUpload();
    }
    else {
      Object.values(form.controls).forEach(control => control.markAsTouched());
    }
  }

  closeUploadModel(): void {
    this.isUploadModelOpen = false;
  }

  resetUploadState(): void {
    this.selectedFile = null;
    this.invalidFile = null;
    if (this.fileInput && this.fileInput.nativeElement) {
      this.fileInput.nativeElement.value = '';
    }
    this.superAdminPlant = null;
    this.superAdminZone = null;
  }

  resetDjpForm(form?: NgForm): void {
    if (form) {
      form.resetForm();
      this.endDateHourGap = false;
      this.endDateTimeInPast = false
      this.endDateBeforeStart = false;
    }
  }

  downloadfile() {
    this.excelExportService.exportToExcel(
      `djp-template`
    );
  }

  @ViewChild('filterForm') filterForm!: NgForm;
  applyFilters(): void {
    if (this.selectedTabIndex === 0) {

      if (this.listLoading) return;
      if (this.filterForm && this.filterForm.invalid) {
        this.toast?.showErrorToast("Please correct the validation errors before applying filters.");
        return;
      }
      if (this.filters.djpTitle) {
        this.filters.djpTitle = this.filters.djpTitle.trim();
      }
      if (this.filters.risk) {
        this.filters.risk = this.filters.risk;
      }

      if (this.isSuperAdminLogin) {
        this.loggedInPlantId = Number(this.filters.plantId);
      }
      // if (this.filters.plantId) {
      //   this.loggedInPlantId = Number(this.filters.plantId);
      // }
      this.currentPage = 1;
      this.getDjpData(this.currentPage);
      this.closeModal();
    }
    else {
      if (this.listLoading) return;
      if (this.filterForm && this.filterForm.invalid) {
        this.toast?.showErrorToast("Please correct the validation errors before applying filters.");
        return;
      }
      if (this.filters.djpTitle) {
        this.filters.djpTitle = this.filters.djpTitle.trim();
      }
      if (this.filters.risk) {
        this.filters.risk = this.filters.risk;
      }
      if (this.filters.plantId) {
        this.filters.plantId = this.filters.plantId;
      }
      if (this.isSuperAdminLogin) {
        this.loggedInPlantId = Number(this.filters.plantId);
      }
      this.currentPageHistory = 1;
      this.getDjpDataHistory(this.currentPageHistory);
      this.closeModal();
    }
  }

  resetFilters(): void {
    if (this.listLoading) return;
    this.filters = { djpTitle: null, enabled: null, sortField: 'id', sortDirection: 'DESC', risk: '', endDate: '', startDate: '' };
    this.loggedInPlantId = this.defaultLoggedInPlantId;
    this.currentPage = 1;
    this.currentPageHistory = 1;
    this.getDjpData(this.currentPage);
    this.getDjpDataHistory(this.currentPageHistory);
  }

  openFilterModal() {
    this.isFilterModalOpen = true;
  }

  closeModal(): void {
    this.isFilterModalOpen = false;
  }

  selectAll(event: any) {
    const checked = event.target.checked;
    this.validRecordsBulkUpload.forEach(records => records.selected = checked);
  }

  toggleSelection(validRecordSelected: any) {
    validRecordSelected.selected = !validRecordSelected.selected;
  }

  get allSelected(): boolean {
    return this.validRecordsBulkUpload &&
      this.validRecordsBulkUpload.length > 0 &&
      this.validRecordsBulkUpload.every(r => r.selected);
  }

  onOpenUploadConfirmation() {
    this.uploadDjpModalInstance?.show();
  }

  cancelUploadConfirmation() {
    this.cancelDjpModalInstance?.show();
  }

  closeCancelConfimation() {
    this.cancelDjpModalInstance?.hide();
  }

  CancelUploadConfirmation() {
    this.cancelDjpModalInstance?.hide();
    this.djpResponse.forEach(djp => djp.selected = false);
    this.isUploadModelOpen = false;
    this.resetUploadState();
  }

  closeUploadDjp() {
    this.uploadDjpModalInstance?.hide();
  }

  getCurrentListData(): AddDjpItemData[] | undefined {
    return this.filteredDjpResponse;
  }

  async downloadExcel(type: 'current' | 'all', dataType: 'info' | 'history') {
    if (this.isDownloadingExcel || this.listLoading) return;

    this.isDownloadingExcel = true;
    this.downloadType = type;
    this.toast?.showSuccessToast(`Preparing download for ${type === 'current' ? 'current page' : 'all filtered'} djp...`);

    let dataToExport: AddDjpItemData[] | null = null;

    try {
      if (type === 'all' && dataType === 'info') {
        dataToExport = await this.fetchAllFilteredDjp();
      }
      if (type === 'all' && dataType === 'history') {
        dataToExport = await this.fetchAllFilteredDjp();
      }
      if (type === 'current' && dataType === 'info') {
        dataToExport = this.getCurrentListData() ?? null;
      }
      if (type === 'current' && dataType === 'history') {
        dataToExport = this.getCurrentListData() ?? null;
      }

      if (dataToExport === null) { this.toast?.showErrorToast("Failed to retrieve data for download."); return; }
      if (!dataToExport || dataToExport.length === 0) { this.toast?.showErrorToast(`No djp available to download.`); return; }

      const dataForExcel = dataToExport.map(n => ({
        'Title': n.djpTitle,
        'Zone': n?.zone?.zoneName,
        'Job Description': n.jobDescription,
        'Criticality': n.risk,
        'Start Date': n.fromDate,
        'End Date': n.toDate
      }));

      const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(dataForExcel);
      const wb: XLSX.WorkBook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, 'Djp');

      const dateStr = new Date().toISOString().slice(0, 10);
      const typeStr = type === 'current' ? `Page${this.currentPage}` : 'All';
      const fileName = `djp_${typeStr}_${dateStr}.xlsx`;

      XLSX.writeFile(wb, fileName);
      this.toast?.showSuccessToast(`Excel file download started (${type}).`);

    } catch (error) {
      console.error(`Error generating Excel file (${type}):`, error);
      this.toast?.showErrorToast(`An error occurred while generating the Excel file (${type}).`);
    } finally {
      this.isDownloadingExcel = false;
      this.downloadType = null;
    }
  }

  async fetchAllFilteredDjp(): Promise<AddDjpItemData[] | null> {
    this.listLoading = true; // Indicate loading
    const filterParams: string[] = [];
    if (this.filters.enabled !== null && this.filters.enabled !== undefined && this.filters.enabled !== '') {
      filterParams.push(`enabled||$eq||${this.filters.enabled}`);
    }

    if (this.loggedInPlantId) {
      filterParams.push(`plantId||eq||${this.loggedInPlantId}`);
    }
    if (this.filters.djpTitle) {
      filterParams.push(`djpTitle||$contL||${this.filters.djpTitle}`);
    }
    if (this.filters.startDate) {
      filterParams.push(`createdTimestamp||$gte||${this.filters.startDate}`);
    }
    if (this.filters.endDate) {
      filterParams.push(`createdTimestamp||$lte||${this.filters.endDate}`);
    }
    if (this.selectedTabIndex === 0) {
      filterParams.push(`status||eq||${1}`);
    }
    if (this.selectedTabIndex === 1) {
      filterParams.push(`status||eq||${3}`);
    }

    const data = {
      limit: 10000,
      sort: `${this.filters.sortField || 'id'},${this.filters.sortDirection || 'DESC'}`,
      filter: filterParams
    };

    try {
      const params = createAxiosConfig(data);
      const response = await this.getDjpList(data);
      return response.data ?? response ?? [];
    } catch (error: any) {
      console.error("Error fetching all djps for download:", error);
      this.toast?.showErrorToast(error?.response?.data?.message || "Failed to retrieve full data for download.");
      return null;
    } finally {
      this.listLoading = false;
    }
  }

  async getDjpList(data: any): Promise<{ data: AddDjpItemData[], total: number }> {
    const param = createAxiosConfig(data);
    const response = await this.addDjpService.getDjp(param);
    return { data: response, total: response.length };
  }
}
