<div class="main-dialog-style">
    <div class="header with-border fixed-header">
        <h5 class="dialog-title" style="color: white !important;">Observation Info</h5>
        <button class="close-button" style="color: white !important;" (click)="djpCrossClick()">×</button>
    </div>
    @if(!noDataMessage) {
    <div class="mb-2 dropdown-card" *ngFor="let panel of expandPanelData; let i = index">
        <div class="dropdown-content">
            <div class="dropdown-header d-flex justify-content-between align-items-center hl">
                <span class="title-index">{{ i + 1 }}.</span>
                <span class="title-name">{{ panel.name }}</span>
                <span class="title-email">{{ panel.email }}</span>
                <span class="title-number">{{ panel.contactNumber }}</span>
                <button class="btn btn-link p-0" (click)="toggleCollapse(i)">
                    <img [src]="
              selectedIndex === i
                ? '../../../assets/svg/dropdown-up-arrow.svg'
                : '../../../assets/svg/dropdown-down-arrow.svg'
            " alt="toggle" />
                </button>
            </div>
            <div [ngClass]="{
          'collapse show': selectedIndex === i,
          collapse: selectedIndex !== i
        }">
                <div class="two-column-container">
                    <div class="column">
                        <div class="item col-box-1"><span class="label">Types of Work:</span>
                            <span class="tooltipStyle panelFontStyle" [matTooltip]="panel.workPermit">{{panel.workPermit}}</span>
                        </div>
                        <div class="item"><span class="label">Description of the work:</span>
                            <span class="tooltipStyle panelFontStyle" [matTooltip]="panel.description">{{panel.description}}</span>
                        </div>
                        <div class="item"><span class="label">Major Deviation/ Positive:</span>
                            <span class="tooltipStyle panelFontStyle" [matTooltip]="panel.deviation">{{panel.deviation}}</span>
                        </div>
                    </div>
                    <div class="column">
                        <div class="item"><span class="label">Risk Level of the Finding:</span>
                            <span class="tooltipStyle panelFontStyle" [matTooltip]="panel.riskLevel">{{panel.riskLevel}}</span>
                        </div>
                        <div class="item"><span class="label">Action Taken on the Deviation:</span>
                            <span class="tooltipStyle panelFontStyle" [matTooltip]="panel.actionTaken">{{panel.actionTaken}}</span>
                        </div>
                        <div class="item"><span class="label">Is there any Violation:</span>
                            <span class="tooltipStyle panelFontStyle" [matTooltip]="panel.violation">{{panel.violation}}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    }
    @else {
    <span class="no-data-found">No Observation found</span>
    }
</div>