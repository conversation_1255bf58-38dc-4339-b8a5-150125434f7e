import { ComponentFixture, TestBed } from '@angular/core/testing';

import { DjpInfoDialogComponent } from './djp-info-dialog.component';

describe('DjpInfoDialogComponent', () => {
  let component: DjpInfoDialogComponent;
  let fixture: ComponentFixture<DjpInfoDialogComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [DjpInfoDialogComponent]
    })
    .compileComponents();

    fixture = TestBed.createComponent(DjpInfoDialogComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
