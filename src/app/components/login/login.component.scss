$lightGray: #e0e0e0;
$darkGray: #505050;
$loginBg: #808080;
$loginCursorColor: #e0e0e0;

.register {
  text-decoration: underline;
  color: #0B74B0;
  text-align: center;
  text-decoration-thickness: inherit;
  cursor: pointer;
}

.login-container {
  height: 100%;
  width: 100%;
  position: relative;
  overflow: hidden;
  background-size: cover;
  box-shadow: inset 0 0 0 2000px rgba(0, 0, 0, 0.1);

  .boxed {
    width: 80%;
    position: absolute;
    margin: auto;
    top: 50%;
    left: 50%;
    margin-bottom: 0px;
    transform: translate(-50%, -50%);
    display: flex;
    justify-content: center;
    align-items: baseline;

    .login-logo {
      margin: 10% auto;
      display: flex;
      align-items: center;
      justify-content: center;
      object-fit: contain;
    }
  }

  .login-form,
  .register-form {
    max-width: 90%;
    margin: 10% auto;
    width: 40%;
    overflow: hidden;
    background: #fff;
    padding: 50px;
    border-radius: 1em;
  }

  .form-actions {

    .btn-back,
    .btn-register {
      padding: 10px 20px;
      font-size: 16px;
      height: 45px;
      /* Ensure both buttons have the same height */
      line-height: 1.2;
      display: flex;
      align-items: center;
      /* Align text inside buttons */
      justify-content: center;
    }
  }

  .login-form-register {
    max-width: 90%;
    margin: 10% auto;
    width: 40%;
    background: #fff;
    padding: 50px;
    border-radius: 1em;
  }

  .el-input {
    display: inline-block;
    height: 47px;
    caret-color: black;

    input {
      height: 47px;
      background: transparent;
      border: 0px;
      border-radius: 0px;
      padding: 12px 5px 12px 15px;
      color: black;
      -webkit-text-fill-color: black !important;
      -webkit-appearance: none;

      &:-webkit-autofill {
        box-shadow: 0 0 0px 1000px $loginBg inset !important;
        -webkit-text-fill-color: black !important;
      }
    }
  }

  ::placeholder {
    color: black;
    opacity: 1;
  }

  .el-form-item {
    border: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(0, 0, 0, 0.1);
    border-radius: 5px;
    color: #454545;
  }
}

.unicon {
  fill: black !important;
}

.appstore-wrapper {
  width: 80%;
  margin: auto;
}

.el-row {
  border-bottom: unset;
}

.el-dialog__body {
  padding: 5px 20px !important;
}

.el-dialog__header {
  text-align: center !important;
}

.title-container {
  position: relative;

  .login-logo {
    max-width: 8em;
    margin: 0 auto;
    display: inherit;
  }

  .login-logo-signup {
    max-width: 8em;
    margin: 0 auto;
    display: inherit;
  }

  .title {
    font-size: 15px;
    color: rgb(29, 60, 66);
    margin: 0px auto 20px auto;
    text-align: center;
    font-weight: bold;
  }

  .set-language {
    color: #fff;
    position: absolute;
    top: 3px;
    font-size: 18px;
    right: 0px;
    cursor: pointer;
  }
}

.login-container {
  .tips {
    font-size: 14px;
    color: black;
    margin-bottom: 10px;

    span {
      &:first-of-type {
        margin-right: 16px;
      }
    }
  }

  .svg-container {
    padding: 6px 5px 6px 15px;
    color: $darkGray;
    vertical-align: middle;
    width: 30px;
    display: inline-block;
  }

  .show-pwd {
    position: absolute;
    right: 10px;
    top: 7px;
    font-size: 16px;
    color: $darkGray;
    cursor: pointer;
  }

  .thirdparty-button {
    position: absolute;
    right: 0;
    bottom: 6px;
  }

  .leaves {
    margin-top: -26%;
    width: 100%;
  }

  .midbutton {
    display: flex;
    justify-content: center;
    background: linear-gradient(90deg, #0b74b0 0%, #75479c 52.08%, #bd3861 100%);
  }

  .btn {
    background: #964576;
  }
}

.login-logo-bog {
  max-width: 6em;
  margin: auto;
  display: block;
  margin-top: 2em;
}

.app-qr {
  width: 170px;
  height: 170px;
  object-fit: cover;
}

@media only screen and (max-width: 470px) {
  .thirdparty-button {
    display: none;
  }
}

@media only screen and (max-width: 1100px) {
  .login-container {
    .boxed {
      width: 100%;
      margin: 0px;

      .login-logo {
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }

    .login-form {
      position: relative;
      max-width: 100%;
      margin: 0 auto;
      overflow: hidden;
      background: #fff;
      padding: 2em 2em 1em;
      border-radius: 1em;
    }
  }
}

@media only screen and (max-width: 770px) {
  .login-container {
    .login-form {
      width: 80% !important;
    }
  }

  .appstore-wrapper {
    width: 100%;
  }

  .app-qr {
    width: 130px;
    height: 130px;
    object-fit: cover;
  }

  .login-dialog-wrapper {
    .admin-info {
      width: 90%;
      padding: 10px;

      h4,
      p {
        margin: 10px 0px;
      }

      .user-icon {
        height: 70px;
        width: 70px;
      }
    }

    .title-wrapper {
      margin: 20px 10px;
    }
  }
}

@media only screen and (max-width: 550px) {
  .login-container {
    .boxed {
      width: 95%;
    }

    .login-form {
      width: 90% !important;
      padding: 3em 2em 1em;
    }
  }
}

.input-field {
  width: 100%;
  padding: 10px;
  margin: 10px 0;
  border: 2px solid #ccc;
  border-radius: 5px;
  font-size: 16px;
}

.midbutton {
  width: 100%;
  padding: 12px;
  margin: 10px 0;
  border: none;
  border-radius: 5px;
  font-size: 16px;
  cursor: pointer;
}

.btn-submit {
  background-color: #28a745;
  color: white;
}

.btn-sso {
  background-color: #007bff;
  color: white;
}

.or-text {
  text-align: center;
  margin: 10px 0;
}

.register {
  text-align: center;
  font-size: 14px;
  color: #007bff;
  cursor: pointer;
}

.register:hover {
  text-decoration: underline;
}

.otp-verification {
  padding: 20px;
}

.otp-input {
  display: flex;
  justify-content: center; /* Centers horizontally */
  align-items: center; /* Centers vertically (if needed) */
  width: 100%; /* Ensures full width */
  margin-top: 20px; /* Adjust spacing if needed */
}

.otp-input-container {
  display: flex;
  justify-content: space-between;
}

.otp-actions {
  margin-top: 20px;
  display: flex;
  justify-content: space-between;
}

.btn-back,
.btn-resend {
  padding: 10px 20px;
  font-size: 16px;
  border: none;
  border-radius: 5px;
}

.btn-back {
  background-color: #ccc;
}

.btn-resend {
  background-color: #007bff;
  color: white;
}

.signup-steps {
  margin-top: 20px;
}

.signup-steps input,
.signup-steps select {
  width: 100%;
  padding: 10px;
  margin: 10px 0;
  border: 2px solid #ccc;
  border-radius: 5px;
  font-size: 16px;
}

.btn-next,
.btn-register {
  width: 100%;
  padding: 12px;
  margin: 10px 0;
  font-size: 16px;
  border: none;
  border-radius: 5px;
}

.btn-next {
  background-color: #28a745;
  color: white;
}

.btn-register {
  background-color: #007bff;
  color: white;
}

.success-message {
  text-align: center;
  padding: 20px;
}

.success-message b {
  font-size: 18px;
  color: #28a745;
}

.success-message p {
  font-size: 14px;
  color: #555;
}

.btn-back-to-login {
  padding: 10px 20px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 5px;
}

.login-dialog-wrapper {
  background-color: white;
  padding: 30px;
  border-radius: 10px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  width: 400px;
  max-width: 100%;
}

.title-wrapper {
  text-align: center;
  margin-bottom: 20px;
}

.admin-info {
  margin-bottom: 20px;
}

.admin-info p {
  font-size: 14px;
  color: #555;
}

.appstore-wrapper {
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
}

.app-qr {
  width: 100px;
  margin-right: 10px;
}

.appStore-black {
  width: 100px;
}

.btn-close {
  padding: 10px 20px;
  background-color: #ccc;
  color: white;
  border: none;
  border-radius: 5px;
  width: 100%;
  font-size: 16px;
}
/* Stepper Container */
.stepper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  width: 100%;
  max-width: 500px;
  margin: auto;
}

/* Step Container */
.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 2;
  flex: 1;
}

/* Circle */
.circle {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #ccc;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: white;
  font-weight: bold;
  transition: 0.3s;
  z-index: 3;
}

/* Connector Line */
.line {
  position: absolute;
  top: 20px;
  left: 50%;
  width: 50%;
  height: 5px;
  background-color: #ccc;
  z-index: 1;
  transform: translateX(-50%);
}

/* Hide last connector line */
.step:last-child + .line {
  display: none;
}

/* Step Label */
.step span {
  margin-top: 10px;
  font-weight: bold;
  color: #6c757d;
}

/* Active Step */
.step.active .circle {
  background-color: #0d6efd;
}

/* Completed Step */
.step.completed .circle {
  background-color: #28a745;
}

.step.completed + .line {
  background-color: #28a745;
}
