<app-toast-message></app-toast-message>
<app-loading-overlay [loading]="loading || signUpLoading"></app-loading-overlay> <!-- Combine loading states -->
<div class="login-container">
  <img alt="Adani Logo" src="../../../assets/img/adanibg.png" class="background-image">

  <!-- Login Section -->
  @if(!isSignUp){
  <div>
    <div class="boxed">
      <div class="login-form">
        <div class="title-container" style="margin-bottom: 30px;">
          <img alt="Adani Logo" src="../../../assets/img/adanibg3.png" class="login-logo">
        </div>
        @if(!sentOtp){
        <div>
          <!-- LOGIN FORM -->
          <form [formGroup]="loginForm" (ngSubmit)="handleLogin()" class="transition-container">
            <div class="form-item mb-3"> <!-- Added margin bottom -->
              <input
                name="email"
                formControlName="email"
                placeholder="Type your registered email id"
                type="email"
                class="input-field form-control"
                [ngClass]="{'is-invalid': loginForm.get('email')?.invalid && (loginForm.get('email')?.touched || loginForm.get('email')?.dirty)}"
                autocomplete="email"
              />
              <!-- Bootstrap Validation Feedback -->
              <div *ngIf="loginForm.get('email')?.invalid && (loginForm.get('email')?.touched || loginForm.get('email')?.dirty)" class="invalid-feedback d-block">
                 <small *ngIf="loginForm.get('email')?.errors?.['required']">
                    Email is required.
                 </small>
                 <small *ngIf="loginForm.get('email')?.errors?.['email']">
                    Enter a valid email address.
                 </small>
                 <small *ngIf="loginForm.get('email')?.errors?.['adaniDomain']">
                 Only adani.com domain emails are allowed.
              </small>
                 <!-- Add other specific errors for login email if needed -->
              </div>
            </div>
            <button type="submit" [disabled]="loading || loginForm.invalid" class="midbutton btn-submit">
              <b>Login To Proceed</b>
            </button>
            <div class="or-text">
              <label>OR</label>
            </div>
            <button type="button" (click)="handleSSOLogin()" [disabled]="loading || loginForm.invalid" class="midbutton btn-sso">
              <b>SSO Login</b>
            </button>
            <div (click)="handleRegister()" class="register">Register as BOG user</div>
          </form>
        </div>
        }
        @if(sentOtp){
        <!-- OTP Section -->
        <div class="otp-verification">
          <h5 class="title">Please enter the OTP sent to <b>{{ email }}</b></h5> <!-- Use email variable -->
          <div class="form-group">
            <!-- Make sure ng-otp-input styles don't clash heavily -->
            <div class="otp-input d-flex justify-content-center mb-3">
                <ng-otp-input (onInputChange)="onOtpChange($event)" [config]="{length:4, allowNumbersOnly: true, inputClass: 'form-control otp-single-input'}"></ng-otp-input>
            </div>
          </div>
          <div class="otp-actions d-flex justify-content-between"> <!-- Use flex for spacing -->
            <button type="button" (click)="back()" class="btn adani-btn btn-sm">Back</button> 
            <button type="button" (click)="handleResendOTP()" [disabled]="resentLoading" class="btn adani-btn btn-sm">{{ resentLoading ? 'Sending...' : 'Resend OTP' }}
            </button>
          </div>
        </div>
        }
      </div>
    </div>
  </div>
  }

  <!-- Signup Section -->
  @if(isSignUp){
  <div>
    <div class="boxed">
      <div class="register-form">
        <div class="title-container" style="margin-bottom: 20px;">
          <img alt="Adani Logo" src="assets/img/adanibg3.png" class="login-logo-signup">
        </div>
        <div class="row mb-4"> <!-- Added margin bottom -->
          <div *ngIf="signUpResponseCode !== 200" class="col-md-12">
            <!-- Stepper -->
            <div  class="stepper">
              <div class="step" [ngClass]="{'active': activeTab === 0, 'completed': activeTab > 0}">
                <div class="circle"><i class="bi bi-person"></i></div>
                <span>Personal Details</span>
              </div>
              <div class="line" [ngClass]="{'completed': activeTab > 0}"></div>
              <div class="step" [ngClass]="{'active': activeTab === 1, 'completed': activeTab > 1}">
                <div class="circle"><i class="bi bi-flower1"></i></div>
                <span>Plant Details</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Signup Form Steps -->
        <div *ngIf="signUpResponseCode !== 200">
            <!-- Bind the whole form group, handle submit only at the end -->
            <form [formGroup]="signUpForm" (ngSubmit)="validateRegisterForm()">
              <!-- Step 1 Fields -->
              <div *ngIf="activeTab === 0">
                <div class="mb-3">
                  <label for="firstName" class="form-label">First Name</label>
                  <input
                    id="firstName"
                    formControlName="firstName"
                    placeholder="Enter your First Name"
                    class="form-control"
                    [ngClass]="{'is-invalid': isInvalid('firstName')}"
                  />
                  <div *ngIf="isInvalid('firstName')" class="invalid-feedback">
                    {{ getErrorMessage('firstName') }}
                  </div>
                </div>
            
                <div class="mb-3">
                  <label for="lastName" class="form-label">Last Name</label>
                  <input
                    id="lastName"
                    formControlName="lastName"
                    placeholder="Enter your Last Name"
                    class="form-control"
                    [ngClass]="{'is-invalid': isInvalid('lastName')}"
                  />
                  <div *ngIf="isInvalid('lastName')" class="invalid-feedback">
                    {{ getErrorMessage('lastName') }}
                  </div>
                </div>
            
                <div class="mb-3">
                  <label for="dob" class="form-label">Date of Birth</label>
                  <input
                    id="dob"
                    formControlName="dob"
                    type="date"
                    class="form-control"
                    [ngClass]="{'is-invalid': isInvalid('dob')}"
                  />
                  <div *ngIf="isInvalid('dob')" class="invalid-feedback">
                    {{ getErrorMessage('dob') }}
                  </div>
                </div>
            
                <div class="mb-3">
                  <label for="gender" class="form-label">Gender</label>
                  <select
                    id="gender"
                    formControlName="gender"
                    class="form-select"
                    [ngClass]="{'is-invalid': isInvalid('gender')}"
                  >
                    <option selected disabled value="">Select Gender</option>
                    <option *ngFor="let gender of genderList" [value]="gender.value">
                      {{ gender.label }}
                    </option>
                  </select>
                  <div *ngIf="isInvalid('gender')" class="invalid-feedback">
                    {{ getErrorMessage('gender') }}
                  </div>
                </div>
            
                <div class="mb-3">
                  <label for="contactNumber" class="form-label">Contact Number</label>
                  <input
                    id="contactNumber"
                    formControlName="contactNumber"
                    placeholder="Enter 10 Digit Contact Number"
                    class="form-control"
                    [ngClass]="{'is-invalid': isInvalid('contactNumber')}"
                    (blur)="checkContactExist()"
                  />
                  <div *ngIf="isInvalid('contactNumber')" class="invalid-feedback">
                    {{ getErrorMessage('contactNumber') }}
                  </div>
                </div>
            
                <!-- Step 1 Actions -->
                <div class="d-flex justify-content-between align-items-center">
                  <div class="register" (click)="handleRegister()">Back to Login</div>
                  <button type="button" (click)="nextStep()" class="btn btn-primary">Next</button>
                </div>
              </div>
            
              <!-- Step 2 Fields -->
              <div *ngIf="activeTab === 1">
                <div class="mb-3">
                  <label for="email" class="form-label">Email</label>
                  <input
                    id="email"
                    formControlName="email"
                    (input)="checkMailExist()"
                    placeholder="Enter your Adani email"
                    class="form-control"
                    [ngClass]="{'is-invalid': isInvalid('email')}"
                  />
                  <div *ngIf="isInvalid('email')" class="invalid-feedback">
                    {{ getErrorMessage('email') }}
                  </div>
                </div>
  
                <div class="mb-3">
                  <label for="businessUnitId" class="form-label">Business Unit</label>
                  <select
                    id="businessUnitId"
                    formControlName="businessUnitId"
                    class="form-select"
                    [ngClass]="{'is-invalid': isInvalid('businessUnitId')}"
                    (change)="onBusinessUnitChange(signUpForm.value.businessUnitId)"
                  >
                    <option selected disabled [value]="null">Select Business Unit</option>
                    <option *ngFor="let bu of businessUnitsList" [value]="bu.id">
                      {{ bu.id }} - {{ bu.title }}
                    </option>
                  </select>
                  <div *ngIf="isInvalid('businessUnitId')" class="invalid-feedback">
                    {{ getErrorMessage('businessUnitId') }}
                  </div>
                </div>
            
                <div class="mb-3">
                  <label for="departmentId" class="form-label">Department</label>
                  <select
                    id="departmentId"
                    formControlName="departmentId"
                    class="form-select"
                    [ngClass]="{'is-invalid': isInvalid('departmentId')}"
                  >
                    <option selected disabled value="">Select Department</option>
                    <option *ngFor="let department of filteredDepartmentList" [value]="department.id">
                      {{ department.title }}
                    </option>
                  </select>
                  <div *ngIf="isInvalid('departmentId')" class="invalid-feedback">
                    {{ getErrorMessage('departmentId') }}
                  </div>
                </div>
            
                <div class="mb-3">
                  <label for="designationId" class="form-label">Designation</label>
                  <select
                    id="designationId"
                    formControlName="designationId"
                    class="form-select"
                    [ngClass]="{'is-invalid': isInvalid('designationId')}"
                  >
                    <option selected disabled value="">Select Designation</option>
                    <option *ngFor="let designation of filteredDesignationList" [value]="designation.id">
                      {{ designation.title }}
                    </option>
                  </select>
                  <div *ngIf="isInvalid('designationId')" class="invalid-feedback">
                    {{ getErrorMessage('designationId') }}
                  </div>
                </div>
            
                <div class="mb-3">
                  <label for="plantIds" class="form-label">Plant</label>
                  <select
                    id="plantIds"
                    formControlName="plantIds"
                    class="form-select"
                    [ngClass]="{'is-invalid': isInvalid('plantIds')}"
                  >
                    <option selected disabled value="">Select Plant</option>
                    <option *ngFor="let plant of filteredPlantsList" [value]="plant.id">
                      {{ plant.name }}
                    </option>
                  </select>
                  <div *ngIf="isInvalid('plantIds')" class="invalid-feedback">
                    {{ getErrorMessage('plantIds') }}
                  </div>
                </div>
            
                <!-- Step 2 Actions -->
                <div class="form-actions d-flex justify-content-between align-items-center gap-3">
                  <button type="button" (click)="prevStep()" class="btn btn-secondary">Back</button>
                  <button
                    type="submit"
                    [disabled]="isEmailExist || signUpLoading"
                    class="btn btn-success btn-register"
                  >
                    {{ signUpLoading ? 'Registering...' : 'Register' }}
                  </button>
                </div>
              </div>
            </form>            
        </div>

        <!-- Success Message -->
        <div *ngIf="signUpResponseCode === 200">
            <div class="success-message text-center p-4"> <!-- Added text-center and padding -->
              <i class="bi bi-check-circle-fill text-success" style="font-size: 3rem; margin-bottom: 1rem;"></i> <!-- Success Icon -->
              <h4><b>Registration Submitted</b></h4> <!-- Changed heading -->
              <p class="mb-4">Your request has been sent for approval. You will be notified once approved, after which you can login.</p> <!-- Adjusted message -->
              <button type="button" (click)="handleRegister()" class="btn btn-primary btn-back-to-login">Back To Login</button> <!-- Bootstrap button -->
            </div>
        </div>


      </div> <!-- End register-form -->
    </div> <!-- End boxed -->
  </div> <!-- End wrapper div -->
  }

  <!-- Registration Confirmation Modal -->
  <div class="modal fade" #registerConfirmationModalElement id="registerConfirmationModal" tabindex="-1"
    aria-labelledby="registerConfirmationModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
      <div class="modal-content modelStyle">
        <div class="modal-header bg-primary text-white">
          <h5 class="modal-title" id="registerConfirmationModalLabel">Confirmation</h5>
          <!-- No cross/close button here -->
        </div>
        <div class="modal-body">
          <p>You are about to register with the business unit: <b>{{ selectedBusinessUnitName }}</b>. Do you want to proceed?</p>
        </div>
        <div class="modal-footer justify-content-center">
          <button type="button" class="btn btn-secondary" (click)="closeRegisterConfirmation()">No</button>
          <button type="button" class="btn btn-primary" (click)="confirmRegistration()">Yes</button>
        </div>
      </div>
    </div>
  </div>


  <!-- Cannot Login Dialog -->
  @if(cannotLoginDialog){
  <!-- Consider using a proper Modal component (like ng-bootstrap or material) for dialogs -->
  <div class="dialog-backdrop"> <!-- Added backdrop -->
      <div class="login-dialog-wrapper card p-4"> <!-- Styled as card -->
        <div class="title-wrapper text-center mb-3">
          <h5 class="card-title text-danger">Cannot Login</h5> <!-- Adjusted heading -->
          <h6>This email is registered as a BOG User. Please contact your plant admin for Admin access.</h6>
        </div>
        <div class="admin-info border rounded p-3 mb-3">
          <p class="mb-1"><strong>Plant Admin Details:</strong></p> <!-- Added context -->
          <p class="mb-1"><strong>Name:</strong> {{ cannotLoginData?.firstName }} {{ cannotLoginData?.lastName }}</p>
          <p class="mb-1"><strong>Plant:</strong> {{ cannotLoginData?.plant }}</p>
          <p class="mb-1"><strong>Email:</strong> {{ cannotLoginData?.email }}</p>
          <p class="mb-0"><strong>Contact:</strong> {{ cannotLoginData?.contactNumber }}</p>
        </div>
        <h6 class="text-center mb-3">You can download the BOG app and login as a user:</h6>
        <div class="appstore-wrapper d-flex justify-content-center align-items-center gap-4 mb-4">
          <!-- Consider making QR code larger or more prominent -->
          <img alt="App Store QR" src="assets/img/qrcode_appStore.png" class="app-qr img-thumbnail" style="max-width: 100px;"> <!-- Added img-thumbnail -->
          <img alt="Google Play QR" src="assets/img/appStore_black.png" style="max-height: 40px;"/> <!-- Adjusted size -->
        </div>
        <button (click)="handleClose()" class="btn btn-secondary w-100">OK</button> <!-- Bootstrap OK button -->
      </div>
  </div>
  }

</div>