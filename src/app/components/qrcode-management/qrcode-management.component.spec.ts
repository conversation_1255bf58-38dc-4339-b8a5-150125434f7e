import { ComponentFixture, TestBed } from '@angular/core/testing';

import { QrcodeManagementComponent } from './qrcode-management.component';

describe('QrcodeManagementComponent', () => {
  let component: QrcodeManagementComponent;
  let fixture: ComponentFixture<QrcodeManagementComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [QrcodeManagementComponent]
    })
    .compileComponents();

    fixture = TestBed.createComponent(QrcodeManagementComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
