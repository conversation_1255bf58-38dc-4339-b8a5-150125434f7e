<app-toast-message></app-toast-message> <!-- Ensure ToastMessageComponent is present -->

<app-tab [tabs]="tabs" [selectedTabIndex]="selectedTabIndex" (tabSelected)="onTabSelected($event)">
</app-tab>

<!-- Common Header Structure (Repeated for each tab section below) -->
<ng-template #tableHeader>
    <div class="row align-items-center">
        <div class="col d-flex align-items-center">
            <!-- Title will be specific to each tab -->
            <h6 class="mb-0">List of {{ tabs[selectedTabIndex]?.title || 'QR Codes' }}</h6>
        </div>
        <div class="col text-end d-flex align-items-center justify-content-end">

            <!-- *** UPDATED: Download Excel Button with Dropdown *** -->
            <div ngbDropdown class="d-inline-block me-3">
                <button type="button" class="btn adani-btn dropdown-toggle" id="downloadExcelDropdown" ngbDropdownToggle
                    [disabled]="isDownloadingExcel || listLoading">
                    <span *ngIf="!isDownloadingExcel">
                        <i class="fas fa-file-excel me-1"></i> Download Excel
                    </span>
                    <span *ngIf="isDownloadingExcel">
                        <span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
                        Downloading {{ downloadType === 'current' ? 'Page' : (downloadType === 'all' ? 'All' : '') }}...
                    </span>
                </button>
                <ul ngbDropdownMenu aria-labelledby="downloadExcelDropdown">
                    <li>
                        <button ngbDropdownItem (click)="downloadExcel('current')" [disabled]="isDownloadingExcel || listLoading || (getCurrentListData()?.length ?? 0) === 0">
                            <i class="fas fa-file-download me-1"></i> Download Current Page ({{ getCurrentListData()?.length ?? 0 }})
                        </button>
                    </li>
                    <li>
                        <button ngbDropdownItem (click)="downloadExcel('all')" [disabled]="isDownloadingExcel || listLoading || totalItems === 0">
                            <i class="fas fa-server me-1"></i> Download All Filtered ({{ totalItems }})
                        </button>
                    </li>
                </ul>
            </div>
            <!-- *** END: Download Excel Button Update *** -->

            <img src="../../../assets/svg/filter.svg" class="filter-button ms-0" (click)="openFilterModal()" alt="Filter"
                style="width: 35px; cursor: pointer;" />
        </div>
    </div>
</ng-template>


<!--Activated QR-->
<div *ngIf="selectedTabIndex == 0" class="card" id="activated-qrcode">
    <div class="card-header">
        <ng-container *ngTemplateOutlet="tableHeader"></ng-container> <!-- Use common header -->
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered table-hover">
                <thead class="table-header">
                    <tr class="text-center">
                        <th scope="col" style="width: 15%;">Actions</th>
                        <th scope="col" style="width: 50%;">QR and Details</th>
                        <th scope="col" style="width: 35%;">QR Code</th>
                    </tr>
                </thead>
                <tbody>
                    <tr *ngIf="listLoading">
                        <td colspan="3" class="text-center p-4">
                            <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                            Loading activated QR codes...
                        </td>
                    </tr>
                    <tr *ngIf="!listLoading && (!activated_qrcode_list || activated_qrcode_list.length === 0)">
                        <td colspan="3" class="text-center p-4 text-muted">
                            No activated QR codes found matching the current filters.
                        </td>
                    </tr>
                    <tr *ngFor="let qr of activated_qrcode_list">
                        <td class="actions text-center">
                            <button class="btn btn-sm adani-btn" (click)="openEditModal(qr)" title="Edit QR Code">
                                <i class="bi bi-pencil edit"></i> Edit
                            </button>
                        </td>
                        <td>
                            <div class="details-container">
                                <p class="label-value"><strong>Last Scan By:</strong> <span class="value-text">{{ qr.lastScanByAdmin?.firstName || qr.lastScanByAdminId || 'N/A' }}</span></p>
                                <p class="label-value"><strong>Last Scan Date:</strong> <span class="value-text">{{ qr.lastScanDate ? (qr.lastScanDate | date:'medium') : 'N/A' }}</span></p>
                                <p class="label-value"><strong>Latitude:</strong> <span class="value-text">{{ qr.lat || 'N/A' }}</span></p>
                                <p class="label-value"><strong>Longitude:</strong> <span class="value-text">{{ qr.long || 'N/A' }}</span></p>
                                <p class="label-value"><strong>No Of Scan:</strong> <span class="value-text">{{ qr.noOfScan ?? 0 }}</span></p>
                                <p class="label-value"><strong>QR Type:</strong> <span class="value-text">{{ qr.qrType?.title || qr.qrTypeId || 'N/A' }}</span></p>
                                <p class="label-value"><strong>Zone Area:</strong> <span class="value-text">{{ qr.zoneArea || 'N/A' }}</span></p>
                                <p class="label-value"><strong>Zone:</strong> <span class="value-text">{{ qr.zone?.zoneName || qr.zoneId || 'N/A' }}</span></p>
                                <p class="label-value"><strong>UUID:</strong> <span class="value-text">{{ qr.uuid || 'N/A' }}</span></p>
                            </div>
                        </td>
                        <td class="qr-data text-center align-middle"> <!-- Centered QR -->
                            <img *ngIf="qr.qrCode" [src]="qr.qrCode" alt="QR Code" class="img-fluid rounded qr-img"
                                [width]="100" [height]="100">
                            <span *ngIf="!qr.qrCode" class="text-muted small">No Image</span>
                            <div class="text-center mt-2">
                                <span *ngIf="qr.type === 0" class="badge bg-success text-white">Permanent QR</span>
                                <span *ngIf="qr.type === 2" class="badge bg-warning text-white">Temporary QR</span>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
    <div class="card-footer text-muted text-center">
        <app-pagination [currentPage]="currentPage" [totalItems]="totalItems" [itemsPerPage]="itemsPerPage"
            (pageChange)="onPageChange($event)"></app-pagination>
    </div>
</div>

<!-- Unregistered QR-->
<div *ngIf="selectedTabIndex == 1" class="card">
    <div class="card-header">
         <ng-container *ngTemplateOutlet="tableHeader"></ng-container> <!-- Use common header -->
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered table-hover">
                 <thead class="table-header">
                    <tr class="text-center">
                        <th scope="col" style="width: 15%;">Actions</th>
                        <th scope="col" style="width: 50%;">QR and Details</th>
                        <th scope="col" style="width: 35%;">QR Code</th>
                    </tr>
                </thead>
                <tbody>
                    <tr *ngIf="listLoading">
                        <td colspan="3" class="text-center p-4">
                            <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                            Loading unregistered QR codes...
                        </td>
                    </tr>
                     <tr *ngIf="!listLoading && (!unregistered_qrcode_list || unregistered_qrcode_list.length === 0)">
                        <td colspan="3" class="text-center p-4 text-muted">
                             No unregistered QR codes found matching the current filters.
                        </td>
                    </tr>
                    <tr *ngFor="let qr of unregistered_qrcode_list">
                        <td class="actions text-center">
                             <button class="adani-btn" (click)="openEditModal(qr)" title="Edit QR Code">
                                <i class="bi bi-pencil edit"></i> Edit
                            </button>
                        </td>
                       <td>
                            <div class="details-container">
                                <!-- Unregistered QRs might have less info -->
                                <p class="label-value"><strong>QR Type:</strong> <span class="value-text">{{ qr.qrType?.title || qr.qrTypeId || 'N/A' }}</span></p>
                                <p class="label-value"><strong>Zone Area:</strong> <span class="value-text">{{ qr.zoneArea || 'N/A' }}</span></p>
                                <p class="label-value"><strong>Zone:</strong> <span class="value-text">{{ qr.zone?.zoneName || qr.zoneId || 'N/A' }}</span></p>
                                <p class="label-value"><strong>UUID:</strong> <span class="value-text">{{ qr.uuid || 'N/A' }}</span></p>
                                <p class="label-value"><strong>Created:</strong> <span class="value-text">{{ qr.createdTimestamp ? (qr.createdTimestamp | date:'medium') : 'N/A' }}</span></p>
                            </div>
                        </td>
                        <td class="qr-data text-center align-middle">
                            <img *ngIf="qr.qrCode" [src]="qr.qrCode" alt="QR Code" class="img-fluid rounded qr-img"
                                [width]="100" [height]="100">
                             <span *ngIf="!qr.qrCode" class="text-muted small">No Image</span>
                             <div class="text-center mt-2">
                                <span *ngIf="qr.type === 0" class="badge bg-success text-white">Permanent QR</span>
                                <span *ngIf="qr.type === 2" class="badge bg-warning text-white">Temporary QR</span>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
    <div class="card-footer text-muted text-center">
        <app-pagination [currentPage]="currentPage" [totalItems]="totalItems" [itemsPerPage]="itemsPerPage"
            (pageChange)="onPageChange($event)"></app-pagination>
    </div>
</div>

<!-- Deactivated QR (No Edit Button) -->
<div *ngIf="selectedTabIndex == 2" class="card">
     <div class="card-header">
         <ng-container *ngTemplateOutlet="tableHeader"></ng-container> <!-- Use common header -->
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered table-hover">
                 <thead class="table-header">
                    <tr class="text-center">
                        <!-- No Actions column -->
                        <th scope="col" style="width: 65%;">QR and Details</th>
                        <th scope="col" style="width: 35%;">QR Code</th>
                    </tr>
                </thead>
                <tbody>
                     <tr *ngIf="listLoading">
                        <td colspan="2" class="text-center p-4">
                            <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                            Loading deactivated QR codes...
                        </td>
                    </tr>
                     <tr *ngIf="!listLoading && (!deactivated_qrcode_list || deactivated_qrcode_list.length === 0)">
                        <td colspan="2" class="text-center p-4 text-muted">
                           No deactivated QR codes found matching the current filters.
                        </td>
                    </tr>
                    <tr *ngFor="let qr of deactivated_qrcode_list">
                         <td>
                            <div class="details-container">
                                <p class="label-value"><strong>Last Scan By:</strong> <span class="value-text">{{ qr.lastScanByAdmin?.firstName || qr.lastScanByAdminId || 'N/A' }}</span></p>
                                <p class="label-value"><strong>Last Scan Date:</strong> <span class="value-text">{{ qr.lastScanDate ? (qr.lastScanDate | date:'medium') : 'N/A' }}</span></p>
                                <p class="label-value"><strong>Latitude:</strong> <span class="value-text">{{ qr.lat || 'N/A' }}</span></p>
                                <p class="label-value"><strong>Longitude:</strong> <span class="value-text">{{ qr.long || 'N/A' }}</span></p>
                                <p class="label-value"><strong>No Of Scan:</strong> <span class="value-text">{{ qr.noOfScan ?? 0 }}</span></p>
                                <p class="label-value"><strong>QR Type:</strong> <span class="value-text">{{ qr.qrType?.title || qr.qrTypeId || 'N/A' }}</span></p>
                                <p class="label-value"><strong>Zone Area:</strong> <span class="value-text">{{ qr.zoneArea || 'N/A' }}</span></p>
                                <p class="label-value"><strong>Zone:</strong> <span class="value-text">{{ qr.zone?.zoneName || qr.zoneId || 'N/A' }}</span></p>
                                <p class="label-value"><strong>UUID:</strong> <span class="value-text">{{ qr.uuid || 'N/A' }}</span></p>
                            </div>
                        </td>
                        <td class="qr-data text-center align-middle">
                            <img *ngIf="qr.qrCode" [src]="qr.qrCode" alt="QR Code" class="img-fluid rounded qr-img"
                                [width]="100" [height]="100">
                            <span *ngIf="!qr.qrCode" class="text-muted small">No Image</span>
                             <div class="text-center mt-2">
                                <span *ngIf="qr.type === 0" class="badge bg-success text-white">Permanent QR</span>
                                <span *ngIf="qr.type === 2" class="badge bg-warning text-white">Temporary QR</span>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
    <div class="card-footer text-muted text-center">
        <app-pagination [currentPage]="currentPage" [totalItems]="totalItems" [itemsPerPage]="itemsPerPage"
            (pageChange)="onPageChange($event)"></app-pagination>
    </div>
</div>

<!--Deleted QR (No Edit Button) -->
<div *ngIf="selectedTabIndex == 3" class="card">
     <div class="card-header">
        <ng-container *ngTemplateOutlet="tableHeader"></ng-container> <!-- Use common header -->
    </div>
     <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered table-hover">
                <thead class="table-header">
                    <tr class="text-center">
                         <!-- No Actions column -->
                        <th scope="col" style="width: 65%;">QR and Details</th>
                        <th scope="col" style="width: 35%;">QR Code</th>
                    </tr>
                </thead>
                <tbody>
                    <tr *ngIf="listLoading">
                        <td colspan="2" class="text-center p-4">
                            <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                            Loading deleted QR codes...
                        </td>
                    </tr>
                     <tr *ngIf="!listLoading && (!deleted_qrcode_list || deleted_qrcode_list.length === 0)">
                        <td colspan="2" class="text-center p-4 text-muted">
                            No deleted QR codes found matching the current filters.
                        </td>
                    </tr>
                    <tr *ngFor="let qr of deleted_qrcode_list">
                        <td>
                            <div class="details-container">
                                <p class="label-value"><strong>Last Scan By:</strong> <span class="value-text">{{ qr.lastScanByAdmin?.firstName || qr.lastScanByAdminId || 'N/A' }}</span></p>
                                <p class="label-value"><strong>Last Scan Date:</strong> <span class="value-text">{{ qr.lastScanDate ? (qr.lastScanDate | date:'medium') : 'N/A' }}</span></p>
                                <p class="label-value"><strong>Latitude:</strong> <span class="value-text">{{ qr.lat || 'N/A' }}</span></p>
                                <p class="label-value"><strong>Longitude:</strong> <span class="value-text">{{ qr.long || 'N/A' }}</span></p>
                                <p class="label-value"><strong>No Of Scan:</strong> <span class="value-text">{{ qr.noOfScan ?? 0 }}</span></p>
                                <p class="label-value"><strong>QR Type:</strong> <span class="value-text">{{ qr.qrType?.title || qr.qrTypeId || 'N/A' }}</span></p>
                                <p class="label-value"><strong>Zone Area:</strong> <span class="value-text">{{ qr.zoneArea || 'N/A' }}</span></p>
                                <p class="label-value"><strong>Zone:</strong> <span class="value-text">{{ qr.zone?.zoneName || qr.zoneId || 'N/A' }}</span></p>
                                <p class="label-value"><strong>UUID:</strong> <span class="value-text">{{ qr.uuid || 'N/A' }}</span></p>
                            </div>
                        </td>
                         <td class="qr-data text-center align-middle">
                            <img *ngIf="qr.qrCode" [src]="qr.qrCode" alt="QR Code" class="img-fluid rounded qr-img"
                                [width]="100" [height]="100">
                             <span *ngIf="!qr.qrCode" class="text-muted small">No Image</span>
                             <div class="text-center mt-2">
                                <span *ngIf="qr.type === 0" class="badge bg-success text-white">Permanent QR</span>
                                <span *ngIf="qr.type === 2" class="badge bg-warning text-white">Temporary QR</span>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
    <div class="card-footer text-muted text-center">
        <app-pagination [currentPage]="currentPage" [totalItems]="totalItems" [itemsPerPage]="itemsPerPage"
            (pageChange)="onPageChange($event)"></app-pagination>
    </div>
</div>

<!-- Filter Offcanvas (No changes needed here) -->
<app-offcanvas [title]="'Filter QR Codes'" *ngIf="isFilterModalOpen" (onClickCross)="closeFilterModal()">
    <div class="filter-container p-3">
        <form #filterForm="ngForm" (ngSubmit)="applyFilters()">
            <div class="row g-3">
                 <div class="col-12">
                    <label for="filterNoOfScan" class="form-label">No Of Scans (Exact)</label>
                    <input type="number" class="form-control" id="filterNoOfScan"
                        placeholder="Enter exact number of scans" min="0" [(ngModel)]="filters.noOfScan"
                        name="noOfScan">
                </div>
                <div class="col-12">
                    <label for="filterLongitude" class="form-label">Longitude (Exact)</label>
                    <input type="number" class="form-control" id="filterLongitude" placeholder="Enter Longitude"
                        [(ngModel)]="filters.longitude" name="longitude" step="any">
                </div>
                <div class="col-12">
                    <label for="filterLatitude" class="form-label">Latitude (Exact)</label>
                    <input type="number" class="form-control" id="filterLatitude" placeholder="Enter Latitude"
                        [(ngModel)]="filters.latitude" name="latitude" step="any">
                </div>
                <div class="col-12">
                    <label for="filterZoneArea" class="form-label">Zone Area (Contains)</label>
                    <input type="text" class="form-control" id="filterZoneArea" placeholder="Search Zone Area text"
                        [(ngModel)]="filters.zoneArea" name="zoneArea" maxlength="200"
                        (blur)="trimInputField(filters, 'zoneArea')">
                    <small *ngIf="filters && filters.zoneArea" class="text-muted">
                        {{ filters.zoneArea.length }}/200 characters
                    </small>
                </div>
                <div class="col-12">
                    <label for="filterPlantId" class="form-label">
                        {{ currentUserRole === componentRoles.PLANT_ADMIN ? 'Select Your Plant' : 'Select Plant' }}
                    </label>
                    <select class="form-select" id="filterPlantId"
                            [(ngModel)]="filters.plantId" name="plantId"
                            (ngModelChange)="onPlantSelectionChange($event)">
                        <!-- No longer disabled -->
                        <!-- Show 'All Plants' only for Super Admin -->
                        <option [ngValue]="null" *ngIf="currentUserRole !== componentRoles.PLANT_ADMIN">All Plants</option>
                        <!-- Add a default selection prompt for Plant Admin -->
                        <option [ngValue]="null" *ngIf="currentUserRole === componentRoles.PLANT_ADMIN">-- Select Your Plant --</option>
                        <!-- availablePlants is now filtered correctly in TS -->
                        <option *ngFor="let plant of availablePlants" [value]="plant.id">{{ plant.name }}</option>
                        <!-- Message if Plant Admin has no plants available -->
                        <option [ngValue]="null" *ngIf="currentUserRole === componentRoles.PLANT_ADMIN && availablePlants.length === 0" disabled>No plants assigned or available</option>
                    </select>
                </div>
                <div class="col-12">
                    <label for="filterZoneId" class="form-label">Select Zone</label>
                    <select class="form-select" id="filterZoneId" [(ngModel)]="filters.zoneId" name="zoneId"
                            [disabled]="!filters.plantId"> <!-- Disable if no plant selected -->
                         <option [ngValue]="null">
                            {{ filters.plantId ? (availableZones.length > 0 ? 'All Zones in Plant' : 'No zones found') : 'Select Plant First' }}
                        </option>
                        <option *ngFor="let zone of availableZones" [value]="zone.id">{{ zone.zoneName }}</option>
                    </select>
                </div>
                <div class="col-12">
                    <label for="filterEnabledQR" class="form-label">Enabled Status</label>
                    <select class="form-select" id="filterEnabledQR" [(ngModel)]="filters.enabled" name="enabled">
                        <option [ngValue]="null">Any</option>
                        <option value="true">Yes</option>
                        <option value="false">No</option>
                    </select>
                </div>
                <div class="col-12">
                    <label for="filterSortByQR" class="form-label">Sort By</label>
                    <select class="form-select" id="filterSortByQR" [(ngModel)]="filters.sortField" name="sortField">
                        <option value="id">Default (ID)</option> <!-- Changed default label -->
                        <option *ngFor="let field of availableSortFields" [value]="field.value">{{ field.label }}
                        </option>
                    </select>
                    <label for="filterSortDirQR" class="form-label mt-2">Sort Direction</label>
                    <select class="form-select" id="filterSortDirQR" [(ngModel)]="filters.sortDirection"
                        name="sortDirection">
                        <option value="DESC">Descending</option>
                        <option value="ASC">Ascending</option>
                    </select>
                </div>
                <div class="col-12 mt-4 d-grid gap-2">
                    <button type="submit" class="btn adani-btn">
                        <i class="bi bi-search me-1"></i> Apply Filters
                    </button>
                    <button type="button" class="btn btn-secondary" (click)="resetFilters()">
                        <i class="bi bi-arrow-clockwise me-1"></i> Reset Filters
                    </button>
                </div>
            </div>
        </form>
    </div>
</app-offcanvas>


<!-- Edit Offcanvas (Template-Driven) -->
<app-offcanvas [title]="'Edit QR Code'" *ngIf="isEditModalOpen" (onClickCross)="closeEditModal()">
    <div class="edit-container p-3">
        <div *ngIf="!editFormData && isEditModalOpen" class="text-center p-5">
            <div class="spinner-border" role="status"> <!-- Slightly larger spinner -->
                <span class="visually-hidden">Loading QR Details...</span>
            </div>
            <p class="mt-2 text-muted">Loading details...</p>
        </div>

        <form *ngIf="editFormData" #editForm="ngForm" (ngSubmit)="submitEditForm()">
             <div class="row g-3">
                <div class="col-12">
                    <label class="form-label" for="editQrId">QR ID</label>
                    <input type="text" id="editQrId" class="form-control"
                           [value]="editFormData.id" name="id" readonly disabled>
                </div>

                <div class="col-12">
                    <label for="editQrCodeText" class="form-label">QR Code Identifier</label>
                    <input type="text" class="form-control" id="editQrCodeText" name="qrCodeTextDisplay" placeholder="QR Identifier (e.g., UUID)"
                           [value]="editFormData.qrCodeText" readonly disabled> <!-- Use qrCodeText from form data -->
                </div>

                <div class="col-12">
                    <label for="editNoOfScan" class="form-label">No. Of Scans</label>
                    <input type="number" class="form-control" id="editNoOfScan" name="noOfScanDisplay" placeholder="Number of Scans"
                           [value]="editFormData.noOfScan" readonly disabled>
                </div>

                <div class="col-12">
                    <label for="editStatus" class="form-label">Status <span class="text-danger">*</span></label>
                    <select class="form-select" id="editStatus" name="status"
                           [(ngModel)]="editFormData.status" required
                           #statusInput="ngModel"
                           [class.is-invalid]="statusInput.invalid && (statusInput.dirty || statusInput.touched)">
                        <option [ngValue]="null" disabled>Select Status</option>
                        <option [ngValue]="0">Unregistered</option>
                        <option [ngValue]="1">Activated</option>
                        <option [ngValue]="2">Deactivated</option>
                    </select>
                    <div *ngIf="statusInput.invalid && (statusInput.dirty || statusInput.touched)" class="invalid-feedback">
                        Status is required.
                    </div>
                </div>
                 <div class="col-12">
                    <label for="editPlantId" class="form-label">Assign Plant</label>
                    <select class="form-select" id="editPlantId" name="plantId"
                           [(ngModel)]="editFormData.plantId"
                           (ngModelChange)="onEditPlantSelectionChange($event)">
                        <!-- No longer disabled -->
                        <!-- Allow unassigning only for Super Admin -->
                        <option [ngValue]="null" *ngIf="currentUserRole !== componentRoles.PLANT_ADMIN">-- No Plant --</option>
                        <!-- Plant Admin must select one of theirs -->
                        <option [ngValue]="null" *ngIf="currentUserRole === componentRoles.PLANT_ADMIN && !editFormData?.plantId">-- Select Plant --</option>
                        <!-- availablePlants should contain only allowed plants for Plant Admin -->
                        <option *ngFor="let plant of availablePlants" [value]="plant.id">{{ plant.name }}</option>
                        <option [ngValue]="null" *ngIf="currentUserRole === componentRoles.PLANT_ADMIN && availablePlants.length === 0" disabled>No plants assigned/available</option>
                    </select>
                </div>
                <div class="col-12">
                    <label for="editZoneId" class="form-label">Assign Zone</label>
                    <select class="form-select" id="editZoneId" name="zoneId"
                           [(ngModel)]="editFormData.zoneId"
                           [disabled]="!editFormData.plantId"> <!-- Keep disabled if no plant -->
                        <option [ngValue]="null">
                          {{ !editFormData.plantId ? 'Select Plant First' : '-- No Zone --' }}
                        </option>
                        <option *ngFor="let zone of editAvailableZones" [value]="zone.id">{{ zone.zoneName }}</option>
                        <option [ngValue]="null" *ngIf="editFormData.plantId && editAvailableZones.length === 0" disabled>No zones found for plant</option>
                    </select>
                </div>

                <div class="col-12">
                    <label for="editZoneArea" class="form-label">Zone Area Description</label>
                    <input type="text" class="form-control" id="editZoneArea" name="zoneArea" placeholder="e.g., Near Entrance A"
                           [(ngModel)]="editFormData.zoneArea" maxlength="200"
                           (blur)="trimInputField(editFormData, 'zoneArea')">
                    <small *ngIf="editFormData && editFormData.zoneArea" class="text-muted">
                        {{ editFormData.zoneArea.length }}/200 characters
                    </small>
                </div>

                <div class="col-12">
                    <label for="editQrTypeId" class="form-label">QR Type</label>
                    <select class="form-select" id="editQrTypeId" name="qrTypeId"
                           [(ngModel)]="editFormData.qrTypeId">
                        <option [ngValue]="null">-- No Type --</option>
                        <option *ngFor="let type of availableQrTypes" [value]="type.id">{{ type.title }}</option>
                    </select>
                </div>

                <div class="col-12">
                    <label for="editLat" class="form-label">Latitude</label>
                    <input type="number" step="any" class="form-control" id="editLat" name="lat" placeholder="e.g., 23.0225"
                           [(ngModel)]="editFormData.lat">
                </div>

                <div class="col-12">
                    <label for="editLong" class="form-label">Longitude</label>
                    <input type="number" step="any" class="form-control" id="editLong" name="long" placeholder="e.g., 72.5714"
                           [(ngModel)]="editFormData.long">
                </div>
                
                <div class="col-6 d-flex align-items-center"> <!-- Enabled Switch -->
                    <div class="form-check form-switch mt-2"> <!-- Adjusted margin -->
                      <app-switch [(checked)]="editFormData.enabled" name="enabled" id="editQrEnabled" onLabel="Enabled" offLabel="Disabled"></app-switch>
                    </div>
                 </div>

                 <div class="col-6 d-flex align-items-center"> <!-- Flag Switch -->
                     <div class="form-check form-switch mt-2"> <!-- Adjusted margin -->
                       <app-switch [(checked)]="editFormData.flag" name="flag" id="editQrFlag" onLabel="Flagged" offLabel="Not Flagged"></app-switch>
                     </div>
                 </div>

                <!-- Action Buttons -->
                <div class="col-12 mt-4 d-flex justify-content-end gap-2">
                     <button type="button" class="btn btn-secondary" (click)="closeEditModal()" [disabled]="editLoading">
                          Cancel
                      </button>
                    <button type="submit" class="btn adani-btn" [disabled]="editForm.invalid || editLoading">
                       <span *ngIf="!editLoading"><i class="bi bi-save me-1"></i> Save Changes</span>
                       <span *ngIf="editLoading">
                           <span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
                           Saving...
                       </span>
                    </button>
                </div>
            </div> <!-- End row -->
        </form> <!-- End form -->
    </div> <!-- End container -->
</app-offcanvas>
<!-- ********** END: Edit Offcanvas ********** -->
