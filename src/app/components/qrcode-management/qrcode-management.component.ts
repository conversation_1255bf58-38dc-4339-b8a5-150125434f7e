import { Component, OnInit, ViewChild } from '@angular/core';
import { FormsModule, NgForm } from '@angular/forms'; // For Template-Driven Forms [(ngModel), #editForm]
import { TabComponent } from "../../shared/tab/tab.component";
import { OffcanvasComponent } from "../../shared/offcanvas/offcanvas.component";
import { CommonModule } from '@angular/common';
import { QrCodeService } from '../../services/qr-code/qr-code.service';
import { QrTypeService } from '../../services/master-management/qr-type/qr-type.service';
import { UpdateService } from '../../services/update/update.service'; // Service for updating records
import { createAxiosConfig } from '../../core/utilities/axios-param-config';
import { PaginationComponent } from "../../shared/pagination/pagination.component";
import { PlantManagementService } from '../../services/plant-management/plant-management.service';
import { ZoneService } from '../../services/zone/zone.service';
import { SwitchComponent } from "../../shared/switch/switch.component"; // For Enabled/Flag switches
import { ToastMessageComponent } from "../../shared/toast-message/toast-message.component"; // For user feedback
import * as XLSX from 'xlsx'; // Import the library
import { NgbDropdownModule } from '@ng-bootstrap/ng-bootstrap'; // Import NgbDropdownModule

// --- Define ROLES constant (adjust values if needed) ---
const ROLES = {
    SUPER_ADMIN: 'super_admin',
    PLANT_ADMIN: 'plant_admin',
};

// --- Interfaces ---

// Structure for filtering the QR code list
interface QrCodeFilter {
    noOfScan?: number | null;
    longitude?: number | null;
    latitude?: number | null;
    zoneArea?: string | null;
    plantId?: number | null; // User selected plant (from filtered list if Plant Admin)
    zoneId?: number | null;
    enabled?: string | null; // 'true', 'false', or null for select binding
    sortField?: string | null;
    sortDirection?: 'ASC' | 'DESC';
}

// Generic interface for simple master data (like QR Type)
interface SimpleMaster {
    id: number;
    title: string;
}

// Interface for Plant data
interface Plant {
    id: number;
    name: string;
    // Add other plant properties if needed
}

// Interface for Zone data
interface Zone {
    id: number;
    zoneName: string;
    plantId?: number; // Useful for filtering zones based on selected plant
    // Add other zone properties if needed
}

// Interface for Admin User data (used in lastScanBy)
interface AdminUser {
    id: number;
    firstName: string;
    // other relevant user fields if needed
}

// Interface matching the 'data' object structure in the required payload
// Also represents the detailed data received from the GET API for a single QR code
export interface QrCode {
    id: number;
    enabled: boolean;
    isDeleted: boolean;
    noOfScan: number;
    type?: number | null;
    uuid?: string | null;
    isFlag: boolean;
    lastScanDate?: string | null; // ISO Date string
    lastScanById?: number | null;
    lastScanByAdminId?: number | null;
    qrCode?: string | null; // Base64 Data URL image string from API
    qrCodeData?: any | null;
    qrCodePdf?: any | null;
    lat: string | number | null;
    long: string | number | null;
    status: number; // 0=Unreg, 1=Active, 2=Deactivated
    beaconStatus?: any | null;
    plantId?: number | null;
    segmentId?: any | null;
    locationId?: any | null;
    locationTypeId?: any | null;
    qrTypeId?: number | null;
    zoneId?: number | null;
    zoneArea: string | null;
    qrDetail?: any | null;
    remarks?: any | null;
    fromDate?: string | null;
    toDate?: string | null;
    priority?: any | null;
    createdBy?: number | null;
    createdTimestamp?: string | null; // ISO Date string
    updatedBy?: number | null;
    updatedTimestamp?: string | null; // ISO Date string
    // Optional nested objects received from GET API (useful for display)
    plant?: Plant | null;
    zone?: Zone | null;
    qrType?: SimpleMaster | null;
    lastScanByAdmin?: AdminUser | null;
    segment?: any | null;
    location?: any | null;
    locationType?: any | null;
}

// Interface for data specifically bound to the Edit Form via [(ngModel)]
interface EditFormData {
    id: number; // Needed for reference
    enabled: boolean; // Bound to switch
    flag: boolean; // Bound to switch (maps to isFlag)
    // Readonly fields for display in form (optional)
    qrCodeText?: string | null;
    noOfScan: number;
    // Editable fields
    status: number | null; // 0, 1, 2 (allow null for default dropdown option)
    plantId?: number | null; // Editable
    zoneId?: number | null; // Editable (dependent on plantId)
    zoneArea: string | null; // Editable
    qrTypeId?: number | null; // Editable
    lat: number | null; // Editable, bind to number input
    long: number | null; // Editable, bind to number input
}


@Component({
    selector: 'app-qrcode-management',
    standalone: true,
    imports: [
        CommonModule,
        FormsModule, // Essential for template-driven forms [(ngModel), #editForm]
        TabComponent,
        OffcanvasComponent,
        PaginationComponent,
        SwitchComponent, // For the status/flag switches
        ToastMessageComponent, // Import Toast Message Component
        NgbDropdownModule, // For dropdowns
    ],
    templateUrl: './qrcode-management.component.html',
    styleUrl: './qrcode-management.component.scss'
})
export class QrcodeManagementComponent implements OnInit {
    // --- Expose ROLES constant to the template ---
    public componentRoles = ROLES;

    // --- ViewChild References ---
    @ViewChild('editForm') editForm?: NgForm;
    @ViewChild(ToastMessageComponent) toast!: ToastMessageComponent;

    // --- State Variables ---
    isFilterModalOpen = false;
    isDownloadingExcel = false;
    downloadType: 'current' | 'all' | null = null;
    isEditModalOpen = false;
    listLoading = false;
    editLoading = false;
    // zoneLoading = false; // Optional: Add if specific zone loading indicator is needed

    // --- Data Lists for Tabs ---
    activated_qrcode_list: QrCode[] = [];
    unregistered_qrcode_list: QrCode[] = [];
    deactivated_qrcode_list: QrCode[] = [];
    deleted_qrcode_list: QrCode[] = [];

    // --- Pagination & User Info ---
    currentPage = 1;
    itemsPerPage = 10;
    totalItems = 0;
    loggedInUserId: number | null = null; // ID of logged-in user for 'createdBy' etc.

    // --- Role-Based Access Control ---
    currentUserRole: string = ''; // e.g., 'super_admin', 'plant_admin'
    loggedInAdminId: number | null = null; // Added based on your method
    loggedInPlantIds: number[] = []; // Store all assigned plant IDs for Plant Admin

    // --- Tab Configuration ---
    selectedTabIndex = 0;
    tabs = [
        { title: 'Activated QR', listKey: 'activated_qrcode_list' as const, baseFilter: ['enabled||eq||true', 'isDeleted||eq||false', 'status||eq||1'] },
        { title: 'Unregistered QR', listKey: 'unregistered_qrcode_list' as const, baseFilter: ['enabled||eq||true', 'isDeleted||eq||false', 'status||eq||0'] },
        { title: 'Deactivated QR', listKey: 'deactivated_qrcode_list' as const, baseFilter: ['enabled||eq||true', 'isDeleted||eq||false', 'status||eq||2'] },
        { title: 'Deleted QR', listKey: 'deleted_qrcode_list' as const, baseFilter: ['isDeleted||eq||true'] },
    ];

    // --- Sorting Fields Available ---
    availableSortFields = [
        { value: 'id', label: 'ID' },
        { value: 'noOfScan', label: 'No. of Scans' },
        { value: 'plantId', label: 'Plant' },
        { value: 'zoneId', label: 'Zone' },
        { value: 'createdTimestamp', label: 'Created Date' }
    ];

    // --- Filter State ---
    filters: QrCodeFilter = {
        noOfScan: null, longitude: null, latitude: null, zoneArea: null,
        plantId: null, zoneId: null, enabled: null,
        sortField: 'id', sortDirection: 'DESC'
    };

    // --- Data Sources for Dropdowns ---
    availablePlants: Plant[] = []; // Filtered based on role
    availableZones: Zone[] = []; // For FILTER dropdown (depends on selected plant)
    availableQrTypes: SimpleMaster[] = [];
    editAvailableZones: Zone[] = []; // Separate list for EDIT form zone dropdown (depends on selected plant)

    // --- Editing State ---
    editFormData: EditFormData | null = null;
    originalQrCodeData: QrCode | null = null; // Store original data before edit


    constructor(
        private readonly qrCodeService: QrCodeService,
        readonly plantService: PlantManagementService,
        private readonly zoneService: ZoneService,
        private readonly qrTypeService: QrTypeService,
        private readonly updateService: UpdateService,
    ) { }


    ngOnInit() {
        // --- Set role and plant IDs first ---
        this.setCurrentUserRoleAndDetailsById();

        // Set the logged-in user ID for potential use (e.g., update payload)
        this.loggedInUserId = this.loggedInAdminId;

        // Load initial data
        this.getPlants(); // Fetches plants based on role
        this.loadQrTypes();
        this.loadQrCodesForCurrentTab(); // Fetches QR codes based on initial filters (plantId likely null initially)

        // Note: Zone loading is triggered by plant selection change handlers
    }

    // --- Role Setup Method ---
    private setCurrentUserRoleAndDetailsById(): void {
        try {
            const userString = localStorage.getItem('user');
            if (!userString || userString.trim() === "" || ['null', 'undefined', '""', '" "'].includes(userString.trim().toLowerCase())) {
                console.error("User data not found or is invalid in localStorage.");
                this.currentUserRole = '';
                this.loggedInAdminId = null;
                this.loggedInPlantIds = [];
                this.toast?.showErrorToast("User session invalid. Please log in again.");
                return;
            }

            const currentUser = JSON.parse(userString);
            console.log('Current User Parsed for Role Check (QR Mgmt):', currentUser);

            this.loggedInAdminId = currentUser?.id ?? null;

            // Store the entire array of plant IDs, ensuring they are numbers
            this.loggedInPlantIds = (Array.isArray(currentUser?.plantIds) && currentUser.plantIds.length > 0)
                                      ? currentUser.plantIds.map((id: any) => Number(id)).filter((id: number) => !isNaN(id))
                                      : [];

            const roleId = currentUser?.adminsRoleId;

            if (roleId === 1 || roleId === 6) {
                this.currentUserRole = this.componentRoles.SUPER_ADMIN;
            } else if (roleId === 2) {
                this.currentUserRole = this.componentRoles.PLANT_ADMIN;
                if (this.loggedInPlantIds.length === 0) {
                    console.error(`Plant Admin (Role ID ${roleId}, User ID ${this.loggedInAdminId}) has no plants assigned! Access may be incorrect.`);
                    this.toast?.showErrorToast("User configuration error: Plant Admin has no assigned plants.");
                    // Optionally revoke role: this.currentUserRole = '';
                }
            } else {
                console.error(`Invalid or missing adminsRoleId (${roleId}) for User ID ${this.loggedInAdminId}.`);
                this.currentUserRole = '';
                this.toast?.showErrorToast("User configuration error: Invalid role.");
            }

            console.log(`User Role Determined: ${this.currentUserRole || 'None'}, Plant IDs: [${this.loggedInPlantIds.join(', ')}]`);

        } catch (error) {
            console.error("Error parsing user data from localStorage:", error);
            this.currentUserRole = '';
            this.loggedInAdminId = null;
            this.loggedInPlantIds = [];
            this.toast?.showErrorToast("Error reading user session. Please log in again.");
        }

        // Final check
        if (!this.currentUserRole && this.loggedInAdminId) {
            console.warn("Could not determine a valid user role. Functionality might be limited.");
        }
    }

    // --- Filter Modal Methods ---
    openFilterModal(): void { this.isFilterModalOpen = true; }
    closeFilterModal(): void { this.isFilterModalOpen = false; }

    applyFilters(): void {
        // Trim string values to prevent whitespace-only searches
        if (this.filters.zoneArea) {
            this.filters.zoneArea = this.filters.zoneArea.trim();
        }

        this.currentPage = 1; // Reset to first page when applying filters
        this.loadQrCodesForCurrentTab();
        this.closeFilterModal();
    }

    resetFilters(): void {
        this.filters = {
            noOfScan: null, longitude: null, latitude: null, zoneArea: null,
            plantId: null, // Reset plant
            zoneId: null, enabled: null,
            sortField: 'id', sortDirection: 'DESC'
        };
        this.availableZones = []; // Clear zones as plant is reset
        this.currentPage = 1;
        this.loadQrCodesForCurrentTab();
        // Keep filter modal open after reset? User preference.
        // this.closeFilterModal();
    }

    // --- Edit Modal Methods ---
    async openEditModal(qr: QrCode): Promise<void> {
        if (!qr || qr.id === undefined) {
            console.error("Cannot edit: QR Code data or ID is missing.");
            this.toast?.showErrorToast("Could not load QR Code data for editing.");
            return;
        }
        console.log("Opening edit modal for QR Code ID:", qr.id);

        this.isEditModalOpen = true; // Open modal immediately
        this.editFormData = null; // Reset form data while loading
        this.editLoading = true; // Indicate loading details
        this.originalQrCodeData = null;
        this.editAvailableZones = []; // Reset zones for edit modal

        // Simulate fetching details if needed, or just prepare data
        // If GET /qr-code/{id} is needed: await this.fetchQrDetailsForEdit(qr.id);
        // For now, assume 'qr' has enough details:

        this.originalQrCodeData = { ...qr }; // Store the original state

        // Prepare form data (use existing 'qr' object)
        this.editFormData = {
            id: qr.id,
            enabled: qr.enabled ?? false,
            flag: qr.isFlag ?? false,
            qrCodeText: qr.uuid ?? qr.qrCodeData ?? `ID: ${qr.id}`, // Display identifier
            noOfScan: qr.noOfScan ?? 0,
            status: qr.status ?? null,
            plantId: qr.plant?.id ?? qr.plantId ?? null,
            zoneId: qr.zone?.id ?? qr.zoneId ?? null,
            zoneArea: qr.zoneArea ?? null,
            qrTypeId: qr.qrType?.id ?? qr.qrTypeId ?? null,
            lat: qr.lat !== null && qr.lat !== undefined ? Number(qr.lat) : null,
            long: qr.long !== null && qr.long !== undefined ? Number(qr.long) : null
        };

        // Load necessary dropdown data if not already loaded
        if (this.availablePlants.length === 0) await this.getPlants(); // Ensure plants are loaded (filtered by role)
        if (this.availableQrTypes.length === 0) await this.loadQrTypes();

        // Load zones *if* a plant is already selected for this QR code
        if (this.editFormData.plantId) {
            await this.loadZonesForEdit(this.editFormData.plantId);
        }

        this.editLoading = false; // Details loaded
    }

    closeEditModal(): void {
        this.isEditModalOpen = false;
        this.editFormData = null;
        this.originalQrCodeData = null;
        this.editAvailableZones = [];
        this.editForm?.resetForm(); // Reset form validation state
    }

    async submitEditForm(): Promise<void> {
        if (!this.editForm?.valid || !this.editFormData || !this.originalQrCodeData || this.loggedInUserId === null) {
            console.warn('Edit form invalid, data missing, or user not logged in.');
            if (this.editForm) { Object.values(this.editForm.controls).forEach(control => control.markAsTouched()); }
            if (this.loggedInUserId === null) this.toast?.showErrorToast("Cannot save: User session invalid. Please log in again.");
            else this.toast?.showErrorToast("Please review the form for errors and required fields.");
            return;
        }

        this.editLoading = true;

        // Trim string values before submitting
        if (this.editFormData.zoneArea) {
            this.editFormData.zoneArea = this.editFormData.zoneArea.trim();
        }

        // Construct the payload based ONLY on editable fields in the form
        // Start with essential IDs and fields
        const dataForApi: Partial<QrCode> = {
            id: this.originalQrCodeData.id, // Essential: ID must be in the data part for update service
            enabled: this.editFormData.enabled,
            isFlag: this.editFormData.flag,
            status: this.editFormData.status !== null ? this.editFormData.status : undefined, // Convert null to undefined
            plantId: this.editFormData.plantId || null,
            // Only include zoneId if plantId is also set
            zoneId: this.editFormData.plantId ? (this.editFormData.zoneId || null) : null,
            zoneArea: this.editFormData.zoneArea || null,
            qrTypeId: this.editFormData.qrTypeId || null,
            lat: this.editFormData.lat !== null ? String(this.editFormData.lat) : null, // Convert back to string/null if needed
            long: this.editFormData.long !== null ? String(this.editFormData.long) : null,
            // Include other potentially editable fields if they exist in EditFormData
            // remarks: this.editFormData.remarks, // Example
        };

        // Add user ID for tracking
        const updatePayload = {
            tableName: 'qr-code', // Verify this table name
            id: this.originalQrCodeData.id, // ID for the endpoint URL
            data: dataForApi, // Data object for the request body
            createdBy: this.loggedInUserId // required 'createdBy' field for the payload structure
        };

        console.log("Submitting QR Update Payload:", JSON.stringify(updatePayload, null, 2));

        try {
            await this.updateService.update(updatePayload);
            console.log(`QR Code ${this.originalQrCodeData.id} updated successfully.`);
            this.toast?.showSuccessToast('QR Code updated successfully!');
            this.closeEditModal();
            this.loadQrCodesForCurrentTab(); // Refresh the list

        } catch (error) {
            console.error(`Error submitting QR code update for ID ${this.originalQrCodeData.id}:`, error);
            const errorMsg = (error as any)?.response?.data?.message || 'Failed to update QR code. Please try again.';
            this.toast?.showErrorToast(errorMsg);
        } finally {
            this.editLoading = false;
        }
    }


    // --- Data Fetching for Dropdowns ---
    async getPlants() {
        // Only fetch if needed or force refresh? For now, fetch always or if list is empty.
        // if (this.availablePlants.length > 0) return;

        const data = { sort: 'name,ASC', filter: ['enabled||eq||true'], limit: 1000 };
        const param = createAxiosConfig(data);
        try {
            console.log("Fetching plants...");
            const response = await this.plantService.getPlants(param);
            let allEnabledPlants: Plant[] = response?.data ?? response ?? [];
            console.log("Fetched all enabled plants:", allEnabledPlants.length);

            // Filter plants based on role
            if (this.currentUserRole === this.componentRoles.PLANT_ADMIN && this.loggedInPlantIds.length > 0) {
                this.availablePlants = allEnabledPlants.filter(plant =>
                    this.loggedInPlantIds.includes(plant.id)
                );
                console.log(`Plant Admin: Filtered ${this.availablePlants.length} plants from assigned IDs: [${this.loggedInPlantIds.join(', ')}]`);
                if (this.availablePlants.length !== this.loggedInPlantIds.length) {
                     console.warn(`Some assigned plants might be disabled or not found.`);
                }
            } else if (this.currentUserRole === this.componentRoles.SUPER_ADMIN) {
                this.availablePlants = allEnabledPlants;
                console.log(`Super Admin: Showing all ${this.availablePlants.length} enabled plants.`);
            } else {
                 this.availablePlants = []; // No role or Plant Admin with no plants
                 console.log("No plants available for current user role/assignment.");
            }

        } catch (error) {
            console.error("Error fetching plants:", error);
            this.availablePlants = [];
            this.toast?.showErrorToast("Failed to load plants.");
        }
    }

    // --- Zone Loading Triggered by Plant Selection ---
    async onPlantSelectionChange(selectedPlantId: number | null): Promise<void> {
        console.log('Filter Plant Selection Changed:', selectedPlantId);
        this.filters.zoneId = null; // Reset zone filter
        this.availableZones = [];   // Clear zone list
        if (selectedPlantId !== null) {
            await this.loadZonesForFilter(selectedPlantId);
        } else {
            // Optionally trigger list refresh if plant is deselected (set to All Plants / All My Plants)
            // this.applyFilters(); // Or just let user click Apply
        }
    }

    async onEditPlantSelectionChange(selectedPlantId: number | null): Promise<void> {
        console.log('Edit Plant Selection Changed:', selectedPlantId);
        if (this.editFormData) {
            this.editFormData.zoneId = null; // Reset zone in form data
        }
        this.editAvailableZones = []; // Clear zone list in edit modal
        if (selectedPlantId !== null) {
            await this.loadZonesForEdit(selectedPlantId);
        }
    }

    // --- Common Zone Loading Logic ---
    async loadZonesForFilter(plantId: number): Promise<void> {
        await this.loadZones(plantId, 'filter');
    }

    async loadZonesForEdit(plantId: number): Promise<void> {
        await this.loadZones(plantId, 'edit');
    }

    async loadZones(plantId: number, target: 'filter' | 'edit'): Promise<void> {
        const targetListKey = target === 'filter' ? 'availableZones' : 'editAvailableZones';
        // this.zoneLoading = true; // Optional: set loading state

        console.log(`Loading zones for plant ${plantId} into ${target} list`);
        this[targetListKey] = []; // Clear target list immediately

        const data = { sort: 'zoneName,ASC', filter: [`plantId||eq||${plantId}`, 'enabled||eq||true'], limit: 1000 };
        try {
            const param = createAxiosConfig(data);
            const response = await this.zoneService.getZone(param);
            this[targetListKey] = response?.data ?? response ?? [];
            console.log(`Loaded ${this[targetListKey].length} zones for plant ${plantId} into ${target} list.`);
        } catch (error) {
            console.error(`Error fetching zones for plant ${plantId}:`, error);
            this[targetListKey] = []; // Ensure list is empty on error
            this.toast?.showErrorToast(`Failed to load zones for the selected plant.`);
        } finally {
            // this.zoneLoading = false; // Optional: clear loading state
        }
    }

    // --- Load QR Types ---
    async loadQrTypes(): Promise<void> {
        // Avoid reloading if already populated, unless forced
        if (this.availableQrTypes.length > 0) return;

        const data = { sort: 'title,ASC', filter: ['enabled||eq||true'], limit: 1000 };
        const params = createAxiosConfig(data);
        try {
            const response = await this.qrTypeService.getQrType(params);
            this.availableQrTypes = response?.data ?? response ?? [];
        } catch (error) {
            console.error("Error fetching QR types:", error);
            this.availableQrTypes = [];
            this.toast?.showErrorToast("Failed to load QR Types.");
        }
    }

    // --- Centralized Data Fetching Logic for QR Code Lists ---
    async fetchQrCodes(page: number, baseFilters: string[], currentFilters: QrCodeFilter): Promise<{ data: QrCode[], total: number } | null> {
        this.listLoading = true;
        const filterParams: string[] = [...baseFilters];

        // --- Apply Role-Based Plant Filtering ---
        if (this.currentUserRole === this.componentRoles.PLANT_ADMIN) {
            if (currentFilters.plantId !== null && currentFilters.plantId !== undefined) {
                // Plant Admin selected a specific plant from their list
                filterParams.push(`plantId||eq||${currentFilters.plantId}`);
                console.log("Plant Admin: Filtering by selected plant ID:", currentFilters.plantId);
            } else if (this.loggedInPlantIds.length > 0) {
                // Plant Admin selected "All My Plants" (or default) - filter by all their assigned plants
                // Assuming backend supports $in operator with comma-separated values
                filterParams.push(`plantId||$in||${this.loggedInPlantIds.join(',')}`);
                console.log("Plant Admin: Filtering by assigned plant IDs:", this.loggedInPlantIds);
            } else {
                // Plant Admin has no plants assigned - this case should ideally be prevented
                // or result in no data. Adding a filter that likely returns nothing.
                console.warn("Plant Admin has no assigned plants. Applying impossible filter.");
                filterParams.push(`plantId||eq||-1`); // Or handle as needed
            }
        } else if (this.currentUserRole === this.componentRoles.SUPER_ADMIN) {
            // Super Admin: Only filter if they explicitly selected a plant
            if (currentFilters.plantId !== null && currentFilters.plantId !== undefined) {
                filterParams.push(`plantId||eq||${currentFilters.plantId}`);
                console.log("Super Admin: Filtering by selected plant ID:", currentFilters.plantId);
            } else {
                 console.log("Super Admin: No specific plant selected, showing all.");
                 // No plant filter added
            }
        } else {
            // No role or unknown role - potentially restrict or show all? Showing all for now.
            console.warn("Unknown user role, not applying specific plant filters.");
            // No plant filter added by default
        }
        // --- End Role-Based Plant Filtering ---


        // Apply other filters (Zone filter depends on a plant being selected)
        if (currentFilters.zoneId !== null && currentFilters.zoneId !== undefined) {
            // Only filter by zone if a plant is also effectively selected (either specific or implicit for Plant Admin)
            const isPlantSelected = (currentFilters.plantId !== null && currentFilters.plantId !== undefined) ||
                                    (this.currentUserRole === this.componentRoles.PLANT_ADMIN && this.loggedInPlantIds.length > 0);
            if (isPlantSelected) {
                filterParams.push(`zoneId||eq||${currentFilters.zoneId}`);
            } else {
                console.warn("Zone filter ignored as no specific plant is selected or applicable.");
            }
        }
        if (currentFilters.enabled !== null && currentFilters.enabled !== undefined && currentFilters.enabled !== '') filterParams.push(`enabled||eq||${currentFilters.enabled}`);
        if (currentFilters.noOfScan !== null && currentFilters.noOfScan !== undefined) filterParams.push(`noOfScan||eq||${currentFilters.noOfScan}`);
        if (currentFilters.longitude !== null && currentFilters.longitude !== undefined) filterParams.push(`long||eq||${currentFilters.longitude}`);
        if (currentFilters.latitude !== null && currentFilters.latitude !== undefined) filterParams.push(`lat||eq||${currentFilters.latitude}`);
        if (currentFilters.zoneArea) {
            const trimmedZoneArea = currentFilters.zoneArea.trim();
            if (trimmedZoneArea) {
                filterParams.push(`zoneArea||$contL||${trimmedZoneArea}`);
            }
        }

        const data = {
            page: page, limit: this.itemsPerPage,
            sort: `${currentFilters.sortField || 'id'},${currentFilters.sortDirection || 'DESC'}`,
            filter: filterParams,
            join: ['plant', 'zone', 'qrType', 'lastScanByAdmin'] // Ensure joins cover display needs
        };

        console.log("API Request Params (fetchQrCodes):", JSON.stringify(data, null, 2));

        try {
            const param = createAxiosConfig(data);
            const response = await this.qrCodeService.getQrCode(param);
            return { data: response?.data ?? [], total: response?.total ?? 0 };
        } catch (error) {
            console.error("Error fetching QR codes:", error);
            this.toast?.showErrorToast("Failed to load QR code list.");
            return null;
        } finally {
            this.listLoading = false;
        }
    }

    async loadQrCodesForCurrentTab() {
        const currentTab = this.tabs[this.selectedTabIndex];
        if (!currentTab) return;

        console.log(`Loading data for tab: ${currentTab.title}`);
        // Pass the component's filters object to fetchQrCodes
        const result = await this.fetchQrCodes(this.currentPage, currentTab.baseFilter || [], this.filters);

        // Assign to the correct list property based on the tab's listKey
        const listKey = currentTab.listKey;
        if (listKey) {
            // Reset all lists first to ensure only the current tab's list is populated
            this.activated_qrcode_list = [];
            this.unregistered_qrcode_list = [];
            this.deactivated_qrcode_list = [];
            this.deleted_qrcode_list = [];
            this.totalItems = 0;

            if (result) {
                console.log(`Fetched ${result.data.length} records for ${listKey}, total: ${result.total}`);
                this.totalItems = result.total;
                this[listKey] = result.data; // Dynamic assignment
            }
        } else {
            console.error("Tab configuration missing listKey:", currentTab);
        }
    }

    // --- Tab and Pagination Handling ---
    onTabSelected(index: number) {
        if (this.selectedTabIndex === index) return;
        console.log(`Tab selected: ${index}`);
        this.selectedTabIndex = index;
        this.currentPage = 1; // Reset page on tab change
        this.loadQrCodesForCurrentTab();
    }

    onPageChange(page: number) {
        if (this.currentPage === page) return;
        console.log(`Page changed to: ${page}`);
        this.currentPage = page;
        this.loadQrCodesForCurrentTab();
    }

    // --- Sorting Helpers ---
    getSortClass(key: string): string {
        return this.filters.sortField === key ? `sort-${this.filters.sortDirection?.toLowerCase()}` : 'sort-none';
    }
    sortBy(field: string) {
        if (this.listLoading) return; // Prevent sorting while loading
        console.log(`Sorting by: ${field}`);
        if (this.filters.sortField === field) {
            this.filters.sortDirection = this.filters.sortDirection === 'ASC' ? 'DESC' : 'ASC';
        } else {
            this.filters.sortField = field;
            this.filters.sortDirection = 'DESC'; // Default to DESC on new field
        }
        this.currentPage = 1; // Reset to first page on sort change
        this.loadQrCodesForCurrentTab();
    }

    // --- Helper to get status text ---
    getStatusText(status: number | null | undefined): string {
        switch (status) {
            case 0: return 'Unregistered';
            case 1: return 'Active';
            case 2: return 'Deactivated';
            default: return 'Unknown';
        }
    }

    // --- Helper to trim input fields ---
    trimInputField(formData: any, fieldName: string): void {
        if (formData && formData[fieldName] && typeof formData[fieldName] === 'string') {
            formData[fieldName] = formData[fieldName].trim();
        }
    }

    // --- Download Excel Logic ---

    // Helper to get the data list currently displayed
    getCurrentListData(): QrCode[] | undefined {
        const currentTab = this.tabs[this.selectedTabIndex];
        if (currentTab && currentTab.listKey) {
          if (Array.isArray(this[currentTab.listKey])) {
              return this[currentTab.listKey];
          }
        }
        console.warn("Could not get list data for download for tab index:", this.selectedTabIndex);
        return undefined;
    }

    // Method to fetch ALL QR codes matching current filters (no pagination)
    async fetchAllFilteredQrCodes(): Promise<QrCode[] | null> {
        const currentTab = this.tabs[this.selectedTabIndex];
        if (!currentTab) {
            console.error("Cannot fetch all data: Invalid tab selected.");
            return null;
        }

        const filterParams: string[] = [...currentTab.baseFilter];

        // --- Apply Role-Based Plant Filtering (Same logic as fetchQrCodes) ---
        if (this.currentUserRole === this.componentRoles.PLANT_ADMIN) {
            if (this.filters.plantId !== null && this.filters.plantId !== undefined) {
                filterParams.push(`plantId||eq||${this.filters.plantId}`);
            } else if (this.loggedInPlantIds.length > 0) {
                filterParams.push(`plantId||$in||${this.loggedInPlantIds.join(',')}`);
            } else {
                console.warn("Plant Admin has no assigned plants for 'Download All'. Applying impossible filter.");
                filterParams.push(`plantId||eq||-1`);
            }
        } else if (this.currentUserRole === this.componentRoles.SUPER_ADMIN) {
            if (this.filters.plantId !== null && this.filters.plantId !== undefined) {
                filterParams.push(`plantId||eq||${this.filters.plantId}`);
            }
            // No filter if Super Admin selected "All Plants"
        }
        // --- End Role-Based Plant Filtering ---

        // Apply other filters
        if (this.filters.zoneId !== null && this.filters.zoneId !== undefined) {
            const isPlantSelected = (this.filters.plantId !== null && this.filters.plantId !== undefined) ||
                                    (this.currentUserRole === this.componentRoles.PLANT_ADMIN && this.loggedInPlantIds.length > 0);
            if (isPlantSelected) {
                 filterParams.push(`zoneId||eq||${this.filters.zoneId}`);
            }
        }
        if (this.filters.enabled !== null && this.filters.enabled !== undefined && this.filters.enabled !== '') filterParams.push(`enabled||eq||${this.filters.enabled}`);
        if (this.filters.noOfScan !== null && this.filters.noOfScan !== undefined) filterParams.push(`noOfScan||eq||${this.filters.noOfScan}`);
        if (this.filters.longitude !== null && this.filters.longitude !== undefined) filterParams.push(`long||eq||${this.filters.longitude}`);
        if (this.filters.latitude !== null && this.filters.latitude !== undefined) filterParams.push(`lat||eq||${this.filters.latitude}`);
        if (this.filters.zoneArea) {
            const trimmedZoneArea = this.filters.zoneArea.trim();
            if (trimmedZoneArea) {
                filterParams.push(`zoneArea||$contL||${trimmedZoneArea}`);
            }
        }

        // Use a large limit for 'all'
        const data = {
            limit: 10000, // Adjust as needed, consider backend capabilities
            sort: `${this.filters.sortField || 'id'},${this.filters.sortDirection || 'DESC'}`,
            filter: filterParams,
            join: ['plant', 'zone', 'qrType', 'lastScanByAdmin'] // Ensure needed joins
        };

        console.log('API Request Params (QR Download - All Data):', JSON.stringify(data, null, 2));

        try {
            const param = createAxiosConfig(data);
            const response = await this.qrCodeService.getQrCode(param);

            // Handle potential response structures (nestjsx/crud often returns { data: [], total: N })
            if (response && Array.isArray(response.data)) {
                return response.data;
            } else if (response && Array.isArray(response)) {
                return response; // Handle direct array response if API does that
            } else {
                console.warn("Received unexpected response structure when fetching all QR codes:", response);
                this.toast?.showErrorToast("Could not retrieve data in the expected format for download.");
                return null;
            }
        } catch (error) {
            console.error("Error fetching all QR codes for download:", error);
            const errorMsg = (error as any)?.response?.data?.message || "Failed to retrieve full data for download.";
            this.toast?.showErrorToast(errorMsg);
            return null;
        }
        // No finally block here, handled by caller
    }

    // Main download function
    async downloadExcel(type: 'current' | 'all') {
        const currentTab = this.tabs[this.selectedTabIndex];
        if (!currentTab || this.isDownloadingExcel) {
            if (this.isDownloadingExcel) console.warn("Download already in progress.");
            else console.error("No valid tab selected for download.");
            return;
        }

        this.isDownloadingExcel = true;
        this.downloadType = type;
        this.toast?.showSuccessToast(`Preparing download for ${type === 'current' ? 'current page' : 'all filtered'} data...`);

        let dataToExport: QrCode[] | null = null;

        try {
            // 1. Get Data based on type
            if (type === 'all') {
                dataToExport = await this.fetchAllFilteredQrCodes();
            } else { // type === 'current'
                dataToExport = this.getCurrentListData() ?? null;
                 if (dataToExport === null){ // Check explicit null from helper
                    console.error("Could not get current page data for download.");
                    // Toast already shown by getCurrentListData or fetch
                 }
            }

            // 2. Check if data fetching was successful and if there's data
            if (dataToExport === null) {
                // Error toast likely already shown by fetch methods
                return; // Exit if data is null (error occurred during fetch)
            }
            if (!dataToExport || dataToExport.length === 0) {
                this.toast?.showErrorToast(`No data available to download for ${type === 'current' ? 'the current page' : 'the current filters'}.`);
                return;
            }

            console.log(`Fetched ${dataToExport.length} records for Excel export (${type}).`);

            // 3. Transform data for Excel Sheet
            const dataForExcel = dataToExport.map(qr => ({
                'ID': qr.id,
                'Status': this.getStatusText(qr.status),
                'Enabled': qr.enabled ? 'Yes' : 'No',
                'Flagged': qr.isFlag ? 'Yes' : 'No',
                'Scan Count': qr.noOfScan ?? 0,
                'Last Scan Date': qr.lastScanDate ? new Date(qr.lastScanDate).toLocaleString() : '',
                'Last Scan By': qr.lastScanByAdmin?.firstName ?? qr.lastScanByAdminId ?? '',
                'Plant': qr.plant?.name ?? qr.plantId ?? '',
                'Zone': qr.zone?.zoneName ?? qr.zoneId ?? '',
                'Zone Area': qr.zoneArea ?? '',
                'QR Type': qr.qrType?.title ?? qr.qrTypeId ?? '',
                'Latitude': qr.lat ?? '',
                'Longitude': qr.long ?? '',
                'UUID': qr.uuid ?? '',
                'Created Date': qr.createdTimestamp ? new Date(qr.createdTimestamp).toLocaleString() : '',
                'Updated Date': qr.updatedTimestamp ? new Date(qr.updatedTimestamp).toLocaleString() : '',
            }));

            // 4. Create Worksheet and Workbook
            const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(dataForExcel);
            const wb: XLSX.WorkBook = XLSX.utils.book_new();
            const safeSheetName = currentTab.title.replace(/[/\\?*[\]]/g, '').substring(0, 30); // Sanitize sheet name
            XLSX.utils.book_append_sheet(wb, ws, safeSheetName || 'QR Codes');

            // 5. Generate File Name
            const dateStr = new Date().toISOString().slice(0, 10);
            const typeStr = type === 'current' ? `Page${this.currentPage}` : 'All';
            const fileName = `QRCodes_${safeSheetName}_${typeStr}_${dateStr}.xlsx`;

            // 6. Trigger Download
            XLSX.writeFile(wb, fileName);

            this.toast?.showSuccessToast(`Excel file download started (${type}).`);

        } catch (error) {
            console.error(`Error generating Excel file (${type}):`, error);
            this.toast?.showErrorToast(`An error occurred while generating the Excel file (${type}).`);
        } finally {
            this.isDownloadingExcel = false; // Reset flag
            this.downloadType = null; // Reset type
        }
    }

} // End of QrcodeManagementComponent Class
