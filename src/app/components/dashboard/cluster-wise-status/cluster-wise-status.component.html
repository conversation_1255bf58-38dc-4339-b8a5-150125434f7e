<div id="chart" *ngIf="chartInitializedStatus">
    <div style="text-align: left; font-size: 14px; margin-bottom: -7px;">Cluster Wise Status</div>
    <apx-chart [series]="chartOptions.series" [chart]="chartOptions.chart" [labels]="chartOptions.labels"
        [responsive]="chartOptions.responsive" [dataLabels]="chartOptions.dataLabels" [legend]="chartOptions?.legend"
        [colors]="chartOptions?.colors">
    </apx-chart>
</div>
<div class="loading-container" *ngIf="!chartInitializedStatus && !noDataAvailable">
    <div class="spinner-border text-primary" style="width: 10rem; height: 10rem;" role="status">
        <span class="visually-hidden">Loading...</span>
    </div>
</div>
<div class="no-data-message" *ngIf="noDataAvailable">
    <p>No data available for Cluster Wise Status.</p>
</div>