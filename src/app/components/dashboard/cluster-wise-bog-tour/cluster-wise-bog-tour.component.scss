.loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 432px;
    width: 100%;
}

.no-data-message {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 295px; /* Maintain the same height as the loading container */
    width: 100%;
    text-align: center;
    font-size: 16px;
    color: #666;
}

.p {
    font-size: 14px;
    font-weight: bold;
}

#chart {
    height: 295px;
}

:host {
    ::ng-deep {
        .apexcharts-legend {
            max-height: 42px !important;
            overflow-x: auto ortant;
            position: absolute !important;
            right: -78px !important;
            top: 80% !important;
            width: 280px !important;
            margin-left: 0px;

            &::-webkit-scrollbar {
                width: 4px;
            }

            &::-webkit-scrollbar-track {
                background: #f1f1f1;
            }

            &::-webkit-scrollbar-thumb {
                background: #888;
                border-radius: 4px;
            }
        }

        .apexcharts-donut {
            transform: translateX(20px);
        }
    }
}