.dialog-container {
    min-width: 500px;
    max-width: 80vw;
    max-height: 70vh;
    padding: 16px;
    font-family: 'Arial', sans-serif;
    display: flex;
    flex-direction: column;

    .header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 8px;
        flex-shrink: 0;

        .dialog-title {
            font-size: 18px;
            color: #000000;
            margin: 0 120px 0 0;
        }

        .cluster-btn {
            width: 77px;
            background-color: #ffcc99;
            color: #2D336B;
            border-radius: 5px;
            margin: 0 8px;
            border: none;
        }

        .close-button {
            background: none;
            border: none;
            font-size: 2.9rem;
            cursor: pointer;
            color: #2D336B;
            line-height: 1;
            padding: 0;
        }
    }

    .table-container {
        overflow-y: auto;
        max-height: 60vh;
        position: relative;
        background-color: #F3F4F8;
        border-radius: 4px;
        flex-grow: 1;

        .custom-table {
            width: 100%;
            border-collapse: separate;

            .sticky-header {
                position: sticky;
                top: 0;
                z-index: 2;
                background-color: #2D336B !important;
                color: white !important;
            }

            .sticky-header-row {
                position: sticky;
                top: 0;
                z-index: 2;
                background-color: #2D336B;
                color: white;
            }

            th {
                background-color: #2D336B;
                color: white !important;
                font-weight: 500;
                padding: 12px 16px;
            }

            td {
                color: #2D336B;
                padding: 12px 16px;
                background-color: white;
                border-bottom: 1px solid #e0e0e0;
            }

            tr.mat-row {
                &:hover {
                    background-color: #f5f5f5;
                }
            }
        }
    }

    .no-data-found {
        text-align: center;
        padding: 24px;
        color: #666;
        flex-shrink: 0;
    }

    .loading-spinner-container {
        display: flex;
        justify-content: center;
        padding: 24px;
        flex-shrink: 0;
    }
}

::ng-deep {
    .mat-mdc-dialog-container .mdc-dialog__surface {
        border-radius: 20px !important;
    }

    .mat-mdc-dialog-container {
        padding: 0 !important;
        overflow: hidden !important;
    }
}