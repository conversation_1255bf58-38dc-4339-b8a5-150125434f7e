import { AfterViewInit, Component, Inject, OnInit, ViewChild } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatIcon } from '@angular/material/icon';
import { MatPaginator } from '@angular/material/paginator';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { ClusterService } from '../../../../services/master-management/cluster/cluster.service';
import { createAxiosConfig } from '../../../../core/utilities/axios-param-config';
import { CommonModule } from '@angular/common';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { DashboardService } from '../../../../services/dashboard/dashboard.service';

@Component({
  selector: 'app-cluster-wise-tour-dialog',
  imports: [CommonModule, MatDialogModule, MatTableModule, MatIcon, MatProgressSpinnerModule],
  templateUrl: './cluster-wise-bog-tour.dialog.component.html',
  styleUrl: './cluster-wise-bog-tour.dialog.component.scss',
  standalone: true
})

export class ClusterWiseBogTourDialog implements OnInit, AfterViewInit {

  displayedColumns: string[] = ['no', 'plantsName', 'NoBogTours'];
  dataSource = new MatTableDataSource<any>();
  isLoading = false;
  errorMessage = '';

  @ViewChild(MatPaginator) paginator!: MatPaginator;

  constructor(
    @Inject(MAT_DIALOG_DATA) public data: { clusterTitle: string },
    private dialogRef: MatDialogRef<ClusterWiseBogTourDialog>,
    private clusterService: ClusterService,
    private dashboardService: DashboardService
  ) {

  }

  ngOnInit(): void {
    this.loadClusterData();
  }

  async loadClusterData(): Promise<void> {
    this.isLoading = true;
    this.errorMessage = '';

    try {
      const clusterParams = createAxiosConfig({
        page: 1,
        limit: 1000,
        sort: 'title,ASC',
        filter: ['isDeleted||eq||false', 'enabled||eq||true']
      });

      const clusterRes = await this.clusterService.getCluster(clusterParams);
      const clusterData = clusterRes?.data?.find((item: any) => item.title === this.data.clusterTitle);

      if (!clusterData) {
        throw new Error('Cluster data not found');
      }

      await this.loadPlantData(clusterData.id);
    } catch (error) {
      console.error('Error loading data:', error);
      this.errorMessage = 'Failed to load data. Please try again later.';
    } finally {
      this.isLoading = false;
    }
  }

  async loadPlantData(clusterId: string): Promise<void> {
    try {
      const body = {
        clusterId: clusterId
      }
      const plantRes = await this.dashboardService.getPlantTourCount(body);

      if (!plantRes?.data) {
        throw new Error('No plant data found');
      }

      this.dataSource.data = plantRes.data.map((plant: any, index: number) => ({
        no: index + 1,
        plantsName: plant?.name,
        NoBogTours: plant?.count
      }));
    } catch (error) {
      console.error('Error loading plant data:', error);
      throw error;
    }
  }

  ngAfterViewInit(): void {
    this.dataSource.paginator = this.paginator;
  }

  crossClick() {
    this.dialogRef.close();
  }
}
