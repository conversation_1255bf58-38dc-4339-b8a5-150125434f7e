/* Base styles */
.dashboard-container,
.dashboard-container2 {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    margin-left: 11px;
}

.graphs {
    margin-top: 0px;
    padding-left: 10px;
    padding-right: 10px;
    margin-bottom: 10px;
}

.graph-card {
    margin-bottom: 4px;
    padding: 4px 12px 5px 20px;
}

.custom-width-one {
    width: 48%;
}

.custom-width-two {
    width: 52%;
    padding-left: 0px;
}

@media (min-width: 1348px) and (max-width: 1364px) {

    .dashboard-container,
    .dashboard-container2 {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        margin-left: -5px;
    }
}

/* Large screens: laptops and desktops */
@media (min-width: 1310px) and (max-width: 1330px) {
    .custom-width-one {
        width: 48%;
    }

    .custom-width-two {
        width: 52%;
        padding-left: 10px;
        margin-left: 0;
        padding-left: 0px;
    }

    .dashboard-container,
    .dashboard-container2 {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        margin-left: 5px;
    }
}



@media (max-width: 1310px) {

    .custom-width-one {
        width: 100%;
    }

}

@media (max-width: 1100px) {

    .custom-width-one,
    .custom-width-two {
        width: 100%;
    }

    .custom-width-two {
        padding-left: 26px;
        margin-left: 0;
    }

    .graphs .col-5 {
        width: 55%;
        /* Two graphs side by side */
        margin-bottom: 10px;
    }

    .graphs .col-8 {
        width: 100%;
        /* Full width for bigger cards */
        margin-bottom: 10px;
    }
}

@media (max-width: 1100px) {

    .custom-width-one,
    .custom-width-two {
        width: 100%;
    }

    .custom-width-two {
        padding-left: 26px;
        margin-left: 0;
    }

    .graphs .col-5 {
        width: 55%;
        /* Two graphs side by side */
        margin-bottom: 10px;
    }

    .graphs .col-8 {
        width: 100%;
        /* Full width for bigger cards */
        margin-bottom: 10px;
    }
}

@media (max-width: 900px) {

    .custom-width-one,
    .custom-width-two {
        width: 100%;
    }

    .custom-width-two {
        padding-left: 26px;
        margin-left: 0;
    }

    .dashboard-container,
    .dashboard-container2 {
        flex-direction: row;
        gap: 5px;
    }

    .graphs .col-5 {
        width: 65%;
        /* Two graphs side by side */
        margin-bottom: 10px;
    }

    .graphs .col-8 {
        width: 100%;
        /* Full width for bigger cards */
        margin-bottom: 10px;
    }
}

@media (max-width: 767px) {

    /* Stack main containers vertically */
    .custom-width-one,
    .custom-width-two {
        width: 100% !important;
        /* Full width */
        padding-left: '10px' !important;
        margin-left: '10px' !important;
    }

    /* Dashboard containers flex-wrap to column */
    .dashboard-container,
    .dashboard-container2 {
        flex-direction: row;
        gap: 10px;
    }

    /* Graph rows stack vertically */
    .graphs {
        padding-left: 10px;
        padding-right: 10px;
        margin-top: 10px;
        margin-bottom: 10px;
        display: flex;
        flex-direction: column;
        gap: 10px;
    }

    /* Graph cards take full width */
    .graphs .col-5,
    .graphs .col-8 {
        width: 100% !important;
        margin-bottom: 10px;
    }

    /* Remove extra padding inside graph cards for better fit */
    .graph-card {
        padding: 10px 10px 5px 10px;
        margin-bottom: 10px;
    }
}

@media only screen and (min-width: 481px) and (max-width: 768px) {
    .cards {
        grid-auto-flow: row;
        /* Switch to grid rows (default) */
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        overflow-x: unset;
    }

    .dashboard-container2 {
        display: flex;
        flex-wrap: nowrap;
        /* prevent wrapping */
        overflow-x: auto;
        /* enable horizontal scroll */
        gap: 10px;
    }

    /* Stack main containers vertically */
    .custom-width-one,
    .custom-width-two {
        width: auto !important;
        /* Full width */
        padding-left: '10px' !important;
        margin-left: '12px' !important;
    }

    .dashboard-container,
    .dashboard-container2 {
        flex-direction: row;
        gap: 10px;
    }
}