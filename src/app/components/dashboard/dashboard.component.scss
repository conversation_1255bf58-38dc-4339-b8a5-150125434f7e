/* Modern Dashboard Styles */
.modern-dashboard {
  padding: 24px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
  font-family: 'adani', -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
}

/* Dashboard Header */
.dashboard-header {
  margin-bottom: 32px;

  .welcome-section {
    text-align: center;
    padding: 20px 0;

    .dashboard-title {
      font-size: 2.5rem;
      font-weight: 700;
      background: linear-gradient(135deg, #0b74b0 0%, #75479c 50%, #bd3861 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      margin-bottom: 8px;
      letter-spacing: -0.02em;
    }

    .dashboard-subtitle {
      font-size: 1.1rem;
      color: #64748b;
      font-weight: 400;
      margin: 0;
    }
  }
}

/* Dashboard Content */
.dashboard-content {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

/* Dashboard Rows */
.dashboard-row {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* Top Section: Chart and KPI Cards */
.top-section {
  .chart-section {
    flex: 1;
    margin-bottom: 24px;

    .chart-container {
      background: white;
      border-radius: 20px;
      padding: 24px;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
      border: 1px solid rgba(255, 255, 255, 0.2);
      backdrop-filter: blur(10px);
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-4px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.12);
      }
    }
  }

  .kpi-section {
    .kpi-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
      gap: 20px;

      .kpi-card-wrapper {
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-2px);
        }
      }
    }
  }
}

/* Analytics Section: Three Column Layout */
.analytics-section {
  .analytics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 24px;

    .analytics-card {
      background: white;
      border-radius: 20px;
      overflow: hidden;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
      border: 1px solid rgba(255, 255, 255, 0.2);
      backdrop-filter: blur(10px);
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-4px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.12);
      }

      .card-header {
        background: linear-gradient(135deg, #0b74b0 0%, #75479c 50%, #bd3861 100%);
        color: white;
        padding: 20px 24px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .card-title {
          font-size: 1.1rem;
          font-weight: 600;
          margin: 0;
          letter-spacing: -0.01em;
        }

        .card-icon {
          font-size: 1.2rem;
          opacity: 0.9;
        }
      }

      .card-content {
        padding: 24px;
      }
    }
  }
}


/* Reports Section: Two Column Layout */
.reports-section {
  .reports-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 24px;

    .report-card {
      background: white;
      border-radius: 20px;
      overflow: hidden;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
      border: 1px solid rgba(255, 255, 255, 0.2);
      backdrop-filter: blur(10px);
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-4px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.12);
      }

      .card-header {
        background: linear-gradient(135deg, #0b74b0 0%, #75479c 50%, #bd3861 100%);
        color: white;
        padding: 20px 24px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .card-title {
          font-size: 1.1rem;
          font-weight: 600;
          margin: 0;
          letter-spacing: -0.01em;
        }

        .card-icon {
          font-size: 1.2rem;
          opacity: 0.9;
        }
      }

      .card-content {
        padding: 24px;
      }
    }
  }
}

/* Responsive Design */

/* Large Desktop */
@media (min-width: 1200px) {
  .top-section {
    flex-direction: row;
    align-items: flex-start;
    gap: 32px;

    .chart-section {
      flex: 2;
      margin-bottom: 0;
    }

    .kpi-section {
      flex: 1;

      .kpi-grid {
        grid-template-columns: repeat(2, 1fr);
      }
    }
  }

  .analytics-grid {
    grid-template-columns: repeat(3, 1fr) !important;
  }

  .reports-grid {
    grid-template-columns: repeat(2, 1fr) !important;
  }
}

/* Medium Desktop */
@media (max-width: 1199px) and (min-width: 992px) {
  .analytics-grid {
    grid-template-columns: repeat(2, 1fr) !important;
  }

  .kpi-grid {
    grid-template-columns: repeat(4, 1fr) !important;
  }
}

/* Tablet */
@media (max-width: 991px) and (min-width: 768px) {
  .modern-dashboard {
    padding: 20px;
  }

  .dashboard-title {
    font-size: 2rem !important;
  }

  .analytics-grid {
    grid-template-columns: repeat(2, 1fr) !important;
  }

  .reports-grid {
    grid-template-columns: 1fr !important;
  }

  .kpi-grid {
    grid-template-columns: repeat(2, 1fr) !important;
  }
}

/* Mobile */
@media (max-width: 767px) {
  .modern-dashboard {
    padding: 16px;
  }

  .dashboard-header {
    margin-bottom: 24px;

    .dashboard-title {
      font-size: 1.75rem !important;
    }

    .dashboard-subtitle {
      font-size: 1rem !important;
    }
  }

  .dashboard-content {
    gap: 24px;
  }

  .analytics-grid,
  .reports-grid {
    grid-template-columns: 1fr !important;
    gap: 16px !important;
  }

  .kpi-grid {
    grid-template-columns: repeat(2, 1fr) !important;
    gap: 12px !important;
  }

  .analytics-card,
  .report-card {
    .card-header {
      padding: 16px 20px !important;

      .card-title {
        font-size: 1rem !important;
      }
    }

    .card-content {
      padding: 20px !important;
    }
  }

  .chart-container {
    padding: 20px !important;
  }
}

/* Small Mobile */
@media (max-width: 480px) {
  .modern-dashboard {
    padding: 12px;
  }

  .dashboard-title {
    font-size: 1.5rem !important;
  }

  .kpi-grid {
    grid-template-columns: 1fr !important;
    gap: 10px !important;
  }

  .analytics-card,
  .report-card {
    .card-header {
      padding: 12px 16px !important;

      .card-title {
        font-size: 0.9rem !important;
      }

      .card-icon {
        font-size: 1rem !important;
      }
    }

    .card-content {
      padding: 16px !important;
    }
  }

  .chart-container {
    padding: 16px !important;
  }
}

/* Animation and Interaction Enhancements */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.analytics-card,
.report-card,
.chart-container {
  animation: fadeInUp 0.6s ease-out;
}

.analytics-card:nth-child(2) {
  animation-delay: 0.1s;
}

.analytics-card:nth-child(3) {
  animation-delay: 0.2s;
}

.report-card:nth-child(2) {
  animation-delay: 0.1s;
}