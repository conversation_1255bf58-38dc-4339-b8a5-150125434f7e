import { Component, OnInit, ViewChild } from '@angular/core';
import { DashboardService } from '../../../services/dashboard/dashboard.service';
import { AuthService } from '../../../services/auth.service'; // Import AuthService
import { ChartComponent, NgApexchartsModule } from "ng-apexcharts";
import {
  ApexChart,
} from "ng-apexcharts";
import { CommonModule } from '@angular/common';
import { createAxiosConfig } from '../../../core/utilities/axios-param-config';
import { FormsModule } from '@angular/forms';

export type ChartOptions = {
  series: ApexAxisChartSeries | any;
  chart: ApexChart | any;
  xaxis: ApexXAxis | any;
  dataLabels: ApexDataLabels | any;
  title: ApexTitleSubtitle | any;
  plotOptions: ApexPlotOptions | any;
  colors?: any;
  tooltip: any;
  legend: any;
};

@Component({
  selector: 'app-observation-status',
  standalone: true, // Add standalone: true for standalone components
  imports: [NgApexchartsModule, CommonModule, FormsModule],
  templateUrl: './observation-status.component.html',
  styleUrl: './observation-status.component.scss'
})
export class ObservationStatusComponent implements OnInit {

  chartInitializedStatus: boolean = false;
  @ViewChild("chart") chart!: ChartComponent;
  public selectedPeriod: 'monthly' | 'yearly' = 'monthly';
  businessUnitId: number | null = null; // Declare businessUnitId property
  noDataAvailable: boolean = false;
  public chartOptions!: Partial<ChartOptions>;

  constructor(
    private dashboardService: DashboardService,
    private authService: AuthService // Inject AuthService
  ) {
    this.chartOptions = {

    };
  }

  ngOnInit() {
    this.businessUnitId = this.authService.getBusinessUnitId(); // Get businessUnitId
    this.loadObservationStatusData();
  }

  private formatToISO(date: Date): string {
    date.setHours(0, 0, 0, 0);
    return date.toISOString();
  }

  loadObservationStatusData() {
    const currentDate = new Date();
    const formatYmd = (date: any) => date.toISOString().slice(0, 10);

    const calculateFromDate = (selection: any) => {
      const fromDate = new Date(currentDate);

      if (this.selectedPeriod === "monthly") {
        fromDate.setMonth(fromDate.getMonth() - 1);
      } else if (this.selectedPeriod === "yearly") {
        fromDate.setFullYear(fromDate.getFullYear() - 1);
      }

      return formatYmd(fromDate);
    };

    const from = calculateFromDate(this.selectedPeriod);
    const to = formatYmd(currentDate);

    const body: any = {
      from: from,
      to: to
    };
    if (this.businessUnitId) {
      body.businessUnitId = this.businessUnitId;
    }
    const params = createAxiosConfig(body);
    this.dashboardService.getTourObservationCount(params).then((res) => {
      if (res && res.responseCode === 200 && res.observationCount && Array.isArray(res.observationCount) && res.observationCount.length > 0) {
        const observationCount = res.observationCount[0];
        const allZero = Object.values(observationCount).every(value => value === "0");

        if (allZero) {
          this.noDataAvailable = true;
          this.chartOptions = {};
          this.chartInitializedStatus = false;
        }
        else {
          const categories = Object.keys(observationCount).map(key => key);
          const data = Object.values(observationCount).map(value => value);

          this.chartOptions = {
            series: [
              {
                name: 'Count',
                data: data
              }
            ],
            chart: {
              type: 'bar',
              height: 275,
            },
            colors: ['#7886C7'],
            xaxis: {
              categories: categories,
              labels: {
                rotate: -45
              },
            },
            dataLabels: {
              enabled: false,
              style: {
                fontSize: '11px',
              }
            },
            legend: {
              itemMargin: {
                horizontal: 5,
                vertical: 0
              },
            },
            plotOptions: {
              bar: {
                borderRadius: [10],
                columnWidth: '58%',
                fontSize: '10px',
              }
            },
            tooltip: {
              enabled: true,
              style: {
                fontSize: '12px',
                background: '#000',
                color: '#fff'
              },
              y: {
                formatter: function (value: number) {
                  return value.toString();
                }
              }
            }

          };
          this.chartInitializedStatus = true
        }
      }
      else {
        this.noDataAvailable = true;
        this.chartOptions = {};
        this.chartInitializedStatus = false;
        if (res.observationCount && Array.isArray(res.observationCount) && res.observationCount.length === 0) {
          console.info('Observation Status data is empty.');
        } else {
          console.error('Unexpected response format or missing Observation Status', res);
        }
      }
    }).catch(error => {
      console.error('Error fetching Observation Status', error);
    });
  }

  onPeriodChange() {
    this.chartInitializedStatus = false;
    this.loadObservationStatusData();
  }
}