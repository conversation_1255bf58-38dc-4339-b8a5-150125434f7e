<div class="card" *ngIf="chartInitializedStatus">
    <div class="card-header-img">
        <img [src]="icon" [alt]="title + ' icon'" class="icon" />
    </div>
    <div class="card-content">
        <h3>{{title}}</h3>
        <p>{{count | number}}</p>
        <div class="progress-bar">
            <div class="progress"
                 [ngStyle]="{
                   width: progress + '%',
                   '--progress-color': color
                 }">
            </div>
        </div>
    </div>
</div>
<div class="loading-container" *ngIf="!chartInitializedStatus">
    <div class="spinner-border text-primary" style="width: 10rem; height: 10rem;" role="status">
        <span class="visually-hidden">Loading...</span>
    </div>
</div>