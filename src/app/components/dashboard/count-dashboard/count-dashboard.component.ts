import { CommonModule } from '@angular/common';
import { Component, Input, OnInit } from '@angular/core';

@Component({
  selector: 'app-count-dashboard',
  imports: [CommonModule],
  templateUrl: './count-dashboard.component.html',
  styleUrl: './count-dashboard.component.scss'
})
export class CountDashboardComponent implements OnInit {

  chartInitializedStatus: boolean = false;
  @Input() icon!: string;
  @Input() title!: string;
  @Input() count!: number;
  @Input() total!: number;
  @Input() color!: string;

  get progress(): number {
    const calculatedProgress = (this.count / this.total) * 100;
    const scaledProgress = calculatedProgress > 100 ? calculatedProgress / 10 : calculatedProgress;
    return parseFloat(scaledProgress.toFixed(1));
  }

  ngOnInit() {
    this.chartInitializedStatus = true;
  }

  // Generate a complementary color for gradient effect
  getProgressEndColor(startColor: string): string {
    const colorMap: { [key: string]: string } = {
      '#522ACC': '#7C3AED', // Purple to lighter purple
      '#FDA02C': '#F59E0B', // Orange to amber
      '#FB6EA4': '#EC4899', // Pink to lighter pink
      '#046332': '#059669', // Green to emerald
      '#116AAD': '#0EA5E9'  // Blue to sky blue
    };

    return colorMap[startColor] || startColor;
  }
}
