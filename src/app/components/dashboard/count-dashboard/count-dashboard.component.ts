import { CommonModule } from '@angular/common';
import { Component, Input, OnInit } from '@angular/core';

@Component({
  selector: 'app-count-dashboard',
  imports: [CommonModule],
  templateUrl: './count-dashboard.component.html',
  styleUrl: './count-dashboard.component.scss'
})
export class CountDashboardComponent implements OnInit {

  chartInitializedStatus: boolean = false;
  @Input() icon!: string;
  @Input() title!: string;
  @Input() count!: number;
  @Input() total!: number;
  @Input() color!: string;

  get progress(): number {
    const calculatedProgress = (this.count / this.total) * 100;
    const scaledProgress = calculatedProgress > 100 ? calculatedProgress / 10 : calculatedProgress;
    return parseFloat(scaledProgress.toFixed(1));
  }

  ngOnInit() {
    this.chartInitializedStatus = true;
  }
}
