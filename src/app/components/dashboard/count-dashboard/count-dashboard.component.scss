.card {
    width: 110px;
    height: 128px;
    padding: 5px;
    border-radius: 8px;
    background: #fff;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 5px;
}

.card-header-img {
    margin-bottom: 4px;
    display: flex;
    justify-content: center;
    width: 100%;
}

.icon {
    width: 50px;
    height: 50px;
}

.card-content {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.card-content h3 {
    margin: 5px 0 5px;
    font-size: 12px;
    text-align: center;
}

.card-content p {
    font-size: 11px;
    color: #666;
    text-align: center;
}


.progress-bar {
    width: 90%;
    height: 6px;
    background: #ddd;
    border-radius: 5px;
    margin-top: 5px;
}

.progress {
    height: 100%;
    border-radius: 5px;
    transition: width 0.3s ease;
}


.loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 432px;
    width: 100%;
}


.graphs {
    margin-top: 0px;
    padding-left: 10px;
    padding-right: 10px;
    margin-bottom: 10px;
}

.graph-card {
    margin-bottom: 4px;
    padding: 4px 12px 5px 20px;
}

@media (min-width: 1345px) and (max-width: 1364px) {
    .card {
        width: 110px;
        height: 128px;
        padding: 5px;
        border-radius: 8px;
        background: #fff;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
        text-align: center;
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-bottom: 5px;
    }
}

@media (min-width: 1310px) and (max-width: 1330px) {
    .card {
        width: 108px;
        height: 128px;
        padding: 5px;
        border-radius: 8px;
        background: #fff;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
        text-align: center;
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-bottom: 5px;
    }
}