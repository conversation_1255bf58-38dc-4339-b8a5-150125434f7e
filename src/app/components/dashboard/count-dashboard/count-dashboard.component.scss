/* Modern KPI Card Styles */
.card {
  width: 100%;
  min-width: 140px;
  height: 160px;
  padding: 20px 16px;
  border-radius: 20px;
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
  box-shadow:
    0 10px 30px rgba(0, 0, 0, 0.08),
    0 1px 8px rgba(0, 0, 0, 0.06),
    inset 0 1px 0 rgba(255, 255, 255, 0.7);
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0;
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #0b74b0 0%, #75479c 50%, #bd3861 100%);
    border-radius: 20px 20px 0 0;
  }

  &:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow:
      0 20px 40px rgba(0, 0, 0, 0.12),
      0 8px 16px rgba(0, 0, 0, 0.08),
      inset 0 1px 0 rgba(255, 255, 255, 0.8);
  }
}

.card-header-img {
  margin-bottom: 12px;
  display: flex;
  justify-content: center;
  width: 100%;

  .icon {
    width: 48px;
    height: 48px;
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
    transition: all 0.3s ease;
  }
}

.card:hover .icon {
  transform: scale(1.1);
}

.card-content {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  flex-grow: 1;
  justify-content: space-between;

  h3 {
    margin: 0 0 8px 0;
    font-size: 0.875rem;
    font-weight: 600;
    text-align: center;
    color: #334155;
    line-height: 1.2;
    letter-spacing: -0.01em;
  }

  p {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1e293b;
    text-align: center;
    margin: 8px 0;
    line-height: 1;
    letter-spacing: -0.02em;
  }
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: linear-gradient(90deg, #e2e8f0 0%, #f1f5f9 100%);
  border-radius: 12px;
  margin-top: 12px;
  overflow: hidden;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, rgba(255,255,255,0.3) 0%, transparent 100%);
    border-radius: 12px;
  }
}

.progress {
  height: 100%;
  border-radius: 12px;
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  background: var(--progress-color, #0b74b0);

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.3) 50%, transparent 100%);
    border-radius: 12px;
  }

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, rgba(255,255,255,0.4) 0%, transparent 50%, rgba(255,255,255,0.2) 100%);
    border-radius: 12px;
  }
}


/* Loading State */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 160px;
  width: 100%;
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);

  .spinner-border {
    width: 3rem !important;
    height: 3rem !important;
    border-width: 0.3em;
    border-color: #0b74b0 transparent #75479c transparent !important;
    animation: spinner-grow 1.5s linear infinite;
  }
}

@keyframes spinner-grow {
  0% {
    transform: rotate(0deg) scale(0.8);
    opacity: 0.5;
  }
  50% {
    transform: rotate(180deg) scale(1);
    opacity: 1;
  }
  100% {
    transform: rotate(360deg) scale(0.8);
    opacity: 0.5;
  }
}

/* Responsive Design for KPI Cards */
@media (max-width: 768px) {
  .card {
    height: 140px;
    padding: 16px 12px;

    .card-header-img .icon {
      width: 40px;
      height: 40px;
    }

    .card-content {
      h3 {
        font-size: 0.8rem;
      }

      p {
        font-size: 1.25rem;
      }
    }

    .progress-bar {
      height: 6px;
      margin-top: 8px;
    }
  }
}

@media (max-width: 480px) {
  .card {
    height: 120px;
    padding: 12px 8px;

    .card-header-img .icon {
      width: 32px;
      height: 32px;
    }

    .card-content {
      h3 {
        font-size: 0.75rem;
        margin-bottom: 4px;
      }

      p {
        font-size: 1.1rem;
        margin: 4px 0;
      }
    }

    .progress-bar {
      height: 4px;
      margin-top: 6px;
    }
  }
}