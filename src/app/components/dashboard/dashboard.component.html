<!-- <div class="container"> -->
<div class="row graphs">
    <div class="custom-width-one">
        <app-dashboard-bubble-chart></app-dashboard-bubble-chart>
    </div>
    <div class="custom-width-two">
        <div class="dashboard-container2">
            <app-count-dashboard *ngFor="let card of cards" [icon]="card.icon" [title]="card.title" [count]="card.count"
                [total]="card.total" [color]="card.color">
            </app-count-dashboard>
        </div>
    </div>
</div>
<!-- </div> -->
<div class="row graphs">
    <div class="col-5 col-md-4">
        <div class="card graph-card">
            <app-cluster-wise-bog-tour></app-cluster-wise-bog-tour>
        </div>
    </div>
    <div class="col-5 col-md-4">
        <div class="card graph-card">
            <app-cluster-wise-graph></app-cluster-wise-graph>
        </div>
    </div>
    <div class="col-5 col-md-4">
        <div class="card graph-card">
            <app-cluster-wise-status></app-cluster-wise-status>
        </div>
    </div>

</div>
<div class="row graphs">
    <div class="col-8 col-md-6 ">
        <div class="card graph-card">
            <app-top-find-and-fixed-it></app-top-find-and-fixed-it>
        </div>
    </div>

    <div class="col-8 col-md-6 ">
        <div class="card graph-card">
            <app-observation-status></app-observation-status>
        </div>
    </div>

</div>