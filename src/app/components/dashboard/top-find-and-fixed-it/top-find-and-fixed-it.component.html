<div id="chart" *ngIf="chartInitializedStatus && !noDataAvailable">
    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
        <div style="font-size: 14px; font-weight: 500;">Top find it, fixed it</div>
        <select [(ngModel)]="selectedPeriod" (change)="onPeriodChange()" class="period-select">
            <option value="monthly">Monthly</option>
            <option value="yearly">Yearly</option>
        </select>
    </div>
    <apx-chart [series]="chartOptions!.series" [chart]="chartOptions!.chart!" [xaxis]="chartOptions!.xaxis!"
        [plotOptions]="chartOptions.plotOptions" [dataLabels]="chartOptions!.dataLabels!" [colors]="chartOptions.colors"
        [title]="chartOptions!.title!">
    </apx-chart>
</div>
<div class="no-data-message" *ngIf="noDataAvailable">
    <p>No data available for Top find it, fixed it.</p>
</div>