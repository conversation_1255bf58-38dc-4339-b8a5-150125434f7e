<div id="chart" *ngIf="chartInitialized">
    <div style="text-align: left; font-size: 14px; margin-bottom: -7px;">Cluster Wise Categories</div>
    <apx-chart [series]="chartOptions.series" [chart]="chartOptions.chart" [labels]="chartOptions.labels"
        [tooltip]="chartOptions.title" [responsive]="chartOptions.responsive" [dataLabels]="chartOptions.dataLabels"
        [legend]="chartOptions?.legend" [colors]="chartOptions?.colors">
    </apx-chart>
</div>
<div class="loading-container" *ngIf="!chartInitialized && !noDataAvailable">
    <div class="spinner-border text-primary" style="width: 10rem; height: 10rem;" role="status">
        <span class="visually-hidden">Loading...</span>
    </div>
</div>
<div class="no-data-message" *ngIf="noDataAvailable">
    <p>No data available for Cluster Wise Categories.</p>
</div>