import { Component, OnInit, ViewChild } from '@angular/core';
import { DashboardService } from '../../../services/dashboard/dashboard.service';
import { AuthService } from '../../../services/auth.service'; // Import AuthService
import { ChartComponent, NgApexchartsModule } from "ng-apexcharts";
import {
  ApexNonAxisChartSeries,
  ApexResponsive,
  ApexChart
} from "ng-apexcharts";
import { CommonModule } from '@angular/common';

export type ChartOptions = {
  series: ApexNonAxisChartSeries | any;
  chart: ApexChart | any;
  responsive: ApexResponsive[] | any;
  labels: any;
  title: any;
  dataLabels?: any;
  tooltip?: any;
  colors?: any;
  legend: any;
};


@Component({
  selector: 'app-cluster-wise-graph',
  standalone: true, // Add standalone: true for standalone components
  imports: [NgApexchartsModule, CommonModule],
  templateUrl: './cluster-wise-graph.component.html',
  styleUrls: ['./cluster-wise-graph.component.scss']
})
export class ClusterWiseGraphComponent implements OnInit {

  activeUserCount: number = 5;
  inActiveUserCount: number = 10;
  unsafe: number = 0;
  chartInitialized: boolean = false;
  @ViewChild("chart") chart!: ChartComponent;
  businessUnitId: number | null = null; // Declare businessUnitId property
  noDataAvailable: boolean = false;
  public chartOptions!: Partial<ChartOptions>;

  constructor(
    private dashboardService: DashboardService,
    private authService: AuthService // Inject AuthService
  ) {
    this.chartOptions = {
    }
  }

  ngOnInit() {
    this.businessUnitId = this.authService.getBusinessUnitId(); // Get businessUnitId
    this.getClusterWiseGraph();
  }

  getClusterWiseGraph() {
    const data: any = {}; // Initialize data as any to allow adding properties
    if (this.businessUnitId) {
      data.businessUnitId = this.businessUnitId; // Add businessUnitId to the body
    }
    this.dashboardService.getClusterWiseGraph(data).then((res) => {
      if (res && res.responseCode === 200 && res.data && Array.isArray(res.data) && res.data.length > 0) {

        const clusterData = res.data[0];

        const safeCount = clusterData.safeCount != null ? Number(clusterData.safeCount) : null;
        const unSafeCount = clusterData.unSafeCount != null ? Number(clusterData.unSafeCount) : null;
        const unSafeConditionCount = clusterData.unSafeConditionCount != null ? Number(clusterData.unSafeConditionCount) : null;
        if (
          (safeCount !== 0 && safeCount != null) ||
          (unSafeCount !== 0 && unSafeCount != null) ||
          (unSafeConditionCount !== 0 && unSafeConditionCount != null)
        ) {
          this.chartOptions.series = [
            safeCount,
            unSafeCount,
            unSafeConditionCount
          ];
          this.chartOptions.chart = {
            type: 'pie',
            height: 300,
            width: '302px'
          }
          this.chartOptions.colors = ['#97DAF3', '#BDF6DF', '#FAC2CB']
          this.chartOptions.labels = ['Safe Act', 'Unsafe Act', 'Unsafe Conditions'];
          this.chartOptions.legend = {
            position: 'bottom',
            horizontalAlign: 'center',
            floating: false,
            fontSize: '11px',
            left: '60px',
            offsetY: -10, // Adjust as needed
            labels: {
              colors: ['#000'],
              useSeriesColors: false
            }
          }
          this.chartOptions.dataLabels = {
            enabled: true,
            formatter: (value: number, options: { seriesIndex: number; w: any }) => {
              return options.w.config.series[options.seriesIndex];
            },
            style: {
              fontSize: '11px',
              fontWeight: '400',
              position: 'center',
              colors: ['#000000'],
            }
          }

          this.chartInitialized = true;
        }
        else {
          this.noDataAvailable = true;
          this.chartInitialized = false;
          this.chartOptions = {};
        }
      } else {
        this.noDataAvailable = true;
        this.chartInitialized = false;
        this.chartOptions = {};
        if (res && Array.isArray(res.data) && res.data.length === 0) {
          console.info('Cluster Wise Categories data is empty.');
        } else {
          console.error('Unexpected response format or missing Cluster Wise Categories', res);
        }
      }
    }).catch(error => {
      console.error('Error fetching cluster-wise data', error);
    });
  }
}
