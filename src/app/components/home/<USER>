import { Component, OnInit, AfterViewInit, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { HeaderComponent } from '../../shared/header/header.component';
import { MatSidenavModule } from '@angular/material/sidenav';
import { MatListModule } from '@angular/material/list';
import { MatIconModule } from '@angular/material/icon';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatButtonModule } from '@angular/material/button';
import { RouterModule } from '@angular/router';
// Assuming NavigationItem model is in a separate file now, adjust import if needed
// import NavigationItem from '../../model/navigation.model';
import { NavigationService } from '../../services/navigation/navigation.service';
import NavigationItem from '../../model/navigation.model';
import { ManageSideMenuService } from '../../services/manage-sidemenu/manage-side-menu.service';
import { Subscription } from 'rxjs';
import { FeatureManagementService } from '../../services/master-management/feature-management/feature-management.service';
import { AuthService } from '../../services/auth.service';
import { createAxiosConfig } from '../../core/utilities/axios-param-config';
import { Feature } from '../../model/feature.model';

// --- 1. Define Internal Role Identifiers ---
// Use consistent internal names for roles within this component
const ROLES = {
  SUPER_ADMIN: 'super_admin',
  PLANT_ADMIN: 'plant_admin',
  GLOBAL_ADMIN: 'global_admin',
}


@Component({
  selector: 'app-home',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatSidenavModule,
    MatListModule,
    MatIconModule,
    MatToolbarModule,
    MatButtonModule,
    HeaderComponent
  ],
  templateUrl: './home.component.html',
  styleUrl: './home.component.scss',
})
export class HomeComponent implements OnInit, AfterViewInit {
  activeNav: string = '';
  isCollapsed: boolean = false;
  currentUserRole: string = ''; // Will store 'super_admin' or 'plant_admin'

  loggedInAdminId: number | null = null;
  loggedInplantId: number | null = null;
  loggedInUserPlantName: string | null = null;

  navItems: NavigationItem[] = [];
  filteredNavItems: NavigationItem[] = [];
  private readonly subscription: Subscription;

  // Properties to store feature data and map for quick lookup
  // Properties to store feature data and map for quick lookup
  private allFeatures: Feature[] = [];
  private featureRouteStatusMap: { [route: string]: boolean } = {};

  constructor(private readonly navigation: NavigationService, private readonly manageSideMenuService: ManageSideMenuService,
    private featureManagementService: FeatureManagementService,
    private authService: AuthService,
    private cdr: ChangeDetectorRef) {
    // Subscribe to sidebar toggle events
    this.subscription = this.manageSideMenuService.headerClicked$.subscribe((isCollapsed) => {
      console.log('Sidebar state received in component:', isCollapsed);
      this.isCollapsed = isCollapsed;
    });
  }

  ngOnInit(): void {
    // Initialize sidebar state from service
    this.isCollapsed = this.manageSideMenuService.isCollapsed;

    // --- 1. Get Current User Details and Determine Role via ID ---
    this.setCurrentUserRoleAndDetailsById(); // Renamed for clarity

    // --- 2. Fetch Features based on Business Unit ---
    this.fetchUserFeatures().then(() => {
      // --- 3. Initialize the Full Navigation List with Internal Role Identifiers ---
      this.initializeNavigationList();

      // --- 4. Filter the list based on the user's internal role identifier and feature flags ---
      this.filterNavigationByRole();

      // --- 5. Set Initial Active Navigation ---
      const savedRoute = sessionStorage.getItem('activatedNav');
      const firstAccessibleRoute = this.findFirstAccessibleRoute(this.filteredNavItems);

      if (savedRoute && this.isRouteAccessible(savedRoute, this.filteredNavItems)) {
        // Set active route
        this.activeNav = savedRoute;

        // Expand parent menus for the active route
        this.expandParentMenus(savedRoute);

        // Navigate to the route
        this.navigation.navigateTo(savedRoute);
      } else if (firstAccessibleRoute) {
        this.activateNav(firstAccessibleRoute);
      } else {
        console.warn("No accessible navigation items found for this user role ID.");
        this.activeNav = '';
      }

      // Fix for scrolling issue: Force layout recalculation after navigation setup
      this.forceLayoutRecalculation();
      
      // Additional fix: Trigger change detection and layout recalculation after navigation items are rendered
      setTimeout(() => {
        this.cdr.detectChanges();
        this.forceLayoutRecalculation();
      }, 300);
    }).catch(error => {
      console.error("Error initializing navigation:", error);
      // Handle error, perhaps by showing a message or setting a default state
      this.initializeNavigationList(); // Attempt to initialize with default if feature fetch fails
      this.filterNavigationByRole();
      this.forceLayoutRecalculation();
    });
  }

  // --- New method to fetch user features ---
  private async fetchUserFeatures(): Promise<void> {
    try {
      const userString = localStorage.getItem('user');
      if (!userString) {
        console.error("User data not found in localStorage.");
        return;
      }
      const currentUser = JSON.parse(userString);

      // Get businessUnitId from user object - prioritize businessUnitId over plantIds
      let businessUnitId = currentUser?.businessUnitId;

      // If businessUnitId is not available, fall back to first plantId
      if (!businessUnitId && currentUser?.plantIds && currentUser.plantIds.length > 0) {
        businessUnitId = currentUser.plantIds[0];
        console.log("Using plantIds[0] as businessUnitId fallback:", businessUnitId);
      }

      if (businessUnitId) {
        console.log("Fetching features for businessUnitId:", businessUnitId);
        const requestData = {
          page: 1,
          limit: 1000, // Fetch all features for the business unit
          filter: [`businessUnitId||$eq||${businessUnitId}`]
        };
        const params = createAxiosConfig(requestData);
        const response = await this.featureManagementService.getFeatures(params);
        this.allFeatures = response?.data ?? [];

        // Populate the featureRouteStatusMap
        // Rule: Use the root 'enabled' field, not featureMaster.enabled
        this.allFeatures.forEach(feature => {
          if (feature.adminRoute) {
            this.featureRouteStatusMap[feature.adminRoute] = feature.enabled;
            console.log(`Feature route ${feature.adminRoute} is ${feature.enabled ? 'enabled' : 'disabled'}`);
          }
        });
        console.log('Feature Route Status Map:', this.featureRouteStatusMap);
        console.log('Total features loaded:', this.allFeatures.length);
      } else {
        console.warn("Business Unit ID not found for the user. Feature filtering will not be applied.");
      }
    } catch (error) {
      console.error("Error fetching user features:", error);
      // Continue without feature filtering if an error occurs
    }
  }

  // --- 2. Modified Method to Set Role Based on ID ---
  private setCurrentUserRoleAndDetailsById(): void {
    try {
      const userString = localStorage.getItem('user');
      if (!userString || userString.trim() === "" || userString.trim().toLowerCase() === 'null' || userString.trim() === '" "') {
        console.error("User data not found or is invalid in localStorage.");
        this.currentUserRole = '';
        return;
      }

      const currentUser = JSON.parse(userString);
      console.log('Current User Parsed:', currentUser);

      // Set other properties (same as before)
      this.loggedInAdminId = currentUser?.id ?? null;
      this.loggedInplantId = currentUser?.plantIds?.[0] ?? null;
      this.loggedInUserPlantName = currentUser?.plant?.[0]?.name ?? null;

      // *** Determine role based on adminsRoleId ***
      const roleId = currentUser?.adminsRoleId; // Get the ID

      if (roleId === 1) {
        this.currentUserRole = ROLES.SUPER_ADMIN; // Map ID 1 to 'super_admin'
        console.log(`Role ID ${roleId} mapped to:`, this.currentUserRole);
      } else if (roleId === 2) {
        this.currentUserRole = ROLES.PLANT_ADMIN; // Map ID 2 to 'plant_admin'
        console.log(`Role ID ${roleId} mapped to:`, this.currentUserRole);
      } else if (roleId === 6) {
        this.currentUserRole = ROLES.GLOBAL_ADMIN; // Map ID 6 to 'global_admin'
        console.log(`Role ID ${roleId} mapped to:`, this.currentUserRole);
      } else {
        // Handle cases where roleId is missing, null, undefined, or not 1, 2, or 6
        console.error(`Invalid or missing adminsRoleId (${roleId}) found in user data. Access might be restricted.`);
        this.currentUserRole = ''; // Assign no role or a default non-privileged role
      }

    } catch (error) {
      console.error("Error parsing user data from localStorage:", error);
      this.currentUserRole = '';
    }

    // Final check (same as before)
    if (!this.currentUserRole) {
      console.error("Failed to determine a valid user role from adminsRoleId. Navigation will be restricted.");
    }
  }

  // --- 3. Helper methods using the internal role identifiers ---

  toggleSidebar() {
    // Use the service to toggle the sidebar
    console.log('toggleSidebar called directly');
    this.manageSideMenuService.triggerHeaderClick();
  }

  // hasAccess now compares 'super_admin'/'plant_admin' from currentUserRole
  // with the same identifiers in itemRoles
  hasAccess(itemRoles: string[]): boolean {
    if (!this.currentUserRole) {
      return false;
    }
    // Check if the item requires no specific role OR if the user's role is included
    return itemRoles.length === 0 || itemRoles.includes(this.currentUserRole);
  }

  filterNavigationByRole(): void {
    // ... (same as before - relies on hasAccess)
    this.filteredNavItems = this.filterItemsRecursive(this.navItems);
  }

  filterItemsRecursive(items: NavigationItem[]): NavigationItem[] {
    // Filter by role first, then by feature flag
    return items
      .filter(item => {
        // Role check - if user doesn't have role access, hide the item
        const hasRoleAccess = this.hasAccess(item.allowedRole);
        if (!hasRoleAccess) {
          console.log(`Item ${item.title} hidden due to role restrictions`);
          return false;
        }

        // Feature flag check based on requirements:
        // IMPORTANT: Global admins (adminRoleId = 6) should bypass feature hiding
        // 1. If adminRoute is not present, it should be by default enabled
        // 2. If adminRoute is present and disabled, it should be hidden (except for global admin)
        // 3. If adminRoute is present and enabled, it should be visible

        // Skip feature hiding for global admins
        if (this.currentUserRole === ROLES.GLOBAL_ADMIN) {
          console.log(`Item ${item.title} visible for global admin (bypassing feature flags)`);
          return true;
        }

        if (item.route) {
          // Check if this route exists in our feature map
          if (this.featureRouteStatusMap.hasOwnProperty(item.route)) {
            // Route exists in feature map, use its enabled status
            const isFeatureEnabled = this.featureRouteStatusMap[item.route];
            if (isFeatureEnabled === false) {
              console.log(`Item ${item.title} (${item.route}) hidden because feature is disabled`);
              return false;
            }
          } else {
            // Route doesn't exist in feature map, default to enabled
            console.log(`Item ${item.title} (${item.route}) visible by default (no feature config found)`);
          }
        }

        // Keep item if it has role access and feature is enabled or not applicable
        return true;
      })
      .map(item => {
        if (item.subitems && item.subitems.length > 0) {
          // Recursively filter subitems
          const filteredSubitems = this.filterItemsRecursive(item.subitems);

          // Only keep parent if it has accessible children or if it's a route itself
          const keepParent = filteredSubitems.length > 0 ||
            (item.route &&
              (this.featureRouteStatusMap.hasOwnProperty(item.route) ?
                this.featureRouteStatusMap[item.route] !== false :
                true) &&
              this.hasAccess(item.allowedRole));

          if (keepParent) {
            return new NavigationItem(
              item.title, item.route, item.icon,
              filteredSubitems, item.expanded, item.allowedRole
            );
          }
          return null; // Remove parent if it has no route and no accessible children
        }

        // Keep item if it has a route, is accessible by role, and feature is enabled
        return item.route ? new NavigationItem(
          item.title, item.route, item.icon,
          [], item.expanded, item.allowedRole
        ) : null; // Remove item if it has no route and no subitems
      }).filter((item): item is NavigationItem => item !== null); // Filter out null items
  }

  isRouteAccessible(route: string, items: NavigationItem[]): boolean {
    // ... (same as before)
    for (const item of items) {
      if (item.route === route) return true;
      if (item.subitems?.length && this.isRouteAccessible(route, item.subitems)) return true;
    }
    return false;
  }

  findFirstAccessibleRoute(items: NavigationItem[]): string | null {
    for (const item of items) {
      // Check if the item is accessible by role
      const isRoleAccessible = this.hasAccess(item.allowedRole);

      // Global admins bypass feature hiding
      let isFeatureEnabled = true; // Default to enabled

      // Skip feature check for global admins
      if (this.currentUserRole !== ROLES.GLOBAL_ADMIN && item.route) {
        // Check if feature is enabled based on our requirements:
        // 1. If adminRoute is not present in featureRouteStatusMap, it should be by default enabled
        // 2. If adminRoute is present and disabled, it should be hidden
        // 3. If adminRoute is present and enabled, it should be visible

        // If route exists in feature map, use its status
        if (this.featureRouteStatusMap.hasOwnProperty(item.route)) {
          isFeatureEnabled = this.featureRouteStatusMap[item.route];
        }
        // Otherwise, keep default (enabled)
      }

      if (item.route && isRoleAccessible && isFeatureEnabled) {
        console.log(`Found first accessible route: ${item.route}`);
        return item.route;
      }

      if (item.subitems?.length) {
        const firstChildRoute = this.findFirstAccessibleRoute(item.subitems);
        if (firstChildRoute) return firstChildRoute;
      }
    }
    return null;
  }

  // Helper method to find parent navigation items for a given route
  findParentItems(route: string, items: NavigationItem[], parents: NavigationItem[] = []): NavigationItem[] {
    for (const item of items) {
      // Check if this item has the route
      if (item.route === route) {
        return [...parents];
      }

      // Check if the route is in this item's subitems
      if (item.subitems && item.subitems.length > 0) {
        const foundParents = this.findParentItems(route, item.subitems, [...parents, item]);
        if (foundParents.length > 0) {
          return foundParents;
        }
      }
    }

    return [];
  }

  // Helper method to expand all parent menus for a given route
  expandParentMenus(route: string) {
    const parentItems = this.findParentItems(route, this.filteredNavItems);
    console.log('Parent items for route', route, ':', parentItems.map(p => p.title));

    // Expand all parent items
    parentItems.forEach(parent => {
      parent.expanded = true;
    });
  }

  activateNav(route: string) {
    console.log('activateNav called for route:', route);

    // If sidebar is collapsed, expand it for better user experience
    if (this.isCollapsed) {
      console.log('Sidebar is collapsed, expanding for navigation');
      this.manageSideMenuService.expandSidebar();
      this.isCollapsed = false;
    }

    // Check if the route is accessible
    if (this.isRouteAccessible(route, this.filteredNavItems)) {
      this.activeNav = route;

      // Expand all parent menus for this route
      this.expandParentMenus(route);

      // Navigate to the route
      this.navigation.navigateTo(route);
      sessionStorage.setItem('activatedNav', route);
    } else {
      console.warn(`Attempted to activate inaccessible route: ${route}. Falling back.`);
      const firstRoute = this.findFirstAccessibleRoute(this.filteredNavItems);
      if (firstRoute && firstRoute !== route) this.activateNav(firstRoute);
      else this.activeNav = '';
    }
  }

  // Helper method to find the first navigable route in a navigation item's subitems
  findFirstChildRoute(nav: NavigationItem): string | null {
    if (nav.subitems && nav.subitems.length > 0) {
      // If the first child has a route, return it
      if (nav.subitems[0].route) {
        return nav.subitems[0].route;
      }
      // Otherwise, recursively check its children
      return this.findFirstChildRoute(nav.subitems[0]);
    }
    return null;
  }

  toggleSubMenu(nav: NavigationItem) {
    console.log('toggleSubMenu called for:', nav.title);

    // If sidebar is collapsed and menu has subitems
    if (this.isCollapsed && nav.subitems.length > 0) {
      console.log('Sidebar is collapsed with subitems');

      // Option 1: Expand sidebar first, then toggle submenu
      this.isCollapsed = false;
      this.manageSideMenuService.expandSidebar();

      // Set a small timeout to ensure the sidebar expands before toggling the submenu
      setTimeout(() => {
        nav.expanded = !nav.expanded;
      }, 300); // Match this with the transition duration in CSS

      // Option 2 (alternative): Navigate to first child directly
      // const firstChildRoute = this.findFirstChildRoute(nav);
      // if (firstChildRoute) {
      //   this.activateNav(firstChildRoute);
      // }
    } else if (nav.subitems.length > 0) {
      // Normal behavior when sidebar is already expanded
      nav.expanded = !nav.expanded;
    }
  }

  // --- NAVIGATION HELPER FUNCTIONS ---

  // Check if this item or any of its children are active
  isNavItemActive(item: NavigationItem, currentActiveNav: string): boolean {
    if (item.route === currentActiveNav) {
      return true;
    }
    if (item.subitems && item.subitems.length > 0) {
      // Check if any subitem (recursively) is active
      return item.subitems.some(subitem => this.isNavItemActive(subitem, currentActiveNav));
    }
    return false;
  }

  // Check if this item is directly active (not through children)
  isDirectlyActive(item: NavigationItem, currentActiveNav: string): boolean {
    return item.route === currentActiveNav;
  }

  // Check if any child is active but the item itself is not
  hasActiveChild(item: NavigationItem, currentActiveNav: string): boolean {
    if (!item.subitems || item.subitems.length === 0) {
      return false;
    }

    // Check if any direct child is active
    return item.subitems.some(subitem =>
      this.isNavItemActive(subitem, currentActiveNav)
    );
  }
  // --- END NAVIGATION HELPER FUNCTIONS ---


  // initializeNavigationList now uses the internal role identifiers ('super_admin', 'plant_admin')
  // defined in the ROLES constant at the top.
  private initializeNavigationList() {
    const ALL_ADMIN_ROLES = [ROLES.SUPER_ADMIN, ROLES.PLANT_ADMIN, ROLES.GLOBAL_ADMIN]; // -> ['super_admin', 'plant_admin', 'global_admin']
    const SUPER_ADMIN_ONLY = [ROLES.SUPER_ADMIN, ROLES.GLOBAL_ADMIN];                   // -> ['super_admin', 'global_admin']
    const GLOBAL_ADMIN_ONLY = [ROLES.GLOBAL_ADMIN];                                     // -> ['global_admin']

    this.navItems = [
      new NavigationItem('Dashboard', '/home/<USER>', 'bi-speedometer2', [], false, ALL_ADMIN_ROLES),
      new NavigationItem('QR Code Management', '/home/<USER>', 'bi-qr-code', [], false, ALL_ADMIN_ROLES),
      new NavigationItem('Tour Management', '/home/<USER>', 'bi-map', [], false, ALL_ADMIN_ROLES),
      new NavigationItem('Incident Management', '/home/<USER>', 'bi-exclamation-triangle', [], false, ALL_ADMIN_ROLES),
      new NavigationItem('Plant Management', '/home/<USER>', 'bi-building', [], false, ALL_ADMIN_ROLES),
      new NavigationItem('Feature Management', '/home/<USER>', 'bi-gear', [], false, GLOBAL_ADMIN_ONLY),
      new NavigationItem('Manage Observation', '/home/<USER>', 'bi-person', [], false, ALL_ADMIN_ROLES),
      new NavigationItem('Safety Training', '/home/<USER>', 'bi-book', [
        new NavigationItem('Dashboard', '/home/<USER>/dashboard', 'bi-speedometer2', [], false, ALL_ADMIN_ROLES),
        new NavigationItem('Manage Training', '/home/<USER>/manage', 'bi-gear', [], false, ALL_ADMIN_ROLES),
      ], false, ALL_ADMIN_ROLES),
      new NavigationItem('Digisafe', '/home/<USER>', 'bi-shield-lock', [
        new NavigationItem('Manage Digisafe', '/home/<USER>/manage', 'bi-shield-lock', [], false, ALL_ADMIN_ROLES),
        new NavigationItem('GMR Dashboard', '/home/<USER>/gmr', 'bi-graph-up', [], false, ALL_ADMIN_ROLES),
        new NavigationItem('MIS Dashboard', '/home/<USER>/mis', 'bi-graph-up', [], false, ALL_ADMIN_ROLES),
        new NavigationItem('Unfilled Report', '/home/<USER>/unfilled-report', 'bi-file-earmark-bar-graph', [], false, SUPER_ADMIN_ONLY),
      ], false, ALL_ADMIN_ROLES),
      new NavigationItem('Add DJP', '/home/<USER>', 'bi-files', [], false, ALL_ADMIN_ROLES),
      new NavigationItem('Other Task', '/home/<USER>', 'bi-list-task', [], false, ALL_ADMIN_ROLES),
      new NavigationItem('Crisis Management', '/home/<USER>', 'bi-lightning-fill', [], false, ALL_ADMIN_ROLES),
      new NavigationItem('Leaderboard', '/home/<USER>', 'bi-trophy', [], false, ALL_ADMIN_ROLES),
      new NavigationItem('Report Management', '/home/<USER>', 'bi-file-earmark-text', [
        new NavigationItem('BoG Tour', '/home/<USER>/tour', 'bi-file-earmark-text', [
          new NavigationItem('User Wise Report', '/home/<USER>/tour/userwise', 'bi-file-earmark-text', [], false, ALL_ADMIN_ROLES),
          new NavigationItem('Plant Wise Report', '/home/<USER>/tour/plantwise', 'bi-file-earmark-text', [], false, ALL_ADMIN_ROLES),
        ], false, ALL_ADMIN_ROLES),
        new NavigationItem('BoG Zone', '/home/<USER>/zone', 'bi-file-earmark-text', [
          new NavigationItem('User Wise Report', '/home/<USER>/zone/userwise', 'bi-file-earmark-text', [], false, ALL_ADMIN_ROLES),
          new NavigationItem('Plant Wise Report', '/home/<USER>/zone/plantwise', 'bi-file-earmark-text', [], false, ALL_ADMIN_ROLES),
        ], false, ALL_ADMIN_ROLES),
        new NavigationItem('BoG RAG', '/home/<USER>/rag', 'bi-file-earmark-text', [
          new NavigationItem('Plant Wise Report', '/home/<USER>/rag/plantwise', 'bi-file-earmark-text', [], false, ALL_ADMIN_ROLES),
          new NavigationItem('QR Code Report', '/home/<USER>/rag/qrcode', 'bi-file-earmark-text', [], false, ALL_ADMIN_ROLES),
        ], false, ALL_ADMIN_ROLES),
        new NavigationItem('BoG Observation', '/home/<USER>/observation', 'bi-file-earmark-text', [
          new NavigationItem('Plant Wise Report', '/home/<USER>/observation/plantwise', 'bi-file-earmark-text', [], false, ALL_ADMIN_ROLES),
        ], false, ALL_ADMIN_ROLES),
      ], false, ALL_ADMIN_ROLES),
      new NavigationItem('Admin Management', '/home/<USER>', 'bi-gear', [
        new NavigationItem('Active Users', '/home/<USER>/activeusers', 'bi-people-fill', [], false, ALL_ADMIN_ROLES),
        new NavigationItem('Inactive Users', '/home/<USER>/inactiveusers', 'bi-person-x', [], false, ALL_ADMIN_ROLES),
        new NavigationItem('Transfer Request', '/home/<USER>/transferrequest', 'bi-arrow-left-right', [], false, ALL_ADMIN_ROLES),
        new NavigationItem('Deleted Users', '/home/<USER>/deletedusers', 'bi-person-dash-fill', [], false, SUPER_ADMIN_ONLY),
        new NavigationItem('Roles', '/home/<USER>/roles', 'bi-person-gear', [], false, ALL_ADMIN_ROLES),
      ], false, ALL_ADMIN_ROLES),
      new NavigationItem('App Settings', '/home/<USER>', 'bi-sliders', [], false, SUPER_ADMIN_ONLY),

      // --- Items restricted to Super Admin ---
      new NavigationItem('Master Management', '/home/<USER>', 'bi-database', [
        new NavigationItem('Department', '/home/<USER>/department', 'bi-people', [], false, SUPER_ADMIN_ONLY),
        new NavigationItem('Feature Master', '/home/<USER>/featuremaster', 'bi-card-list', [], false, GLOBAL_ADMIN_ONLY),
        new NavigationItem('Business Unit', '/home/<USER>/business-unit', 'bi-building', [], false, GLOBAL_ADMIN_ONLY),
        new NavigationItem('Designation', '/home/<USER>/designation', 'bi-person-badge', [], false, SUPER_ADMIN_ONLY),
        new NavigationItem('Cluster', '/home/<USER>/cluster', 'bi-geo-alt-fill', [], false, SUPER_ADMIN_ONLY),
        new NavigationItem('Plant Type', '/home/<USER>/plantType', 'bi-buildings', [], false, SUPER_ADMIN_ONLY),
        new NavigationItem('Segment', '/home/<USER>/segment', 'bi-diagram-3', [], false, SUPER_ADMIN_ONLY),
        new NavigationItem('QR Type', '/home/<USER>/qrtype', 'bi-upc-scan', [], false, SUPER_ADMIN_ONLY),
        new NavigationItem('Location Type', '/home/<USER>/locationtype', 'bi-geo-fill', [], false, SUPER_ADMIN_ONLY),
        new NavigationItem('Location', '/home/<USER>/location', 'bi-geo-alt', [], false, SUPER_ADMIN_ONLY),
        new NavigationItem('OPCO', '/home/<USER>/opco', 'bi-globe', [], false, SUPER_ADMIN_ONLY),
        new NavigationItem('Relates-to', '/home/<USER>/relatesto', 'bi-link', [], false, SUPER_ADMIN_ONLY),
        new NavigationItem('Zone', '/home/<USER>/area', 'bi-border', [], false, SUPER_ADMIN_ONLY),
        new NavigationItem('Equipments', '/home/<USER>/equipments', 'bi-tools', [], false, SUPER_ADMIN_ONLY),
        new NavigationItem('Incident Master', '/home/<USER>/incidentmaster', 'bi-exclamation-circle', [], false, SUPER_ADMIN_ONLY),
        new NavigationItem('Body Part', '/home/<USER>/bodypart', 'bi-person', [], false, SUPER_ADMIN_ONLY),
        new NavigationItem('Root Cause', '/home/<USER>/rootcause', 'bi-exclamation-diamond', [], false, SUPER_ADMIN_ONLY),
        new NavigationItem('Inspection Tool', '/home/<USER>/inspectiontool', 'bi-search', [], false, SUPER_ADMIN_ONLY),
        new NavigationItem('Recommended Type', '/home/<USER>/recommendedtype', 'bi-check2-circle', [], false, SUPER_ADMIN_ONLY),
      ], false, SUPER_ADMIN_ONLY), // Apply SUPER_ADMIN_ONLY to the main 'Master Management' item as well
      new NavigationItem('Notification Management', '/home/<USER>', 'bi-bell', [], false, SUPER_ADMIN_ONLY),

    ];
  }

  ngAfterViewInit(): void {
    // Additional fix for scrolling issue - ensure layout is recalculated after view init
    // This handles cases where the navigation items are rendered after the initial layout calculation
    setTimeout(() => {
      this.forceLayoutRecalculation();
      // Trigger change detection to ensure all navigation items are properly rendered
      this.cdr.detectChanges();
      
      // Final layout recalculation after change detection
      setTimeout(() => {
        this.forceLayoutRecalculation();
      }, 100);
    }, 200);
  }

  // Method to force layout recalculation - fixes scrolling issue on initial load
  private forceLayoutRecalculation(): void {
    // Use setTimeout to ensure this runs after the current execution cycle
    setTimeout(() => {
      // Force a reflow by accessing offsetHeight
      const sidebarContainer = document.querySelector('.sidebar-container') as HTMLElement;
      const sidebar = document.querySelector('#sidebar') as HTMLElement;
      
      if (sidebarContainer && sidebar) {
        // Trigger reflow by accessing layout properties
        const containerHeight = sidebarContainer.offsetHeight;
        const sidebarHeight = sidebar.scrollHeight;
        console.log('Forced layout recalculation:');
        console.log('- Container height:', containerHeight);
        console.log('- Sidebar scroll height:', sidebarHeight);
        console.log('- Sidebar client height:', sidebar.clientHeight);
        console.log('- Scrollable:', sidebarHeight > sidebar.clientHeight);
        
        // Force a repaint by temporarily changing a style property
        sidebar.style.transform = 'translateZ(0)';
        setTimeout(() => {
          sidebar.style.transform = '';
        }, 10);
        
        // Dispatch a resize event to ensure all components recalculate their dimensions
        window.dispatchEvent(new Event('resize'));
      }
    }, 50); // Smaller delay for more immediate effect
  }

  ngOnDestroy() {
    this.subscription.unsubscribe();
  }
}
