.sidebar-container {
    padding: 10px;
    border-radius: 10px;
    display: flex;
    height: calc(100vh - 52px); /* Full viewport height minus header height */
    min-height: 0; /* Allow flex items to shrink below content size */
}

#sidebar {
    width: 200px;
    min-width: 60px; /* Ensure minimum width when collapsed */
    height: 100%; /* Use full height of the container */
    overflow-y: auto;
    color: #ecf0f1;
    transition: width 0.3s ease-in-out;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.2);
    z-index: 100;
    text-align: left;
    border-radius: 10px;
    flex-shrink: 0; /* Prevent sidebar from shrinking */

    /* Show scrollbar for better UX during debugging */
    scrollbar-width: thin; /* Firefox */
    scrollbar-color: rgba(11, 116, 176, 0.3) transparent; /* Firefox */

    /* Custom scrollbar for Chrome, Safari and Opera */
    &::-webkit-scrollbar {
        width: 6px;
    }

    &::-webkit-scrollbar-track {
        background: transparent;
    }

    &::-webkit-scrollbar-thumb {
        background: rgba(11, 116, 176, 0.3);
        border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb:hover {
        background: rgba(11, 116, 176, 0.5);
    }
}

/* Collapsed sidebar */
#sidebar.collapsed {
    width: 60px;
}

/* Title text in navigation items */
#sidebar .nav-title {
    transition: opacity 0.2s ease-in-out, visibility 0.2s ease-in-out;
    opacity: 1;
    visibility: visible;
    white-space: nowrap;
}

/* Hide text and submenu icons when collapsed */
#sidebar.collapsed .nav-title {
    opacity: 0;
    visibility: hidden;
    width: 0;
    margin: 0;
    padding: 0;
}

#sidebar.collapsed .submenu-icon {
    display: none;
}

/* Hide submenus when collapsed */
#sidebar.collapsed .submenu {
    display: none;
}

/* List Items */
#sidebar .list-group-item,
#sidebar .nav-link {
    background: transparent;
    border: none;
    color: #000000;
    padding: 15px 10px;
    transition: all 0.3s ease-in-out;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 10px;
    overflow: hidden;
    position: relative;
}

/* Active state for list items */
#sidebar .list-group-item.active {
    background: white;
    color: #0b74b0;
    font-weight: 500;
}

/* Parent items with active children */
#sidebar .list-group-item.active .iconStyle {
    color: #0b74b0;
}

/* Enhanced styling for parent items with active children */
#sidebar .list-group-item.parent-of-active {
    background-color: rgba(11, 116, 176, 0.08);
    border-left: 2px solid rgba(11, 116, 176, 0.5);
    font-weight: 500;
    transition: all 0.3s ease-in-out;
}

#sidebar .list-group-item.parent-of-active .iconStyle {
    color: #0b74b0;
    transform: scale(1.1);
    transition: transform 0.3s ease, color 0.3s ease;
}

.active-indicator {
    width: 5px;
    height: 100%;
    background: linear-gradient(to bottom, rgb(11, 116, 176), rgb(117, 71, 156), rgb(189, 54, 129));
    border-top-left-radius: 5px;
    border-bottom-left-radius: 5px;
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    box-shadow: 0 0 8px rgba(11, 116, 176, 0.5);
    animation: glow 1.5s ease-in-out infinite alternate;
}

.iconStyle {
    margin-right: 4px;
    font-size: 16px;
    min-width: 20px;
    text-align: center;
    transition: margin 0.3s ease-in-out;
}

/* Center icons when sidebar is collapsed */
#sidebar.collapsed .iconStyle {
    margin: 0 auto;
}

/* Navigation item content container */
.nav-item-content {
    width: 100%;
    transition: all 0.3s ease-in-out;
}

#sidebar.collapsed .nav-item-content {
    justify-content: center;
}

/* Special styling for menu items with subitems when collapsed */
#sidebar.collapsed .list-group-item {
    position: relative;
}

#sidebar.collapsed .list-group-item:hover {
    background-color: rgba(11, 116, 176, 0.1);
}

/* Content margin adjustment */
.content-wrapper {
    margin-top: -12px;
    min-height: 0; /* Allow content to shrink */
    height: 100%; /* Ensure full height utilization */
}

/* Connection line between parent and child */
.submenu {
    position: relative;
}

.submenu::before {
    content: '';
    position: absolute;
    top: 0;
    left: -10px;
    width: 2px;
    height: 100%;
    background: linear-gradient(to bottom, rgba(11, 116, 176, 0.2), rgba(117, 71, 156, 0.2));
    border-radius: 1px;
}

/* Enhanced connection for active items */
.parent-of-active + .submenu::before {
    background: linear-gradient(to bottom, rgba(11, 116, 176, 0.5), rgba(117, 71, 156, 0.5));
    width: 3px;
    left: -11px;
}

/* Animation for active indicator glow effect - using opacity for better performance */
@keyframes glow {
    from {
        opacity: 0.7;
    }
    to {
        opacity: 1;
    }
}