<!-- ADD this line at the top -->
<app-toast-message></app-toast-message>

<div class="card custom-card" id="notification-list"> <!-- Changed ID -->
  <div class="card-header">
    <div class="row align-items-center"> <!-- Use align-items-center -->
      <div class="col">
        <h6 class="mb-0">Notifications List</h6> <!-- More specific title -->
      </div>
      <div class="col text-end d-flex align-items-center justify-content-end">

        <!-- *** REPLACE the existing simple Download button with this Dropdown *** -->
        <div ngbDropdown class="d-inline-block me-2">
            <button type="button" class="btn btn-sm adani-btn dropdown-toggle" id="downloadNotifExcelDropdown" ngbDropdownToggle
                [disabled]="isDownloadingExcel || listLoading">
                <span *ngIf="!isDownloadingExcel">
                    <i class="bi bi-file-earmark-excel me-1"></i> Download Excel
                </span>
                <span *ngIf="isDownloadingExcel">
                    <span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
                    Downloading {{ downloadType === 'current' ? 'Page' : (downloadType === 'all' ? 'All' : '') }}...
                </span>
            </button>
            <ul ngbDropdownMenu aria-labelledby="downloadNotifExcelDropdown">
                <li>
                    <button ngbDropdownItem (click)="downloadExcel('current')" [disabled]="isDownloadingExcel || listLoading || (notificationList?.length ?? 0) === 0">
                        <i class="bi bi-download me-1"></i> Download Current Page ({{ notificationList?.length ?? 0 }})
                    </button>
                </li>
                <li>
                    <button ngbDropdownItem (click)="downloadExcel('all')" [disabled]="isDownloadingExcel || listLoading || totalItems === 0">
                        <i class="bi bi-cloud-download me-1"></i> Download All Filtered ({{ totalItems }})
                    </button>
                </li>
            </ul>
        </div>
        <!-- *** END REPLACEMENT *** -->

        <!-- ADD Add Notification Button -->
        <button type="button" class="btn btn-sm adani-btn me-2" (click)="openAddModal()">
            <i class="bi bi-plus-circle me-1"></i> Add Notification
        </button>

        <!-- Keep existing Filter button -->
        <img src="../../../assets/svg/filter.svg" class="filter-button ms-1" (click)="openFilterModal()" alt="Filter" style="width: 35px; cursor: pointer;" title="Filter Notifications"/>
      </div>
    </div>
  </div>
    <div class="card-body">
        <div class="table-container">
            <div class="table-responsive">
                <table class="table table-bordered table-hover">
                    <thead class="table-header">
                        <tr class="text-center">
                            <th scope="col">Id</th>
                            <th scope="col">Enabled/Disabled</th>
                            <th scope="col">Image</th>
                            <th scope="col">Description</th>
                            <th scope="col">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- ADD Loading Indicator -->
                        <tr *ngIf="listLoading">
                            <td colspan="5" class="text-center p-4">
                                <div class="spinner-border spinner-border-sm text-primary" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <span class="ms-2">Loading notifications...</span>
                            </td>
                        </tr>
                         <!-- ADD No Data Message -->
                         <tr *ngIf="!listLoading && (!notificationList || notificationList.length === 0)">
                             <td colspan="5" class="text-center p-4 text-muted">
                                 <i class="bi bi-info-circle me-2"></i>No notifications found matching the criteria.
                             </td>
                         </tr>
                         <!-- Keep existing Data Rows -->
                        <tr *ngFor="let notification of notificationList" class="text-center">
                           <!-- Keep existing <td> elements -->
                           <td>{{notification.id}}</td>
                           <td><app-switch [(checked)]="notification.enabled" [requireConfirmation]="true" (checkedChange)="onSwitchToggle($event,notification.id)" onLabel="Active" offLabel="Inactive"></app-switch></td>
                           <td class="text-center align-middle">
                              <!-- Image with conditional display -->
                              <img *ngIf="notification.image && notification.image !== 'null'"
                                   [src]="notification.image"
                                   class="img-thumbnail"
                                   alt="Notification"
                                   style="max-width: 80px; max-height: 50px;">
                              <span *ngIf="!notification.image || notification.image === 'null'"
                                    class="text-muted small fst-italic">No Image</span>
                           </td>
                           <td>{{notification.title}}</td>
                           <td class="actions">
                               <!-- UPDATE Edit Button -->
                               <button class="btn btn-sm btn-outline-primary" (click)="openEditModal(notification)" title="Edit Notification">
                                   <i class="bi bi-pencil"></i>
                               </button>
                           </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <!-- Keep existing footer -->
    <div class="card-footer text-muted text-center" *ngIf="!listLoading && notificationList.length > 0"> <!-- Hide footer if no data -->
        <app-pagination [currentPage]="currentPage" [totalItems]="totalItems" [itemsPerPage]="itemsPerPage" (pageChange)="onPageChange($event)"></app-pagination>
    </div>
</div>

<!-- MODIFIED Filter Offcanvas to use filters object -->
<app-offcanvas [title]="'Filter Notifications'" *ngIf="isEditUserModalOpen" (onClickCross)="closeModal()">
    <div class="filter-container p-3">
        <form #filterForm="ngForm" (ngSubmit)="applyFilters()"> <!-- Use applyFilters -->
             <div class="row g-3">

                <div class="col-12">
                    <label class="form-label" for="filterTitle">Title/Description</label>
                    <input type="text" id="filterTitle" class="form-control" placeholder="Search by Title"
                           [(ngModel)]="filters.title" name="title" maxlength="30"
                           #filterTitle="ngModel" [ngClass]="{'is-invalid': filterTitle.invalid && (filterTitle.dirty || filterTitle.touched)}">
                    <div *ngIf="filterTitle.invalid && (filterTitle.dirty || filterTitle.touched)" class="invalid-feedback">
                        <div *ngIf="filterTitle.errors?.['maxlength']">Title cannot exceed 30 characters.</div>
                    </div>
                    <!-- Character count display - only visible when there's text -->
                    <small *ngIf="filters.title" class="text-muted d-block text-end mt-1">
                        {{ filters.title.length }}/30 characters
                    </small>
                </div>

                 <div class="col-12">
                    <label class="form-label" for="filterEnabledNotif">Enabled Status</label>
                    <select id="filterEnabledNotif" class="form-select"
                            [(ngModel)]="filters.enabled" name="enabled"> <!-- Bind to filters.enabled -->
                        <option [ngValue]="null">Any</option>
                        <option value="true">Yes</option>
                        <option value="false">No</option>
                    </select>
                </div>

                 <div class="col-12">
                    <label class="form-label" for="filterSortByNotif">Sort By</label>
                    <select id="filterSortByNotif" class="form-select"
                            [(ngModel)]="filters.sortField" name="sortField"> <!-- Bind to filters.sortField -->
                        <option [ngValue]="null">Default Sort (ID DESC)</option>
                        <option *ngFor="let field of availableSortFields" [value]="field.value">{{ field.label }}</option>
                    </select>
                     <label class="form-label mt-2" for="filterSortDirNotif">Sort Direction</label>
                     <select id="filterSortDirNotif" class="form-select"
                            [(ngModel)]="filters.sortDirection" name="sortDirection"> <!-- Bind to filters.sortDirection -->
                        <option value="DESC">Descending</option>
                        <option value="ASC">Ascending</option>
                    </select>
                </div>

                <div class="col-12 mt-4 d-grid gap-2">
                    <button type="submit" class="btn adani-btn" [disabled]="listLoading">
                        <i class="bi bi-search me-1"></i> Apply Filters
                    </button>
                     <button type="button" class="btn btn-secondary" (click)="resetFilters()" [disabled]="listLoading">
                        <i class="bi bi-arrow-clockwise me-1"></i> Reset Filters
                    </button>
                </div>

            </div>
        </form>
    </div>
</app-offcanvas>

<!-- *** ADD Add/Edit Notification Offcanvas *** -->
<app-offcanvas [title]="selectedNotificationData?.id ? 'Edit Notification' : 'Add New Notification'" *ngIf="isAddEditModalOpen" (onClickCross)="closeAddEditModal()">
    <div class="add-edit-container p-3">
        <form #notificationForm="ngForm" (ngSubmit)="saveNotification()">
            <div class="row g-3" *ngIf="selectedNotificationData !== null"> <!-- Use selectedNotificationData -->

                <!-- Title Input -->
                <div class="col-12">
                    <label for="notificationTitle" class="form-label">Title <span class="text-danger">*</span></label>
                    <input type="text" id="notificationTitle" class="form-control form-control-sm" placeholder="Enter notification title"
                           [(ngModel)]="selectedNotificationData.title" name="title" required #titleInput="ngModel"
                           maxlength="30"
                           [ngClass]="{'is-invalid': titleInput.invalid && (titleInput.dirty || titleInput.touched)}">
                    <div *ngIf="titleInput.invalid && (titleInput.dirty || titleInput.touched)" class="text-danger mt-1 small">
                        <div *ngIf="titleInput.errors?.['required']">Title is required.</div>
                        <div *ngIf="titleInput.errors?.['maxlength']">Title cannot exceed 30 characters.</div>
                    </div>
                    <!-- Character count display - only visible when there's text -->
                    <small *ngIf="selectedNotificationData.title" class="text-muted d-block text-end mt-1">
                        {{ selectedNotificationData.title.length }}/30 characters
                    </small>
                </div>

                <!-- Message Textarea -->
                <div class="col-12">
                    <label for="notificationMessage" class="form-label">Message <span class="text-danger">*</span></label>
                    <textarea id="notificationMessage" class="form-control form-control-sm" rows="4" placeholder="Enter notification message"
                              [(ngModel)]="selectedNotificationData.message" name="message" required #messageInput="ngModel"
                              maxlength="200"></textarea>
                    <div class="d-flex justify-content-between mt-1">
                        <div *ngIf="messageInput.invalid && (messageInput.dirty || messageInput.touched)" class="text-danger small">
                            <div *ngIf="messageInput.errors?.['required']">Message is required.</div>
                            <div *ngIf="messageInput.errors?.['maxlength']">Message cannot exceed 200 characters.</div>
                        </div>
                        <!-- Character count display - only visible when there's text -->
                        <small *ngIf="selectedNotificationData.message" class="text-muted ms-auto">
                            {{ selectedNotificationData.message.length }}/200 characters
                        </small>
                    </div>
                </div>

                <!-- Image Upload -->
                <div class="col-12">
                    <label for="notificationImageFile" class="form-label">Upload Image (Optional)</label>
                    <input class="form-control form-control-sm" type="file" id="notificationImageFile" name="imageFile"
                           (change)="onFileSelect($event)" accept="image/png, image/jpeg, image/gif, image/webp">

                    <!-- Display Selected File Info -->
                    <div *ngIf="currentImageFile" class="mt-1 small text-muted d-flex align-items-center">
                       <i class="bi bi-paperclip me-1"></i>
                       <span>{{ currentImageFile.name }} ({{ (currentImageFile.size / 1024).toFixed(1) }} KB)</span>
                       <button type="button" class="btn btn-sm btn-outline-danger p-0 px-1 ms-2" (click)="clearSelectedFile()" title="Clear Selection">
                           <i class="bi bi-x-lg" style="font-size: 0.8em;"></i>
                       </button>
                    </div>

                     <!-- Image Preview -->
                     <div class="mt-2 text-center border rounded p-1" style="max-width: 150px;">
                         <!-- Show image only if it exists -->
                         <img *ngIf="imagePreviewUrl && imagePreviewUrl !== 'null'"
                           [src]="imagePreviewUrl"
                           alt="Preview"
                           class="img-fluid"
                           style="max-height: 100px;">
                         <!-- Show "No Image" text when no image -->
                         <div *ngIf="!imagePreviewUrl || imagePreviewUrl === 'null'"
                              class="py-3 px-2 text-muted small fst-italic">
                           No Image
                         </div>
                     </div>
                     <!-- End Image Preview -->
                </div>
                <!-- END Image Upload -->

                <!-- Plant Targeting -->
                <div class="col-12 border-top pt-3">
                    <div class="form-check form-switch mb-2">
                        <input class="form-check-input" type="checkbox" role="switch" id="sendToAllPlants"
                               [(ngModel)]="selectedNotificationData.sendToAllPlants" name="sendToAllPlants"
                               (ngModelChange)="onSendToAllPlantsChange($event)">
                        <label class="form-check-label" for="sendToAllPlants">Send to All Plants</label>
                    </div>
                    <div *ngIf="!selectedNotificationData.sendToAllPlants">
                        <label for="targetPlants" class="form-label">Select Target Plants <span class="text-danger">*</span></label>
                        <ng-select id="targetPlants" [items]="availablePlants" bindLabel="name" bindValue="id"
                                   [multiple]="true" placeholder="Select plants"
                                   [(ngModel)]="selectedNotificationData.targetPlantIds" name="targetPlantIds"
                                   [required]="!selectedNotificationData.sendToAllPlants" #targetPlantsInput="ngModel"
                                   (change)="getAdminList()">
                            <ng-option *ngFor="let plant of availablePlants" [value]="plant.id">{{ plant.name }}</ng-option>
                        </ng-select>
                        <div *ngIf="targetPlantsInput.invalid && (targetPlantsInput.dirty || targetPlantsInput.touched) && !selectedNotificationData.sendToAllPlants" class="text-danger mt-1 small">
                            Please select at least one plant.
                        </div>
                    </div>
                </div>

                <!-- User Targeting -->
                <div class="col-12 border-top pt-3">
                    <div class="form-check form-switch mb-2">
                        <input class="form-check-input" type="checkbox" role="switch" id="sendToAllUsers"
                               [(ngModel)]="selectedNotificationData.sendToAllUsers" name="sendToAllUsers"
                               (ngModelChange)="onSendToAllUsersChange($event)">
                        <label class="form-check-label" for="sendToAllUsers">Send to All Users</label>
                    </div>
                    <div *ngIf="!selectedNotificationData.sendToAllUsers">
                        <label for="targetUsers" class="form-label">Select Target Users <span class="text-danger">*</span></label>
                        <ng-select id="targetUsers" [items]="availableAdmins" bindLabel="firstName" bindValue="id"
                                   [multiple]="true" placeholder="Select users"
                                   [(ngModel)]="selectedNotificationData.targetUserIds" name="targetUserIds"
                                   [required]="!selectedNotificationData.sendToAllUsers" #targetUsersInput="ngModel"
                                   [loading]="adminsLoading">
                             <ng-option *ngFor="let admin of availableAdmins" [value]="admin.id">{{ admin.firstName }} {{ admin.lastName }}</ng-option>
                        </ng-select>
                        <div *ngIf="targetUsersInput.invalid && (targetUsersInput.dirty || targetUsersInput.touched) && !selectedNotificationData.sendToAllUsers" class="text-danger mt-1 small">
                            Please select at least one user.
                        </div>
                        <div *ngIf="!selectedNotificationData.sendToAllPlants && selectedNotificationData.targetPlantIds.length === 0" class="text-muted small mt-1">
                            Please select plants first to load available users.
                        </div>
                    </div>
                </div>

                <!-- Enabled Toggle -->
                <div class="col-12 border-top pt-3">
                    <div class="form-check form-switch">
                        <input class="form-check-input" type="checkbox" role="switch" id="notificationEnabled"
                               [(ngModel)]="selectedNotificationData.enabled" name="enabled">
                        <label class="form-check-label" for="notificationEnabled">Enabled (Notification Active)</label>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="col-12 mt-4 d-flex justify-content-end gap-2 border-top pt-3">
                    <button type="button" class="btn btn-sm btn-secondary" (click)="closeAddEditModal()">Cancel</button>
                    <button type="submit" class="btn btn-sm adani-btn" [disabled]="notificationForm.invalid || saveLoading">
                        <span *ngIf="!saveLoading">
                            <i class="bi bi-check-circle me-1"></i> {{ selectedNotificationData.id ? 'Update' : 'Create' }} Notification
                        </span>
                        <span *ngIf="saveLoading">
                            <span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
                            {{ selectedNotificationData.id ? 'Updating...' : 'Creating...' }}
                        </span>
                    </button>
                </div>

            </div>
             <!-- Show loading or error if selectedNotificationData is null somehow -->
             <div *ngIf="selectedNotificationData === null && isAddEditModalOpen" class="text-center p-4">
                 <p>Loading form...</p> <!-- Or an error message -->
             </div>
        </form>
    </div>
</app-offcanvas>
<!-- *** END Add/Edit Notification Offcanvas *** -->
