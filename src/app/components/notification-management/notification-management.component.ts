// **** Keep existing imports ****
import { Component, OnInit, ViewChild, inject, ChangeDetectorRef } from '@angular/core';
import { NotificationManagementService } from '../../services/notification-management/notification-management.service';
import { createAxiosConfig } from '../../core/utilities/axios-param-config';
import { CommonModule } from '@angular/common';
import { PlantManagementService } from '../../services/plant-management/plant-management.service'; // Import Plant Service
import { AdminService } from '../../services/admin/admin.service'; // Import Admin Service
import { SwitchComponent } from "../../shared/switch/switch.component";
import { PaginationComponent } from "../../shared/pagination/pagination.component";
import { OffcanvasComponent } from "../../shared/offcanvas/offcanvas.component";
import { UpdateService } from '../../services/update/update.service';
// *** ADD THESE IMPORTS ***
import { FormsModule, NgForm } from '@angular/forms'; // Needed for filter [(ngModel)]
import { ToastMessageComponent } from '../../shared/toast-message/toast-message.component';
import { NgbDropdownModule } from '@ng-bootstrap/ng-bootstrap';
import * as XLSX from 'xlsx';
import { UploadService } from '../../services/upload/upload.service';
import { NgSelectModule } from '@ng-select/ng-select'; // Import NgSelectModule

// Interface for Filters
interface NotificationFilter {
    title?: string | null;
    enabled?: string | null; // 'true'/'false'/null
    sortField?: string | null;
    sortDirection?: 'ASC' | 'DESC';
}

// Interface for Notification Data (List View)
interface NotificationItem {
    id: number;
    title: string;
    message?: string; // Add message
    image: string | null;
    enabled: boolean;
    createdAt?: string | Date;
    updatedAt?: string | Date;
    // Add optional targeting fields, assuming they might come from the list API
    targetPlantIds?: number[];
    targetUserIds?: number[];
}

// Interface for Form Data (Add/Edit)
interface NotificationFormData {
    id: number | null; // Use null for new items
    title: string;
    message: string; // Added message field
    image: string | null;
    enabled: boolean;
    sendToAllPlants: boolean;
    targetPlantIds: number[]; // Array of selected plant IDs
    sendToAllUsers: boolean;
    targetUserIds: number[]; // Array of selected user IDs
}

// Simple interfaces for dropdowns
interface Plant {
    id: number;
    name: string;
}
interface AdminUser {
    id: number;
    firstName: string;
    lastName: string;
    // Add other relevant fields if needed
}


@Component({
  selector: 'app-notification-management',
  standalone: true,
  imports: [
      // **** Keep existing imports ****
      CommonModule,
      SwitchComponent,
      PaginationComponent,
      OffcanvasComponent,
      FormsModule,
      NgbDropdownModule,
      ToastMessageComponent,
      NgSelectModule // Import NgSelectModule
    ],
  templateUrl: './notification-management.component.html',
  styleUrl: './notification-management.component.scss'
})
export class NotificationManagementComponent implements OnInit {
  // *** ADD ViewChild for Toast ***
  @ViewChild(ToastMessageComponent) toast!: ToastMessageComponent;

  // **** Keep existing properties ****
  notificationList: NotificationItem[] = []; // Use interface
  currentPage = 1;
  itemsPerPage = 10;
  totalItems = 0;
  isEditUserModalOpen: boolean = false; // Filter modal
  isAddEditModalOpen: boolean = false; // Add/Edit modal
  // Use the new form data interface for the selected item being edited/created
  selectedNotificationData: NotificationFormData | null = null;

  // Filter properties
  filters: NotificationFilter = {
      title: null,
      enabled: null,
      sortField: 'id',
      sortDirection: 'DESC'
  };
   availableSortFields = [ // Example sort fields
        { value: 'id', label: 'ID' },
        { value: 'title', label: 'Description/Title' },
        { value: 'enabled', label: 'Status' },
        { value: 'createdAt', label: 'Created Date' } // If available
    ];

  // *** ADD Loading State ***
  listLoading = false;

  // *** ADD Download State ***
  isDownloadingExcel = false;
  downloadType: 'current' | 'all' | null = null;

  // *** ADD File Upload State ***
  currentImageFile: File | null = null;
  imagePreviewUrl: string | ArrayBuffer | null = null;
  saveLoading = false;

  // Dropdown data
  availablePlants: Plant[] = [];
  availableAdmins: AdminUser[] = [];
  plantsLoading = false;
  adminsLoading = false;

  // Inject services
  private notificationService = inject(NotificationManagementService);
  private updateService = inject(UpdateService);
  private uploadService = inject(UploadService);
  private plantService = inject(PlantManagementService); // Inject Plant Service
  private adminService = inject(AdminService); // Inject Admin Service
  private cdr = inject(ChangeDetectorRef);

  constructor() { }

  async ngOnInit(): Promise<void> {
    this.loadNotifications(this.currentPage);
    await this.loadPlants(); // Load plants initially for the form
    await this.loadAdmins(); // Load admins initially for the form
  }

  // --- Load Dropdown Data ---
  async loadPlants(): Promise<void> {
      this.plantsLoading = true;
      const plantParams = { sort: 'name,ASC', filter: ['enabled||eq||true'], limit: 1000 };
      const axiosConfig = createAxiosConfig(plantParams);
      try {
          const response = await this.plantService.getPlants(axiosConfig);
          this.availablePlants = response?.data ?? response ?? [];
          console.log(`Loaded ${this.availablePlants.length} plants.`);
      } catch (error) {
          console.error("Error fetching plants:", error);
          this.availablePlants = [];
          this.toast?.showErrorToast('Failed to load plant list.');
      } finally {
          this.plantsLoading = false;
      }
  }

  async loadAdmins(): Promise<void> {
      this.adminsLoading = true;
      // Adjust filters as needed (e.g., only active admins)
      const adminParams = { sort: 'firstName,ASC', filter: ['enabled||eq||true', 'status||eq||1'], limit: 1000 };
      const axiosConfig = createAxiosConfig(adminParams);
      try {
          const response = await this.adminService.getAdmin(axiosConfig);
          // Map response to AdminUser interface if necessary
          this.availableAdmins = (response?.data ?? response ?? []).map((admin: any) => ({
              id: admin.id,
              firstName: admin.firstName,
              lastName: admin.lastName
          }));
          console.log(`Loaded ${this.availableAdmins.length} admins.`);
      } catch (error) {
          console.error("Error fetching admins:", error);
          this.availableAdmins = [];
          this.toast?.showErrorToast('Failed to load admin list.');
      } finally {
          this.adminsLoading = false;
      }
  }
  // --- End Load Dropdown Data ---

  // --- Filter Modal Methods (Added/Updated) ---
  openFilterModal(): void {
      this.isEditUserModalOpen = true; // Keep original name if preferred
  }
  closeModal(): void { // Renamed for consistency
      this.isEditUserModalOpen = false;
  }

  @ViewChild('filterForm') filterForm!: NgForm;

  applyFilters(): void {
      if (this.listLoading) return;

      // Check if form is valid before applying filters
      if (this.filterForm && this.filterForm.invalid) {
          this.toast?.showErrorToast("Please correct the validation errors before applying filters.");
          return;
      }

      // We've removed pattern validation for title to allow all characters
      // The isValidName method is still available but not used for validation here

      // Trim string values to prevent whitespace-only searches
      if (this.filters.title) {
          this.filters.title = this.filters.title.trim();
      }

      this.currentPage = 1;
      this.loadNotifications(this.currentPage);
      this.closeModal();
  }

  resetFilters(): void {
      if (this.listLoading) return;
      this.filters = { title: null, enabled: null, sortField: 'id', sortDirection: 'DESC' };
      this.currentPage = 1;
      this.loadNotifications(this.currentPage);
      // Optionally close modal: this.closeModal();
  }

  // --- Validation Helper Methods ---
  private isValidName(name: string): boolean {
      // Updated regex to allow alphanumeric characters and special characters
      // This will allow almost all characters except those that might cause issues in HTML/JS
      const nameRegex = /^[a-zA-Z0-9\s!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]*$/;
      return nameRegex.test(name);
  }
  // --- End Filter Methods ---

  // --- Add/Edit Modal Methods (Updated) ---
  openAddModal(): void {
      // Initialize a new notification form data object
      this.selectedNotificationData = {
          id: null, // Indicate new item
          title: '',
          message: '', // Initialize message
          image: null,
          enabled: true,
          sendToAllPlants: true, // Default to all plants
          targetPlantIds: [],
          sendToAllUsers: true, // Default to all users
          targetUserIds: []
      };
      this.currentImageFile = null;
      this.imagePreviewUrl = null; // Will show placeholder in template
      this.isAddEditModalOpen = true;
      this.saveLoading = false;
      // Reset file input visually
      const fileInput = document.getElementById('notificationImageFile') as HTMLInputElement;
      if (fileInput) fileInput.value = '';
  }

  openEditModal(notification: NotificationItem): void {
      // **Important**: Fetch full details if list view is partial
      // Assuming 'notification' has all necessary fields for now, including message, plant/user targets
      // If not, you'd need an API call here: this.notificationService.getNotificationById(notification.id)

      // Handle the case where image is 'null' string
      const imageValue = notification.image && notification.image !== 'null' ? notification.image : null;

      this.selectedNotificationData = {
          id: notification.id,
          title: notification.title,
          message: notification.message || '', // Use existing message or default
          image: imageValue,
          enabled: notification.enabled,
          // **Backend Dependent**: Determine these flags/arrays based on the fetched notification data
          // Example placeholder logic (replace with actual logic based on your API response):
          sendToAllPlants: !notification.targetPlantIds || notification.targetPlantIds.length === 0, // Example: Assume empty array means all
          targetPlantIds: notification.targetPlantIds || [],
          sendToAllUsers: !notification.targetUserIds || notification.targetUserIds.length === 0, // Example
          targetUserIds: notification.targetUserIds || []
      };
      this.currentImageFile = null; // Reset file selection
      this.imagePreviewUrl = imageValue; // Show existing image or null (which will show placeholder)
      this.isAddEditModalOpen = true;
      this.saveLoading = false;
      // Reset file input visually
      const fileInput = document.getElementById('notificationImageFile') as HTMLInputElement;
      if (fileInput) fileInput.value = '';
  }

  closeAddEditModal(): void {
      this.isAddEditModalOpen = false;
      this.selectedNotificationData = null; // Clear form data
      this.currentImageFile = null;
      this.imagePreviewUrl = null;
      this.saveLoading = false;
  }

  // --- Helper to handle switch changes and clear selections ---
  onSendToAllPlantsChange(isAll: boolean): void {
      if (this.selectedNotificationData) {
          this.selectedNotificationData.sendToAllPlants = isAll;
          if (isAll) {
              this.selectedNotificationData.targetPlantIds = []; // Clear specific selection if switching to all
          }
      }
  }

  onSendToAllUsersChange(isAll: boolean): void {
      if (this.selectedNotificationData) {
          this.selectedNotificationData.sendToAllUsers = isAll;
          if (isAll) {
              this.selectedNotificationData.targetUserIds = []; // Clear specific selection if switching to all
          }
      }
  }

  // Load users based on selected plants
  async getAdminList(): Promise<void> {
      if (!this.selectedNotificationData || this.selectedNotificationData.sendToAllPlants ||
          this.selectedNotificationData.targetPlantIds.length === 0) {
          return;
      }

      this.adminsLoading = true;
      try {
          const response = await this.notificationService.getUsersByPlantIds({
              plantIds: this.selectedNotificationData.targetPlantIds
          });

          if (response && response.data) {
              this.availableAdmins = response.data.map((admin: any) => ({
                  id: admin.id,
                  firstName: admin.firstName,
                  lastName: admin.lastName
              }));
              console.log(`Loaded ${this.availableAdmins.length} users for selected plants.`);
          } else {
              this.availableAdmins = [];
              console.log('No users found for selected plants.');
          }
      } catch (error) {
          console.error('Error fetching users by plant IDs:', error);
          this.toast?.showErrorToast('Failed to load users for selected plants.');
          this.availableAdmins = [];
      } finally {
          this.adminsLoading = false;
      }
  }
  // --- End Helper ---


  @ViewChild('notificationForm') notificationForm!: NgForm;

  async saveNotification(): Promise<void> {
      if (!this.selectedNotificationData || this.saveLoading) return;

      // Check form validity
      if (this.notificationForm && this.notificationForm.invalid) {
          this.toast?.showErrorToast("Please correct the validation errors before saving.");
          // Mark fields as touched to show validation errors
          Object.values(this.notificationForm.controls).forEach(control => {
              control.markAsTouched();
          });
          return;
      }

      // We've removed pattern validation for title to allow all characters
      // The isValidName method is still available but not used for validation here

      // Trim string values
      if (this.selectedNotificationData.title) {
          this.selectedNotificationData.title = this.selectedNotificationData.title.trim();
      }
      if (this.selectedNotificationData.message) {
          this.selectedNotificationData.message = this.selectedNotificationData.message.trim();
      }

       // Validation: Check if specific plants/users are selected when 'All' is off
       if (!this.selectedNotificationData.sendToAllPlants && this.selectedNotificationData.targetPlantIds.length === 0) {
           this.toast?.showErrorToast("Please select at least one plant or enable 'Send to All Plants'.");
           return;
       }
       if (!this.selectedNotificationData.sendToAllUsers && this.selectedNotificationData.targetUserIds.length === 0) {
           this.toast?.showErrorToast("Please select at least one user or enable 'Send to All Users'.");
           return;
       }

      this.saveLoading = true;
      let uploadedImageUrl: string | null = this.selectedNotificationData.image; // Start with existing image URL

      // 1. Upload image if a new one is selected
      if (this.currentImageFile) {
          const fileFormData = new FormData();
          fileFormData.append('file', this.currentImageFile, this.currentImageFile.name);
          try {
              const uploadResponse = await this.uploadService.upload(fileFormData);
              if (uploadResponse && typeof uploadResponse === 'string' && uploadResponse.trim() !== '') {
                  uploadedImageUrl = uploadResponse; // Use the new URL
              } else {
                  throw new Error(`Invalid upload response: ${uploadResponse}`);
              }
          } catch (uploadError) {
              console.error('Error uploading image:', uploadError);
              this.toast?.showErrorToast('Failed to upload image. Please try again.');
              this.saveLoading = false;
              return;
          }
      }

      // 2. Prepare payload for custom notification (following Vue.js reference)
      let notificationType = 0; // Default: ALL (send to all plants and all users)

      if (this.selectedNotificationData.sendToAllPlants && !this.selectedNotificationData.sendToAllUsers) {
          notificationType = 1; // Send to all plants but specific users
      } else if (!this.selectedNotificationData.sendToAllPlants && this.selectedNotificationData.sendToAllUsers) {
          notificationType = 2; // Send to specific plants but all users in those plants
      } else if (!this.selectedNotificationData.sendToAllPlants && !this.selectedNotificationData.sendToAllUsers) {
          notificationType = 3; // Send to specific plants and specific users
      }

      // Prepare the custom notification payload
      const customPayload: any = {
          title: this.selectedNotificationData.title,
          message: this.selectedNotificationData.message,
          image: uploadedImageUrl,
          enabled: this.selectedNotificationData.enabled,
          type: notificationType,
          plantIds: this.selectedNotificationData.sendToAllPlants ? [] : this.selectedNotificationData.targetPlantIds,
          userIds: this.selectedNotificationData.sendToAllUsers ? [] : this.selectedNotificationData.targetUserIds,
          sendToAll: this.selectedNotificationData.sendToAllUsers,
          // Include plant array in the format expected by the backend
          plant: this.selectedNotificationData.sendToAllPlants ? [] :
                 this.selectedNotificationData.targetPlantIds.map(id => ({ id: id }))
      };

      console.log("Saving notification payload:", JSON.stringify(customPayload, null, 2));

      // 3. Call API (Create or Update)
      try {
          if (this.selectedNotificationData.id) {
              // For editing existing notification
              // Add createdBy field to match the Vue.js reference code
              const loggedInAdminId = this.getLoggedInAdminId(); // Get the logged-in admin ID

              const updateData = {
                  tableName: 'notifications',
                  id: this.selectedNotificationData.id,
                  data: {
                      ...customPayload,
                      createdBy: loggedInAdminId // Add createdBy field
                  }
              };

              console.log('Updating notification with data:', JSON.stringify(updateData, null, 2));
              await this.updateService.update(updateData);
              this.toast?.showSuccessToast("Notification updated successfully.");
          } else {
              // For creating new notification - use the custom endpoint that sends notifications
              console.log('Creating new notification with data:', JSON.stringify(customPayload, null, 2));
              await this.notificationService.createNotificationCustom(customPayload);
              this.toast?.showSuccessToast("Notification sent successfully.");
          }
          this.closeAddEditModal();
          this.loadNotifications(this.currentPage); // Refresh list
      } catch (error: any) {
          console.error("Error saving/sending notification:", error);
          this.toast?.showErrorToast(error?.response?.data?.message || "Failed to save/send notification.");
      } finally {
          this.saveLoading = false;
      }
  }
  // --- End Add/Edit Methods (Updated) ---

  // --- ADD File Handling Methods ---
  onFileSelect(event: Event): void {
      const element = event.currentTarget as HTMLInputElement;
      const fileList: FileList | null = element.files;

      if (fileList && fileList.length > 0) {
          const file = fileList[0];
          const allowedTypes = ['image/png', 'image/jpeg', 'image/gif', 'image/webp']; // Common web image types
          const maxSize = 5 * 1024 * 1024; // 5MB limit

          // Validate Type
          if (!allowedTypes.includes(file.type)) {
              this.toast?.showErrorToast('Invalid file type. Please select a PNG, JPG, GIF, or WEBP image.');
              this.clearSelectedFile(element); // Clear the input
              return;
          }

          // Validate Size
          if (file.size > maxSize) {
              this.toast?.showErrorToast(`File size exceeds ${maxSize / 1024 / 1024}MB limit.`);
              this.clearSelectedFile(element); // Clear the input
              return;
          }

          // Store file and generate preview
          this.currentImageFile = file;
          this.generatePreview(file);
          // Update the form model's image property if needed, or rely on imagePreviewUrl
          if (this.selectedNotificationData) {
              // Maybe clear the existing URL when a new file is chosen?
              // this.selectedNotificationData.image = null;
          }

      } else {
          this.clearSelectedFile(element);
      }
  }

  clearSelectedFile(inputElement?: HTMLInputElement | null): void {
      this.currentImageFile = null;
      this.imagePreviewUrl = null;
      // Restore original image preview if editing and an original image exists
      if (this.selectedNotificationData?.id && this.selectedNotificationData.image) {
          this.imagePreviewUrl = this.selectedNotificationData.image;
      }
      // Note: When imagePreviewUrl is null, the template will show the placeholder image

      // Optionally clear the image URL in the form model when the file is cleared
      // if (this.selectedNotificationData) {
      //     this.selectedNotificationData.image = null;
      // }

      const fileInput = inputElement ?? document.getElementById('notificationImageFile') as HTMLInputElement;
      if (fileInput) {
          fileInput.value = ''; // This clears the browser's file selection state
      }
      this.cdr.detectChanges(); // Ensure UI updates
  }

  generatePreview(file: File): void {
      const reader = new FileReader();
      reader.onload = () => {
          this.imagePreviewUrl = reader.result;
          this.cdr.detectChanges(); // Trigger change detection for preview update
      };
      reader.onerror = (error) => {
          console.error('Error reading file for preview:', error);
          this.imagePreviewUrl = null; // Clear preview on error
          this.toast?.showErrorToast("Could not generate image preview.");
          this.cdr.detectChanges();
      };
      reader.readAsDataURL(file); // Read file as Data URL for preview
  }
  // --- END File Handling Methods ---

  // Updated onSwitchToggle and update methods
  onSwitchToggle($event: boolean, id: number) {
    const loggedInAdminId = this.getLoggedInAdminId(); // Get the logged-in admin ID
    const data = {
      tableName: 'notifications',
      id: id,
      data: {
        enabled: $event,
        createdBy: loggedInAdminId // Add createdBy field
      }
    };
    console.log('Toggle switch with data:', JSON.stringify(data, null, 2));
    this.update(data).catch(err => console.error("Switch toggle update failed:", err));
  }

  async update(data: any) {
      try {
          await this.updateService.update(data);
          this.toast?.showSuccessToast("Status updated successfully.");
      } catch(error) {
          console.error("Update error:", error);
          this.toast?.showErrorToast("Failed to update status.");
          // Revert UI state if needed
          this.loadNotifications(this.currentPage); // Reload list to show current state
          throw error; // Re-throw if needed by caller
      }
  }

  // Renamed from getNotifications, added loading state and filtering/sorting
  async loadNotifications(page: number): Promise<void> { // Renamed method
    if (this.listLoading) return; // Prevent concurrent loads
    this.listLoading = true;
    this.notificationList = []; // Clear list

    const filterParams: string[] = [];
    // Add filters based on this.filters object
    if (this.filters.title) {
        filterParams.push(`title||$contL||${this.filters.title}`);
    }
    if (this.filters.enabled !== null && this.filters.enabled !== undefined && this.filters.enabled !== '') {
        filterParams.push(`enabled||$eq||${this.filters.enabled}`);
    }

    const data = {
      page: page,
      limit: this.itemsPerPage,
      sort: `${this.filters.sortField || 'id'},${this.filters.sortDirection || 'DESC'}`,
      filter: filterParams // Pass constructed filters
    };

    try {
        const response = await this.getNotificationList(data); // Call helper
        this.notificationList = response.data ?? response ?? [];
        this.totalItems = response.total ?? 0;
    } catch (error) {
        console.error("Error loading notifications:", error);
        this.notificationList = [];
        this.totalItems = 0;
        this.toast?.showErrorToast("Failed to load notifications.");
    } finally {
        this.listLoading = false;
    }
  }

  // Kept original helper function, now returns data/total object
  async getNotificationList(data: any): Promise<{ data: NotificationItem[], total: number }> {
    const param = createAxiosConfig(data);
    // Assuming the service returns { data: [], total: number } structure
    const response = await this.notificationService.getNotifications(param);
    return { data: response.data, total: response.total };
  }

  onPageChange(page: number) {
    // *** ADD this check ***
    if (this.currentPage === page || this.listLoading) return;
    this.currentPage = page;
    this.loadNotifications(this.currentPage); // Use new method name
  }


  // --- *** ADD THESE NEW METHODS for Excel Download *** ---

    // Helper to get current list data
    getCurrentListData(): NotificationItem[] | undefined {
        return this.notificationList;
    }

    // Fetch ALL notifications matching current filters (no pagination)
    async fetchAllFilteredNotifications(): Promise<NotificationItem[] | null> {
        this.listLoading = true; // Indicate loading
        const filterParams: string[] = [];
        if (this.filters.title) { filterParams.push(`title||$contL||${this.filters.title}`); }
        if (this.filters.enabled !== null && this.filters.enabled !== undefined && this.filters.enabled !== '') {
             filterParams.push(`enabled||$eq||${this.filters.enabled}`);
        }

        const data = {
            limit: 10000, // Large limit for "all"
            sort: `${this.filters.sortField || 'id'},${this.filters.sortDirection || 'DESC'}`,
            filter: filterParams
        };

        try {
            const params = createAxiosConfig(data);
            console.log('API Request Params (Notifications Download - All Data):', JSON.stringify(params, null, 2));
            // Use the helper function but expect the full data array
            const response = await this.getNotificationList(data); // Re-use existing helper
            return response.data ?? response ?? [];
        } catch (error: any) {
            console.error("Error fetching all notifications for download:", error);
            this.toast?.showErrorToast(error?.response?.data?.message || "Failed to retrieve full data for download.");
            return null;
        } finally {
             this.listLoading = false; // Reset loading indicator
        }
    }

    // Main download function
    async downloadExcel(type: 'current' | 'all') {
        if (this.isDownloadingExcel || this.listLoading) return; // Prevent concurrent actions

        this.isDownloadingExcel = true;
        this.downloadType = type;
        this.toast?.showSuccessToast(`Preparing download for ${type === 'current' ? 'current page' : 'all filtered'} notifications...`);

        let dataToExport: NotificationItem[] | null = null;

        try {
            // 1. Get Data
            if (type === 'all') {
                dataToExport = await this.fetchAllFilteredNotifications();
            } else { // 'current'
                dataToExport = this.getCurrentListData() ?? null;
                if (dataToExport === undefined) { dataToExport = null; }
            }

            // 2. Check data
            if (dataToExport === null) { this.toast?.showErrorToast("Failed to retrieve data for download."); return; }
            if (!dataToExport || dataToExport.length === 0) { this.toast?.showErrorToast(`No notifications available to download.`); return; }

            console.log(`Fetched ${dataToExport.length} notifications for Excel export (${type}).`);

            // 3. Transform data
            const dataForExcel = dataToExport.map(n => ({
                'Notification ID': n.id,
                'Title/Description': n.title,
                'Status': n.enabled ? 'Active' : 'Inactive',
                'Image URL': n.image ?? '', // Include URL if helpful
                'Created At': n.createdAt ? new Date(n.createdAt).toLocaleString() : 'N/A',
                'Updated At': n.updatedAt ? new Date(n.updatedAt).toLocaleString() : 'N/A',
            }));

            // 4. Create Worksheet and Workbook
            const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(dataForExcel);
            const wb: XLSX.WorkBook = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(wb, ws, 'Notifications'); // Sheet name

            // 5. Generate File Name
            const dateStr = new Date().toISOString().slice(0, 10);
            const typeStr = type === 'current' ? `Page${this.currentPage}` : 'All';
            const fileName = `Notifications_${typeStr}_${dateStr}.xlsx`;

            // 6. Trigger Download
            XLSX.writeFile(wb, fileName);
            this.toast?.showSuccessToast(`Excel file download started (${type}).`);

        } catch (error) {
            console.error(`Error generating Excel file (${type}):`, error);
            this.toast?.showErrorToast(`An error occurred while generating the Excel file (${type}).`);
        } finally {
            this.isDownloadingExcel = false;
            this.downloadType = null;
        }
    }
    // --- *** END ADDED: Excel Download Logic *** ---

    // Helper method to get the logged-in admin ID
    private getLoggedInAdminId(): number {
        try {
            // Get the user data from localStorage
            const userData = localStorage.getItem('user');
            if (userData) {
                const user = JSON.parse(userData);
                return user.id || 0; // Return the user ID or 0 if not found
            }
            return 0; // Default value if user data not found
        } catch (error) {
            console.error('Error getting logged-in admin ID:', error);
            return 0; // Default value on error
        }
    }

} // End of Class
