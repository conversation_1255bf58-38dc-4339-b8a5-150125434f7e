<app-toast-message></app-toast-message>
<div class="card" id="activated-qrcode" *ngIf="canManageFeatureMaster">
  <div class="card-header d-flex align-items-center justify-content-between">
    <div>
      <h6 class="mb-0">Feature Master</h6>
    </div>
    <div class="d-flex align-items-center">
        <button class="btn-sm adani-btn ms-2" (click)="openCreateModal()" title="Create New Feature">
          <i class="bi bi-plus-circle"></i> Create New
        </button>
        <img src="../../../assets/svg/filter.svg" class="filter-button ms-3" (click)="openFilterModal()" alt="Filter"
          style="width: 35px;" />
      </div>
    </div>
  <div class="card-body">
    <div class="table-container">
        <div class="table-responsive">
            <table class="table table-bordered table-hover">
                <thead class="table-header">
                  <tr class="text-center">
                    <th scope="col">Id</th>
                    <th scope="col">Feature Name</th>
                  </tr>
                </thead>
                <tbody>
                   <tr *ngIf="listLoading">
                      <td colspan="2" class="text-center">
                          <div class="spinner-border spinner-border-sm" role="status">
                              <span class="visually-hidden">Loading...</span>
                          </div>
                          Loading Features...
                      </td>
                  </tr>
                   <tr *ngIf="!listLoading && featureMasterList.length === 0">
                      <td colspan="2" class="text-center">No features found.</td>
                   </tr>
                   <tr *ngFor="let feature of featureMasterList">
                     <td class="text-center">{{ feature.id }}</td>
                     <td class="text-center">{{ feature.title }}</td>
                  </tr>
                </tbody>
              </table>
        </div>
    </div>
  </div>
  <div class="card-footer text-muted text-center">
    <app-pagination [currentPage]="currentPage" [totalItems]="totalItems" [itemsPerPage]="itemsPerPage"
    (pageChange)="onPageChange($event)"></app-pagination>
  </div>
</div>
<div *ngIf="!canManageFeatureMaster" class="alert alert-danger text-center">
    You do not have permission to access this page.
</div>

<app-offcanvas [title]="'Filter Features'" *ngIf="isFilterModalOpen" (onClickCross)="closeFilterModal()">
<div class="filter-container p-3">
    <form #filterForm="ngForm" (ngSubmit)="applyFilters()">
        <div class="row g-3">
            <div class="col-12">
                <label class="form-label" for="filterFeatureName">Feature Name</label>
                <input type="text" id="filterFeatureName" class="form-control" placeholder="Search by Feature Name"
                       [(ngModel)]="filters.name" name="name" #filterName="ngModel"
                       pattern="^[a-zA-Z\s]*$" maxlength="30"
                       [ngClass]="{'is-invalid': filterName.invalid && (filterName.dirty || filterName.touched)}">
                <div *ngIf="filterName.invalid && (filterName.dirty || filterName.touched)" class="invalid-feedback">
                    <div *ngIf="filterName.errors?.['pattern']">Feature name should contain only alphabets.</div>
                </div>
            </div>
            <div class="col-12">
                <label class="form-label" for="filterSortByFeature">Sort By</label>
                <select id="filterSortByFeature" class="form-select"
                        [(ngModel)]="filters.sortField" name="sortField">
                    <option *ngFor="let field of availableSortFields" [value]="field.value">{{ field.label }}</option>
                </select>
                 <label class="form-label mt-2" for="filterSortDirFeature">Sort Direction</label>
                 <select id="filterSortDirFeature" class="form-select"
                        [(ngModel)]="filters.sortDirection" name="sortDirection">
                    <option value="ASC">Ascending</option>
                    <option value="DESC">Descending</option>
                </select>
            </div>
            <div class="col-12 mt-4 d-grid gap-2">
                <button type="submit" class="btn adani-btn" [disabled]="filterForm.invalid">
                    <i class="bi bi-search me-1"></i> Search
                </button>
                 <button type="button" class="btn btn-secondary" (click)="resetFilters()">
                    <i class="bi bi-arrow-clockwise me-1"></i> Reset Filters
                </button>
            </div>
        </div>
    </form>
</div>
</app-offcanvas>

<app-offcanvas [title]="'Create New Feature'" *ngIf="isCreateModalOpen" (onClickCross)="closeCreateModal()">
<div class="create-container p-3">
    <form #createForm="ngForm" (ngSubmit)="submitCreateForm()">
        <div class="row g-3">
            <div class="col-12">
                <label class="form-label" for="createFeatureName">Feature Name</label>
                <input type="text" id="createFeatureName" class="form-control" placeholder="Enter New Feature Name"
                       [(ngModel)]="newFeatureMasterData.title" name="title" required #createTitleInput="ngModel"
                       pattern="^[a-zA-Z\s]*$" maxlength="30"
                       [ngClass]="{'is-invalid': createTitleInput.invalid && (createTitleInput.dirty || createTitleInput.touched)}">
                <div *ngIf="createTitleInput.invalid && (createTitleInput.dirty || createTitleInput.touched)" class="invalid-feedback">
                    <div *ngIf="createTitleInput.errors?.['required']">Feature name is required.</div>
                    <div *ngIf="createTitleInput.errors?.['pattern']">Feature name should contain only alphabets.</div>
                    <div *ngIf="createTitleInput.errors?.['maxlength']">Feature name cannot exceed 30 characters.</div>
                </div>
                <small *ngIf="newFeatureMasterData.title" class="text-muted">
                    {{ newFeatureMasterData.title.length }}/30 characters
                </small>
            </div>
            <div class="col-12 mt-4 d-flex justify-content-end gap-2">
                <button type="button" class="btn btn-secondary" (click)="closeCreateModal()">
                    Cancel
                </button>
                <button type="submit" class="btn adani-btn" [disabled]="createForm.invalid || createLoading">
                    <span *ngIf="!createLoading"><i class="bi bi-plus-circle-fill me-1"></i> Create Feature</span>
                    <span *ngIf="createLoading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                    <span *ngIf="createLoading"> Creating...</span>
                </button>
            </div>
        </div>
    </form>
</div>
</app-offcanvas>