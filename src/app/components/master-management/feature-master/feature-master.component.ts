import { Component, OnInit, ViewChild } from '@angular/core';
import { FormsModule, NgForm } from '@angular/forms';
import { FeatureMasterService } from '../../../services/master-management/feature-master/feature-master.service';
import { createAxiosConfig } from '../../../core/utilities/axios-param-config';
import { CommonModule } from '@angular/common';
import { SwitchComponent } from "../../../shared/switch/switch.component";
import { PaginationComponent } from "../../../shared/pagination/pagination.component";
import { OffcanvasComponent } from "../../../shared/offcanvas/offcanvas.component";
import { UpdateService } from '../../../services/update/update.service';
import { ToastMessageComponent } from '../../../shared/toast-message/toast-message.component';
import { NgbDropdownModule } from '@ng-bootstrap/ng-bootstrap';
import { AuthService } from '../../../services/auth.service';

interface FeatureMasterFilter {
    name?: string | null;
    sortField?: string | null;
    sortDirection?: 'ASC' | 'DESC';
}

export interface FeatureMaster {
    id: number;
    title: string;
    enabled: boolean;
    priority: number | null;
    createdBy: string | null;
    createdTimestamp: string;
    updatedBy: string | null;
    updatedTimestamp: string;
}

interface NewFeatureMasterData {
    title: string;
    enabled: boolean;
}

@Component({
    selector: 'app-feature-master',
    standalone: true,
    imports: [
        CommonModule,
        FormsModule,
        SwitchComponent,
        PaginationComponent,
        OffcanvasComponent,
        NgbDropdownModule,
        ToastMessageComponent
    ],
    templateUrl: './feature-master.component.html',
    styleUrl: './feature-master.component.scss'
})
export class FeatureMasterComponent implements OnInit {
    @ViewChild('createForm') createForm?: NgForm;
    @ViewChild(ToastMessageComponent) toast!: ToastMessageComponent;

    isFilterModalOpen = false;
    isCreateModalOpen = false;
    
    featureMasterList: FeatureMaster[] = [];
    listLoading = false;
    createLoading = false;

    currentPage = 1;
    itemsPerPage = 10;
    totalItems = 0;

    filters: FeatureMasterFilter = {
        name: null,
        sortField: 'createdTimestamp',
        sortDirection: 'DESC'
    };
    availableSortFields = [
        { value: 'id', label: 'ID' },
        { value: 'title', label: 'Feature Name' },
        { value: 'createdTimestamp', label: 'Created Date' }
    ];

    newFeatureMasterData: NewFeatureMasterData = {
        title: '',
        enabled: true
    };
    
    canManageFeatureMaster = false;

    constructor(
        readonly featureMasterService: FeatureMasterService,
        readonly updateService: UpdateService,
        private authService: AuthService
    ) { }

    ngOnInit() {
        this.canManageFeatureMaster = this.authService.getAdminsRoleId() === 6;
        if(this.canManageFeatureMaster) {
            this.loadFeatureMasters(this.currentPage);
        }
    }

    openFilterModal(): void { this.isFilterModalOpen = true; }
    closeFilterModal(): void { this.isFilterModalOpen = false; }

    @ViewChild('filterForm') filterForm!: NgForm;

    applyFilters(): void {
        if (this.filterForm && this.filterForm.invalid) {
            this.toast?.showErrorToast("Please correct the validation errors before applying filters.");
            return;
        }
        if (this.filters.name) {
            this.filters.name = this.filters.name.trim();
        }
        this.currentPage = 1;
        this.loadFeatureMasters(this.currentPage);
        this.closeFilterModal();
    }

    resetFilters(): void {
        this.filters = {
            name: null,
            sortField: 'createdTimestamp',
            sortDirection: 'DESC'
        };
        this.currentPage = 1;
        this.loadFeatureMasters(this.currentPage);
    }

    openCreateModal(): void {
        this.newFeatureMasterData = {
            title: '',
            enabled: true
        };
        this.createForm?.resetForm();
        this.isCreateModalOpen = true;
        this.createLoading = false;
    }

    closeCreateModal(): void {
        this.isCreateModalOpen = false;
    }

    async submitCreateForm(): Promise<void> {
        if (this.createForm?.invalid) {
             Object.values(this.createForm.controls).forEach(control => control.markAsTouched());
             this.toast?.showErrorToast("Please correct the validation errors before creating.");
             return;
        }

        if (this.newFeatureMasterData.title) {
            this.newFeatureMasterData.title = this.newFeatureMasterData.title.trim();
        }

        this.createLoading = true;

        try {
            const createdFeatureMaster = await this.featureMasterService.createFeatureMaster(this.newFeatureMasterData);
            this.toast?.showSuccessToast(`Feature "${createdFeatureMaster.title}" created successfully!`);
            this.closeCreateModal();
            this.currentPage = 1;
            this.loadFeatureMasters(this.currentPage);
        } catch (error) {
            console.error("Error creating feature:", error);
            this.toast?.showErrorToast('Failed to create feature. Please try again.');
        } finally {
            this.createLoading = false;
        }
    }

    async loadFeatureMasters(page: number): Promise<void> {
        this.listLoading = true;

        const filterParams: string[] = [];
        if (this.filters.name) {
            filterParams.push(`title||$contL||${this.filters.name}`);
        }

        const requestData = {
            page: page,
            limit: this.itemsPerPage,
            sort: `${this.filters.sortField || 'createdTimestamp'},${this.filters.sortDirection || 'DESC'}`,
            filter: filterParams
        };

        try {
            const params = createAxiosConfig(requestData);
            const response = await this.featureMasterService.getFeatureMaster(params);
            this.featureMasterList = response?.data ?? [];
            this.totalItems = response?.total ?? 0;
        } catch (error) {
            console.error("Error fetching features:", error);
            this.featureMasterList = [];
            this.totalItems = 0;
        } finally {
            this.listLoading = false;
        }
    }

    onPageChange(page: number): void {
        if (page !== this.currentPage) {
            this.currentPage = page;
            this.loadFeatureMasters(this.currentPage);
        }
    }

    // Removed onSwitchToggle and update methods as they are no longer needed for the enabled/disabled column
}