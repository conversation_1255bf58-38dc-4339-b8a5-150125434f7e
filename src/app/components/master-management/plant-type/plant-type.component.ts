import { Component, OnInit, ViewChild } from '@angular/core'; // Added ViewChild
import { FormsModule, NgForm } from '@angular/forms'; // Added NgForm
import { PlantTypeService } from '../../../services/master-management/plant-type/plant-type.service';
import { createAxiosConfig } from '../../../core/utilities/axios-param-config';
import { CommonModule } from '@angular/common';
import { SwitchComponent } from "../../../shared/switch/switch.component";
import { PaginationComponent } from "../../../shared/pagination/pagination.component";
import { OffcanvasComponent } from "../../../shared/offcanvas/offcanvas.component";
import { UpdateService } from '../../../services/update/update.service';
// Optional: Import ToastrService or similar
// import { ToastrService } from 'ngx-toastr';
import { ToastMessageComponent } from '../../../shared/toast-message/toast-message.component'; // Adjust path if needed
import { NgbDropdownModule } from '@ng-bootstrap/ng-bootstrap'; // For dropdown button
import * as XLSX from 'xlsx'; // For Excel generation


// Interface for filter structure
interface PlantTypeFilter {
    name?: string | null;
    enabled?: string | null;
    sortField?: string | null;
    sortDirection?: 'ASC' | 'DESC';
}

// *** NEW: Interface for Plant Type Data ***
export interface PlantType {
    id: number;
    title: string;
    enabled: boolean;
    createdAt?: string; // Optional
    updatedAt?: string; // Optional
}

// *** NEW: Interface for Create Form Data ***
interface NewPlantTypeData {
    title: string;
    enabled: boolean;
}


@Component({
    selector: 'app-plant-type',
    standalone: true,
    imports: [
        CommonModule,
        FormsModule,
        SwitchComponent,
        PaginationComponent,
        OffcanvasComponent,
        NgbDropdownModule,
        ToastMessageComponent
    ],
    templateUrl: './plant-type.component.html',
    styleUrl: './plant-type.component.scss'
})
export class PlantTypeComponent implements OnInit {
    // Access the form template variable
    @ViewChild('createForm') createForm?: NgForm;
    @ViewChild(ToastMessageComponent) toast!: ToastMessageComponent;

    // --- Modal States ---
    isFilterModalOpen = false; // Renamed from isEditUserModalOpen
    isEditModalOpen = false;   // <-- New state for edit modal
    isCreateModalOpen = false; // <-- New state for create modal

    // --- Data & Loading States ---
    plantTypeList: PlantType[] = []; // Use PlantType interface
    listLoading = false;
    editLoading = false;     // <-- New state for edit form submission
    createLoading = false;   // <-- New state for create form submission

    // --- Pagination ---
    currentPage = 1;
    itemsPerPage = 10;
    totalItems = 0;
    isDownloadingExcel = false;
    downloadType: 'current' | 'all' | null = null;

    // --- Filtering ---
    filters: PlantTypeFilter = {
        name: null,
        enabled: 'true',
        sortField: 'createdTimestamp',
        sortDirection: 'DESC'
    };
    availableSortFields = [
        { value: 'id', label: 'ID' },
        { value: 'title', label: 'Plant Type Name' },
        { value: 'enabled', label: 'Enabled Status' },
        { value: 'createdTimestamp', label: 'Created Date' }
    ];

    // --- Editing ---
    selectedPlantType: PlantType | null = null; // <-- Object to hold data for editing

    // --- Creating ---
    newPlantTypeData: NewPlantTypeData = { // <-- Object to hold new plant type form data
        title: '',
        enabled: true // Default new plant types to active
    };

    constructor(
        readonly plantTypeService: PlantTypeService,
        readonly updateService: UpdateService,
        // Optional: Inject ToastrService
        // private toastr: ToastrService
    ) { }

    ngOnInit(): void {
        this.loadPlantTypes(this.currentPage);
    }

    // Helper to get current list data
    getCurrentListData(): PlantType[] | undefined {
        return this.plantTypeList;
    }

    // Fetch ALL plant types matching current filters (no pagination)
    async fetchAllFilteredPlantTypes(): Promise<PlantType[] | null> {
        this.listLoading = true; // Indicate loading
        const filterParams: string[] = [];
        if (this.filters.name) { filterParams.push(`title||$contL||${this.filters.name}`); }
        if (this.filters.enabled !== null && this.filters.enabled !== undefined && this.filters.enabled !== '') {
             filterParams.push(`enabled||$eq||${this.filters.enabled}`);
        }

        const data = {
            limit: 10000, // Large limit for "all"
            sort: `${this.filters.sortField || 'createdTimestamp'},${this.filters.sortDirection || 'DESC'}`, // Match default sort
            filter: filterParams
        };

        try {
            const params = createAxiosConfig(data);
            console.log('API Request Params (Plant Types Download - All Data):', JSON.stringify(params, null, 2));
            const response = await this.plantTypeService.getPlantType(params); // Use existing service method
            // Adjust based on actual response structure
            return response ?? [];
        } catch (error: any) {
            console.error("Error fetching all plant types for download:", error);
            this.toast?.showErrorToast(error?.response?.data?.message || "Failed to retrieve full data for download.");
            return null;
        } finally {
             this.listLoading = false; // Reset loading indicator
        }
    }

    // Main download function
    async downloadExcel(type: 'current' | 'all') {
        if (this.isDownloadingExcel || this.listLoading) return; // Prevent concurrent actions

        this.isDownloadingExcel = true;
        this.downloadType = type;
        this.toast?.showSuccessToast(`Preparing download for ${type === 'current' ? 'current page' : 'all filtered'} plant types...`);

        let dataToExport: PlantType[] | null = null;

        try {
            // 1. Get Data
            if (type === 'all') {
                dataToExport = await this.fetchAllFilteredPlantTypes();
            } else { // 'current'
                dataToExport = this.getCurrentListData() ?? null;
                if (dataToExport === undefined) { dataToExport = null; }
            }

            // 2. Check data
            if (dataToExport === null) { this.toast?.showErrorToast("Failed to retrieve data for download."); return; }
            if (!dataToExport || dataToExport.length === 0) { this.toast?.showErrorToast(`No plant types available to download.`); return; }

            console.log(`Fetched ${dataToExport.length} plant types for Excel export (${type}).`);

            // 3. Transform data
            const dataForExcel = dataToExport.map(pt => ({
                'Plant Type ID': pt.id,
                'Plant Type Name': pt.title,
                'Status': pt.enabled ? 'Active' : 'Inactive',
                'Created At': pt.createdAt ? new Date(pt.createdAt).toLocaleString() : 'N/A',
                'Updated At': pt.updatedAt ? new Date(pt.updatedAt).toLocaleString() : 'N/A',
            }));

            // 4. Create Worksheet and Workbook
            const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(dataForExcel);
            const wb: XLSX.WorkBook = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(wb, ws, 'PlantTypes'); // Sheet name

            // 5. Generate File Name
            const dateStr = new Date().toISOString().slice(0, 10);
            const typeStr = type === 'current' ? `Page${this.currentPage}` : 'All';
            const fileName = `PlantTypes_${typeStr}_${dateStr}.xlsx`;

            // 6. Trigger Download
            XLSX.writeFile(wb, fileName);
            this.toast?.showSuccessToast(`Excel file download started (${type}).`);

        } catch (error) {
            console.error(`Error generating Excel file (${type}):`, error);
            this.toast?.showErrorToast(`An error occurred while generating the Excel file (${type}).`);
        } finally {
            this.isDownloadingExcel = false;
            this.downloadType = null;
        }
    }

    // --- Filter Modal Methods ---
    openFilterModal(): void { this.isFilterModalOpen = true; }
    closeFilterModal(): void { this.isFilterModalOpen = false; } // Renamed from closeModal

    @ViewChild('filterForm') filterForm!: NgForm;

    applyFilters(): void {
        // Check if form is valid before applying filters
        if (this.filterForm && this.filterForm.invalid) {
            this.toast?.showErrorToast("Please correct the validation errors before applying filters.");
            return;
        }

        // Trim string values to prevent whitespace-only searches
        if (this.filters.name) {
            this.filters.name = this.filters.name.trim();
        }

        this.currentPage = 1;
        this.loadPlantTypes(this.currentPage);
        this.closeFilterModal();
    }

    resetFilters(): void {
        this.filters = {
            name: null,
            enabled: 'true',
            sortField: 'createdTimestamp',
            sortDirection: 'DESC'
        };
        this.currentPage = 1;
        this.loadPlantTypes(this.currentPage);
        // Optionally close modal: this.closeFilterModal();
    }

    // --- *** NEW: Edit Modal Methods *** ---
    /** Opens the edit plant type offcanvas modal. */
    openEditModal(plantType: PlantType): void {
        this.selectedPlantType = { ...plantType }; // Create a copy
        this.isEditModalOpen = true;
        this.editLoading = false;
    }

    /** Closes the edit plant type offcanvas modal. */
    closeEditModal(): void {
        this.isEditModalOpen = false;
        this.selectedPlantType = null;
    }

    @ViewChild('editForm') editForm!: NgForm;

    /** Handles the submission of the edit plant type form. */
    async submitEditForm(): Promise<void> {
        if (!this.selectedPlantType || this.selectedPlantType.id == null) {
            console.error("Cannot save, selected plant type is null or has no ID.");
            this.toast?.showErrorToast('Cannot save, no plant type selected.');
            return;
        }

        // Check form validity
        if (this.editForm && this.editForm.invalid) {
            this.toast?.showErrorToast("Please correct the validation errors before saving.");
            // Mark fields as touched to show validation errors
            Object.values(this.editForm.controls).forEach(control => {
                control.markAsTouched();
            });
            return;
        }

        // Trim the title to remove any leading/trailing whitespace
        if (this.selectedPlantType.title) {
            this.selectedPlantType.title = this.selectedPlantType.title.trim();
        }

        this.editLoading = true;
        const updatePayload = {
            tableName: 'plant-type', // Critical: Ensure this matches your backend/service expectation
            id: this.selectedPlantType.id,
            data: {
                title: this.selectedPlantType.title,
                enabled: this.selectedPlantType.enabled
                // Add other editable fields if necessary
            }
        };

        try {
            await this.update(updatePayload); // Use the generic update method
            this.toast?.showSuccessToast('Plant type updated successfully!');

            // Update list locally (optional)
            const index = this.plantTypeList.findIndex(pt => pt.id === this.selectedPlantType?.id);
            if (index !== -1 && this.selectedPlantType) {
                 this.plantTypeList[index] = { ...this.selectedPlantType };
            }

            this.closeEditModal();
            // Consider reloading if necessary: this.loadPlantTypes(this.currentPage);

        } catch (error) {
            console.error("Error submitting plant type update:", error);
            this.toast?.showErrorToast('Failed to update plant type.');
        } finally {
            this.editLoading = false;
        }
    }


    // --- *** NEW: Create Modal Methods *** ---

    /** Opens the create plant type offcanvas modal and resets the form data. */
    openCreateModal(): void {
        this.newPlantTypeData = {
            title: '',
            enabled: true
        };
        this.createForm?.resetForm({ enabled: true }); // Reset form state with default
        this.isCreateModalOpen = true;
        this.createLoading = false;
    }

    /** Closes the create plant type offcanvas modal. */
    closeCreateModal(): void {
        this.isCreateModalOpen = false;
    }

    /** Handles the submission of the create plant type form. */
    async submitCreateForm(): Promise<void> {
        if (this.createForm?.invalid) {
             Object.values(this.createForm.controls).forEach(control => control.markAsTouched());
             this.toast?.showErrorToast("Please correct the validation errors before creating.");
             console.warn('Create form submitted while invalid.');
             return;
        }

        // Trim the title to remove any leading/trailing whitespace
        if (this.newPlantTypeData.title) {
            this.newPlantTypeData.title = this.newPlantTypeData.title.trim();
        }

        this.createLoading = true;
        console.log('Submitting new plant type:', this.newPlantTypeData);

        try {
            // *** ASSUMPTION: plantTypeService has a createPlantType method ***
            const createdPlantType = await this.plantTypeService.createPlantType(this.newPlantTypeData);
            console.log('Plant type created successfully:', createdPlantType);

            this.toast?.showSuccessToast(`Plant type "${createdPlantType.title}" created successfully!`);

            this.closeCreateModal();
            // Reload list, go to page 1
            this.currentPage = 1;
            this.loadPlantTypes(this.currentPage);

        } catch (error) {
            console.error("Error creating plant type:", error);
            this.toast?.showErrorToast('Failed to create plant type. Please try again.');
        } finally {
            this.createLoading = false;
        }
    }


    // --- Data Loading & Update Methods ---

    /** Fetches plant types from the backend. */
    async loadPlantTypes(page: number): Promise<void> {
        this.listLoading = true;
        // this.plantTypeList = []; // Clear only after loading starts

        const filterParams: string[] = [];
        if (this.filters.name) {
            filterParams.push(`title||$contL||${this.filters.name}`);
        }
        if (this.filters.enabled !== null && this.filters.enabled !== undefined && this.filters.enabled !== '') {
            filterParams.push(`enabled||$eq||${this.filters.enabled}`);
        }

        const requestData = {
            page: page,
            limit: this.itemsPerPage,
            sort: `${this.filters.sortField || 'createdTimestamp'},${this.filters.sortDirection || 'DESC'}`,
            filter: filterParams
        };

        try {
            const params = createAxiosConfig(requestData);
            // Ensure service method returns { data: PlantType[], total: number }
            const response = await this.plantTypeService.getPlantType(params);
            this.plantTypeList = response?.data ?? [];
            this.totalItems = response?.total ?? 0;
        } catch (error) {
            console.error("Error fetching plant types:", error);
            this.plantTypeList = [];
            this.totalItems = 0;
            // this.toastr.error('Failed to load plant types.', 'Error');
        } finally {
            this.listLoading = false;
        }
    }

    /** Handles page changes. */
    onPageChange(page: number): void {
        if (page !== this.currentPage) {
            this.currentPage = page;
            this.loadPlantTypes(this.currentPage);
        }
    }

    /** Handles the toggle switch change event in the table row. */
    async onSwitchToggle(isEnabled: boolean, plantType: PlantType): Promise<void> {
        const originalState = plantType.enabled;
        plantType.enabled = isEnabled; // Optimistic update

        const updatePayload = {
            tableName: 'plant-type', // Use correct table name
            id: plantType.id,
            data: {
                enabled: isEnabled
            }
        };
        console.log(`Attempting to update plant type ${plantType.id} enabled status to: ${isEnabled}`);

        try {
            await this.update(updatePayload);
            console.log(`Successfully updated plant type ${plantType.id} enabled status.`);
            // this.toastr.success('Plant type status updated.', 'Success');
        } catch (error) {
            console.error(`Error updating enabled status for plant type ${plantType.id}:`, error);
            // this.toastr.error('Failed to update plant type status.', 'Update Error');
            // Revert UI change on error
            plantType.enabled = originalState;
            // Force update detection if needed
            const index = this.plantTypeList.findIndex(pt => pt.id === plantType.id);
            if (index !== -1) {
                 this.plantTypeList[index] = {...this.plantTypeList[index], enabled: originalState };
            }
        }
    }

    // Generic update via UpdateService (used by toggle and edit form)
    async update(data: { tableName: string, id: number, data: any }): Promise<void> {
        try {
            await this.updateService.update(data);
        } catch (error) {
            console.error("Update service call failed:", error);
            throw error; // Re-throw
        }
    }

    // --- Removed Placeholder/Unused Methods ---
    // Removed handleCreate, handleUpdate, toggleEnabled, handleDelete, sortBy, getSortClass
}