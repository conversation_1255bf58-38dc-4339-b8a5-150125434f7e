import { Component, OnInit, ViewChild } from '@angular/core'; // Added ViewChild
import { FormsModule, NgForm } from '@angular/forms'; // Added NgForm
import { OpcoService } from '../../../services/master-management/opco/opco.service';
import { createAxiosConfig } from '../../../core/utilities/axios-param-config';
import { CommonModule } from '@angular/common';
import { SwitchComponent } from "../../../shared/switch/switch.component";
import { PaginationComponent } from "../../../shared/pagination/pagination.component";
import { OffcanvasComponent } from "../../../shared/offcanvas/offcanvas.component";
import { UpdateService } from '../../../services/update/update.service';
// Optional: Import ToastrService or similar
// import { ToastrService } from 'ngx-toastr';
import { ToastMessageComponent } from '../../../shared/toast-message/toast-message.component'; // Adjust path if needed
import { NgbDropdownModule } from '@ng-bootstrap/ng-bootstrap'; // For dropdown button
import * as XLSX from 'xlsx'; // For Excel generation


// Interface for filter structure
interface OpcoFilter {
    name?: string | null;
    enabled?: string | null;
    sortField?: string | null;
    sortDirection?: 'ASC' | 'DESC';
}

// *** NEW: Interface for OPCO Data ***
export interface Opco {
    id: number;
    title: string;
    enabled: boolean;
    createdAt?: string; // Optional
    updatedAt?: string; // Optional
}

// *** NEW: Interface for Create Form Data ***
interface NewOpcoData {
    title: string;
    enabled: boolean;
}


@Component({
    selector: 'app-opco',
    standalone: true,
    imports: [
        CommonModule,
        FormsModule,
        SwitchComponent,
        PaginationComponent,
        OffcanvasComponent,
        NgbDropdownModule,
        ToastMessageComponent
    ],
    templateUrl: './opco.component.html',
    styleUrl: './opco.component.scss'
})
export class OpcoComponent implements OnInit {
    // Access the form template variables
    @ViewChild('createForm') createForm?: NgForm;
    @ViewChild('editForm') editForm?: NgForm;
    @ViewChild('filterForm') filterForm!: NgForm;
    @ViewChild(ToastMessageComponent) toast!: ToastMessageComponent;

    // --- Modal States ---
    isFilterModalOpen = false; // Renamed from isEditUserModalOpen
    isEditModalOpen = false;   // <-- New state for edit modal
    isCreateModalOpen = false; // <-- New state for create modal

    // --- Data & Loading States ---
    opcoList: Opco[] = []; // Corrected variable name and used Opco interface
    listLoading = false;
    editLoading = false;     // <-- New state for edit form submission
    createLoading = false;   // <-- New state for create form submission

    // --- Pagination ---
    currentPage = 1;
    itemsPerPage = 10;
    totalItems = 0;
    isDownloadingExcel = false;
    downloadType: 'current' | 'all' | null = null;

    // --- Filtering ---
    filters: OpcoFilter = {
        name: null,
        enabled: 'true',
        sortField: 'createdTimestamp',
        sortDirection: 'DESC'
    };
    availableSortFields = [
        { value: 'id', label: 'ID' },
        { value: 'title', label: 'OPCO Name' },
        { value: 'enabled', label: 'Enabled Status' },
        { value: 'createdTimestamp', label: 'Created Date' }
    ];

    // --- Editing ---
    selectedOpco: Opco | null = null; // <-- Object to hold data for editing

    // --- Creating ---
    newOpcoData: NewOpcoData = { // <-- Object to hold new OPCO form data
        title: '',
        enabled: true // Default new OPCOs to active
    };

    constructor(
        readonly opcoService: OpcoService,
        readonly updateService: UpdateService,
        // Optional: Inject ToastrService
        // private toastr: ToastrService
    ) { }


    ngOnInit(): void {
        this.loadOpcos(this.currentPage);
    }

    // Helper to get current list data
    getCurrentListData(): Opco[] | undefined {
        return this.opcoList;
    }

    // Fetch ALL OPCOs matching current filters (no pagination)
    async fetchAllFilteredOpcos(): Promise<Opco[] | null> {
        this.listLoading = true; // Indicate loading
        const filterParams: string[] = [];
        if (this.filters.name) { filterParams.push(`title||$contL||${this.filters.name}`); }
        if (this.filters.enabled !== null && this.filters.enabled !== undefined && this.filters.enabled !== '') {
             filterParams.push(`enabled||$eq||${this.filters.enabled}`);
        }

        const data = {
            limit: 10000, // Large limit for "all"
            sort: `${this.filters.sortField || 'createdTimestamp'},${this.filters.sortDirection || 'DESC'}`, // Match default sort
            filter: filterParams
        };

        try {
            const params = createAxiosConfig(data);
            console.log('API Request Params (OPCOs Download - All Data):', JSON.stringify(params, null, 2));
            const response = await this.opcoService.getOpco(params); // Use existing service method
            return response ?? [];
        } catch (error: any) {
            console.error("Error fetching all OPCOs for download:", error);
            this.toast?.showErrorToast(error?.response?.data?.message || "Failed to retrieve full data for download.");
            return null;
        } finally {
             this.listLoading = false; // Reset loading indicator
        }
    }

    // Main download function
    async downloadExcel(type: 'current' | 'all') {
        if (this.isDownloadingExcel || this.listLoading) return; // Prevent concurrent actions

        this.isDownloadingExcel = true;
        this.downloadType = type;
        this.toast?.showSuccessToast(`Preparing download for ${type === 'current' ? 'current page' : 'all filtered'} OPCOs...`);

        let dataToExport: Opco[] | null = null;

        try {
            // 1. Get Data
            if (type === 'all') {
                dataToExport = await this.fetchAllFilteredOpcos();
            } else { // 'current'
                dataToExport = this.getCurrentListData() ?? null;
                if (dataToExport === undefined) { dataToExport = null; }
            }

            // 2. Check data
            if (dataToExport === null) { this.toast?.showErrorToast("Failed to retrieve data for download."); return; }
            if (!dataToExport || dataToExport.length === 0) { this.toast?.showErrorToast(`No OPCOs available to download.`); return; }

            console.log(`Fetched ${dataToExport.length} OPCOs for Excel export (${type}).`);

            // 3. Transform data
            const dataForExcel = dataToExport.map(opco => ({
                'OPCO ID': opco.id,
                'OPCO Name': opco.title,
                'Status': opco.enabled ? 'Active' : 'Inactive',
                'Created At': opco.createdAt ? new Date(opco.createdAt).toLocaleString() : 'N/A',
                'Updated At': opco.updatedAt ? new Date(opco.updatedAt).toLocaleString() : 'N/A',
            }));

            // 4. Create Worksheet and Workbook
            const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(dataForExcel);
            const wb: XLSX.WorkBook = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(wb, ws, 'OPCOs'); // Sheet name

            // 5. Generate File Name
            const dateStr = new Date().toISOString().slice(0, 10);
            const typeStr = type === 'current' ? `Page${this.currentPage}` : 'All';
            const fileName = `OPCOs_${typeStr}_${dateStr}.xlsx`;

            // 6. Trigger Download
            XLSX.writeFile(wb, fileName);
            this.toast?.showSuccessToast(`Excel file download started (${type}).`);

        } catch (error) {
            console.error(`Error generating Excel file (${type}):`, error);
            this.toast?.showErrorToast(`An error occurred while generating the Excel file (${type}).`);
        } finally {
            this.isDownloadingExcel = false;
            this.downloadType = null;
        }
    }

    // --- Filter Modal Methods ---
    openFilterModal(): void { this.isFilterModalOpen = true; }
    closeFilterModal(): void { this.isFilterModalOpen = false; } // Renamed from closeModal

    applyFilters(): void {
        // Check if form is valid before applying filters
        if (this.filterForm && this.filterForm.invalid) {
            this.toast?.showErrorToast("Please correct the validation errors before applying filters.");
            return;
        }

        // Trim string values to prevent whitespace-only searches
        if (this.filters.name) {
            this.filters.name = this.filters.name.trim();
        }

        this.currentPage = 1;
        this.loadOpcos(this.currentPage);
        this.closeFilterModal();
    }
    resetFilters(): void {
        this.filters = {
            name: null,
            enabled: 'true',
            sortField: 'createdTimestamp',
            sortDirection: 'DESC'
        };
        this.currentPage = 1;
        this.loadOpcos(this.currentPage);
        // Optionally close modal: this.closeFilterModal();
    }

    // --- *** NEW: Edit Modal Methods *** ---
    /** Opens the edit OPCO offcanvas modal. */
    openEditModal(opco: Opco): void {
        this.selectedOpco = { ...opco }; // Create a copy
        this.isEditModalOpen = true;
        this.editLoading = false;
    }

    /** Closes the edit OPCO offcanvas modal. */
    closeEditModal(): void {
        this.isEditModalOpen = false;
        this.selectedOpco = null;
    }

    /** Handles the submission of the edit OPCO form. */
    async submitEditForm(): Promise<void> {
        if (!this.selectedOpco || this.selectedOpco.id == null) {
            console.error("Cannot save, selected OPCO is null or has no ID.");
            this.toast?.showErrorToast('Cannot save, no OPCO selected.');
            return;
        }

        // Check form validity
        if (this.editForm?.invalid) {
            Object.values(this.editForm.controls).forEach(control => control.markAsTouched());
            this.toast?.showErrorToast("Please correct the validation errors before saving.");
            return;
        }

        // Trim the title to remove any leading/trailing whitespace
        if (this.selectedOpco.title) {
            this.selectedOpco.title = this.selectedOpco.title.trim();
        }

        this.editLoading = true;
        const updatePayload = {
            tableName: 'opco', // Critical: Ensure this matches your backend/service expectation
            id: this.selectedOpco.id,
            data: {
                title: this.selectedOpco.title,
                enabled: this.selectedOpco.enabled
                // Add other editable fields if necessary
            }
        };

        try {
            await this.update(updatePayload); // Use the generic update method
            // this.toastr.success('OPCO updated successfully!', 'Saved');

            // Update list locally (optional)
            const index = this.opcoList.findIndex(o => o.id === this.selectedOpco?.id);
            if (index !== -1 && this.selectedOpco) {
                 this.opcoList[index] = { ...this.selectedOpco };
            }

            this.closeEditModal();
            // Consider reloading if necessary: this.loadOpcos(this.currentPage);

        } catch (error) {
            console.error("Error submitting OPCO update:", error);
            // this.toastr.error('Failed to update OPCO.', 'Error');
        } finally {
            this.editLoading = false;
        }
    }


    // --- *** NEW: Create Modal Methods *** ---

    /** Opens the create OPCO offcanvas modal and resets the form data. */
    openCreateModal(): void {
        this.newOpcoData = {
            title: '',
            enabled: true
        };
        this.createForm?.resetForm({ enabled: true }); // Reset form state with default
        this.isCreateModalOpen = true;
        this.createLoading = false;
    }

    /** Closes the create OPCO offcanvas modal. */
    closeCreateModal(): void {
        this.isCreateModalOpen = false;
    }

    /** Handles the submission of the create OPCO form. */
    async submitCreateForm(): Promise<void> {
        if (this.createForm?.invalid) {
             Object.values(this.createForm.controls).forEach(control => control.markAsTouched());
             this.toast?.showErrorToast("Please correct the validation errors before creating.");
             console.warn('Create form submitted while invalid.');
             return;
        }

        // Trim the title to remove any leading/trailing whitespace
        if (this.newOpcoData.title) {
            this.newOpcoData.title = this.newOpcoData.title.trim();
        }

        this.createLoading = true;
        console.log('Submitting new OPCO:', this.newOpcoData);

        try {
            // *** ASSUMPTION: opcoService has a createOpco method ***
            const createdOpco = await this.opcoService.createOpco(this.newOpcoData);
            console.log('OPCO created successfully:', createdOpco);

            // this.toastr.success(`OPCO "${createdOpco.title}" created successfully!`, 'Created');

            this.closeCreateModal();
            // Reload list, go to page 1
            this.currentPage = 1;
            this.loadOpcos(this.currentPage);

        } catch (error) {
            console.error("Error creating OPCO:", error);
            // this.toastr.error('Failed to create OPCO. Please try again.', 'Creation Error');
        } finally {
            this.createLoading = false;
        }
    }


    // --- Data Loading & Update Methods ---

    /** Fetches OPCOs from the backend. */
    async loadOpcos(page: number): Promise<void> {
        this.listLoading = true;
        // this.opcoList = []; // Clear only after loading starts // Corrected variable name

        const filterParams: string[] = [];
        if (this.filters.name) {
            filterParams.push(`title||$contL||${this.filters.name}`);
        }
        if (this.filters.enabled !== null && this.filters.enabled !== undefined && this.filters.enabled !== '') {
            filterParams.push(`enabled||$eq||${this.filters.enabled}`);
        }

        const requestData = {
            page: page,
            limit: this.itemsPerPage,
            sort: `${this.filters.sortField || 'createdTimestamp'},${this.filters.sortDirection || 'DESC'}`,
            filter: filterParams
        };

        try {
            const params = createAxiosConfig(requestData);
            // Ensure service method returns { data: Opco[], total: number }
            const response = await this.opcoService.getOpco(params);
            this.opcoList = response?.data ?? []; // Corrected variable name
            this.totalItems = response?.total ?? 0;
        } catch (error) {
            console.error("Error fetching OPCOs:", error);
            this.opcoList = []; // Corrected variable name
            this.totalItems = 0;
            // this.toastr.error('Failed to load OPCOs.', 'Error');
        } finally {
            this.listLoading = false;
        }
    }

    /** Handles page changes. */
    onPageChange(page: number): void {
        if (page !== this.currentPage) {
            this.currentPage = page;
            this.loadOpcos(this.currentPage);
        }
    }

    /** Handles the toggle switch change event in the table row. */
    async onSwitchToggle(isEnabled: boolean, opco: Opco): Promise<void> {
        const originalState = opco.enabled;
        opco.enabled = isEnabled; // Optimistic update

        const updatePayload = {
            tableName: 'opco', // Use correct table name
            id: opco.id,
            data: {
                enabled: isEnabled
            }
        };
        console.log(`Attempting to update OPCO ${opco.id} enabled status to: ${isEnabled}`);

        try {
            await this.update(updatePayload);
            console.log(`Successfully updated OPCO ${opco.id} enabled status.`);
            // this.toastr.success('OPCO status updated.', 'Success');
        } catch (error) {
            console.error(`Error updating enabled status for OPCO ${opco.id}:`, error);
            // this.toastr.error('Failed to update OPCO status.', 'Update Error');
            // Revert UI change on error
            opco.enabled = originalState;
            // Force update detection if needed
            const index = this.opcoList.findIndex(o => o.id === opco.id);
            if (index !== -1) {
                 this.opcoList[index] = {...this.opcoList[index], enabled: originalState }; // Corrected variable name
            }
        }
    }

    // Generic update via UpdateService (used by toggle and edit form)
    async update(data: { tableName: string, id: number, data: any }): Promise<void> {
        try {
            await this.updateService.update(data);
        } catch (error) {
            console.error("Update service call failed:", error);
            throw error; // Re-throw
        }
    }

    // --- Removed Placeholder/Unused Methods ---
    // Removed handleCreate, handleUpdate, toggleEnabled, handleDelete, sortBy, getSortClass
}