import { Component, OnInit, ViewChild } from '@angular/core'; // Added ViewChild
import { FormsModule, NgForm } from '@angular/forms'; // Added NgForm
import { LocationTypeService } from '../../../services/master-management/location-type/location-type.service';
import { createAxiosConfig } from '../../../core/utilities/axios-param-config';
import { CommonModule } from '@angular/common';
import { SwitchComponent } from "../../../shared/switch/switch.component";
import { PaginationComponent } from "../../../shared/pagination/pagination.component";
import { OffcanvasComponent } from "../../../shared/offcanvas/offcanvas.component";
import { UpdateService } from '../../../services/update/update.service';
// Optional: Import ToastrService or similar
// import { ToastrService } from 'ngx-toastr';
import { ToastMessageComponent } from '../../../shared/toast-message/toast-message.component'; // Adjust path if needed
import { NgbDropdownModule } from '@ng-bootstrap/ng-bootstrap'; // For dropdown button
import * as XLSX from 'xlsx'; // For Excel generation


// Interface for filter structure
interface LocationTypeFilter {
    name?: string | null;
    enabled?: string | null;
    sortField?: string | null;
    sortDirection?: 'ASC' | 'DESC';
}

// *** NEW: Interface for Location Type Data ***
export interface LocationType {
    id: number;
    title: string;
    enabled: boolean;
    createdAt?: string; // Optional
    updatedAt?: string; // Optional
}

// *** NEW: Interface for Create Form Data ***
interface NewLocationTypeData {
    title: string;
    enabled: boolean;
}


@Component({
    selector: 'app-location-type',
    standalone: true,
    imports: [
        CommonModule,
        FormsModule,
        SwitchComponent,
        PaginationComponent,
        OffcanvasComponent,
        NgbDropdownModule,
        ToastMessageComponent
    ],
    templateUrl: './location-type.component.html',
    styleUrl: './location-type.component.scss'
})
export class LocationTypeComponent implements OnInit {
    // Access the form template variables
    @ViewChild('createForm') createForm?: NgForm;
    @ViewChild('filterForm') filterForm?: NgForm;
    @ViewChild(ToastMessageComponent) toast!: ToastMessageComponent;

    // --- Modal States ---
    isFilterModalOpen = false; // Renamed from isEditUserModalOpen
    isEditModalOpen = false;   // <-- New state for edit modal
    isCreateModalOpen = false; // <-- New state for create modal

    // --- Data & Loading States ---
    locationTypeList: LocationType[] = []; // Use LocationType interface
    listLoading = false;
    editLoading = false;     // <-- New state for edit form submission
    createLoading = false;   // <-- New state for create form submission
    isDownloadingExcel = false;
    downloadType: 'current' | 'all' | null = null;

    // --- Pagination ---
    currentPage = 1;
    itemsPerPage = 10;
    totalItems = 0;

    // --- Filtering ---
    filters: LocationTypeFilter = {
        name: null,
        enabled: 'true',
        sortField: 'createdTimestamp',
        sortDirection: 'DESC'
    };
    availableSortFields = [
        { value: 'id', label: 'ID' },
        { value: 'title', label: 'Location Type Name' },
        { value: 'enabled', label: 'Enabled Status' },
        { value: 'createdTimestamp', label: 'Created Date' }
    ];

    // --- Editing ---
    selectedLocationType: LocationType | null = null; // <-- Object to hold data for editing

    // --- Creating ---
    newLocationTypeData: NewLocationTypeData = { // <-- Object to hold new location type form data
        title: '',
        enabled: true // Default new location types to active
    };

    constructor(
        readonly locationTypeService: LocationTypeService,
        readonly updateService: UpdateService,
        // Optional: Inject ToastrService
        // private toastr: ToastrService
    ) { }

    ngOnInit(): void {
        this.loadLocationTypes(this.currentPage);
    }

    // Helper to get current list data
    getCurrentListData(): LocationType[] | undefined {
        return this.locationTypeList;
    }

    // Fetch ALL location types matching current filters (no pagination)
    async fetchAllFilteredLocationTypes(): Promise<LocationType[] | null> {
        this.listLoading = true; // Indicate loading
        const filterParams: string[] = [];
        if (this.filters.name) { filterParams.push(`title||$contL||${this.filters.name}`); }
        if (this.filters.enabled !== null && this.filters.enabled !== undefined && this.filters.enabled !== '') {
             filterParams.push(`enabled||$eq||${this.filters.enabled}`);
        }

        const data = {
            limit: 10000, // Large limit for "all"
            sort: `${this.filters.sortField || 'createdTimestamp'},${this.filters.sortDirection || 'DESC'}`, // Match default sort
            filter: filterParams
        };

        try {
            const params = createAxiosConfig(data);
            console.log('API Request Params (Location Types Download - All Data):', JSON.stringify(params, null, 2));
            const response = await this.locationTypeService.getLocationType(params); // Use existing service method
            return response ?? [];
        } catch (error: any) {
            console.error("Error fetching all location types for download:", error);
            this.toast?.showErrorToast(error?.response?.data?.message || "Failed to retrieve full data for download.");
            return null;
        } finally {
             this.listLoading = false; // Reset loading indicator
        }
    }

    // Main download function
    async downloadExcel(type: 'current' | 'all') {
        if (this.isDownloadingExcel || this.listLoading) return; // Prevent concurrent actions

        this.isDownloadingExcel = true;
        this.downloadType = type;
        this.toast?.showSuccessToast(`Preparing download for ${type === 'current' ? 'current page' : 'all filtered'} location types...`);

        let dataToExport: LocationType[] | null = null;

        try {
            // 1. Get Data
            if (type === 'all') {
                dataToExport = await this.fetchAllFilteredLocationTypes();
            } else { // 'current'
                dataToExport = this.getCurrentListData() ?? null;
                if (dataToExport === undefined) { dataToExport = null; }
            }

            // 2. Check data
            if (dataToExport === null) { this.toast?.showErrorToast("Failed to retrieve data for download."); return; }
            if (!dataToExport || dataToExport.length === 0) { this.toast?.showErrorToast(`No location types available to download.`); return; }

            console.log(`Fetched ${dataToExport.length} location types for Excel export (${type}).`);

            // 3. Transform data
            const dataForExcel = dataToExport.map(lt => ({
                'Location Type ID': lt.id,
                'Location Type Name': lt.title,
                'Status': lt.enabled ? 'Active' : 'Inactive',
                'Created At': lt.createdAt ? new Date(lt.createdAt).toLocaleString() : 'N/A',
                'Updated At': lt.updatedAt ? new Date(lt.updatedAt).toLocaleString() : 'N/A',
            }));

            // 4. Create Worksheet and Workbook
            const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(dataForExcel);
            const wb: XLSX.WorkBook = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(wb, ws, 'LocationTypes'); // Sheet name

            // 5. Generate File Name
            const dateStr = new Date().toISOString().slice(0, 10);
            const typeStr = type === 'current' ? `Page${this.currentPage}` : 'All';
            const fileName = `LocationTypes_${typeStr}_${dateStr}.xlsx`;

            // 6. Trigger Download
            XLSX.writeFile(wb, fileName);
            this.toast?.showSuccessToast(`Excel file download started (${type}).`);

        } catch (error) {
            console.error(`Error generating Excel file (${type}):`, error);
            this.toast?.showErrorToast(`An error occurred while generating the Excel file (${type}).`);
        } finally {
            this.isDownloadingExcel = false;
            this.downloadType = null;
        }
    }

    // --- Filter Modal Methods ---
    openFilterModal(): void { this.isFilterModalOpen = true; }
    closeFilterModal(): void { this.isFilterModalOpen = false; } // Renamed from closeModal
    applyFilters(): void {
        if (this.filterForm?.invalid) {
            // Mark all controls as touched to show validation errors
            Object.values(this.filterForm.controls).forEach(control => control.markAsTouched());
            return;
        }
        this.currentPage = 1;
        this.loadLocationTypes(this.currentPage);
        this.closeFilterModal();
    }
    resetFilters(): void {
        this.filters = {
            name: null,
            enabled: 'true',
            sortField: 'createdTimestamp',
            sortDirection: 'DESC'
        };
        this.currentPage = 1;
        this.loadLocationTypes(this.currentPage);
        // Optionally close modal: this.closeFilterModal();
    }

    // --- *** NEW: Edit Modal Methods *** ---
    /** Opens the edit location type offcanvas modal. */
    openEditModal(locationType: LocationType): void {
        this.selectedLocationType = { ...locationType }; // Create a copy
        this.isEditModalOpen = true;
        this.editLoading = false;
    }

    /** Closes the edit location type offcanvas modal. */
    closeEditModal(): void {
        this.isEditModalOpen = false;
        this.selectedLocationType = null;
    }

    /** Handles the submission of the edit location type form. */
    async submitEditForm(): Promise<void> {
        if (!this.selectedLocationType || this.selectedLocationType.id == null) {
            console.error("Cannot save, selected location type is null or has no ID.");
            // this.toastr.error('Cannot save, no location type selected.', 'Error');
            return;
        }

        this.editLoading = true;
        const updatePayload = {
            tableName: 'location-type', // Critical: Ensure this matches your backend/service expectation
            id: this.selectedLocationType.id,
            data: {
                title: this.selectedLocationType.title,
                enabled: this.selectedLocationType.enabled
                // Add other editable fields if necessary
            }
        };

        try {
            await this.update(updatePayload); // Use the generic update method
            // this.toastr.success('Location type updated successfully!', 'Saved');

            // Update list locally (optional)
            const index = this.locationTypeList.findIndex(lt => lt.id === this.selectedLocationType?.id);
            if (index !== -1 && this.selectedLocationType) {
                 this.locationTypeList[index] = { ...this.selectedLocationType };
            }

            this.closeEditModal();
            // Consider reloading if necessary: this.loadLocationTypes(this.currentPage);

        } catch (error) {
            console.error("Error submitting location type update:", error);
            // this.toastr.error('Failed to update location type.', 'Error');
        } finally {
            this.editLoading = false;
        }
    }


    // --- *** NEW: Create Modal Methods *** ---

    /** Opens the create location type offcanvas modal and resets the form data. */
    openCreateModal(): void {
        this.newLocationTypeData = {
            title: '',
            enabled: true
        };
        this.createForm?.resetForm({ enabled: true }); // Reset form state with default
        this.isCreateModalOpen = true;
        this.createLoading = false;
    }

    /** Closes the create location type offcanvas modal. */
    closeCreateModal(): void {
        this.isCreateModalOpen = false;
    }

    /** Handles the submission of the create location type form. */
    async submitCreateForm(): Promise<void> {
        if (this.createForm?.invalid) {
             Object.values(this.createForm.controls).forEach(control => control.markAsTouched());
             console.warn('Create form submitted while invalid.');
             return;
        }

        this.createLoading = true;
        console.log('Submitting new location type:', this.newLocationTypeData);

        try {
            // *** ASSUMPTION: locationTypeService has a createLocationType method ***
            const createdLocType = await this.locationTypeService.createLocationType(this.newLocationTypeData);
            console.log('Location type created successfully:', createdLocType);

            // this.toastr.success(`Location type "${createdLocType.title}" created successfully!`, 'Created');

            this.closeCreateModal();
            // Reload list, go to page 1
            this.currentPage = 1;
            this.loadLocationTypes(this.currentPage);

        } catch (error) {
            console.error("Error creating location type:", error);
            // this.toastr.error('Failed to create location type. Please try again.', 'Creation Error');
        } finally {
            this.createLoading = false;
        }
    }


    // --- Data Loading & Update Methods ---

    /** Fetches location types from the backend. */
    async loadLocationTypes(page: number): Promise<void> {
        this.listLoading = true;
        // this.locationTypeList = []; // Clear only after loading starts

        const filterParams: string[] = [];
        if (this.filters.name) {
            filterParams.push(`title||$contL||${this.filters.name}`);
        }
        if (this.filters.enabled !== null && this.filters.enabled !== undefined && this.filters.enabled !== '') {
            filterParams.push(`enabled||$eq||${this.filters.enabled}`);
        }

        const requestData = {
            page: page,
            limit: this.itemsPerPage,
            sort: `${this.filters.sortField || 'createdTimestamp'},${this.filters.sortDirection || 'DESC'}`,
            filter: filterParams
        };

        try {
            const params = createAxiosConfig(requestData);
            // Ensure service method returns { data: LocationType[], total: number }
            const response = await this.locationTypeService.getLocationType(params);
            this.locationTypeList = response?.data ?? [];
            this.totalItems = response?.total ?? 0;
        } catch (error) {
            console.error("Error fetching location types:", error);
            this.locationTypeList = [];
            this.totalItems = 0;
            // this.toastr.error('Failed to load location types.', 'Error');
        } finally {
            this.listLoading = false;
        }
    }

    /** Handles page changes. */
    onPageChange(page: number): void {
        if (page !== this.currentPage) {
            this.currentPage = page;
            this.loadLocationTypes(this.currentPage);
        }
    }

    /** Handles the toggle switch change event in the table row. */
    async onSwitchToggle(isEnabled: boolean, locationType: LocationType): Promise<void> {
        const originalState = locationType.enabled;
        locationType.enabled = isEnabled; // Optimistic update

        const updatePayload = {
            tableName: 'location-type', // Use correct table name
            id: locationType.id,
            data: {
                enabled: isEnabled
            }
        };
        console.log(`Attempting to update location type ${locationType.id} enabled status to: ${isEnabled}`);

        try {
            await this.update(updatePayload);
            console.log(`Successfully updated location type ${locationType.id} enabled status.`);
            // this.toastr.success('Location type status updated.', 'Success');
        } catch (error) {
            console.error(`Error updating enabled status for location type ${locationType.id}:`, error);
            // this.toastr.error('Failed to update location type status.', 'Update Error');
            // Revert UI change on error
            locationType.enabled = originalState;
            // Force update detection if needed
            const index = this.locationTypeList.findIndex(lt => lt.id === locationType.id);
            if (index !== -1) {
                 this.locationTypeList[index] = {...this.locationTypeList[index], enabled: originalState };
            }
        }
    }

    // Generic update via UpdateService (used by toggle and edit form)
    async update(data: { tableName: string, id: number, data: any }): Promise<void> {
        try {
            await this.updateService.update(data);
        } catch (error) {
            console.error("Update service call failed:", error);
            throw error; // Re-throw
        }
    }

    // --- Removed Placeholder/Unused Methods ---
    // Removed handleCreate, handleUpdate, toggleEnabled, handleDelete, sortBy, getSortClass
}