import { Component, OnInit, ViewChild } from '@angular/core';
import { FormsModule, NgForm } from '@angular/forms'; // Added FormsModule, NgForm
import { AreaService } from '../../../services/master-management/area/area.service';
import { createAxiosConfig } from '../../../core/utilities/axios-param-config';
import { CommonModule } from '@angular/common'; // Added CommonModule
import { PaginationComponent } from "../../../shared/pagination/pagination.component"; // Added PaginationComponent
import { OffcanvasComponent } from "../../../shared/offcanvas/offcanvas.component"; // Added OffcanvasComponent
import { SwitchComponent } from "../../../shared/switch/switch.component"; // Added SwitchComponent
import { UpdateService } from '../../../services/update/update.service'; // Added UpdateService
import { ZoneService } from '../../../services/zone/zone.service'; // Added ZoneService
import { PlantManagementService } from '../../../services/plant-management/plant-management.service'; // Added PlantManagementService
import { NgbDropdownModule } from '@ng-bootstrap/ng-bootstrap'; // Added NgbDropdownModule for dropdown
import * as XLSX from 'xlsx'; // Import XLSX for Excel generation
import { ToastMessageComponent } from "../../../shared/toast-message/toast-message.component"; // Added ToastMessageComponent
// Optional: Import ToastrService or similar
// import { ToastrService } from 'ngx-toastr';

// *** Interface for Plant Data ***
interface Plant {
    id: number;
    name: string;
    enabled: boolean;
}

// *** Interface for Filter structure ***
interface ZoneFilter {
    name?: string | null;
    enabled?: string | null;
    sortField?: string | null;
    sortDirection?: 'ASC' | 'DESC';
    zoneId?: number | null;
    plantId?: number | null; // Added for plant filtering
}

// *** Interface for Zone Data ***
interface Zone {
    id: number;
    zoneName: string;
    enabled: boolean;
    plantId?: number;
    plant?: Plant;
    createdAt?: string;
    updatedAt?: string;
}

// *** Interface for Area Data (now Zone) ***
export interface Area {
    id: number;
    title: string;
    zoneName?: string;
    zoneArea?: string;
    enabled: boolean;
    zone?: Zone | null;
    plant?: Plant;
    createdAt?: string;
    updatedAt?: string;
}

// *** Interface for Create Form Data ***
interface NewZoneData {
    zoneName: string;
    zoneArea?: string;
    enabled: boolean;
    zoneId?: number | null;
    plantId?: number | null; // Added for plant selection
}

@Component({
  selector: 'app-area',
  standalone: true,
  // Added necessary imports
  imports: [
    CommonModule,
    FormsModule,
    PaginationComponent,
    OffcanvasComponent,
    SwitchComponent,
    NgbDropdownModule,
    ToastMessageComponent
  ],
  templateUrl: './area.component.html',
  styleUrl: './area.component.scss'
})
export class AreaComponent implements OnInit {
  // Access the form template variable
  @ViewChild('createForm') createForm?: NgForm;
  @ViewChild('toast') toast?: ToastMessageComponent;

  // --- Modal States ---
  isFilterModalOpen = false; // Added state for filter modal
  isEditModalOpen = false;   // <-- New state for edit modal
  isCreateModalOpen = false; // <-- New state for create modal

  // --- Data & Loading States ---
  zoneList: Area[] = []; // Use Area interface
  listLoading = false; // Added loading state
  editLoading = false;     // <-- New state for edit form submission
  createLoading = false;   // <-- New state for create form submission
  zonesLoading = false;    // <-- New state for loading zones
  plantsLoading = false;   // <-- New state for loading plants
  isDownloadingExcel = false; // <-- New state for Excel download
  downloadType: 'current' | 'all' | '' = ''; // <-- Type of download

  // --- Pagination ---
  currentPage = 1;     // Added pagination state
  itemsPerPage = 10;   // Added pagination state
  totalItems = 0;      // Added pagination state

  // --- Filtering ---
  filters: ZoneFilter = { // Added filters object
      name: null,
      enabled: 'true', // Default based on original getAreaList call
      sortField: 'createdTimestamp', // Default based on original getAreaList call
      sortDirection: 'DESC', // Default based on original getAreaList call
      zoneId: null,
      plantId: null
  };
   availableSortFields = [ // Added sort fields for dropdown
        { value: 'id', label: 'ID' },
        { value: 'zoneName', label: 'Zone Name' },
        { value: 'enabled', label: 'Enabled Status' },
        { value: 'createdTimestamp', label: 'Created Date' }
    ];
    availableZones: Zone[] = []; // For filtering/creating/editing zones
    availablePlants: Plant[] = []; // For filtering/creating/editing plants

  // --- Editing ---
  selectedZone: Area | null = null; // <-- Object to hold data for editing

  // --- Creating ---
  newZoneData: NewZoneData = { // <-- Object to hold new zone form data
      zoneName: '',
      zoneArea: '',
      enabled: true, // Default new zones to active
      zoneId: null, // Initialize zoneId
      plantId: null // Initialize plantId
  };


  constructor(
    readonly areaService: AreaService,
    readonly zoneService: ZoneService, // Added ZoneService
    readonly plantService: PlantManagementService, // Added PlantManagementService
    readonly updateService: UpdateService, // Added UpdateService
    // Optional: Inject ToastrService
    // private toastr: ToastrService
    ) { }

  ngOnInit() {
    // Load zones on component initialization
    this.loadZones(this.currentPage);
    // Load available zones for dropdowns
    this.loadZonesForDropdown();
    // Load available plants for dropdowns
    this.loadPlants();
  }

  // Method to fetch plants for dropdowns
  async loadPlants(): Promise<void> {
      this.plantsLoading = true;
      try {
          const data = {
              sort: 'name,ASC',
              filter: ['enabled||eq||true'],
              limit: 1000
          };
          const param = createAxiosConfig(data);
          const response = await this.plantService.getPlants(param);
          // Handle different response formats
          this.availablePlants = response?.data ?? response ?? [];
          console.log("Loaded plants:", this.availablePlants.length);
      } catch (error) {
          console.error("Error fetching plants for dropdown:", error);
          this.availablePlants = [];
          // this.toastr.error("Could not load plants for selection.");
      } finally {
          this.plantsLoading = false;
      }
  }

  // --- Filter Modal Methods ---
  openFilterModal(): void { this.isFilterModalOpen = true; }
  closeFilterModal(): void { this.isFilterModalOpen = false; } // Renamed from closeModal
  applyFilters(): void {
      this.currentPage = 1; // Reset page on new filter apply
      this.loadZones(this.currentPage);
      this.closeFilterModal(); // Close modal after applying
  }
  resetFilters(): void {
      // Reset filter object to defaults
      this.filters = {
          name: null,
          enabled: 'true',
          sortField: 'createdTimestamp',
          sortDirection: 'DESC',
          zoneId: null,
          plantId: null
      };
      this.currentPage = 1; // Reset page
      this.loadZones(this.currentPage);
      // Optionally close modal: this.closeFilterModal();
  }


  // --- *** Edit Modal Methods *** ---
    /** Opens the edit zone offcanvas modal. */
    openEditModal(zone: Area): void {
        this.selectedZone = { 
            ...zone,
            // Ensure zoneName and zoneArea are properly mapped
            zoneName: zone.zoneName || zone.title || '',
            zoneArea: zone.zoneArea || ''
        }; // Create a copy with proper field mapping
        this.isEditModalOpen = true;
        this.editLoading = false;
    }

    /** Closes the edit zone offcanvas modal. */
    closeEditModal(): void {
        this.isEditModalOpen = false;
        this.selectedZone = null;
    }

    /** Handles the submission of the edit zone form. */
    async submitEditForm(): Promise<void> {
        if (!this.selectedZone || this.selectedZone.id == null) {
            console.error("Cannot save, selected zone is null or has no ID.");
            // this.toastr.error('Cannot save, no zone selected.', 'Error');
            return;
        }

        this.editLoading = true;
        const updatePayload = {
            tableName: 'zone', // Changed table name to zone
            id: this.selectedZone.id,
            data: {
                zoneName: this.selectedZone.zoneName,
                zoneArea: this.selectedZone.zoneArea,
                enabled: this.selectedZone.enabled
                // NOTE: Updating zoneId is omitted for simplicity. If needed, add it here.
                // zoneId: this.selectedZone.zone?.id // Or however the zone ID is handled
            }
        };

        try {
            await this.update(updatePayload); // Use the generic update method
            // this.toastr.success('Zone updated successfully!', 'Saved');

            // Update list locally (optional)
            const index = this.zoneList.findIndex(z => z.id === this.selectedZone?.id);
            if (index !== -1 && this.selectedZone) {
                 // Need to preserve/update the zone data if not editing it or if it changes
                 // This might require re-fetching the zone or handling the zone update separately
                 const originalZone = this.zoneList[index].zone; // Simplistic preservation
                 this.zoneList[index] = { ...this.selectedZone, zone: originalZone };
            }

            this.closeEditModal();
            // Reloading might be safer if zone relationships change or backend modifies data
            this.loadZones(this.currentPage);


        } catch (error) {
            console.error("Error submitting zone update:", error);
            // this.toastr.error('Failed to update zone.', 'Error');
        } finally {
            this.editLoading = false;
        }
    }


    // --- *** Create Modal Methods *** ---

    /** Opens the create zone offcanvas modal and resets the form data. */
    openCreateModal(): void {
        this.newZoneData = {
            zoneName: '',
            zoneArea: '',
            enabled: true,
            zoneId: null,
            plantId: null
        };
        this.createForm?.resetForm({ enabled: true, zoneId: null, plantId: null }); // Reset form state with defaults
        this.isCreateModalOpen = true;
        this.createLoading = false;
    }

    /** Closes the create zone offcanvas modal. */
    closeCreateModal(): void {
        this.isCreateModalOpen = false;
    }

    /** Handles the submission of the create zone form. */
    async submitCreateForm(): Promise<void> {
        // Add check for zoneId if it's made required in the form
        if (this.createForm?.invalid) {
             Object.values(this.createForm.controls).forEach(control => control.markAsTouched());
             console.warn('Create form submitted while invalid.');
             return;
        }

        this.createLoading = true;
        // Prepare data - include plantId
        const payload = {
            zoneName: this.newZoneData.zoneName,
            zoneArea: this.newZoneData.zoneArea,
            enabled: this.newZoneData.enabled,
            plantId: this.newZoneData.plantId // Include plantId
        };

        console.log('Submitting new zone:', payload);

        try {
            // Create zone using the zone service
            const createdZone = await this.zoneService.createZone(payload);
            console.log('Zone created successfully:', createdZone);

            // this.toastr.success(`Zone "${createdZone.zoneName}" created successfully!`, 'Created');

            this.closeCreateModal();
            // Reload list, go to page 1
            this.currentPage = 1;
            this.loadZones(this.currentPage);

        } catch (error) {
            console.error("Error creating zone:", error);
            // this.toastr.error('Failed to create zone. Please try again.', 'Creation Error');
        } finally {
            this.createLoading = false;
        }
    }


  // --- Data Loading & Update Methods ---

  /** Fetches zones from the backend. */
  async loadZones(page: number): Promise<void> {
      this.listLoading = true; // Set loading true
      // this.zoneList = []; // Clear only after loading starts

      // Build filter params dynamically
      const filterParams: string[] = [];
      if (this.filters.name) {
          filterParams.push(`zoneName||$contL||${this.filters.name}`);
      }
      if (this.filters.enabled !== null && this.filters.enabled !== undefined && this.filters.enabled !== '') {
          filterParams.push(`enabled||$eq||${this.filters.enabled}`);
      }
      if (this.filters.zoneId) {
          filterParams.push(`id||$eq||${this.filters.zoneId}`);
      }
      if (this.filters.plantId) {
          filterParams.push(`plantId||$eq||${this.filters.plantId}`);
      }

      const requestData = {
          page: page,
          limit: this.itemsPerPage,
          sort: `${this.filters.sortField || 'createdTimestamp'},${this.filters.sortDirection || 'DESC'}`,
          filter: filterParams, // Use dynamic filters
          join: ['plant'] // Join plant data
      };

      try {
          const params = createAxiosConfig(requestData); // Use utility function
          // Ensure service method returns { data: Zone[], total: number }
          const response = await this.zoneService.getZone(params); // Pass params object
          this.zoneList = response?.data ?? []; // Use response.data
          this.totalItems = response?.total ?? 0; // Use response.total
      } catch (error) {
          console.error("Error fetching zones:", error);
          this.zoneList = []; // Clear list on error
          this.totalItems = 0;
          // this.toastr.error('Failed to load zones.', 'Error');
      } finally {
          this.listLoading = false; // Set loading false
      }
  }

  /** Handles page changes. */
  onPageChange(page: number): void {
      if (page !== this.currentPage) {
          this.currentPage = page;
          this.loadZones(this.currentPage); // Call refactored method
      }
  }

  /** Handles the toggle switch change event in the table row. */
  async onSwitchToggle(isEnabled: boolean, zone: Area): Promise<void> {
      const originalState = zone.enabled;
      zone.enabled = isEnabled; // Optimistic update

      const updatePayload = {
          tableName: 'zone', // Changed table name to zone
          id: zone.id,
          data: {
              enabled: isEnabled
          }
      };
      console.log(`Attempting to update zone ${zone.id} enabled status to: ${isEnabled}`);

      try {
          await this.update(updatePayload);
          console.log(`Successfully updated zone ${zone.id} enabled status.`);
          // this.toastr.success('Zone status updated.', 'Success');
      } catch (error) {
          console.error(`Error updating enabled status for zone ${zone.id}:`, error);
          // this.toastr.error('Failed to update zone status.', 'Update Error');
          // Revert UI change on error
          zone.enabled = originalState;
          // Force update detection if needed
          const index = this.zoneList.findIndex((z: Area) => z.id === zone.id);
          if (index !== -1) {
                this.zoneList[index] = {...this.zoneList[index], enabled: originalState };
          }
      }
  }

  // Generic update via UpdateService (used by toggle and edit form)
  async update(data: { tableName: string, id: number, data: any }): Promise<void> {
      try {
          await this.updateService.update(data);
      } catch (error) {
          console.error("Update service call failed:", error);
          throw error; // Re-throw
      }
  }

  // Method to fetch zones for dropdowns
  async loadZonesForDropdown(): Promise<void> {
      this.zonesLoading = true;
      try {
          const data = {
              sort: 'zoneName,ASC',
              filter: ['enabled||eq||true'],
              limit: 1000
          };
          const param = createAxiosConfig(data);
          const response = await this.zoneService.getZone(param);
          this.availableZones = response?.data ?? [];
      } catch (error) {
          console.error("Error fetching zones for dropdown:", error);
          this.availableZones = [];
          // this.toastr.error("Could not load zones for selection.");
      } finally {
          this.zonesLoading = false;
      }
  }

  // Helper method to get current list data
  getCurrentListData(): Area[] {
      return this.zoneList;
  }

  // Helper method to fetch all filtered zones
  async fetchAllFilteredZones(): Promise<Area[]> {
      // Prepare filter params for API call
      const filterParams: string[] = [];
      if (this.filters.name) {
          filterParams.push(`zoneName||$contL||${this.filters.name}`);
      }
      if (this.filters.enabled !== null && this.filters.enabled !== undefined && this.filters.enabled !== '') {
          filterParams.push(`enabled||$eq||${this.filters.enabled}`);
      }
      if (this.filters.zoneId) {
          filterParams.push(`id||$eq||${this.filters.zoneId}`);
      }
      if (this.filters.plantId) {
          filterParams.push(`plantId||$eq||${this.filters.plantId}`);
      }

      const requestData = {
          page: 1,
          limit: 1000, // Set a high limit to get all records
          sort: `${this.filters.sortField || 'createdTimestamp'},${this.filters.sortDirection || 'DESC'}`,
          filter: filterParams,
          join: ['plant'] // Join plant data
      };

      try {
          const params = createAxiosConfig(requestData);
          const response = await this.zoneService.getZone(params);
          return response?.data ?? [];
      } catch (error) {
          console.error("Error fetching all filtered zones:", error);
          this.toast?.showErrorToast('Failed to fetch all zones for Excel download.');
          return [];
      }
  }

  // Download Excel file
  async downloadExcel(type: 'current' | 'all'): Promise<void> {
      if (this.isDownloadingExcel) return;

      this.isDownloadingExcel = true;
      this.downloadType = type;
      this.toast?.showSuccessToast(`Preparing download for ${type === 'current' ? 'current page' : 'all filtered'} zones...`);

      let dataToExport: Area[] | null = null;

      try {
          // 1. Get Data
          if (type === 'all') {
              dataToExport = await this.fetchAllFilteredZones();
          } else { // 'current'
              dataToExport = this.getCurrentListData();
          }

          if (!dataToExport || dataToExport.length === 0) {
              throw new Error('No data available to export');
          }

          // 2. Transform data for Excel
          const dataForExcel = dataToExport.map(zone => ({
              'ID': zone.id,
              'Zone Name': zone.zoneName || zone.title || '',
              'Zone Area': zone.zoneArea || 'N/A',
              'Plant': zone.plant?.name || 'N/A',
              'Status': zone.enabled ? 'Active' : 'Inactive',
              'Created At': zone.createdAt ? new Date(zone.createdAt).toLocaleString() : 'N/A',
              'Updated At': zone.updatedAt ? new Date(zone.updatedAt).toLocaleString() : 'N/A'
          }));

          // 3. Create Worksheet and Workbook
          const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(dataForExcel);
          const wb: XLSX.WorkBook = XLSX.utils.book_new();
          XLSX.utils.book_append_sheet(wb, ws, 'Zones');

          // 4. Generate File Name
          const dateStr = new Date().toISOString().slice(0, 10);
          const typeStr = type === 'current' ? `Page${this.currentPage}` : 'All';
          const fileName = `Zones_${typeStr}_${dateStr}.xlsx`;

          // 5. Trigger Download
          XLSX.writeFile(wb, fileName);
          this.toast?.showSuccessToast(`Excel file download started (${type}).`);
      } catch (error) {
          console.error(`Error generating Excel file (${type}):`, error);
          this.toast?.showErrorToast(`An error occurred while generating the Excel file (${type}).`);
      } finally {
          this.isDownloadingExcel = false;
          this.downloadType = '';
      }
  }

}
