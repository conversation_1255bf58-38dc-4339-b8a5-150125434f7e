<app-toast-message #toast></app-toast-message>
<div class="card" id="activated-qrcode">
    <div class="card-header d-flex align-items-center justify-content-between">
      <div>
          <h6 class="mb-0">Zone</h6>
      </div>
      <div class="d-flex align-items-center">
          <div ngbDropdown class="d-inline-block me-2">
            <button type="button" class="btn btn-sm adani-btn dropdown-toggle" id="downloadZoneExcelDropdown"
                ngbDropdownToggle [disabled]="isDownloadingExcel || listLoading">
                <span *ngIf="!isDownloadingExcel">
                    <i class="bi bi-file-earmark-excel me-1"></i> Download Excel
                </span>
                <span *ngIf="isDownloadingExcel">
                    <span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
                    Downloading {{ downloadType === 'current' ? 'Page' : (downloadType === 'all' ? 'All' : '') }}...
                </span>
            </button>
            <ul ngbDropdownMenu aria-labelledby="downloadZoneExcelDropdown">
                <li>
                    <button type="button" ngbDropdownItem (click)="downloadExcel('current')"
                        [disabled]="isDownloadingExcel || listLoading || (zoneList.length === 0)">
                        <i class="bi bi-download me-1"></i> Download Current Page ({{ zoneList.length }})
                    </button>
                </li>
                <li>
                    <button type="button" ngbDropdownItem (click)="downloadExcel('all')"
                        [disabled]="isDownloadingExcel || listLoading || totalItems === 0">
                        <i class="bi bi-cloud-download me-1"></i> Download All Filtered ({{ totalItems }})
                    </button>
                </li>
            </ul>
          </div>
          <!-- *** Create Button *** -->
          <button type="button" class="btn-sm adani-btn ms-2" (click)="openCreateModal()" title="Create New Zone">
            <i class="bi bi-plus-circle"></i> Create New
          </button>
          <!-- *** Filter Icon *** -->
          <img src="../../../assets/svg/filter.svg" class="filter-button ms-3" (click)="openFilterModal()" alt="Filter" title="Filter Zones" style="width: 35px; height: 35px;" />
        </div>
      </div>
    <div class="card-body">
      <div class="table-container">
          <div class="table-responsive">
              <table class="table table-bordered table-hover">
                  <thead class="table-header">
                      <tr class="text-center">
                          <th scope="col">Id</th>
                          <th scope="col">Enabled/Disabled</th>
                          <th scope="col">Zone Name</th>
                          <th scope="col">Zone Area</th>
                          <th scope="col">Plant</th>
                          <th scope="col">Actions</th>
                      </tr>
                  </thead>
                  <tbody>
                     <!-- Loading Indicator -->
                     <tr *ngIf="listLoading">
                        <td colspan="6" class="text-center">
                            <div class="spinner-border spinner-border-sm" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            Loading Zones...
                        </td>
                    </tr>
                     <!-- No Data Message -->
                     <tr *ngIf="!listLoading && zoneList.length === 0">
                        <td colspan="6" class="text-center">No zones found.</td>
                    </tr>
                    <!-- Data Rows -->
                    <tr *ngFor="let item of zoneList">
                      <td class="text-center">{{ item.id }}</td>
                      <td>
                          <app-switch
                              [(checked)]="item.enabled"
                              (checkedChange)="onSwitchToggle($event, item)"
                              onLabel="Active" offLabel="Inactive">
                          </app-switch>
                      </td>
                      <td>{{ item.zoneName || item.title }}</td>
                      <td>{{ item.zoneArea || 'N/A' }}</td>
                      <td>{{ item.plant?.name || 'N/A' }}</td>
                      <td class="actions text-center">
                        <button type="button" class="adani-btn" (click)="openEditModal(item)" title="Edit Zone">
                          <i class="bi bi-pencil edit"></i>
                        </button>
                      </td>
                    </tr>
                  </tbody>
              </table>
          </div>
      </div>
    </div>
    <div class="card-footer text-muted text-center">
      <app-pagination [currentPage]="currentPage" [totalItems]="totalItems" [itemsPerPage]="itemsPerPage"
      (pageChange)="onPageChange($event)"></app-pagination>
    </div>
</div>

<!-- Filter Offcanvas -->
<app-offcanvas [title]="'Filter Zones'" *ngIf="isFilterModalOpen" (onClickCross)="closeFilterModal()">
  <div class="filter-container p-3">
      <form #filterForm="ngForm" (ngSubmit)="applyFilters()">
          <div class="row g-3">

              <div class="col-12">
                  <label class="form-label" for="filterZoneName">Zone Name</label>
                  <input type="text" id="filterZoneName" class="form-control" placeholder="Search by Zone Name"
                         [(ngModel)]="filters.name" name="name" maxlength="30" pattern="^(?=.*[a-zA-Z])[a-zA-Z0-9\s]*$" #filterNameInput="ngModel"
                         [ngClass]="{'is-invalid': filterNameInput.invalid && (filterNameInput.dirty || filterNameInput.touched)}">
                  <!-- Character count display - only visible when there's text -->
                  <small *ngIf="filters.name" class="text-muted d-block text-end mt-1">
                      {{ filters.name.length }}/30 characters
                  </small>
                  <div *ngIf="filterNameInput.invalid && (filterNameInput.dirty || filterNameInput.touched)" class="invalid-feedback">
                      <div *ngIf="filterNameInput.errors?.['pattern']">Zone name should contain only alphanumeric characters.</div>
                  </div>
              </div>

               <!-- Filter by Plant -->
              <div class="col-12">
                  <label class="form-label" for="filterPlant">Plant</label>
                  <select id="filterPlant" class="form-select" [(ngModel)]="filters.plantId" name="plantId">
                      <option [ngValue]="null">Any Plant</option>
                      <option *ngFor="let plant of availablePlants" [value]="plant.id">{{ plant.name }}</option>
                  </select>
              </div>

               <div class="col-12">
                  <label class="form-label" for="filterEnabledZone">Enabled Status</label>
                  <select id="filterEnabledZone" class="form-select"
                          [(ngModel)]="filters.enabled" name="enabled">
                      <option [ngValue]="null">Any</option>
                      <option value="true">Yes</option>
                      <option value="false">No</option>
                  </select>
              </div>

              <div class="col-12">
                  <label class="form-label" for="filterSortByZone">Sort By</label>
                  <select id="filterSortByZone" class="form-select"
                          [(ngModel)]="filters.sortField" name="sortField">
                      
                      <option *ngFor="let field of availableSortFields" [value]="field.value">{{ field.label }}</option>
                  </select>
                   <label class="form-label mt-2" for="filterSortDirZone">Sort Direction</label>
                   <select id="filterSortDirZone" class="form-select"
                          [(ngModel)]="filters.sortDirection" name="sortDirection">
                      <option value="ASC">Ascending</option>
                      <option value="DESC">Descending</option>
                  </select>
              </div>

              <div class="col-12 mt-4 d-grid gap-2">
                  <button type="submit" class="btn adani-btn" [disabled]="filterForm.invalid">
                      <i class="bi bi-search me-1"></i> Search
                  </button>
                   <button type="button" class="btn btn-secondary" (click)="resetFilters()">
                      <i class="bi bi-arrow-clockwise me-1"></i> Reset Filters
                  </button>
              </div>
          </div>
      </form>
  </div>
</app-offcanvas>


<!-- Edit Offcanvas -->
<app-offcanvas [title]="'Edit Zone'" *ngIf="isEditModalOpen" (onClickCross)="closeEditModal()">
  <div class="edit-container p-3">
      <form *ngIf="selectedZone" #editForm="ngForm" (ngSubmit)="submitEditForm()">
          <div class="row g-3">

              <!-- Zone ID (Readonly) -->
              <div class="col-12">
                  <label class="form-label" for="editZoneId">Zone ID</label>
                  <input type="text" id="editZoneId" class="form-control"
                         [value]="selectedZone.id" name="id" readonly disabled>
              </div>

              <!-- Zone Name (Editable) -->
              <div class="col-12">
                  <label class="form-label" for="editZoneName">Zone Name</label>
                  <input type="text" id="editZoneName" class="form-control" placeholder="Enter Zone Name"
                         [(ngModel)]="selectedZone.zoneName" name="zoneName" required #zoneNameInput="ngModel"
                         pattern="^(?=.*[a-zA-Z])[a-zA-Z0-9\s]*$" maxlength="50"
                         [ngClass]="{'is-invalid': zoneNameInput.invalid && (zoneNameInput.dirty || zoneNameInput.touched)}">
                   <!-- Validation Messages -->
                   <div *ngIf="zoneNameInput.invalid && (zoneNameInput.dirty || zoneNameInput.touched)" class="invalid-feedback">
                      <div *ngIf="zoneNameInput.errors?.['required']">Zone name is required.</div>
                      <div *ngIf="zoneNameInput.errors?.['pattern']">Zone name should contain only alphanumeric characters.</div>
                      <div *ngIf="zoneNameInput.errors?.['maxlength']">Zone name cannot exceed 50 characters.</div>
                  </div>
                  <!-- Character count display - only visible when there's text -->
                  <small *ngIf="selectedZone.zoneName" class="text-muted d-block text-end mt-1">
                      {{ selectedZone.zoneName.length }}/50 characters
                  </small>
              </div>

              <!-- Zone Area (Editable) -->
              <div class="col-12">
                  <label class="form-label" for="editZoneArea">Zone Area</label>
                  <input type="text" id="editZoneArea" class="form-control" placeholder="Enter Zone Area Description"
                         [(ngModel)]="selectedZone.zoneArea" name="zoneArea" #zoneAreaInput="ngModel"
                         maxlength="100"
                         [ngClass]="{'is-invalid': zoneAreaInput.invalid && (zoneAreaInput.dirty || zoneAreaInput.touched)}">
                   <!-- Validation Messages -->
                   <div *ngIf="zoneAreaInput.invalid && (zoneAreaInput.dirty || zoneAreaInput.touched)" class="invalid-feedback">
                      <div *ngIf="zoneAreaInput.errors?.['maxlength']">Zone area cannot exceed 100 characters.</div>
                  </div>
                  <!-- Character count display - only visible when there's text -->
                  <small *ngIf="selectedZone.zoneArea" class="text-muted d-block text-end mt-1">
                      {{ selectedZone.zoneArea.length }}/100 characters
                  </small>
              </div>

               <!-- Plant (Display Only) -->
               <div class="col-12">
                   <label class="form-label">Plant</label>
                   <input type="text" class="form-control" [value]="selectedZone.plant?.name || 'N/A'" readonly disabled>
               </div>


               <!-- Enabled Status -->
               <div class="col-12">
                   <label class="form-label d-block mb-2">Status</label>
                    <app-switch
                          [(checked)]="selectedZone.enabled"
                          name="enabled"
                          onLabel="Active"
                          offLabel="Inactive">
                    </app-switch>
               </div>

              <!-- Action Buttons -->
              <div class="col-12 mt-4 d-flex justify-content-end gap-2">
                  <button type="button" class="btn btn-secondary" (click)="closeEditModal()">
                      Cancel
                  </button>
                  <button type="submit" class="btn adani-btn" [disabled]="editForm.invalid || editLoading">
                       <span *ngIf="!editLoading"><i class="bi bi-save me-1"></i> Save Changes</span>
                       <span *ngIf="editLoading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                       <span *ngIf="editLoading"> Saving...</span>
                  </button>
              </div>
          </div>
      </form>
      <div *ngIf="!selectedZone && isEditModalOpen" class="text-center p-5">
           <div class="spinner-border spinner-border-sm" role="status">
               <span class="visually-hidden">Loading form...</span>
           </div>
      </div>
  </div>
</app-offcanvas>

<!-- Create Offcanvas -->
<app-offcanvas [title]="'Create New Zone'" *ngIf="isCreateModalOpen" (onClickCross)="closeCreateModal()">
  <div class="create-container p-3">
      <form #createForm="ngForm" (ngSubmit)="submitCreateForm()">
          <div class="row g-3">

              <!-- Zone Name (Required) -->
              <div class="col-12">
                  <label class="form-label" for="createZoneName">Zone Name</label>
                  <input type="text" id="createZoneName" class="form-control" placeholder="Enter New Zone Name"
                         [(ngModel)]="newZoneData.zoneName" name="zoneName" required #createZoneNameInput="ngModel"
                         pattern="^(?=.*[a-zA-Z])[a-zA-Z0-9\s]*$" maxlength="50"
                         [ngClass]="{'is-invalid': createZoneNameInput.invalid && (createZoneNameInput.dirty || createZoneNameInput.touched)}">
                  <!-- Validation Messages -->
                  <div *ngIf="createZoneNameInput.invalid && (createZoneNameInput.dirty || createZoneNameInput.touched)" class="invalid-feedback">
                      <div *ngIf="createZoneNameInput.errors?.['required']">Zone name is required.</div>
                      <div *ngIf="createZoneNameInput.errors?.['pattern']">Zone name should contain only alphanumeric characters.</div>
                      <div *ngIf="createZoneNameInput.errors?.['maxlength']">Zone name cannot exceed 50 characters.</div>
                  </div>
                  <!-- Character count display - only visible when there's text -->
                  <small *ngIf="newZoneData.zoneName" class="text-muted d-block text-end mt-1">
                      {{ newZoneData.zoneName.length }}/50 characters
                  </small>
              </div>

              <!-- Zone Area -->
              <div class="col-12">
                  <label class="form-label" for="createZoneArea">Zone Area</label>
                  <input type="text" id="createZoneArea" class="form-control" placeholder="Enter Zone Area Description"
                         [(ngModel)]="newZoneData.zoneArea" name="zoneArea" #createZoneAreaInput="ngModel"
                         maxlength="100"
                         [ngClass]="{'is-invalid': createZoneAreaInput.invalid && (createZoneAreaInput.dirty || createZoneAreaInput.touched)}">
                  <!-- Validation Messages -->
                  <div *ngIf="createZoneAreaInput.invalid && (createZoneAreaInput.dirty || createZoneAreaInput.touched)" class="invalid-feedback">
                      <div *ngIf="createZoneAreaInput.errors?.['maxlength']">Zone area cannot exceed 100 characters.</div>
                  </div>
                  <!-- Character count display - only visible when there's text -->
                  <small *ngIf="newZoneData.zoneArea" class="text-muted d-block text-end mt-1">
                      {{ newZoneData.zoneArea.length }}/100 characters
                  </small>
              </div>

              <!-- Plant Selection -->
              <div class="col-12">
                  <label class="form-label" for="createZonePlant">Plant</label>
                   <select id="createZonePlant" class="form-select" [(ngModel)]="newZoneData.plantId" name="plantId" required #plantInput="ngModel">
                       <option [ngValue]="null" disabled>Select Plant</option>
                       <option *ngFor="let plant of availablePlants" [value]="plant.id">{{ plant.name }}</option>
                   </select>
                   <div *ngIf="plantInput.invalid && (plantInput.dirty || plantInput.touched)" class="text-danger small mt-1">
                       Plant is required.
                   </div>
               </div>

              <!-- Enabled Status (Default to Active/true) -->
              <div class="col-12">
                  <label class="form-label d-block mb-2">Status</label>
                  <app-switch
                        [(checked)]="newZoneData.enabled"
                        name="enabled"
                        onLabel="Active"
                        offLabel="Inactive">
                  </app-switch>
              </div>

              <!-- Action Buttons -->
              <div class="col-12 mt-4 d-flex justify-content-end gap-2">
                  <button type="button" class="btn btn-secondary" (click)="closeCreateModal()">
                      Cancel
                  </button>
                  <button type="submit" class="btn adani-btn" [disabled]="createForm.invalid || createLoading">
                      <span *ngIf="!createLoading"><i class="bi bi-plus-circle-fill me-1"></i> Create Zone</span>
                      <span *ngIf="createLoading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                      <span *ngIf="createLoading"> Creating...</span>
                  </button>
              </div>
          </div>
      </form>
  </div>
</app-offcanvas>
