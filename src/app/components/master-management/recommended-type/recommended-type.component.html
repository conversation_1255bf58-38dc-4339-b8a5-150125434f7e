<app-toast-message></app-toast-message>
<div class="card" id="activated-qrcode">
    <div class="card-header d-flex align-items-center justify-content-between">
        <div>
            <h6 class="mb-0">Recommended Type</h6>
        </div>
        <div class="d-flex align-items-center">
                <!-- *** REPLACE the existing simple Download button with this Dropdown *** -->
                <div ngbDropdown class="d-inline-block me-2">
                    <button type="button" class="btn btn-sm adani-btn dropdown-toggle" id="downloadRecTypeExcelDropdown"
                        ngbDropdownToggle [disabled]="isDownloadingExcel || listLoading">
                        <span *ngIf="!isDownloadingExcel">
                            <i class="bi bi-file-earmark-excel me-1"></i> Download Excel
                        </span>
                        <span *ngIf="isDownloadingExcel">
                            <span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
                            Downloading {{ downloadType === 'current' ? 'Page' : (downloadType === 'all' ? 'All' : '')
                            }}...
                        </span>
                    </button>
                    <ul ngbDropdownMenu aria-labelledby="downloadRecTypeExcelDropdown">
                        <li>
                            <button ngbDropdownItem (click)="downloadExcel('current')"
                                [disabled]="isDownloadingExcel || listLoading || (recommendedTypeList?.length ?? 0) === 0">
                                <i class="bi bi-download me-1"></i> Download Current Page ({{
                                recommendedTypeList?.length ?? 0 }})
                            </button>
                        </li>
                        <li>
                            <button ngbDropdownItem (click)="downloadExcel('all')"
                                [disabled]="isDownloadingExcel || listLoading || totalItems === 0">
                                <i class="bi bi-cloud-download me-1"></i> Download All Filtered ({{ totalItems }})
                            </button>
                        </li>
                    </ul>
                </div>
                <!-- *** END REPLACEMENT *** -->
                <!-- *** NEW: Create Button *** -->
                <button class="btn-sm adani-btn ms-2" (click)="openCreateModal()" title="Create New Recommended Type">
                    <i class="bi bi-plus-circle"></i> Create New
                </button>
                <img src="../../../assets/svg/filter.svg" class="filter-button ms-3" (click)="openFilterModal()"
                    alt="Filter" style="width: 35px;" />
            </div>
        </div>
    <div class="card-body">
        <div class="table-container">
            <div class="table-responsive">
                <table class="table table-bordered table-hover">
                    <thead class="table-header">
                        <tr class="text-center">
                            <th scope="col">Id</th>
                            <th scope="col">Enabled/Disabled</th>
                            <th scope="col">Recommended Type Name</th> <!-- Changed header -->
                            <th scope="col">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- Loading Indicator -->
                        <tr *ngIf="listLoading">
                            <td colspan="4" class="text-center">
                                <div class="spinner-border spinner-border-sm" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                Loading Recommended Types...
                            </td>
                        </tr>
                        <!-- No Data Message -->
                        <tr *ngIf="!listLoading && recommendedTypeList.length === 0">
                            <td colspan="4" class="text-center">No recommended types found.</td>
                        </tr>
                        <!-- Data Rows -->
                        <tr *ngFor="let item of recommendedTypeList">
                            <td class="text-center">{{ item.id }}</td>
                            <td>
                                <app-switch [(checked)]="item.enabled" [requireConfirmation]="true"
                                    (checkedChange)="onSwitchToggle($event, item)" onLabel="Active" offLabel="Inactive">
                                </app-switch>
                            </td>
                            <td>{{ item.title }}</td>
                            <td class="actions text-center"> <!-- Added text-center -->
                                <!-- *** UPDATED: Edit Button Click Handler *** -->
                                <button class="adani-btn" (click)="openEditModal(item)" title="Edit Recommended Type">
                                    <i class="bi bi-pencil edit"></i>
                                </button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <div class="card-footer text-muted text-center">
        <app-pagination [currentPage]="currentPage" [totalItems]="totalItems" [itemsPerPage]="itemsPerPage"
            (pageChange)="onPageChange($event)"></app-pagination>
    </div>
</div>

<!-- Filter Offcanvas (Renamed *ngIf variable) -->
<app-offcanvas [title]="'Filter Recommended Types'" *ngIf="isFilterModalOpen" (onClickCross)="closeFilterModal()">
    <div class="filter-container p-3">
        <form #filterForm="ngForm" (ngSubmit)="applyFilters()">
            <div class="row g-3">

                <div class="col-12">
                    <label class="form-label" for="filterRecTypeName">Recommended Type Name</label>
                    <input type="text" id="filterRecTypeName" class="form-control"
                        placeholder="Search by Recommended Type Name" [(ngModel)]="filters.name" name="name"
                        maxlength="30" pattern="^[a-zA-Z\s]+$" #filterName="ngModel">
                    <div *ngIf="filterName.invalid && (filterName.dirty || filterName.touched)" class="text-danger small mt-1">
                        <div *ngIf="filterName.errors?.['pattern']">
                            Only alphabetic characters and spaces are allowed.
                        </div>
                        <div *ngIf="filterName.errors?.['maxlength']">
                            Maximum 30 characters allowed.
                        </div>
                    </div>
                </div>

                <div class="col-12">
                    <label class="form-label" for="filterEnabledRecType">Enabled Status</label>
                    <select id="filterEnabledRecType" class="form-select" [(ngModel)]="filters.enabled" name="enabled">
                        <option [ngValue]="null">Any</option>
                        <option value="true">Yes</option>
                        <option value="false">No</option>
                    </select>
                </div>

                <div class="col-12">
                    <label class="form-label" for="filterSortByRecType">Sort By</label>
                    <select id="filterSortByRecType" class="form-select" [(ngModel)]="filters.sortField"
                        name="sortField">
                        
                        <option *ngFor="let field of availableSortFields" [value]="field.value">{{ field.label }}
                        </option>
                    </select>
                    <label class="form-label mt-2" for="filterSortDirRecType">Sort Direction</label>
                    <select id="filterSortDirRecType" class="form-select" [(ngModel)]="filters.sortDirection"
                        name="sortDirection">
                        <option value="ASC">Ascending</option>
                        <option value="DESC">Descending</option>
                    </select>
                </div>

                <div class="col-12 mt-4 d-grid gap-2">
                    <button type="submit" class="btn adani-btn">
                        <i class="bi bi-search me-1"></i> Search
                    </button>
                    <button type="button" class="btn btn-secondary" (click)="resetFilters()">
                        <i class="bi bi-arrow-clockwise me-1"></i> Reset Filters
                    </button>
                </div>
            </div>
        </form>
    </div>
</app-offcanvas>

<!-- ********** NEW: Edit Offcanvas ********** -->
<app-offcanvas [title]="'Edit Recommended Type'" *ngIf="isEditModalOpen" (onClickCross)="closeEditModal()">
    <div class="edit-container p-3">
        <form *ngIf="selectedRecommendedType" #editForm="ngForm" (ngSubmit)="submitEditForm()">
            <div class="row g-3">

                <!-- Recommended Type ID (Readonly) -->
                <div class="col-12">
                    <label class="form-label" for="editRecTypeId">Recommended Type ID</label>
                    <input type="text" id="editRecTypeId" class="form-control" [value]="selectedRecommendedType.id"
                        name="id" readonly disabled>
                </div>

                <!-- Recommended Type Name (Editable) -->
                <div class="col-12">
                    <label class="form-label" for="editRecTypeName">Recommended Type Name</label>
                    <input type="text" id="editRecTypeName" class="form-control"
                        placeholder="Enter Recommended Type Name" [(ngModel)]="selectedRecommendedType.title"
                        name="title" required maxlength="30" pattern="^[a-zA-Z\s]+$" #titleInput="ngModel">
                    <div *ngIf="titleInput.invalid && (titleInput.dirty || titleInput.touched)"
                        class="text-danger small mt-1">
                        <div *ngIf="titleInput.errors?.['required']">
                            Recommended type name is required.
                        </div>
                        <div *ngIf="titleInput.errors?.['pattern']">
                            Only alphabetic characters and spaces are allowed.
                        </div>
                        <div *ngIf="titleInput.errors?.['maxlength']">
                            Maximum 30 characters allowed.
                        </div>
                    </div>
                    <div class="text-muted small mt-1" *ngIf="selectedRecommendedType.title">
                        {{ selectedRecommendedType.title.length }}/30 characters
                    </div>
                </div>

                <!-- Enabled Status -->
                <div class="col-12">
                    <label class="form-label d-block mb-2">Status</label>
                    <app-switch [(checked)]="selectedRecommendedType.enabled" name="enabled" onLabel="Active"
                        offLabel="Inactive">
                    </app-switch>
                </div>

                <!-- Action Buttons -->
                <div class="col-12 mt-4 d-flex justify-content-end gap-2">
                    <button type="button" class="btn btn-secondary" (click)="closeEditModal()">
                        Cancel
                    </button>
                    <button type="submit" class="btn adani-btn" [disabled]="editForm.invalid || editLoading">
                        <span *ngIf="!editLoading"><i class="bi bi-save me-1"></i> Save Changes</span>
                        <span *ngIf="editLoading" class="spinner-border spinner-border-sm" role="status"
                            aria-hidden="true"></span>
                        <span *ngIf="editLoading"> Saving...</span>
                    </button>
                </div>
            </div>
        </form>
        <div *ngIf="!selectedRecommendedType && isEditModalOpen" class="text-center p-5">
            <div class="spinner-border spinner-border-sm" role="status">
                <span class="visually-hidden">Loading form...</span>
            </div>
        </div>
    </div>
</app-offcanvas>
<!-- ********** END: Edit Offcanvas ********** -->

<!-- ********** NEW: Create Offcanvas ********** -->
<app-offcanvas [title]="'Create New Recommended Type'" *ngIf="isCreateModalOpen" (onClickCross)="closeCreateModal()">
    <div class="create-container p-3">
        <form #createForm="ngForm" (ngSubmit)="submitCreateForm()">
            <div class="row g-3">

                <!-- Recommended Type Name (Required) -->
                <div class="col-12">
                    <label class="form-label" for="createRecTypeName">Recommended Type Name</label>
                    <input type="text" id="createRecTypeName" class="form-control"
                        placeholder="Enter New Recommended Type Name" [(ngModel)]="newRecommendedTypeData.title"
                        name="title" required maxlength="30" pattern="^[a-zA-Z\s]+$" #createTitleInput="ngModel">
                    <div *ngIf="createTitleInput.invalid && (createTitleInput.dirty || createTitleInput.touched)"
                        class="text-danger small mt-1">
                        <div *ngIf="createTitleInput.errors?.['required']">
                            Recommended type name is required.
                        </div>
                        <div *ngIf="createTitleInput.errors?.['pattern']">
                            Only alphabetic characters and spaces are allowed.
                        </div>
                        <div *ngIf="createTitleInput.errors?.['maxlength']">
                            Maximum 30 characters allowed.
                        </div>
                    </div>
                    <div class="text-muted small mt-1" *ngIf="newRecommendedTypeData.title">
                        {{ newRecommendedTypeData.title.length }}/30 characters
                    </div>
                </div>

                <!-- Enabled Status (Default to Active/true) -->
                <div class="col-12">
                    <label class="form-label d-block mb-2">Status</label>
                    <app-switch [(checked)]="newRecommendedTypeData.enabled" name="enabled" onLabel="Active"
                        offLabel="Inactive">
                    </app-switch>
                </div>

                <!-- Action Buttons -->
                <div class="col-12 mt-4 d-flex justify-content-end gap-2">
                    <button type="button" class="btn btn-secondary" (click)="closeCreateModal()">
                        Cancel
                    </button>
                    <button type="submit" class="btn adani-btn" [disabled]="createForm.invalid || createLoading">
                        <span *ngIf="!createLoading"><i class="bi bi-plus-circle-fill me-1"></i> Create Recommended
                            Type</span>
                        <span *ngIf="createLoading" class="spinner-border spinner-border-sm" role="status"
                            aria-hidden="true"></span>
                        <span *ngIf="createLoading"> Creating...</span>
                    </button>
                </div>
            </div>
        </form>
    </div>
</app-offcanvas>
<!-- ********** END: Create Offcanvas ********** -->
