import { Component, OnInit, ViewChild } from '@angular/core'; // Added ViewChild
import { FormsModule, NgForm } from '@angular/forms'; // Added NgForm
import { RecommendedTypeService } from '../../../services/master-management/recommended-type/recommended-type.service';
import { createAxiosConfig } from '../../../core/utilities/axios-param-config';
import { CommonModule } from '@angular/common';
import { SwitchComponent } from "../../../shared/switch/switch.component";
import { PaginationComponent } from "../../../shared/pagination/pagination.component";
import { OffcanvasComponent } from "../../../shared/offcanvas/offcanvas.component";
import { UpdateService } from '../../../services/update/update.service';
// Optional: Import ToastrService or similar
// import { ToastrService } from 'ngx-toastr';
import { ToastMessageComponent } from '../../../shared/toast-message/toast-message.component'; // Adjust path if needed
import { NgbDropdownModule } from '@ng-bootstrap/ng-bootstrap'; // For dropdown button
import * as XLSX from 'xlsx'; // For Excel generation


// Interface for filter structure
interface RecommendedTypeFilter {
    name?: string | null;
    enabled?: string | null;
    sortField?: string | null;
    sortDirection?: 'ASC' | 'DESC';
}

// *** NEW: Interface for Recommended Type Data ***
export interface RecommendedType {
    id: number;
    title: string;
    enabled: boolean;
    createdAt?: string; // Optional
    updatedAt?: string; // Optional
}

// *** NEW: Interface for Create Form Data ***
interface NewRecommendedTypeData {
    title: string;
    enabled: boolean;
}


@Component({
    selector: 'app-recommended-type',
    standalone: true,
    imports: [
        CommonModule,
        FormsModule,
        SwitchComponent,
        PaginationComponent,
        OffcanvasComponent,
        NgbDropdownModule,
        ToastMessageComponent
    ],
    templateUrl: './recommended-type.component.html',
    styleUrl: './recommended-type.component.scss'
})
export class RecommendedTypeComponent implements OnInit {
    // Access the form template variable
    @ViewChild('createForm') createForm?: NgForm;
    @ViewChild(ToastMessageComponent) toast!: ToastMessageComponent;

    // --- Modal States ---
    isFilterModalOpen = false; // Renamed from isEditUserModalOpen
    isEditModalOpen = false;   // <-- New state for edit modal
    isCreateModalOpen = false; // <-- New state for create modal

    // --- Data & Loading States ---
    recommendedTypeList: RecommendedType[] = []; // Use RecommendedType interface
    listLoading = false;
    editLoading = false;     // <-- New state for edit form submission
    createLoading = false;   // <-- New state for create form submission

    // --- Pagination ---
    currentPage = 1;
    itemsPerPage = 10;
    totalItems = 0;
    isDownloadingExcel = false;
    downloadType: 'current' | 'all' | null = null;

    // --- Filtering ---
    filters: RecommendedTypeFilter = {
        name: null,
        enabled: 'true',
        sortField: 'createdTimestamp',
        sortDirection: 'DESC'
    };
    availableSortFields = [
        { value: 'id', label: 'ID' },
        { value: 'title', label: 'Recommended Type Name' },
        { value: 'enabled', label: 'Enabled Status' },
        { value: 'createdTimestamp', label: 'Created Date' }
    ];

    // --- Editing ---
    selectedRecommendedType: RecommendedType | null = null; // <-- Object to hold data for editing

    // --- Creating ---
    newRecommendedTypeData: NewRecommendedTypeData = { // <-- Object to hold new recommended type form data
        title: '',
        enabled: true // Default new recommended types to active
    };

    constructor(
        readonly recommendedTypeService: RecommendedTypeService,
        readonly updateService: UpdateService,
        // Optional: Inject ToastrService
        // private toastr: ToastrService
    ) { }

    ngOnInit(): void {
        this.loadRecommendedTypes(this.currentPage);
    }

    // Helper to get current list data
    getCurrentListData(): RecommendedType[] | undefined {
        return this.recommendedTypeList;
    }

    // Fetch ALL recommended types matching current filters (no pagination)
    async fetchAllFilteredRecommendedTypes(): Promise<RecommendedType[] | null> {
        this.listLoading = true; // Indicate loading
        const filterParams: string[] = [];
        if (this.filters.name) { filterParams.push(`title||$contL||${this.filters.name}`); }
        if (this.filters.enabled !== null && this.filters.enabled !== undefined && this.filters.enabled !== '') {
             filterParams.push(`enabled||$eq||${this.filters.enabled}`);
        }

        const data = {
            limit: 10000, // Large limit for "all"
            sort: `${this.filters.sortField || 'createdTimestamp'},${this.filters.sortDirection || 'DESC'}`, // Match default sort
            filter: filterParams
        };

        try {
            const params = createAxiosConfig(data);
            console.log('API Request Params (Rec Types Download - All Data):', JSON.stringify(params, null, 2));
            const response = await this.recommendedTypeService.getRecommendedType(params); // Use existing service method
            return response ?? [];
        } catch (error: any) {
            console.error("Error fetching all recommended types for download:", error);
            this.toast?.showErrorToast(error?.response?.data?.message || "Failed to retrieve full data for download.");
            return null;
        } finally {
             this.listLoading = false; // Reset loading indicator
        }
    }

    // Main download function
    async downloadExcel(type: 'current' | 'all') {
        if (this.isDownloadingExcel || this.listLoading) return; // Prevent concurrent actions

        this.isDownloadingExcel = true;
        this.downloadType = type;
        this.toast?.showSuccessToast(`Preparing download for ${type === 'current' ? 'current page' : 'all filtered'} recommended types...`);

        let dataToExport: RecommendedType[] | null = null;

        try {
            // 1. Get Data
            if (type === 'all') {
                dataToExport = await this.fetchAllFilteredRecommendedTypes();
            } else { // 'current'
                dataToExport = this.getCurrentListData() ?? null;
                if (dataToExport === undefined) { dataToExport = null; }
            }

            // 2. Check data
            if (dataToExport === null) { this.toast?.showErrorToast("Failed to retrieve data for download."); return; }
            if (!dataToExport || dataToExport.length === 0) { this.toast?.showErrorToast(`No recommended types available to download.`); return; }

            console.log(`Fetched ${dataToExport.length} recommended types for Excel export (${type}).`);

            // 3. Transform data
            const dataForExcel = dataToExport.map(rt => ({
                'Recommended Type ID': rt.id,
                'Recommended Type Name': rt.title,
                'Status': rt.enabled ? 'Active' : 'Inactive',
                'Created At': rt.createdAt ? new Date(rt.createdAt).toLocaleString() : 'N/A',
                'Updated At': rt.updatedAt ? new Date(rt.updatedAt).toLocaleString() : 'N/A',
            }));

            // 4. Create Worksheet and Workbook
            const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(dataForExcel);
            const wb: XLSX.WorkBook = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(wb, ws, 'RecommendedTypes'); // Sheet name

            // 5. Generate File Name
            const dateStr = new Date().toISOString().slice(0, 10);
            const typeStr = type === 'current' ? `Page${this.currentPage}` : 'All';
            const fileName = `RecommendedTypes_${typeStr}_${dateStr}.xlsx`;

            // 6. Trigger Download
            XLSX.writeFile(wb, fileName);
            this.toast?.showSuccessToast(`Excel file download started (${type}).`);

        } catch (error) {
            console.error(`Error generating Excel file (${type}):`, error);
            this.toast?.showErrorToast(`An error occurred while generating the Excel file (${type}).`);
        } finally {
            this.isDownloadingExcel = false;
            this.downloadType = null;
        }
    }

    // --- Filter Modal Methods ---
    openFilterModal(): void { this.isFilterModalOpen = true; }
    closeFilterModal(): void { this.isFilterModalOpen = false; } // Renamed from closeModal
    applyFilters(): void {
        // Validate filter name if provided
        if (this.filters.name && this.filters.name.length > 30) {
            this.toast?.showErrorToast('Filter name cannot exceed 30 characters.');
            return;
        }

        // Validate filter name pattern if provided
        if (this.filters.name && !/^[a-zA-Z\s]+$/.test(this.filters.name)) {
            this.toast?.showErrorToast('Only alphabetic characters and spaces are allowed.');
            return;
        }

        this.currentPage = 1;
        this.loadRecommendedTypes(this.currentPage);
        this.closeFilterModal();
    }
    resetFilters(): void {
        this.filters = {
            name: null,
            enabled: 'true',
            sortField: 'createdTimestamp',
            sortDirection: 'DESC'
        };
        this.currentPage = 1;
        this.loadRecommendedTypes(this.currentPage);
        // Optionally close modal: this.closeFilterModal();
    }

    // --- *** NEW: Edit Modal Methods *** ---
    /** Opens the edit recommended type offcanvas modal. */
    openEditModal(recType: RecommendedType): void {
        this.selectedRecommendedType = { ...recType }; // Create a copy
        this.isEditModalOpen = true;
        this.editLoading = false;
    }

    /** Closes the edit recommended type offcanvas modal. */
    closeEditModal(): void {
        this.isEditModalOpen = false;
        this.selectedRecommendedType = null;
    }

    /** Handles the submission of the edit recommended type form. */
    async submitEditForm(): Promise<void> {
        if (!this.selectedRecommendedType || this.selectedRecommendedType.id == null) {
            console.error("Cannot save, selected recommended type is null or has no ID.");
            this.toast?.showErrorToast('Cannot save, no recommended type selected.');
            return;
        }

        // Validate title field
        if (!this.selectedRecommendedType.title || this.selectedRecommendedType.title.trim() === '') {
            this.toast?.showErrorToast('Recommended type name cannot be empty.');
            return;
        }

        // Check if title exceeds maximum length
        if (this.selectedRecommendedType.title.length > 30) {
            this.toast?.showErrorToast('Recommended type name cannot exceed 30 characters.');
            return;
        }

        // Check if title contains only alphabetic characters and spaces
        if (!/^[a-zA-Z\s]+$/.test(this.selectedRecommendedType.title)) {
            this.toast?.showErrorToast('Only alphabetic characters and spaces are allowed.');
            return;
        }

        this.editLoading = true;
        const updatePayload = {
             // !! Critical: Verify this table name matches your backend !!
            tableName: 'recomended-type-master', // From your original onSwitchToggle
            id: this.selectedRecommendedType.id,
            data: {
                title: this.selectedRecommendedType.title,
                enabled: this.selectedRecommendedType.enabled
                // Add other editable fields if necessary
            }
        };

        try {
            await this.update(updatePayload); // Use the generic update method
            // this.toastr.success('Recommended type updated successfully!', 'Saved');

            // Update list locally (optional)
            const index = this.recommendedTypeList.findIndex(rt => rt.id === this.selectedRecommendedType?.id);
            if (index !== -1 && this.selectedRecommendedType) {
                 this.recommendedTypeList[index] = { ...this.selectedRecommendedType };
            }

            this.closeEditModal();
            // Consider reloading if necessary: this.loadRecommendedTypes(this.currentPage);

        } catch (error) {
            console.error("Error submitting recommended type update:", error);
            // this.toastr.error('Failed to update recommended type.', 'Error');
        } finally {
            this.editLoading = false;
        }
    }


    // --- *** NEW: Create Modal Methods *** ---

    /** Opens the create recommended type offcanvas modal and resets the form data. */
    openCreateModal(): void {
        this.newRecommendedTypeData = {
            title: '',
            enabled: true
        };
        this.createForm?.resetForm({ enabled: true }); // Reset form state with default
        this.isCreateModalOpen = true;
        this.createLoading = false;
    }

    /** Closes the create recommended type offcanvas modal. */
    closeCreateModal(): void {
        this.isCreateModalOpen = false;
    }

    /** Handles the submission of the create recommended type form. */
    async submitCreateForm(): Promise<void> {
        if (this.createForm?.invalid) {
             Object.values(this.createForm.controls).forEach(control => control.markAsTouched());
             console.warn('Create form submitted while invalid.');
             this.toast?.showErrorToast('Please correct the validation errors before submitting.');
             return;
        }

        // Additional validation
        if (this.newRecommendedTypeData.title.trim() === '') {
            this.toast?.showErrorToast('Recommended type name cannot be empty.');
            return;
        }

        // Check if title exceeds maximum length
        if (this.newRecommendedTypeData.title.length > 30) {
            this.toast?.showErrorToast('Recommended type name cannot exceed 30 characters.');
            return;
        }

        // Check if title contains only alphabetic characters and spaces
        if (!/^[a-zA-Z\s]+$/.test(this.newRecommendedTypeData.title)) {
            this.toast?.showErrorToast('Only alphabetic characters and spaces are allowed.');
            return;
        }

        this.createLoading = true;
        console.log('Submitting new recommended type:', this.newRecommendedTypeData);

        try {
            // *** ASSUMPTION: recommendedTypeService has a createRecommendedType method ***
            const createdRecType = await this.recommendedTypeService.createRecommendedType(this.newRecommendedTypeData);
            console.log('Recommended type created successfully:', createdRecType);

            // this.toastr.success(`Recommended type "${createdRecType.title}" created successfully!`, 'Created');

            this.closeCreateModal();
            // Reload list, go to page 1
            this.currentPage = 1;
            this.loadRecommendedTypes(this.currentPage);

        } catch (error) {
            console.error("Error creating recommended type:", error);
            // this.toastr.error('Failed to create recommended type. Please try again.', 'Creation Error');
        } finally {
            this.createLoading = false;
        }
    }


    // --- Data Loading & Update Methods ---

    /** Fetches recommended types from the backend. */
    async loadRecommendedTypes(page: number): Promise<void> {
        this.listLoading = true;
        // this.recommendedTypeList = []; // Clear only after loading starts

        const filterParams: string[] = [];
        if (this.filters.name) {
            filterParams.push(`title||$contL||${this.filters.name}`);
        }
        if (this.filters.enabled !== null && this.filters.enabled !== undefined && this.filters.enabled !== '') {
            filterParams.push(`enabled||$eq||${this.filters.enabled}`);
        }

        const requestData = {
            page: page,
            limit: this.itemsPerPage,
            sort: `${this.filters.sortField || 'createdTimestamp'},${this.filters.sortDirection || 'DESC'}`,
            filter: filterParams
        };

        try {
            const params = createAxiosConfig(requestData);
            // Ensure service method returns { data: RecommendedType[], total: number }
            const response = await this.recommendedTypeService.getRecommendedType(params);
            this.recommendedTypeList = response?.data ?? [];
            this.totalItems = response?.total ?? 0;
        } catch (error) {
            console.error("Error fetching recommended types:", error);
            this.recommendedTypeList = [];
            this.totalItems = 0;
            // this.toastr.error('Failed to load recommended types.', 'Error');
        } finally {
            this.listLoading = false;
        }
    }

    /** Handles page changes. */
    onPageChange(page: number): void {
        if (page !== this.currentPage) {
            this.currentPage = page;
            this.loadRecommendedTypes(this.currentPage);
        }
    }

    /** Handles the toggle switch change event in the table row. */
    async onSwitchToggle(isEnabled: boolean, recType: RecommendedType): Promise<void> {
        const originalState = recType.enabled;
        recType.enabled = isEnabled; // Optimistic update

        const updatePayload = {
             // !! Critical: Verify this table name matches your backend !!
            tableName: 'recomended-type-master', // From your original code
            id: recType.id,
            data: {
                enabled: isEnabled
            }
        };
        console.log(`Attempting to update recommended type ${recType.id} enabled status to: ${isEnabled}`);

        try {
            await this.update(updatePayload);
            console.log(`Successfully updated recommended type ${recType.id} enabled status.`);
            // this.toastr.success('Recommended type status updated.', 'Success');
        } catch (error) {
            console.error(`Error updating enabled status for recommended type ${recType.id}:`, error);
            // this.toastr.error('Failed to update recommended type status.', 'Update Error');
            // Revert UI change on error
            recType.enabled = originalState;
            // Force update detection if needed
            const index = this.recommendedTypeList.findIndex(rt => rt.id === recType.id);
            if (index !== -1) {
                 this.recommendedTypeList[index] = {...this.recommendedTypeList[index], enabled: originalState };
            }
        }
    }

    // Generic update via UpdateService (used by toggle and edit form)
    async update(data: { tableName: string, id: number, data: any }): Promise<void> {
        try {
            await this.updateService.update(data);
        } catch (error) {
            console.error("Update service call failed:", error);
            throw error; // Re-throw
        }
    }

    // --- Removed Placeholder/Unused Methods ---
    // Removed handleCreate, handleUpdate, toggleEnabled, handleDelete, sortBy, getSortClass
}