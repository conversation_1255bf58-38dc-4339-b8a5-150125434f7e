import { Component, OnInit, ViewChild } from '@angular/core'; // Added ViewChild
import { FormsModule, NgForm } from '@angular/forms'; // Added NgForm
import { ClusterService } from '../../../services/master-management/cluster/cluster.service';
import { createAxiosConfig } from '../../../core/utilities/axios-param-config';
import { CommonModule } from '@angular/common';
import { SwitchComponent } from "../../../shared/switch/switch.component";
import { PaginationComponent } from "../../../shared/pagination/pagination.component";
import { OffcanvasComponent } from "../../../shared/offcanvas/offcanvas.component";
import { UpdateService } from '../../../services/update/update.service';
// Optional: Import ToastrService or similar
// import { ToastrService } from 'ngx-toastr';
import { ToastMessageComponent } from '../../../shared/toast-message/toast-message.component'; // Adjust path if needed
import { NgbDropdownModule } from '@ng-bootstrap/ng-bootstrap'; // For dropdown button
import * as XLSX from 'xlsx'; // For Excel generation


// Interface for filter structure
interface ClusterFilter {
    name?: string | null;
    enabled?: string | null;
    sortField?: string | null;
    sortDirection?: 'ASC' | 'DESC';
}

// *** NEW: Interface for Cluster Data ***
export interface Cluster {
    id: number;
    title: string;
    enabled: boolean;
    createdAt?: string; // Optional
    updatedAt?: string; // Optional
}

// *** NEW: Interface for Create Form Data ***
interface NewClusterData {
    title: string;
    enabled: boolean;
}


@Component({
    selector: 'app-cluster',
    standalone: true,
    imports: [
        CommonModule,
        FormsModule,
        SwitchComponent,
        PaginationComponent,
        OffcanvasComponent,
        NgbDropdownModule,
        ToastMessageComponent
    ],
    templateUrl: './cluster.component.html',
    styleUrl: './cluster.component.scss'
})
export class ClusterComponent implements OnInit {
    // Access the form template variable
    @ViewChild('createForm') createForm?: NgForm;
    @ViewChild(ToastMessageComponent) toast!: ToastMessageComponent;

    // --- Modal States ---
    isFilterModalOpen = false; // Renamed from isEditUserModalOpen
    isEditModalOpen = false;   // <-- New state for edit modal
    isCreateModalOpen = false; // <-- New state for create modal

    // --- Data & Loading States ---
    clusterList: Cluster[] = []; // Use Cluster interface
    listLoading = false;
    editLoading = false;     // <-- New state for edit form submission
    createLoading = false;   // <-- New state for create form submission

    // --- Pagination ---
    currentPage = 1;
    itemsPerPage = 10;
    totalItems = 0;
    isDownloadingExcel = false;
    downloadType: 'current' | 'all' | null = null;

    // --- Filtering ---
    filters: ClusterFilter = {
        name: null,
        enabled: 'true',
        sortField: 'createdTimestamp',
        sortDirection: 'DESC'
    };
    availableSortFields = [
        { value: 'id', label: 'ID' },
        { value: 'title', label: 'Cluster Name' },
        { value: 'enabled', label: 'Enabled Status' },
        { value: 'createdTimestamp', label: 'Created Date' }
    ];

    // --- Editing ---
    selectedCluster: Cluster | null = null; // <-- Object to hold data for editing

    // --- Creating ---
    newClusterData: NewClusterData = { // <-- Object to hold new cluster form data
        title: '',
        enabled: true // Default new clusters to active
    };

    constructor(
        readonly clusterService: ClusterService,
        readonly updateService: UpdateService,
        // Optional: Inject ToastrService
        // private toastr: ToastrService
        ) { }

    ngOnInit() {
        this.loadClusters(this.currentPage);
    }

    // Helper to get current list data
    getCurrentListData(): Cluster[] | undefined {
        return this.clusterList;
    }

    // Fetch ALL clusters matching current filters (no pagination)
    async fetchAllFilteredClusters(): Promise<Cluster[] | null> {
        this.listLoading = true; // Indicate loading
        const filterParams: string[] = [];
        if (this.filters.name) { filterParams.push(`title||$contL||${this.filters.name}`); }
        if (this.filters.enabled !== null && this.filters.enabled !== undefined && this.filters.enabled !== '') {
             filterParams.push(`enabled||$eq||${this.filters.enabled}`);
        }

        const data = {
            limit: 10000, // Large limit for "all"
            sort: `${this.filters.sortField || 'createdTimestamp'},${this.filters.sortDirection || 'DESC'}`, // Match default sort
            filter: filterParams
        };

        try {
            const params = createAxiosConfig(data);
            console.log('API Request Params (Clusters Download - All Data):', JSON.stringify(params, null, 2));
            const response = await this.clusterService.getCluster(params); // Use existing service method
            // Adjust based on actual response structure
            return response?.data ?? response ?? [];
        } catch (error: any) {
            console.error("Error fetching all clusters for download:", error);
            this.toast?.showErrorToast(error?.response?.data?.message || "Failed to retrieve full data for download.");
            return null;
        } finally {
             this.listLoading = false; // Reset loading indicator
        }
    }

    // Main download function
    async downloadExcel(type: 'current' | 'all') {
        if (this.isDownloadingExcel || this.listLoading) return; // Prevent concurrent actions

        this.isDownloadingExcel = true;
        this.downloadType = type;
        this.toast?.showSuccessToast(`Preparing download for ${type === 'current' ? 'current page' : 'all filtered'} clusters...`);

        let dataToExport: Cluster[] | null = null;

        try {
            // 1. Get Data
            if (type === 'all') {
                dataToExport = await this.fetchAllFilteredClusters();
            } else { // 'current'
                dataToExport = this.getCurrentListData() ?? null;
                if (dataToExport === undefined) { dataToExport = null; }
            }

            // 2. Check data
            if (dataToExport === null) { this.toast?.showErrorToast("Failed to retrieve data for download."); return; }
            if (!dataToExport || dataToExport.length === 0) { this.toast?.showErrorToast(`No clusters available to download.`); return; }

            console.log(`Fetched ${dataToExport.length} clusters for Excel export (${type}).`);

            // 3. Transform data
            const dataForExcel = dataToExport.map(cluster => ({
                'Cluster ID': cluster.id,
                'Cluster Name': cluster.title,
                'Status': cluster.enabled ? 'Active' : 'Inactive',
                'Created At': cluster.createdAt ? new Date(cluster.createdAt).toLocaleString() : 'N/A',
                'Updated At': cluster.updatedAt ? new Date(cluster.updatedAt).toLocaleString() : 'N/A',
            }));

            // 4. Create Worksheet and Workbook
            const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(dataForExcel);
            const wb: XLSX.WorkBook = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(wb, ws, 'Clusters'); // Sheet name

            // 5. Generate File Name
            const dateStr = new Date().toISOString().slice(0, 10);
            const typeStr = type === 'current' ? `Page${this.currentPage}` : 'All';
            const fileName = `Clusters_${typeStr}_${dateStr}.xlsx`;

            // 6. Trigger Download
            XLSX.writeFile(wb, fileName);
            this.toast?.showSuccessToast(`Excel file download started (${type}).`);

        } catch (error) {
            console.error(`Error generating Excel file (${type}):`, error);
            this.toast?.showErrorToast(`An error occurred while generating the Excel file (${type}).`);
        } finally {
            this.isDownloadingExcel = false;
            this.downloadType = null;
        }
    }

    // --- Filter Modal Methods ---
    openFilterModal(): void { this.isFilterModalOpen = true; }
    closeFilterModal(): void { this.isFilterModalOpen = false; } // Renamed from closeModal

    @ViewChild('filterForm') filterForm!: NgForm;

    applyFilters(): void {
        // Check if form is valid before applying filters
        if (this.filterForm && this.filterForm.invalid) {
            this.toast?.showErrorToast("Please correct the validation errors before applying filters.");
            return;
        }

        // Trim string values to prevent whitespace-only searches
        if (this.filters.name) {
            this.filters.name = this.filters.name.trim();
        }

        this.currentPage = 1;
        this.loadClusters(this.currentPage);
        this.closeFilterModal();
    }

    resetFilters(): void {
        this.filters = {
            name: null,
            enabled: 'true',
            sortField: 'createdTimestamp',
            sortDirection: 'DESC'
        };
        this.currentPage = 1;
        this.loadClusters(this.currentPage);
        // Optionally close modal: this.closeFilterModal();
    }

    // --- *** NEW: Edit Modal Methods *** ---
    /** Opens the edit cluster offcanvas modal. */
    openEditModal(cluster: Cluster): void {
        this.selectedCluster = { ...cluster }; // Create a copy
        this.isEditModalOpen = true;
        this.editLoading = false;
    }

    /** Closes the edit cluster offcanvas modal. */
    closeEditModal(): void {
        this.isEditModalOpen = false;
        this.selectedCluster = null;
    }

    @ViewChild('editForm') editForm!: NgForm;

    /** Handles the submission of the edit cluster form. */
    async submitEditForm(): Promise<void> {
        if (!this.selectedCluster || this.selectedCluster.id == null) {
            console.error("Cannot save, selected cluster is null or has no ID.");
            this.toast?.showErrorToast('Cannot save, no cluster selected.');
            return;
        }

        // Check form validity
        if (this.editForm && this.editForm.invalid) {
            this.toast?.showErrorToast("Please correct the validation errors before saving.");
            // Mark fields as touched to show validation errors
            Object.values(this.editForm.controls).forEach(control => {
                control.markAsTouched();
            });
            return;
        }

        // Trim the title to remove any leading/trailing whitespace
        if (this.selectedCluster.title) {
            this.selectedCluster.title = this.selectedCluster.title.trim();
        }

        this.editLoading = true;
        const updatePayload = {
            tableName: 'cluster', // Critical: Ensure this matches your backend/service expectation
            id: this.selectedCluster.id,
            data: {
                title: this.selectedCluster.title,
                enabled: this.selectedCluster.enabled
                // Add other editable fields if necessary
            }
        };

        try {
            await this.update(updatePayload); // Use the generic update method
            this.toast?.showSuccessToast('Cluster updated successfully!');

            // Update list locally (optional)
            const index = this.clusterList.findIndex(c => c.id === this.selectedCluster?.id);
            if (index !== -1 && this.selectedCluster) {
                 this.clusterList[index] = { ...this.selectedCluster };
            }

            this.closeEditModal();
            // Consider reloading if necessary: this.loadClusters(this.currentPage);

        } catch (error) {
            console.error("Error submitting cluster update:", error);
            this.toast?.showErrorToast('Failed to update cluster.');
        } finally {
            this.editLoading = false;
        }
    }


    // --- *** NEW: Create Modal Methods *** ---

    /** Opens the create cluster offcanvas modal and resets the form data. */
    openCreateModal(): void {
        this.newClusterData = {
            title: '',
            enabled: true
        };
        this.createForm?.resetForm({ enabled: true }); // Reset form state with default
        this.isCreateModalOpen = true;
        this.createLoading = false;
    }

    /** Closes the create cluster offcanvas modal. */
    closeCreateModal(): void {
        this.isCreateModalOpen = false;
    }

    /** Handles the submission of the create cluster form. */
    async submitCreateForm(): Promise<void> {
        if (this.createForm?.invalid) {
             Object.values(this.createForm.controls).forEach(control => control.markAsTouched());
             this.toast?.showErrorToast("Please correct the validation errors before creating.");
             console.warn('Create form submitted while invalid.');
             return;
        }

        // Trim the title to remove any leading/trailing whitespace
        if (this.newClusterData.title) {
            this.newClusterData.title = this.newClusterData.title.trim();
        }

        this.createLoading = true;
        console.log('Submitting new cluster:', this.newClusterData);

        try {
            // *** ASSUMPTION: clusterService has a createCluster method ***
            const createdCluster = await this.clusterService.createCluster(this.newClusterData);
            console.log('Cluster created successfully:', createdCluster);

            this.toast?.showSuccessToast(`Cluster "${createdCluster.title}" created successfully!`);

            this.closeCreateModal();
            // Reload list, go to page 1
            this.currentPage = 1;
            this.loadClusters(this.currentPage);

        } catch (error) {
            console.error("Error creating cluster:", error);
            this.toast?.showErrorToast('Failed to create cluster. Please try again.');
        } finally {
            this.createLoading = false;
        }
    }


    // --- Data Loading & Update Methods ---

    /** Fetches clusters from the backend. */
    async loadClusters(page: number): Promise<void> {
        this.listLoading = true;
        // this.clusterList = []; // Clear only after loading starts

        const filterParams: string[] = [];
        if (this.filters.name) {
            filterParams.push(`title||$contL||${this.filters.name}`);
        }
        if (this.filters.enabled !== null && this.filters.enabled !== undefined && this.filters.enabled !== '') {
            filterParams.push(`enabled||$eq||${this.filters.enabled}`);
        }

        const requestData = {
            page: page,
            limit: this.itemsPerPage,
            sort: `${this.filters.sortField || 'createdTimestamp'},${this.filters.sortDirection || 'DESC'}`,
            filter: filterParams
        };

        try {
            const params = createAxiosConfig(requestData);
            // Ensure service method returns { data: Cluster[], total: number }
            const response = await this.clusterService.getCluster(params);
            this.clusterList = response?.data ?? [];
            this.totalItems = response?.total ?? 0;
        } catch (error) {
            console.error("Error fetching clusters:", error);
            this.clusterList = [];
            this.totalItems = 0;
            // this.toastr.error('Failed to load clusters.', 'Error');
        } finally {
            this.listLoading = false;
        }
    }

    /** Handles page changes. */
    onPageChange(page: number): void {
        if (page !== this.currentPage) {
            this.currentPage = page;
            this.loadClusters(this.currentPage);
        }
    }

    /** Handles the toggle switch change event in the table row. */
    async onSwitchToggle(isEnabled: boolean, cluster: Cluster): Promise<void> {
        const originalState = cluster.enabled;
        cluster.enabled = isEnabled; // Optimistic update

        const updatePayload = {
            tableName: 'cluster', // Use correct table name
            id: cluster.id,
            data: {
                enabled: isEnabled
            }
        };
        console.log(`Attempting to update cluster ${cluster.id} enabled status to: ${isEnabled}`);

        try {
            await this.update(updatePayload);
            console.log(`Successfully updated cluster ${cluster.id} enabled status.`);
            // this.toastr.success('Cluster status updated.', 'Success');
        } catch (error) {
            console.error(`Error updating enabled status for cluster ${cluster.id}:`, error);
            // this.toastr.error('Failed to update cluster status.', 'Update Error');
            // Revert UI change on error
            cluster.enabled = originalState;
            // Force update detection if needed
            const index = this.clusterList.findIndex(c => c.id === cluster.id);
            if (index !== -1) {
                 this.clusterList[index] = {...this.clusterList[index], enabled: originalState };
            }
        }
    }

    // Generic update via UpdateService (used by toggle and edit form)
    async update(data: { tableName: string, id: number, data: any }): Promise<void> {
        try {
            await this.updateService.update(data);
        } catch (error) {
            console.error("Update service call failed:", error);
            throw error; // Re-throw
        }
    }

    // --- Removed Placeholder/Unused Methods ---
    // Removed handleCreate, handleUpdate, toggleEnabled, handleDelete, sortBy, getSortClass
}