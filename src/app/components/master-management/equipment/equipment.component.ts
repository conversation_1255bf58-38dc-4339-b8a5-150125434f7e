import { Component, OnInit, ViewChild } from '@angular/core'; // Added ViewChild
import { FormsModule, NgForm } from '@angular/forms'; // Added NgForm
import { EquipmentService } from '../../../services/master-management/equipment/equipment.service';
import { createAxiosConfig } from '../../../core/utilities/axios-param-config';
import { CommonModule } from '@angular/common';
import { SwitchComponent } from "../../../shared/switch/switch.component";
import { PaginationComponent } from "../../../shared/pagination/pagination.component";
import { OffcanvasComponent } from "../../../shared/offcanvas/offcanvas.component";
import { UpdateService } from '../../../services/update/update.service';
// Optional: Import ToastrService or similar
// import { ToastrService } from 'ngx-toastr';
import { ToastMessageComponent } from '../../../shared/toast-message/toast-message.component'; // Adjust path if needed
import { NgbDropdownModule } from '@ng-bootstrap/ng-bootstrap'; // For dropdown button
import * as XLSX from 'xlsx'; // For Excel generation

// Interface for filter structure
interface EquipmentFilter {
    name?: string | null;
    enabled?: string | null;
    sortField?: string | null;
    sortDirection?: 'ASC' | 'DESC';
}

// *** NEW: Interface for Equipment Data ***
export interface Equipment {
    id: number;
    title: string;
    enabled: boolean;
    createdAt?: string; // Optional
    updatedAt?: string; // Optional
}

// *** NEW: Interface for Create Form Data ***
interface NewEquipmentData {
    title: string;
    enabled: boolean;
}


@Component({
    selector: 'app-equipment',
    standalone: true,
    imports: [
        CommonModule,
        FormsModule,
        SwitchComponent,
        PaginationComponent,
        OffcanvasComponent,
        NgbDropdownModule,
        ToastMessageComponent
    ],
    templateUrl: './equipment.component.html',
    styleUrl: './equipment.component.scss'
})
export class EquipmentComponent implements OnInit {
    // Access the form template variable
    @ViewChild('createForm') createForm?: NgForm;
    @ViewChild(ToastMessageComponent) toast!: ToastMessageComponent;

    // --- Modal States ---
    isFilterModalOpen = false; // Renamed from isEditUserModalOpen
    isEditModalOpen = false;   // <-- New state for edit modal
    isCreateModalOpen = false; // <-- New state for create modal

    // --- Data & Loading States ---
    equipmentList: Equipment[] = []; // Use Equipment interface
    listLoading = false;
    editLoading = false;     // <-- New state for edit form submission
    createLoading = false;   // <-- New state for create form submission
    isDownloadingExcel = false;
    downloadType: 'current' | 'all' | null = null;

    // --- Pagination ---
    currentPage = 1;
    itemsPerPage = 10;
    totalItems = 0;

    // --- Filtering ---
    filters: EquipmentFilter = {
        name: null,
        enabled: 'true',
        sortField: 'createdTimestamp',
        sortDirection: 'DESC'
    };
    availableSortFields = [
        { value: 'id', label: 'ID' },
        { value: 'title', label: 'Equipment Name' },
        { value: 'enabled', label: 'Enabled Status' },
        { value: 'createdTimestamp', label: 'Created Date' }
    ];

    // --- Editing ---
    selectedEquipment: Equipment | null = null; // <-- Object to hold data for editing

    // --- Creating ---
    newEquipmentData: NewEquipmentData = { // <-- Object to hold new equipment form data
        title: '',
        enabled: true // Default new equipment to active
    };

    constructor(
        readonly equipmentService: EquipmentService,
        readonly updateService: UpdateService,
        // Optional: Inject ToastrService
        // private toastr: ToastrService
    ) { }

    ngOnInit() {
        this.loadEquipments(this.currentPage);
    }
    // Helper to get current list data
    getCurrentListData(): Equipment[] | undefined {
        return this.equipmentList;
    }

    // Fetch ALL equipment matching current filters (no pagination)
    async fetchAllFilteredEquipment(): Promise<Equipment[] | null> {
        this.listLoading = true; // Indicate loading
        const filterParams: string[] = [];
        if (this.filters.name) { filterParams.push(`title||$contL||${this.filters.name}`); }
        if (this.filters.enabled !== null && this.filters.enabled !== undefined && this.filters.enabled !== '') {
             filterParams.push(`enabled||$eq||${this.filters.enabled}`);
        }

        const data = {
            limit: 10000, // Large limit for "all"
            sort: `${this.filters.sortField || 'createdTimestamp'},${this.filters.sortDirection || 'DESC'}`, // Match default sort
            filter: filterParams
        };

        try {
            const params = createAxiosConfig(data);
            console.log('API Request Params (Equipment Download - All Data):', JSON.stringify(params, null, 2));
            const response = await this.equipmentService.getEquipment(params); // Use existing service method
            return response ?? [];
        } catch (error: any) {
            console.error("Error fetching all equipment for download:", error);
            this.toast?.showErrorToast(error?.response?.data?.message || "Failed to retrieve full data for download.");
            return null;
        } finally {
             this.listLoading = false; // Reset loading indicator
        }
    }

    // Main download function
    async downloadExcel(type: 'current' | 'all') {
        if (this.isDownloadingExcel || this.listLoading) return; // Prevent concurrent actions

        this.isDownloadingExcel = true;
        this.downloadType = type;
        this.toast?.showSuccessToast(`Preparing download for ${type === 'current' ? 'current page' : 'all filtered'} equipment...`);

        let dataToExport: Equipment[] | null = null;

        try {
            // 1. Get Data
            if (type === 'all') {
                dataToExport = await this.fetchAllFilteredEquipment();
            } else { // 'current'
                dataToExport = this.getCurrentListData() ?? null;
                if (dataToExport === undefined) { dataToExport = null; }
            }

            // 2. Check data
            if (dataToExport === null) { this.toast?.showErrorToast("Failed to retrieve data for download."); return; }
            if (!dataToExport || dataToExport.length === 0) { this.toast?.showErrorToast(`No equipment available to download.`); return; }

            console.log(`Fetched ${dataToExport.length} equipment items for Excel export (${type}).`);

            // 3. Transform data
            const dataForExcel = dataToExport.map(eq => ({
                'Equipment ID': eq.id,
                'Equipment Name': eq.title,
                'Status': eq.enabled ? 'Active' : 'Inactive',
                'Created At': eq.createdAt ? new Date(eq.createdAt).toLocaleString() : 'N/A',
                'Updated At': eq.updatedAt ? new Date(eq.updatedAt).toLocaleString() : 'N/A',
            }));

            // 4. Create Worksheet and Workbook
            const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(dataForExcel);
            const wb: XLSX.WorkBook = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(wb, ws, 'Equipment'); // Sheet name

            // 5. Generate File Name
            const dateStr = new Date().toISOString().slice(0, 10);
            const typeStr = type === 'current' ? `Page${this.currentPage}` : 'All';
            const fileName = `Equipment_${typeStr}_${dateStr}.xlsx`;

            // 6. Trigger Download
            XLSX.writeFile(wb, fileName);
            this.toast?.showSuccessToast(`Excel file download started (${type}).`);

        } catch (error) {
            console.error(`Error generating Excel file (${type}):`, error);
            this.toast?.showErrorToast(`An error occurred while generating the Excel file (${type}).`);
        } finally {
            this.isDownloadingExcel = false;
            this.downloadType = null;
        }
    }

    // --- Filter Modal Methods ---
    openFilterModal(): void { this.isFilterModalOpen = true; }
    closeFilterModal(): void { this.isFilterModalOpen = false; }
    applyFilters(): void {
        // Validate equipment name contains only alphabets if provided
        if (this.filters.name && !this.isValidName(this.filters.name)) {
            this.toast?.showErrorToast("Equipment name should contain only alphabets.");
            return;
        }

        this.currentPage = 1;
        this.loadEquipments(this.currentPage);
        this.closeFilterModal();
    }

    // Validation helper method for name fields
    private isValidName(name: string): boolean {
        const nameRegex = /^[a-zA-Z\s]*$/;
        return nameRegex.test(name);
    }
    resetFilters(): void {
        this.filters = {
            name: null,
            enabled: 'true',
            sortField: 'createdTimestamp',
            sortDirection: 'DESC'
        };
        this.currentPage = 1;
        this.loadEquipments(this.currentPage);
        // Optionally close modal: this.closeFilterModal();
    }

    // --- *** NEW: Edit Modal Methods *** ---
    /** Opens the edit equipment offcanvas modal. */
    openEditModal(equipment: Equipment): void {
        this.selectedEquipment = { ...equipment }; // Create a copy
        this.isEditModalOpen = true;
        this.editLoading = false;
    }

    /** Closes the edit equipment offcanvas modal. */
    closeEditModal(): void {
        this.isEditModalOpen = false;
        this.selectedEquipment = null;
    }

    /** Handles the submission of the edit equipment form. */
    async submitEditForm(): Promise<void> {
        if (!this.selectedEquipment || this.selectedEquipment.id == null) {
            console.error("Cannot save, selected equipment is null or has no ID.");
            // this.toastr.error('Cannot save, no equipment selected.', 'Error');
            return;
        }

        // Validate equipment name
        if (!this.selectedEquipment.title || this.selectedEquipment.title.trim() === '') {
            this.toast?.showErrorToast("Equipment name is required.");
            return;
        }

        if (!this.isValidName(this.selectedEquipment.title)) {
            this.toast?.showErrorToast("Equipment name should contain only alphabets.");
            return;
        }

        if (this.selectedEquipment.title.length > 30) {
            this.toast?.showErrorToast("Equipment name cannot exceed 30 characters.");
            return;
        }

        this.editLoading = true;
        const updatePayload = {
            tableName: 'equipement-master', // Critical: Ensure this matches your backend/service expectation
            id: this.selectedEquipment.id,
            data: {
                title: this.selectedEquipment.title,
                enabled: this.selectedEquipment.enabled
                // Add other editable fields if necessary
            }
        };

        try {
            await this.update(updatePayload); // Use the generic update method
            // this.toastr.success('Equipment updated successfully!', 'Saved');

            // Update list locally (optional)
            const index = this.equipmentList.findIndex(e => e.id === this.selectedEquipment?.id);
            if (index !== -1 && this.selectedEquipment) {
                 this.equipmentList[index] = { ...this.selectedEquipment };
            }

            this.closeEditModal();
            // Consider reloading if necessary: this.loadEquipments(this.currentPage);

        } catch (error) {
            console.error("Error submitting equipment update:", error);
            // this.toastr.error('Failed to update equipment.', 'Error');
        } finally {
            this.editLoading = false;
        }
    }


    // --- *** NEW: Create Modal Methods *** ---

    /** Opens the create equipment offcanvas modal and resets the form data. */
    openCreateModal(): void {
        this.newEquipmentData = {
            title: '',
            enabled: true
        };
        this.createForm?.resetForm({ enabled: true }); // Reset form state with default
        this.isCreateModalOpen = true;
        this.createLoading = false;
    }

    /** Closes the create equipment offcanvas modal. */
    closeCreateModal(): void {
        this.isCreateModalOpen = false;
    }

    /** Handles the submission of the create equipment form. */
    async submitCreateForm(): Promise<void> {
        if (this.createForm?.invalid) {
             Object.values(this.createForm.controls).forEach(control => control.markAsTouched());
             console.warn('Create form submitted while invalid.');
             return;
        }

        // Additional validation
        if (!this.newEquipmentData.title || this.newEquipmentData.title.trim() === '') {
            this.toast?.showErrorToast("Equipment name is required.");
            return;
        }

        if (!this.isValidName(this.newEquipmentData.title)) {
            this.toast?.showErrorToast("Equipment name should contain only alphabets.");
            return;
        }

        if (this.newEquipmentData.title.length > 30) {
            this.toast?.showErrorToast("Equipment name cannot exceed 30 characters.");
            return;
        }

        this.createLoading = true;
        console.log('Submitting new equipment:', this.newEquipmentData);

        try {
            // *** ASSUMPTION: equipmentService has a createEquipment method ***
            const createdEquip = await this.equipmentService.createEquipment(this.newEquipmentData);
            console.log('Equipment created successfully:', createdEquip);

            // this.toastr.success(`Equipment "${createdEquip.title}" created successfully!`, 'Created');

            this.closeCreateModal();
            // Reload list, go to page 1
            this.currentPage = 1;
            this.loadEquipments(this.currentPage);

        } catch (error) {
            console.error("Error creating equipment:", error);
            // this.toastr.error('Failed to create equipment. Please try again.', 'Creation Error');
        } finally {
            this.createLoading = false;
        }
    }


    // --- Data Loading & Update Methods ---

    /** Fetches equipment from the backend. */
    async loadEquipments(page: number): Promise<void> {
        this.listLoading = true;
        // this.equipmentList = []; // Clear only after loading starts

        const filterParams: string[] = [];
        if (this.filters.name) {
            filterParams.push(`title||$contL||${this.filters.name}`);
        }
        if (this.filters.enabled !== null && this.filters.enabled !== undefined && this.filters.enabled !== '') {
            filterParams.push(`enabled||$eq||${this.filters.enabled}`);
        }

        const requestData = {
            page: page,
            limit: this.itemsPerPage,
            sort: `${this.filters.sortField || 'createdTimestamp'},${this.filters.sortDirection || 'DESC'}`,
            filter: filterParams
        };

        try {
            const params = createAxiosConfig(requestData);
            // Ensure service method returns { data: Equipment[], total: number }
            const response = await this.equipmentService.getEquipment(params);
            this.equipmentList = response?.data ?? [];
            this.totalItems = response?.total ?? 0;
        } catch (error) {
            console.error("Error fetching equipment:", error);
            this.equipmentList = [];
            this.totalItems = 0;
            // this.toastr.error('Failed to load equipment.', 'Error');
        } finally {
            this.listLoading = false;
        }
    }

    /** Handles page changes. */
    onPageChange(page: number): void {
        if (page !== this.currentPage) {
            this.currentPage = page;
            this.loadEquipments(this.currentPage);
        }
    }

    /** Handles the toggle switch change event in the table row. */
    async onSwitchToggle(isEnabled: boolean, equipment: Equipment): Promise<void> {
        const originalState = equipment.enabled;
        equipment.enabled = isEnabled; // Optimistic update

        const updatePayload = {
            tableName: 'equipement-master', // Use correct table name
            id: equipment.id,
            data: {
                enabled: isEnabled
            }
        };
        console.log(`Attempting to update equipment ${equipment.id} enabled status to: ${isEnabled}`);

        try {
            await this.update(updatePayload);
            console.log(`Successfully updated equipment ${equipment.id} enabled status.`);
            // this.toastr.success('Equipment status updated.', 'Success');
        } catch (error) {
            console.error(`Error updating enabled status for equipment ${equipment.id}:`, error);
            // this.toastr.error('Failed to update equipment status.', 'Update Error');
            // Revert UI change on error
            equipment.enabled = originalState;
            // Force update detection if needed
            const index = this.equipmentList.findIndex(e => e.id === equipment.id);
            if (index !== -1) {
                 this.equipmentList[index] = {...this.equipmentList[index], enabled: originalState };
            }
        }
    }

    // Generic update via UpdateService (used by toggle and edit form)
    async update(data: { tableName: string, id: number, data: any }): Promise<void> {
        try {
            await this.updateService.update(data);
        } catch (error) {
            console.error("Update service call failed:", error);
            throw error; // Re-throw
        }
    }

    // --- Removed Placeholder/Unused Methods ---
    // Removed handleCreate, handleUpdate, toggleEnabled, handleDelete, sortBy, getSortClass
}