
import { Component, OnInit, ViewChild } from '@angular/core';
import { FormsModule, NgForm } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { BusinessUnitService } from '../../../services/business-unit/business-unit.service';
import { UpdateService } from '../../../services/update/update.service';
import { AuthService } from '../../../services/auth.service';
import { createAxiosConfig } from '../../../core/utilities/axios-param-config';
import { SwitchComponent } from "../../../shared/switch/switch.component";
import { PaginationComponent } from "../../../shared/pagination/pagination.component";
import { OffcanvasComponent } from "../../../shared/offcanvas/offcanvas.component";
import { ToastMessageComponent } from "../../../shared/toast-message/toast-message.component";
import { NgbDropdownModule } from '@ng-bootstrap/ng-bootstrap';
import * as XLSX from 'xlsx';
import { BusinessUnit } from '../../../model/business-unit.model';

interface BusinessUnitFilter {
    name?: string | null;
    enabled?: string | null;
    sortField?: string | null;
    sortDirection?: 'ASC' | 'DESC';
}

interface NewBusinessUnitData {
    title: string;
    enabled: boolean;
}

@Component({
    selector: 'app-business-unit',
    standalone: true,
    imports: [
        CommonModule,
        FormsModule,
        SwitchComponent,
        PaginationComponent,
        OffcanvasComponent,
        NgbDropdownModule,
        ToastMessageComponent
    ],
    templateUrl: './business-unit.component.html',
    styleUrls: ['./business-unit.component.scss']
})
export class BusinessUnitComponent implements OnInit {
    @ViewChild(ToastMessageComponent) toast!: ToastMessageComponent;
    @ViewChild('createForm') createForm?: NgForm;

    isFilterModalOpen = false;
    isEditModalOpen = false;
    isCreateModalOpen = false;

    businessUnitList: BusinessUnit[] = [];
    listLoading = false;
    editLoading = false;
    createLoading = false;

    currentPage = 1;
    itemsPerPage = 10;
    totalItems = 0;
    isDownloadingExcel = false;
    downloadType: 'current' | 'all' | null = null;

    filters: BusinessUnitFilter = {
        name: null,
        enabled: 'true',
        sortField: 'createdTimestamp',
        sortDirection: 'DESC'
    };
    availableSortFields = [
        { value: 'id', label: 'ID' },
        { value: 'title', label: 'Business Unit Name' },
        { value: 'enabled', label: 'Enabled Status' },
        { value: 'createdTimestamp', label: 'Created Date' }
    ];

    selectedBusinessUnit: BusinessUnit | null = null;

    newBusinessUnitData: NewBusinessUnitData = {
        title: '',
        enabled: true
    };

    constructor(
        readonly businessUnitService: BusinessUnitService,
        readonly updateService: UpdateService,
        public authService: AuthService,
    ) { }

    hasAccess: boolean = false;

    ngOnInit(): void {
        // Allow access for users with role ID 6 (Global Admin) or businessUnitId 1
        const userData = localStorage.getItem('user');
        if (userData) {
            try {
                const user = JSON.parse(userData);
                if (user?.adminsRoleId === 6) {
                    this.hasAccess = true;
                }
            } catch (error) {
                console.error('Error parsing user data from localStorage:', error);
            }
        }
        if (this.hasAccess) {
            this.loadBusinessUnits(this.currentPage);
        }
    }

    getCurrentListData(): BusinessUnit[] | undefined {
        return this.businessUnitList;
    }

    async fetchAllFilteredBusinessUnits(): Promise<BusinessUnit[] | null> {
        this.listLoading = true;
        const filterParams: string[] = [];
        if (this.filters.name) { filterParams.push(`title||$contL||${this.filters.name}`); }
        if (this.filters.enabled !== null && this.filters.enabled !== undefined && this.filters.enabled !== '') {
             filterParams.push(`enabled||$eq||${this.filters.enabled}`);
        }

        const data = {
            limit: 10000,
            sort: `${this.filters.sortField || 'createdTimestamp'},${this.filters.sortDirection || 'DESC'}`,
            filter: filterParams
        };

        try {
            const param = createAxiosConfig(data);
            const response = await this.businessUnitService.getBusinessUnits(param);
            return response ?? [];
        } catch (error: any) {
            console.error("Error fetching all business units for download:", error);
            this.toast?.showErrorToast(error?.response?.data?.message || "Failed to retrieve full data for download.");
            return null;
        } finally {
             this.listLoading = false;
        }
    }

    async downloadExcel(type: 'current' | 'all') {
        if (this.isDownloadingExcel || this.listLoading) return;

        this.isDownloadingExcel = true;
        this.downloadType = type;
        this.toast?.showSuccessToast(`Preparing download for ${type === 'current' ? 'current page' : 'all filtered'} business units...`);

        let dataToExport: BusinessUnit[] | null = null;

        try {
            if (type === 'all') {
                dataToExport = await this.fetchAllFilteredBusinessUnits();
            } else {
                dataToExport = this.getCurrentListData() ?? null;
                if (dataToExport === undefined) { dataToExport = null; }
            }

            if (dataToExport === null) { this.toast?.showErrorToast("Failed to retrieve data for download."); return; }
            if (!dataToExport || dataToExport.length === 0) { this.toast?.showErrorToast(`No business units available to download.`); return; }

            const dataForExcel = dataToExport.map(bu => ({
                'Business Unit ID': bu.id,
                'Business Unit Name': bu.title,
                'Status': bu.enabled ? 'Active' : 'Inactive',
                'Created At': bu.createdTimestamp ? new Date(bu.createdTimestamp).toLocaleString() : 'N/A',
                'Updated At': bu.updatedTimestamp ? new Date(bu.updatedTimestamp).toLocaleString() : 'N/A',
            }));

            const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(dataForExcel);
            const wb: XLSX.WorkBook = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(wb, ws, 'BusinessUnits');

            const dateStr = new Date().toISOString().slice(0, 10);
            const typeStr = type === 'current' ? `Page${this.currentPage}` : 'All';
            const fileName = `BusinessUnits_${typeStr}_${dateStr}.xlsx`;

            XLSX.writeFile(wb, fileName);
            this.toast?.showSuccessToast(`Excel file download started (${type}).`);

        } catch (error) {
            console.error(`Error generating Excel file (${type}):`, error);
            this.toast?.showErrorToast(`An error occurred while generating the Excel file (${type}).`);
        } finally {
            this.isDownloadingExcel = false;
            this.downloadType = null;
        }
    }

    openFilterModal(): void { this.isFilterModalOpen = true; }
    closeFilterModal(): void { this.isFilterModalOpen = false; }

    @ViewChild('filterForm') filterForm!: NgForm;

    applyFilters(): void {
        if (this.filterForm && this.filterForm.invalid) {
            this.toast?.showErrorToast("Please correct the validation errors before applying filters.");
            return;
        }

        if (this.filters.name) {
            this.filters.name = this.filters.name.trim();
        }

        this.currentPage = 1;
        this.loadBusinessUnits(1);
        this.closeFilterModal();
    }

    resetFilters(): void {
        this.filters = {
            name: null,
            enabled: 'true',
            sortField: 'createdTimestamp',
            sortDirection: 'DESC'
        };
        this.currentPage = 1;
        this.loadBusinessUnits(1);
    }

    openEditModal(businessUnit: BusinessUnit): void { this.selectedBusinessUnit = { ...businessUnit }; this.isEditModalOpen = true; this.editLoading = false; }
    closeEditModal(): void { this.isEditModalOpen = false; this.selectedBusinessUnit = null; }
    @ViewChild('editForm') editForm!: NgForm;

    async submitEditForm(): Promise<void> {
        if (!this.selectedBusinessUnit || this.selectedBusinessUnit.id == null) { return; }

        if (this.editForm && this.editForm.invalid) {
            this.toast?.showErrorToast("Please correct the validation errors before saving.");
            Object.values(this.editForm.controls).forEach(control => {
                control.markAsTouched();
            });
            return;
        }

        if (this.selectedBusinessUnit.title) {
            this.selectedBusinessUnit.title = this.selectedBusinessUnit.title.trim();
        }

        this.editLoading = true;
        const updatePayload = {
            tableName: 'business_unit',
            id: this.selectedBusinessUnit.id,
            data: {
                title: this.selectedBusinessUnit.title,
                enabled: this.selectedBusinessUnit.enabled,
            }
        };
        try {
            await this.update(updatePayload);
            this.toast?.showSuccessToast('Business unit updated successfully!');
            this.closeEditModal();
            this.loadBusinessUnits(this.currentPage);
        } catch (error) {
            console.error("Error submitting business unit update:", error);
            this.toast?.showErrorToast('Failed to update business unit.');
        } finally {
            this.editLoading = false;
        }
    }

    openCreateModal(): void {
        this.newBusinessUnitData = {
            title: '',
            enabled: true
        };
        this.createForm?.resetForm({ enabled: true });

        this.isCreateModalOpen = true;
        this.createLoading = false;
    }

    closeCreateModal(): void {
        this.isCreateModalOpen = false;
    }

    async submitCreateForm(): Promise<void> {
        if (this.createForm?.invalid) {
            this.toast?.showErrorToast("Please correct the validation errors before creating.");
            Object.values(this.createForm.controls).forEach(control => {
                 control.markAsTouched();
            });
            return;
        }

        if (this.newBusinessUnitData.title) {
            this.newBusinessUnitData.title = this.newBusinessUnitData.title.trim();
        }

        this.createLoading = true;

        try {
            const createdBu = await this.businessUnitService.createBusinessUnit(this.newBusinessUnitData);
            this.toast?.showSuccessToast(`Business unit "${createdBu.title}" created successfully!`);
            this.closeCreateModal();
            this.currentPage = 1;
            this.loadBusinessUnits(this.currentPage);

        } catch (error) {
            console.error("Error creating business unit:", error);
            this.toast?.showErrorToast('Failed to create business unit. Please try again.');
        } finally {
            this.createLoading = false;
        }
    }

    async loadBusinessUnits(page: number): Promise<void> {
         this.listLoading = true;
         this.businessUnitList = [];

        const filterParams: string[] = [];
        if (this.filters.name) { filterParams.push(`title||$contL||${this.filters.name}`); }
        if (this.filters.enabled !== null && this.filters.enabled !== undefined && this.filters.enabled !== '') {
            filterParams.push(`enabled||$eq||${this.filters.enabled}`);
        }

        const requestData = {
            page: page,
            limit: this.itemsPerPage,
            sort: `${this.filters.sortField || 'createdTimestamp'},${this.filters.sortDirection || 'DESC'}`,
            filter: filterParams
        };

        try {
            const params = createAxiosConfig(requestData);
            const response = await this.businessUnitService.getBusinessUnits(params);
            this.businessUnitList = response?.data ?? [];
            this.totalItems = response?.total ?? 0;
        } catch (error) {
            console.error("Error fetching business units:", error);
            this.businessUnitList = [];
            this.totalItems = 0;
        } finally {
            this.listLoading = false;
        }
     }

    onPageChange(page: number): void { if (page !== this.currentPage) { this.currentPage = page; this.loadBusinessUnits(this.currentPage); } }

    async onSwitchToggle(isEnabled: boolean, id: number): Promise<void> {
        const buIndex = this.businessUnitList.findIndex(d => d.id === id);
        const updatePayload = { tableName: 'business_unit', id: id, data: { enabled: isEnabled } };
        try {
            await this.update(updatePayload);
             if (buIndex !== -1) { this.businessUnitList[buIndex].enabled = isEnabled; }
        } catch (error) {
            console.error(`Error updating enabled status for business unit ${id}:`, error);
             if (buIndex !== -1) { this.businessUnitList[buIndex].enabled = !isEnabled; }
        }
     }

    async update(data: { tableName: string, id: number, data: any }): Promise<void> {
        try {
            await this.updateService.update(data);
        } catch (error) {
            console.error("Update service call failed:", error);
            throw error;
        }
    }
}
