.table-container {
    width: 100%;
    overflow-x: auto;
}

.table-responsive {
    overflow-x: auto;
    white-space: nowrap;
    max-width: 100%;
}

table {
    min-width: 1000px;
    /* Adjust based on the number of columns */
    border-collapse: collapse;
}

.table-header th {
    font-size: 12px;
    background-color: #0B74B0 !important;
    color: white !important;
    text-align: center;
}

td {
    font-size: 12px;
    text-align: center;
}

i.edit {
    font-size: 12px;
}

.actions {
    text-align: center !important;
    vertical-align: middle !important;
}

.table-container {
    overflow-x: auto;
}

.unauthorized-message {
    padding: 20px;
    text-align: center;
    font-size: 1.2em;
    color: #dc3545;
}
