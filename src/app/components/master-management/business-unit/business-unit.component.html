
<div *ngIf="hasAccess">
    <app-toast-message></app-toast-message>
    <div class="card" id="activated-qrcode">
        <div class="card-header d-flex align-items-center justify-content-between">
          <div>
            <h6 class="mb-0">Business Units</h6>
          </div>
          <div class="d-flex align-items-center">
            <div ngbDropdown class="d-inline-block me-2">
                <button type="button" class="btn btn-sm adani-btn dropdown-toggle" id="downloadBuExcelDropdown" ngbDropdownToggle
                    [disabled]="isDownloadingExcel || listLoading">
                    <span *ngIf="!isDownloadingExcel">
                        <i class="bi bi-file-earmark-excel me-1"></i> Download Excel
                    </span>
                    <span *ngIf="isDownloadingExcel">
                        <span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
                        Downloading {{ downloadType === 'current' ? 'Page' : (downloadType === 'all' ? 'All' : '') }}...
                    </span>
                </button>
                <ul ngbDropdownMenu aria-labelledby="downloadBuExcelDropdown">
                    <li>
                        <button ngbDropdownItem (click)="downloadExcel('current')" [disabled]="isDownloadingExcel || listLoading || (businessUnitList?.length ?? 0) === 0">
                            <i class="bi bi-download me-1"></i> Download Current Page ({{ businessUnitList?.length ?? 0 }})
                        </button>
                    </li>
                    <li>
                        <button ngbDropdownItem (click)="downloadExcel('all')" [disabled]="isDownloadingExcel || listLoading || totalItems === 0">
                            <i class="bi bi-cloud-download me-1"></i> Download All Filtered ({{ totalItems }})
                        </button>
                    </li>
                </ul>
            </div>
              <button class="btn-sm adani-btn ms-2" (click)="openCreateModal()" title="Create New Business Unit">
                <i class="bi bi-plus-circle"></i> Create New
            </button>
              <img src="../../../assets/svg/filter.svg" class="filter-button ms-3" (click)="openFilterModal()" alt=""
                style="width: 35px;" />
            </div>
              </div>
          <div class="card-body">
            <div class="table-container">
                <div class="table-responsive">
                    <table class="table table-bordered table-hover">
                        <thead class="table-header">
                          <tr class="text-center">
                            <th scope="col">Id</th>
                            <th scope="col">Enabled/Disabled</th>
                            <th scope="col">Business Unit Name</th>
                            <th scope="col">Actions</th>
                          </tr>
                        </thead>
                        <tbody>
                          <tr *ngFor="let bu of businessUnitList">
                            <td>{{ bu.id }}</td>
                            <td>
                                <app-switch [(checked)]="bu.enabled" [requireConfirmation]="true" (checkedChange)="onSwitchToggle($event, bu.id)" onLabel="Active" offLabel="Inactive"></app-switch>
                            </td>
                            <td>{{ bu.title }}</td>
                            <td class="actions">
                              <button class="adani-btn" (click)="openEditModal(bu)">
                                <i class="bi bi-pencil edit"></i>
                            </button>
                            </td>
                          </tr>
                        </tbody>
                      </table>
                </div>
            </div>


          </div>
          <div class="card-footer text-muted text-center">
            <app-pagination [currentPage]="currentPage" [totalItems]="totalItems" [itemsPerPage]="itemsPerPage"
            (pageChange)="onPageChange($event)"></app-pagination>
          </div>
      </div>
      <app-offcanvas [title]="'Filter Business Units'" *ngIf="isFilterModalOpen" (onClickCross)="closeFilterModal()">
        <div class="filter-container p-3">
            <form #filterForm="ngForm" (ngSubmit)="applyFilters()">
                <div class="row g-3">

                    <div class="col-12">
                        <label class="form-label" for="filterBuName">Business Unit Name</label>
                        <input type="text" id="filterBuName" class="form-control" placeholder="Search by Business Unit Name"
                               [(ngModel)]="filters.name" name="name" #filterName="ngModel"
                               pattern="^[a-zA-Z\s]*$" maxlength="30"
                               [ngClass]="{'is-invalid': filterName.invalid && (filterName.dirty || filterName.touched)}">
                        <div *ngIf="filterName.invalid && (filterName.dirty || filterName.touched)" class="invalid-feedback">
                            <div *ngIf="filterName.errors?.['pattern']">Business Unit name should contain only alphabets.</div>
                        </div>
                    </div>

                     <div class="col-12">
                        <label class="form-label" for="filterEnabledBu">Enabled Status</label>
                        <select id="filterEnabledBu" class="form-select"
                                [(ngModel)]="filters.enabled" name="enabled">
                            <option [ngValue]="null">Any</option>
                            <option value="true">Yes</option>
                            <option value="false">No</option>
                        </select>
                    </div>

                    <div class="col-12">
                        <label class="form-label" for="filterSortByBu">Sort By</label>
                        <select id="filterSortByBu" class="form-select"
                                [(ngModel)]="filters.sortField" name="sortField">
                            
                            <option *ngFor="let field of availableSortFields" [value]="field.value">{{ field.label }}</option>
                        </select>
                         <label class="form-label mt-2" for="filterSortDirBu">Sort Direction</label>
                         <select id="filterSortDirBu" class="form-select"
                                [(ngModel)]="filters.sortDirection" name="sortDirection">
                            <option value="ASC">Ascending</option>
                            <option value="DESC">Descending</option>
                        </select>
                    </div>

                    <div class="col-12 mt-4 d-grid gap-2">
                        <button type="submit" class="btn adani-btn" [disabled]="filterForm.invalid">
                            <i class="bi bi-search me-1"></i> Search
                        </button>
                         <button type="button" class="btn btn-secondary" (click)="resetFilters()">
                            <i class="bi bi-arrow-clockwise me-1"></i> Reset Filters
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </app-offcanvas>
    <app-offcanvas [title]="'Edit Business Unit'" *ngIf="isEditModalOpen" (onClickCross)="closeEditModal()">
      <div class="edit-container p-3">
          <form *ngIf="selectedBusinessUnit" #editForm="ngForm" (ngSubmit)="submitEditForm()">
              <div class="row g-3">

                  <div class="col-12">
                      <label class="form-label" for="editBuId">Business Unit ID</label>
                      <input type="text" id="editBuId" class="form-control"
                             [value]="selectedBusinessUnit.id" name="id" readonly disabled>
                  </div>

                  <div class="col-12">
                      <label class="form-label" for="editBuName">Business Unit Name</label>
                      <input type="text" id="editBuName" class="form-control" placeholder="Enter Business Unit Name"
                             [(ngModel)]="selectedBusinessUnit.title" name="title" required #titleInput="ngModel"
                             pattern="^[a-zA-Z\s]*$" maxlength="30"
                             [ngClass]="{'is-invalid': titleInput.invalid && (titleInput.dirty || titleInput.touched)}">
                       <div *ngIf="titleInput.invalid && (titleInput.dirty || titleInput.touched)" class="invalid-feedback">
                          <div *ngIf="titleInput.errors?.['required']">Business Unit name is required.</div>
                          <div *ngIf="titleInput.errors?.['pattern']">Business Unit name should contain only alphabets.</div>
                          <div *ngIf="titleInput.errors?.['maxlength']">Business Unit name cannot exceed 30 characters.</div>
                      </div>
                      <small *ngIf="selectedBusinessUnit?.title" class="text-muted">
                          {{ selectedBusinessUnit.title.length }}/30 characters
                      </small>
                  </div>

                   <div class="col-12">
                       <label class="form-label d-block mb-2">Status</label>
                        <app-switch
                              [(checked)]="selectedBusinessUnit.enabled"
                              name="enabled"
                              onLabel="Active"
                              offLabel="Inactive">
                        </app-switch>
                   </div>


                  <div class="col-12 mt-4 d-flex justify-content-end gap-2">
                      <button type="button" class="btn btn-secondary" (click)="closeEditModal()">
                          Cancel
                      </button>
                      <button type="submit" class="btn adani-btn" [disabled]="editForm.invalid || editLoading">
                           <span *ngIf="!editLoading"><i class="bi bi-save me-1"></i> Save Changes</span>
                           <span *ngIf="editLoading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                           <span *ngIf="editLoading"> Saving...</span>
                      </button>
                  </div>
              </div>
          </form>
          <div *ngIf="!selectedBusinessUnit && isEditModalOpen" class="text-center p-5">
               <div class="spinner-border spinner-border-sm" role="status">
                   <span class="visually-hidden">Loading form...</span>
               </div>
          </div>
      </div>
    </app-offcanvas>
    <app-offcanvas [title]="'Create New Business Unit'" *ngIf="isCreateModalOpen" (onClickCross)="closeCreateModal()">
      <div class="create-container p-3">
          <form #createForm="ngForm" (ngSubmit)="submitCreateForm()">
              <div class="row g-3">

                  <div class="col-12">
                      <label class="form-label" for="createBuName">Business Unit Name</label>
                      <input type="text" id="createBuName" class="form-control" placeholder="Enter New Business Unit Name"
                             [(ngModel)]="newBusinessUnitData.title" name="title" required #createTitleInput="ngModel"
                             pattern="^[a-zA-Z\s]*$" maxlength="30"
                             [ngClass]="{'is-invalid': createTitleInput.invalid && (createTitleInput.dirty || createTitleInput.touched)}">
                      <div *ngIf="createTitleInput.invalid && (createTitleInput.dirty || createTitleInput.touched)" class="invalid-feedback">
                          <div *ngIf="createTitleInput.errors?.['required']">Business Unit name is required.</div>
                          <div *ngIf="createTitleInput.errors?.['pattern']">Business Unit name should contain only alphabets.</div>
                          <div *ngIf="createTitleInput.errors?.['maxlength']">Business Unit name cannot exceed 30 characters.</div>
                      </div>
                      <small *ngIf="newBusinessUnitData.title" class="text-muted">
                          {{ newBusinessUnitData.title.length }}/30 characters
                      </small>
                  </div>

                  <div class="col-12">
                      <label class="form-label d-block mb-2">Status</label>
                      <app-switch
                            [(checked)]="newBusinessUnitData.enabled"
                            name="enabled"
                            onLabel="Active"
                            offLabel="Inactive">
                      </app-switch>
                  </div>

                  <div class="col-12 mt-4 d-flex justify-content-end gap-2">
                      <button type="button" class="btn btn-secondary" (click)="closeCreateModal()">
                          Cancel
                      </button>
                      <button type="submit" class="btn adani-btn" [disabled]="createForm.invalid || createLoading">
                          <span *ngIf="!createLoading"><i class="bi bi-plus-circle-fill me-1"></i> Create Business Unit</span>
                          <span *ngIf="createLoading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                          <span *ngIf="createLoading"> Creating...</span>
                      </button>
                  </div>
              </div>
          </form>
      </div>
    </app-offcanvas>
</div>
<div *ngIf="!hasAccess" class="unauthorized-message">
    You are not authorized to view this page.
</div>
