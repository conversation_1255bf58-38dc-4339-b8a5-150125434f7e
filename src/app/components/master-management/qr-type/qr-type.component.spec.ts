import { ComponentFixture, TestBed } from '@angular/core/testing';

import { QrTypeComponent } from './qr-type.component';

describe('QrTypeComponent', () => {
  let component: QrTypeComponent;
  let fixture: ComponentFixture<QrTypeComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [QrTypeComponent]
    })
    .compileComponents();

    fixture = TestBed.createComponent(QrTypeComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
