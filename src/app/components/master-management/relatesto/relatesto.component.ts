import { Component, OnInit, ViewChild } from '@angular/core'; // Added ViewChild
import { FormsModule, NgForm } from '@angular/forms'; // Added NgForm
import { RelatesToService } from '../../../services/master-management/relates-to/relates-to.service';
import { createAxiosConfig } from '../../../core/utilities/axios-param-config';
import { CommonModule } from '@angular/common';
import { SwitchComponent } from "../../../shared/switch/switch.component";
import { PaginationComponent } from "../../../shared/pagination/pagination.component";
import { OffcanvasComponent } from "../../../shared/offcanvas/offcanvas.component";
import { UpdateService } from '../../../services/update/update.service';
// Optional: Import ToastrService or similar
// import { ToastrService } from 'ngx-toastr';
import { ToastMessageComponent } from '../../../shared/toast-message/toast-message.component'; // Adjust path if needed
import { NgbDropdownModule } from '@ng-bootstrap/ng-bootstrap'; // For dropdown button
import * as XLSX from 'xlsx'; // For Excel generation


// Interface for filter structure
interface RelatesToFilter {
    name?: string | null;
    enabled?: string | null;
    sortField?: string | null;
    sortDirection?: 'ASC' | 'DESC';
}

// *** NEW: Interface for RelatesTo Data ***
export interface RelatesTo {
    id: number;
    title: string;
    enabled: boolean;
    createdAt?: string; // Optional
    updatedAt?: string; // Optional
}

// *** NEW: Interface for Create Form Data ***
interface NewRelatesToData {
    title: string;
    enabled: boolean;
}


@Component({
    selector: 'app-relatesto',
    standalone: true,
    imports: [
        CommonModule,
        FormsModule,
        SwitchComponent,
        PaginationComponent,
        OffcanvasComponent,
        NgbDropdownModule,
        ToastMessageComponent
    ],
    templateUrl: './relatesto.component.html',
    styleUrl: './relatesto.component.scss'
})
export class RelatestoComponent implements OnInit {
    // Access the form template variables
    @ViewChild('createForm') createForm?: NgForm;
    @ViewChild('editForm') editForm?: NgForm;
    @ViewChild('filterForm') filterForm?: NgForm;
    @ViewChild(ToastMessageComponent) toast!: ToastMessageComponent;

    // --- Modal States ---
    isFilterModalOpen = false; // Renamed from isEditUserModalOpen
    isEditModalOpen = false;   // <-- New state for edit modal
    isCreateModalOpen = false; // <-- New state for create modal
    isDownloadingExcel = false;
    downloadType: 'current' | 'all' | null = null;

    // --- Data & Loading States ---
    relatestoList: RelatesTo[] = []; // Use RelatesTo interface
    listLoading = false;
    editLoading = false;     // <-- New state for edit form submission
    createLoading = false;   // <-- New state for create form submission

    // --- Pagination ---
    currentPage = 1;
    itemsPerPage = 10;
    totalItems = 0;

    // --- Filtering ---
    filters: RelatesToFilter = {
        name: null,
        enabled: 'true',
        sortField: 'createdTimestamp',
        sortDirection: 'DESC'
    };
    availableSortFields = [
        { value: 'id', label: 'ID' },
        { value: 'title', label: 'Relates To Name' },
        { value: 'enabled', label: 'Enabled Status' },
        { value: 'createdTimestamp', label: 'Created Date' }
    ];

    // --- Editing ---
    selectedRelatesTo: RelatesTo | null = null; // <-- Object to hold data for editing

    // --- Creating ---
    newRelatesToData: NewRelatesToData = { // <-- Object to hold new relates-to form data
        title: '',
        enabled: true // Default new items to active
    };

    constructor(
        readonly relatesToService: RelatesToService,
        readonly updateService: UpdateService,
        // Optional: Inject ToastrService
        // private toastr: ToastrService
    ) { }

    ngOnInit(): void {
        this.loadRelatesTo(this.currentPage);
    }

    // Helper to get current list data
    getCurrentListData(): RelatesTo[] | undefined {
        return this.relatestoList;
    }

    // Fetch ALL relates-to items matching current filters (no pagination)
    async fetchAllFilteredRelatesTo(): Promise<RelatesTo[] | null> {
        this.listLoading = true; // Indicate loading
        const filterParams: string[] = [];
        if (this.filters.name) { filterParams.push(`title||$contL||${this.filters.name}`); }
        if (this.filters.enabled !== null && this.filters.enabled !== undefined && this.filters.enabled !== '') {
             filterParams.push(`enabled||$eq||${this.filters.enabled}`);
        }

        const data = {
            limit: 10000, // Large limit for "all"
            sort: `${this.filters.sortField || 'createdTimestamp'},${this.filters.sortDirection || 'DESC'}`, // Match default sort
            filter: filterParams
        };

        try {
            const params = createAxiosConfig(data);
            console.log('API Request Params (RelatesTo Download - All Data):', JSON.stringify(params, null, 2));
            const response = await this.relatesToService.getRelatesTo(params); // Use existing service method
            return response ?? [];
        } catch (error: any) {
            console.error("Error fetching all relates-to items for download:", error);
            this.toast?.showErrorToast(error?.response?.data?.message || "Failed to retrieve full data for download.");
            return null;
        } finally {
             this.listLoading = false; // Reset loading indicator
        }
    }

    // Main download function
    async downloadExcel(type: 'current' | 'all') {
        if (this.isDownloadingExcel || this.listLoading) return; // Prevent concurrent actions

        this.isDownloadingExcel = true;
        this.downloadType = type;
        this.toast?.showSuccessToast(`Preparing download for ${type === 'current' ? 'current page' : 'all filtered'} relates-to items...`);

        let dataToExport: RelatesTo[] | null = null;

        try {
            // 1. Get Data
            if (type === 'all') {
                dataToExport = await this.fetchAllFilteredRelatesTo();
            } else { // 'current'
                dataToExport = this.getCurrentListData() ?? null;
                if (dataToExport === undefined) { dataToExport = null; }
            }

            // 2. Check data
            if (dataToExport === null) { this.toast?.showErrorToast("Failed to retrieve data for download."); return; }
            if (!dataToExport || dataToExport.length === 0) { this.toast?.showErrorToast(`No relates-to items available to download.`); return; }

            console.log(`Fetched ${dataToExport.length} relates-to items for Excel export (${type}).`);

            // 3. Transform data
            const dataForExcel = dataToExport.map(item => ({
                'RelatesTo ID': item.id,
                'RelatesTo Name': item.title,
                'Status': item.enabled ? 'Active' : 'Inactive',
                'Created At': item.createdAt ? new Date(item.createdAt).toLocaleString() : 'N/A',
                'Updated At': item.updatedAt ? new Date(item.updatedAt).toLocaleString() : 'N/A',
            }));

            // 4. Create Worksheet and Workbook
            const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(dataForExcel);
            const wb: XLSX.WorkBook = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(wb, ws, 'RelatesTo'); // Sheet name

            // 5. Generate File Name
            const dateStr = new Date().toISOString().slice(0, 10);
            const typeStr = type === 'current' ? `Page${this.currentPage}` : 'All';
            const fileName = `RelatesToItems_${typeStr}_${dateStr}.xlsx`;

            // 6. Trigger Download
            XLSX.writeFile(wb, fileName);
            this.toast?.showSuccessToast(`Excel file download started (${type}).`);

        } catch (error) {
            console.error(`Error generating Excel file (${type}):`, error);
            this.toast?.showErrorToast(`An error occurred while generating the Excel file (${type}).`);
        } finally {
            this.isDownloadingExcel = false;
            this.downloadType = null;
        }
    }

    // --- Filter Modal Methods ---
    openFilterModal(): void { this.isFilterModalOpen = true; }
    closeFilterModal(): void { this.isFilterModalOpen = false; } // Renamed from closeModal
    applyFilters(): void {
        // Check if filter form is valid
        if (this.filterForm?.invalid) {
            Object.values(this.filterForm.controls).forEach(control => control.markAsTouched());
            this.toast?.showErrorToast("Please correct the validation errors in the filter form.");
            return;
        }

        // Trim the name filter to remove any leading/trailing whitespace
        if (this.filters.name) {
            this.filters.name = this.filters.name.trim();
        }

        this.currentPage = 1;
        this.loadRelatesTo(this.currentPage);
        this.closeFilterModal();
    }
    resetFilters(): void {
        this.filters = {
            name: null,
            enabled: 'true',
            sortField: 'createdTimestamp',
            sortDirection: 'DESC'
        };
        this.currentPage = 1;
        this.loadRelatesTo(this.currentPage);
        // Optionally close modal: this.closeFilterModal();
    }

    // --- *** NEW: Edit Modal Methods *** ---
    /** Opens the edit relates-to offcanvas modal. */
    openEditModal(relatesToItem: RelatesTo): void {
        this.selectedRelatesTo = { ...relatesToItem }; // Create a copy
        this.isEditModalOpen = true;
        this.editLoading = false;
    }

    /** Closes the edit relates-to offcanvas modal. */
    closeEditModal(): void {
        this.isEditModalOpen = false;
        this.selectedRelatesTo = null;
    }

    /** Handles the submission of the edit relates-to form. */
    async submitEditForm(): Promise<void> {
        if (!this.selectedRelatesTo || this.selectedRelatesTo.id == null) {
            console.error("Cannot save, selected relates-to item is null or has no ID.");
            // this.toastr.error('Cannot save, no relates-to item selected.', 'Error');
            return;
        }

        // Check form validity
        if (this.editForm?.invalid) {
            Object.values(this.editForm.controls).forEach(control => control.markAsTouched());
            this.toast?.showErrorToast("Please correct the validation errors before saving.");
            return;
        }

        // Trim the title to remove any leading/trailing whitespace
        if (this.selectedRelatesTo.title) {
            this.selectedRelatesTo.title = this.selectedRelatesTo.title.trim();
        }

        this.editLoading = true;
        const updatePayload = {
            tableName: 'relatesto', // Critical: Ensure this matches your backend/service expectation
            id: this.selectedRelatesTo.id,
            data: {
                title: this.selectedRelatesTo.title,
                enabled: this.selectedRelatesTo.enabled
                // Add other editable fields if necessary
            }
        };

        try {
            await this.update(updatePayload); // Use the generic update method
            // this.toastr.success('Relates-to item updated successfully!', 'Saved');

            // Update list locally (optional)
            const index = this.relatestoList.findIndex(r => r.id === this.selectedRelatesTo?.id);
            if (index !== -1 && this.selectedRelatesTo) {
                 this.relatestoList[index] = { ...this.selectedRelatesTo };
            }

            this.closeEditModal();
            // Consider reloading if necessary: this.loadRelatesTo(this.currentPage);

        } catch (error) {
            console.error("Error submitting relates-to update:", error);
            // this.toastr.error('Failed to update relates-to item.', 'Error');
        } finally {
            this.editLoading = false;
        }
    }


    // --- *** NEW: Create Modal Methods *** ---

    /** Opens the create relates-to offcanvas modal and resets the form data. */
    openCreateModal(): void {
        this.newRelatesToData = {
            title: '',
            enabled: true
        };
        this.createForm?.resetForm({ enabled: true }); // Reset form state with default
        this.isCreateModalOpen = true;
        this.createLoading = false;
    }

    /** Closes the create relates-to offcanvas modal. */
    closeCreateModal(): void {
        this.isCreateModalOpen = false;
    }

    /** Handles the submission of the create relates-to form. */
    async submitCreateForm(): Promise<void> {
        if (this.createForm?.invalid) {
             Object.values(this.createForm.controls).forEach(control => control.markAsTouched());
             this.toast?.showErrorToast("Please correct the validation errors before creating.");
             console.warn('Create form submitted while invalid.');
             return;
        }

        // Trim the title to remove any leading/trailing whitespace
        if (this.newRelatesToData.title) {
            this.newRelatesToData.title = this.newRelatesToData.title.trim();
        }

        this.createLoading = true;
        console.log('Submitting new relates-to item:', this.newRelatesToData);

        try {
            // *** ASSUMPTION: relatesToService has a createRelatesTo method ***
            const createdRelatesTo = await this.relatesToService.createRelatesTo(this.newRelatesToData);
            console.log('Relates-to item created successfully:', createdRelatesTo);

            // this.toastr.success(`Relates-to item "${createdRelatesTo.title}" created successfully!`, 'Created');

            this.closeCreateModal();
            // Reload list, go to page 1
            this.currentPage = 1;
            this.loadRelatesTo(this.currentPage);

        } catch (error) {
            console.error("Error creating relates-to item:", error);
            // this.toastr.error('Failed to create relates-to item. Please try again.', 'Creation Error');
        } finally {
            this.createLoading = false;
        }
    }


    // --- Data Loading & Update Methods ---

    /** Fetches relates-to items from the backend. */
    async loadRelatesTo(page: number): Promise<void> {
        this.listLoading = true;
        // this.relatestoList = []; // Clear only after loading starts

        const filterParams: string[] = [];
        if (this.filters.name) {
            filterParams.push(`title||$contL||${this.filters.name}`);
        }
        if (this.filters.enabled !== null && this.filters.enabled !== undefined && this.filters.enabled !== '') {
            filterParams.push(`enabled||$eq||${this.filters.enabled}`);
        }

        const requestData = {
            page: page,
            limit: this.itemsPerPage,
            sort: `${this.filters.sortField || 'createdTimestamp'},${this.filters.sortDirection || 'DESC'}`,
            filter: filterParams
        };

        try {
            const param = createAxiosConfig(requestData);
            // Ensure service method returns { data: RelatesTo[], total: number }
            const response = await this.relatesToService.getRelatesTo(param);
            this.relatestoList = response?.data ?? [];
            this.totalItems = response?.total ?? 0;
        } catch (error) {
            console.error("Error fetching relates-to data:", error);
            this.relatestoList = [];
            this.totalItems = 0;
            // this.toastr.error('Failed to load relates-to items.', 'Error');
        } finally {
            this.listLoading = false;
        }
    }

    /** Handles page changes. */
    onPageChange(page: number): void {
        if (page !== this.currentPage) {
            this.currentPage = page;
            this.loadRelatesTo(this.currentPage);
        }
    }

    /** Handles the toggle switch change event in the table row. */
    async onSwitchToggle(isEnabled: boolean, relatesToItem: RelatesTo): Promise<void> {
        const originalState = relatesToItem.enabled;
        relatesToItem.enabled = isEnabled; // Optimistic update

        const updatePayload = {
            tableName: 'relatesto', // Use correct table name
            id: relatesToItem.id,
            data: {
                enabled: isEnabled
            }
        };
        console.log(`Attempting to update relates-to ${relatesToItem.id} enabled status to: ${isEnabled}`);

        try {
            await this.update(updatePayload);
            console.log(`Successfully updated relates-to ${relatesToItem.id} enabled status.`);
            // this.toastr.success('Relates-to status updated.', 'Success');
        } catch (error) {
            console.error(`Error updating enabled status for relates-to ${relatesToItem.id}:`, error);
            // this.toastr.error('Failed to update relates-to status.', 'Update Error');
            // Revert UI change on error
            relatesToItem.enabled = originalState;
            // Force update detection if needed
            const index = this.relatestoList.findIndex(r => r.id === relatesToItem.id);
            if (index !== -1) {
                 this.relatestoList[index] = {...this.relatestoList[index], enabled: originalState };
            }
        }
    }

    // Generic update via UpdateService (used by toggle and edit form)
    async update(data: { tableName: string, id: number, data: any }): Promise<void> {
        try {
            await this.updateService.update(data);
        } catch (error) {
            console.error("Update service call failed:", error);
            throw error; // Re-throw
        }
    }

    // --- Removed Placeholder/Unused Methods ---
    // Removed handleCreate, handleUpdate, toggleEnabled, handleDelete, sortBy, getSortClass
}