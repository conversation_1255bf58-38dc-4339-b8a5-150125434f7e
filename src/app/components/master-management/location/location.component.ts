import { Component, OnInit, ViewChild } from '@angular/core'; // Added ViewChild
import { FormsModule, NgForm } from '@angular/forms'; // Added NgForm
import { LocationService } from '../../../services/master-management/location/location.service';
import { createAxiosConfig } from '../../../core/utilities/axios-param-config';
import { CommonModule } from '@angular/common';
import { SwitchComponent } from "../../../shared/switch/switch.component";
import { PaginationComponent } from "../../../shared/pagination/pagination.component";
import { OffcanvasComponent } from "../../../shared/offcanvas/offcanvas.component";
import { UpdateService } from '../../../services/update/update.service';
// Optional: Import ToastrService or similar
// import { ToastrService } from 'ngx-toastr';
import { ToastMessageComponent } from '../../../shared/toast-message/toast-message.component'; // Adjust path if needed
import { NgbDropdownModule } from '@ng-bootstrap/ng-bootstrap'; // For dropdown button
import * as XLSX from 'xlsx'; // For Excel generation


// Interface for filter structure
interface LocationFilter {
    name?: string | null;
    enabled?: string | null;
    sortField?: string | null;
    sortDirection?: 'ASC' | 'DESC';
}

// *** NEW: Interface for Location Data ***
export interface Location {
    id: number;
    title: string;
    enabled: boolean;
    createdAt?: string; // Optional
    updatedAt?: string; // Optional
}

// *** NEW: Interface for Create Form Data ***
interface NewLocationData {
    title: string;
    enabled: boolean;
}


@Component({
    selector: 'app-location',
    standalone: true,
    imports: [
        CommonModule,
        FormsModule,
        SwitchComponent,
        PaginationComponent,
        OffcanvasComponent,
        NgbDropdownModule,
        ToastMessageComponent
    ],
    templateUrl: './location.component.html',
    styleUrl: './location.component.scss'
})
export class LocationComponent implements OnInit {
    // Access the form template variables
    @ViewChild('createForm') createForm?: NgForm;
    @ViewChild('filterForm') filterForm?: NgForm;
    @ViewChild(ToastMessageComponent) toast!: ToastMessageComponent;

    // --- Modal States ---
    isFilterModalOpen = false; // Renamed from isEditUserModalOpen
    isEditModalOpen = false;   // <-- New state for edit modal
    isCreateModalOpen = false; // <-- New state for create modal

    // --- Data & Loading States ---
    locationList: Location[] = []; // Use Location interface
    listLoading = false;
    editLoading = false;     // <-- New state for edit form submission
    createLoading = false;   // <-- New state for create form submission

    // --- Pagination ---
    currentPage = 1;
    itemsPerPage = 10;
    totalItems = 0;
    isDownloadingExcel = false;
    downloadType: 'current' | 'all' | null = null;

    // --- Filtering ---
    filters: LocationFilter = {
        name: null,
        enabled: 'true',
        sortField: 'createdTimestamp',
        sortDirection: 'DESC'
    };
    availableSortFields = [
        { value: 'id', label: 'ID' },
        { value: 'title', label: 'Location Name' },
        { value: 'enabled', label: 'Enabled Status' },
        { value: 'createdTimestamp', label: 'Created Date' }
    ];

    // --- Editing ---
    selectedLocation: Location | null = null; // <-- Object to hold data for editing

    // --- Creating ---
    newLocationData: NewLocationData = { // <-- Object to hold new location form data
        title: '',
        enabled: true // Default new locations to active
    };

    constructor(
        readonly locationService: LocationService,
        readonly updateService: UpdateService,
        // Optional: Inject ToastrService
        // private toastr: ToastrService
    ) { }

    ngOnInit(): void {
        this.loadLocations(this.currentPage);
    }

    // Helper to get current list data
    getCurrentListData(): Location[] | undefined {
        return this.locationList;
    }

    // Fetch ALL locations matching current filters (no pagination)
    async fetchAllFilteredLocations(): Promise<Location[] | null> {
        this.listLoading = true; // Indicate loading
        const filterParams: string[] = [];
        if (this.filters.name) { filterParams.push(`title||$contL||${this.filters.name}`); }
        if (this.filters.enabled !== null && this.filters.enabled !== undefined && this.filters.enabled !== '') {
             filterParams.push(`enabled||$eq||${this.filters.enabled}`);
        }

        const data = {
            limit: 10000, // Large limit for "all"
            sort: `${this.filters.sortField || 'createdTimestamp'},${this.filters.sortDirection || 'DESC'}`, // Match default sort
            filter: filterParams
        };

        try {
            const params = createAxiosConfig(data);
            console.log('API Request Params (Locations Download - All Data):', JSON.stringify(params, null, 2));
            const response = await this.locationService.getLocation(params); // Use existing service method
            return response ?? [];
        } catch (error: any) {
            console.error("Error fetching all locations for download:", error);
            this.toast?.showErrorToast(error?.response?.data?.message || "Failed to retrieve full data for download.");
            return null;
        } finally {
             this.listLoading = false; // Reset loading indicator
        }
    }

    // Main download function
    async downloadExcel(type: 'current' | 'all') {
        if (this.isDownloadingExcel || this.listLoading) return; // Prevent concurrent actions

        this.isDownloadingExcel = true;
        this.downloadType = type;
        this.toast?.showSuccessToast(`Preparing download for ${type === 'current' ? 'current page' : 'all filtered'} locations...`);

        let dataToExport: Location[] | null = null;

        try {
            // 1. Get Data
            if (type === 'all') {
                dataToExport = await this.fetchAllFilteredLocations();
            } else { // 'current'
                dataToExport = this.getCurrentListData() ?? null;
                if (dataToExport === undefined) { dataToExport = null; }
            }

            // 2. Check data
            if (dataToExport === null) { this.toast?.showErrorToast("Failed to retrieve data for download."); return; }
            if (!dataToExport || dataToExport.length === 0) { this.toast?.showErrorToast(`No locations available to download.`); return; }

            console.log(`Fetched ${dataToExport.length} locations for Excel export (${type}).`);

            // 3. Transform data
            const dataForExcel = dataToExport.map(loc => ({
                'Location ID': loc.id,
                'Location Name': loc.title,
                'Status': loc.enabled ? 'Active' : 'Inactive',
                'Created At': loc.createdAt ? new Date(loc.createdAt).toLocaleString() : 'N/A',
                'Updated At': loc.updatedAt ? new Date(loc.updatedAt).toLocaleString() : 'N/A',
            }));

            // 4. Create Worksheet and Workbook
            const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(dataForExcel);
            const wb: XLSX.WorkBook = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(wb, ws, 'Locations'); // Sheet name

            // 5. Generate File Name
            const dateStr = new Date().toISOString().slice(0, 10);
            const typeStr = type === 'current' ? `Page${this.currentPage}` : 'All';
            const fileName = `Locations_${typeStr}_${dateStr}.xlsx`;

            // 6. Trigger Download
            XLSX.writeFile(wb, fileName);
            this.toast?.showSuccessToast(`Excel file download started (${type}).`);

        } catch (error) {
            console.error(`Error generating Excel file (${type}):`, error);
            this.toast?.showErrorToast(`An error occurred while generating the Excel file (${type}).`);
        } finally {
            this.isDownloadingExcel = false;
            this.downloadType = null;
        }
    }

    // --- Filter Modal Methods ---
    openFilterModal(): void { this.isFilterModalOpen = true; }
    closeFilterModal(): void { this.isFilterModalOpen = false; } // Renamed from closeModal
    applyFilters(): void {
        if (this.filterForm?.invalid) {
            // Mark all controls as touched to show validation errors
            Object.values(this.filterForm.controls).forEach(control => control.markAsTouched());
            return;
        }
        this.currentPage = 1;
        this.loadLocations(this.currentPage);
        this.closeFilterModal();
    }
    resetFilters(): void {
        this.filters = {
            name: null,
            enabled: 'true',
            sortField: 'createdTimestamp',
            sortDirection: 'DESC'
        };
        this.currentPage = 1;
        this.loadLocations(this.currentPage);
        // Optionally close modal: this.closeFilterModal();
    }

    // --- *** NEW: Edit Modal Methods *** ---
    /** Opens the edit location offcanvas modal. */
    openEditModal(location: Location): void {
        this.selectedLocation = { ...location }; // Create a copy
        this.isEditModalOpen = true;
        this.editLoading = false;
    }

    /** Closes the edit location offcanvas modal. */
    closeEditModal(): void {
        this.isEditModalOpen = false;
        this.selectedLocation = null;
    }

    /** Handles the submission of the edit location form. */
    async submitEditForm(): Promise<void> {
        if (!this.selectedLocation || this.selectedLocation.id == null) {
            console.error("Cannot save, selected location is null or has no ID.");
            // this.toastr.error('Cannot save, no location selected.', 'Error');
            return;
        }

        this.editLoading = true;
        const updatePayload = {
            tableName: 'location', // Critical: Ensure this matches your backend/service expectation
            id: this.selectedLocation.id,
            data: {
                title: this.selectedLocation.title,
                enabled: this.selectedLocation.enabled
                // Add other editable fields if necessary
            }
        };

        try {
            await this.update(updatePayload); // Use the generic update method
            // this.toastr.success('Location updated successfully!', 'Saved');

            // Update list locally (optional)
            const index = this.locationList.findIndex(l => l.id === this.selectedLocation?.id);
            if (index !== -1 && this.selectedLocation) {
                 this.locationList[index] = { ...this.selectedLocation };
            }

            this.closeEditModal();
            // Consider reloading if necessary: this.loadLocations(this.currentPage);

        } catch (error) {
            console.error("Error submitting location update:", error);
            // this.toastr.error('Failed to update location.', 'Error');
        } finally {
            this.editLoading = false;
        }
    }


    // --- *** NEW: Create Modal Methods *** ---

    /** Opens the create location offcanvas modal and resets the form data. */
    openCreateModal(): void {
        this.newLocationData = {
            title: '',
            enabled: true
        };
        this.createForm?.resetForm({ enabled: true }); // Reset form state with default
        this.isCreateModalOpen = true;
        this.createLoading = false;
    }

    /** Closes the create location offcanvas modal. */
    closeCreateModal(): void {
        this.isCreateModalOpen = false;
    }

    /** Handles the submission of the create location form. */
    async submitCreateForm(): Promise<void> {
        if (this.createForm?.invalid) {
             Object.values(this.createForm.controls).forEach(control => control.markAsTouched());
             console.warn('Create form submitted while invalid.');
             return;
        }

        this.createLoading = true;
        console.log('Submitting new location:', this.newLocationData);

        try {
            // *** ASSUMPTION: locationService has a createLocation method ***
            const createdLoc = await this.locationService.createLocation(this.newLocationData);
            console.log('Location created successfully:', createdLoc);

            // this.toastr.success(`Location "${createdLoc.title}" created successfully!`, 'Created');

            this.closeCreateModal();
            // Reload list, go to page 1
            this.currentPage = 1;
            this.loadLocations(this.currentPage);

        } catch (error) {
            console.error("Error creating location:", error);
            // this.toastr.error('Failed to create location. Please try again.', 'Creation Error');
        } finally {
            this.createLoading = false;
        }
    }


    // --- Data Loading & Update Methods ---

    /** Fetches locations from the backend. */
    async loadLocations(page: number): Promise<void> {
        this.listLoading = true;
        // this.locationList = []; // Clear only after loading starts

        const filterParams: string[] = [];
        if (this.filters.name) {
            filterParams.push(`title||$contL||${this.filters.name}`);
        }
        if (this.filters.enabled !== null && this.filters.enabled !== undefined && this.filters.enabled !== '') {
            filterParams.push(`enabled||$eq||${this.filters.enabled}`);
        }

        const requestData = {
            page: page,
            limit: this.itemsPerPage,
            sort: `${this.filters.sortField || 'createdTimestamp'},${this.filters.sortDirection || 'DESC'}`,
            filter: filterParams
        };

        try {
            const param = createAxiosConfig(requestData);
            // Ensure service method returns { data: Location[], total: number }
            const response = await this.locationService.getLocation(param);
            this.locationList = response?.data ?? [];
            this.totalItems = response?.total ?? 0;
        } catch (error) {
            console.error("Error fetching locations:", error);
            this.locationList = [];
            this.totalItems = 0;
            // this.toastr.error('Failed to load locations.', 'Error');
        } finally {
            this.listLoading = false;
        }
    }

    /** Handles page changes. */
    onPageChange(page: number): void {
        if (page !== this.currentPage) {
            this.currentPage = page;
            this.loadLocations(this.currentPage);
        }
    }

    /** Handles the toggle switch change event in the table row. */
    async onSwitchToggle(isEnabled: boolean, location: Location): Promise<void> {
        const originalState = location.enabled;
        location.enabled = isEnabled; // Optimistic update

        const updatePayload = {
            tableName: 'location', // Use correct table name
            id: location.id,
            data: {
                enabled: isEnabled
            }
        };
        console.log(`Attempting to update location ${location.id} enabled status to: ${isEnabled}`);

        try {
            await this.update(updatePayload);
            console.log(`Successfully updated location ${location.id} enabled status.`);
            // this.toastr.success('Location status updated.', 'Success');
        } catch (error) {
            console.error(`Error updating enabled status for location ${location.id}:`, error);
            // this.toastr.error('Failed to update location status.', 'Update Error');
            // Revert UI change on error
            location.enabled = originalState;
            // Force update detection if needed
            const index = this.locationList.findIndex(l => l.id === location.id);
            if (index !== -1) {
                 this.locationList[index] = {...this.locationList[index], enabled: originalState };
            }
        }
    }

    // Generic update via UpdateService (used by toggle and edit form)
    async update(data: { tableName: string, id: number, data: any }): Promise<void> {
        try {
            await this.updateService.update(data);
        } catch (error) {
            console.error("Update service call failed:", error);
            throw error; // Re-throw
        }
    }

    // --- Removed Placeholder/Unused Methods ---
    // Removed handleCreate, handleUpdate, toggleEnabled, handleDelete, sortBy, getSortClass
}