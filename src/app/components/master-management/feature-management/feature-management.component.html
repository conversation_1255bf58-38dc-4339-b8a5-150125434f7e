<app-toast-message></app-toast-message>
<div class="card" id="feature-management">
  <div class="card-header d-flex align-items-center justify-content-between">
    <div>
      <h6 class="mb-0">Feature Management</h6>
    </div>
    <div class="d-flex align-items-center">
        <button class="btn-sm adani-btn ms-2" (click)="openCreateModal()" title="Create New Feature">
            <i class="bi bi-plus-circle"></i> Create New
        </button>
        <img src="../../../assets/svg/filter.svg" class="filter-button ms-3" (click)="openFilterModal()" alt="Filter"
          style="width: 35px;" />
    </div>
  </div>
  <div class="card-body">
    <div class="table-container">
      <div class="table-responsive">
        <table class="table table-bordered table-hover">
          <thead class="table-header">
            <tr class="text-center">
              <th scope="col">Id</th>
              <th scope="col">Feature Name</th>
              <th scope="col">Business Unit</th>
              <th scope="col">Admin Route</th>
              <th scope="col">App Route</th>
              <th scope="col">Enabled/Disabled</th>
              <th scope="col">Action</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngIf="listLoading">
              <td colspan="7" class="text-center">
                <div class="spinner-border spinner-border-sm" role="status">
                  <span class="visually-hidden">Loading...</span>
                </div>
                Loading Features...
              </td>
            </tr>
            <tr *ngIf="!listLoading && featureList.length === 0">
              <td colspan="7" class="text-center">No features found.</td>
            </tr>
            <tr *ngFor="let feature of featureList">
              <td class="text-center">{{ feature.id }}</td>
              <td class="text-center">{{ feature.featureMaster.title }}</td>
              <td class="text-center">{{ feature.businessUnit.title }}</td>
              <td class="text-center">{{ feature.adminRoute }}</td>
              <td class="text-center">{{ feature.appRoute }}</td>
              <td class="text-center">
                <app-switch
                  [(checked)]="feature.enabled"
                  [requireConfirmation]="true"
                  (checkedChange)="onSwitchToggle($event, feature, 'enabled')"
                  onLabel="Active" offLabel="Inactive">
                </app-switch>
              </td>
              <td class="text-center">
                <button class="btn btn-sm btn-primary" (click)="openEditModal(feature)" title="Edit Feature">
                  <i class="bi bi-pencil"></i>
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
  <div class="card-footer text-muted text-center">
    <app-pagination [currentPage]="currentPage" [totalItems]="totalItems" [itemsPerPage]="itemsPerPage"
      (pageChange)="onPageChange($event)"></app-pagination>
  </div>
</div>

<app-offcanvas [title]="'Filter Features'" *ngIf="isFilterModalOpen" (onClickCross)="closeFilterModal()">
  <div class="filter-container p-3">
    <form #filterForm="ngForm" (ngSubmit)="applyFilters()">
      <div class="row g-3">
        <div class="col-12">
          <label class="form-label" for="filterFeatureName">Feature Name</label>
          <input type="text" id="filterFeatureName" class="form-control" placeholder="Search by Feature Name"
            [(ngModel)]="filters.featureMasterTitle" name="featureMasterTitle" #filterFeatureMasterTitle="ngModel"
            pattern="^[a-zA-Z\s]*$" maxlength="30"
            [ngClass]="{'is-invalid': filterFeatureMasterTitle.invalid && (filterFeatureMasterTitle.dirty || filterFeatureMasterTitle.touched)}">
          <div *ngIf="filterFeatureMasterTitle.invalid && (filterFeatureMasterTitle.dirty || filterFeatureMasterTitle.touched)"
            class="invalid-feedback">
            <div *ngIf="filterFeatureMasterTitle.errors?.['pattern']">Feature name should contain only alphabets.</div>
          </div>
        </div>
        <div class="col-12">
          <label class="form-label" for="filterBusinessUnit">Business Unit</label>
          <select id="filterBusinessUnit" class="form-select" [(ngModel)]="filters.businessUnitId"
            name="businessUnitId">
            <option [ngValue]="null">All Business Units</option>
            <option *ngFor="let bu of businessUnits" [ngValue]="bu.id">{{ bu.title }}</option>
          </select>
        </div>
        <div class="col-12">
          <label class="form-label" for="filterEnabled">Enabled Status</label>
          <select id="filterEnabled" class="form-select" [(ngModel)]="filters.enabled" name="enabled">
            <option [ngValue]="null">Any</option>
            <option value="true">Active</option>
            <option value="false">Inactive</option>
          </select>
        </div>
        <div class="col-12">
          <label class="form-label" for="filterSortBy">Sort By</label>
          <select id="filterSortBy" class="form-select" [(ngModel)]="filters.sortField" name="sortField">
            <option *ngFor="let field of availableSortFields" [value]="field.value">{{ field.label }}</option>
          </select>
          <label class="form-label mt-2" for="filterSortDir">Sort Direction</label>
          <select id="filterSortDir" class="form-select" [(ngModel)]="filters.sortDirection" name="sortDirection">
            <option value="ASC">Ascending</option>
            <option value="DESC">Descending</option>
          </select>
        </div>
        <div class="col-12 mt-4 d-grid gap-2">
          <button type="submit" class="btn adani-btn" [disabled]="filterForm.invalid">
            <i class="bi bi-search me-1"></i> Search
          </button>
          <button type="button" class="btn btn-secondary" (click)="resetFilters()">
            <i class="bi bi-arrow-clockwise me-1"></i> Reset Filters
          </button>
        </div>
      </div>
    </form>
  </div>
</app-offcanvas>

<app-offcanvas [title]="'Create New Feature'" *ngIf="isCreateModalOpen" (onClickCross)="closeCreateModal()">
  <div class="create-container p-3">
    <form #createForm="ngForm" (ngSubmit)="submitCreateForm()">
      <div class="row g-3">
        <div class="col-12">
          <label class="form-label" for="createFeatureMasterId">Feature Master</label>
          <select id="createFeatureMasterId" class="form-select" [(ngModel)]="newFeatureData.featureMasterId"
            name="featureMasterId" required #createFeatureMasterIdInput="ngModel"
            [ngClass]="{'is-invalid': createFeatureMasterIdInput.invalid && (createFeatureMasterIdInput.dirty || createFeatureMasterIdInput.touched)}">
            <option [ngValue]="null" disabled>Select Feature Master</option>
            <option *ngFor="let fm of featureMasters" [ngValue]="fm.id">{{ fm.title }}</option>
          </select>
          <div
            *ngIf="createFeatureMasterIdInput.invalid && (createFeatureMasterIdInput.dirty || createFeatureMasterIdInput.touched)"
            class="invalid-feedback">
            <div *ngIf="createFeatureMasterIdInput.errors?.['required']">Feature Master is required.</div>
          </div>
        </div>
        <div class="col-12">
          <label class="form-label d-block mb-2">Enabled Status</label>
          <app-switch [(checked)]="newFeatureData.enabled" name="enabled" onLabel="Active" offLabel="Inactive">
          </app-switch>
        </div>
        <div class="col-12">
          <label class="form-label" for="adminRoute">Admin Route</label>
          <input type="text" id="adminRoute" class="form-control" [(ngModel)]="newFeatureData.adminRoute" name="adminRoute"
            #adminRouteInput="ngModel" pattern="^[a-zA-Z0-9\/]*$"
            [ngClass]="{'is-invalid': adminRouteInput.invalid && (adminRouteInput.dirty || adminRouteInput.touched)}">
          <div *ngIf="adminRouteInput.invalid && (adminRouteInput.dirty || adminRouteInput.touched)" class="invalid-feedback">
            <div *ngIf="adminRouteInput.errors?.['pattern']">Admin route should contain only alphanumeric characters and slashes.</div>
          </div>
        </div>
        <div class="col-12">
          <label class="form-label" for="appRoute">App Route</label>
          <input type="text" id="appRoute" class="form-control" [(ngModel)]="newFeatureData.appRoute" name="appRoute"
            #appRouteInput="ngModel" pattern="^[a-zA-Z0-9\/]*$"
            [ngClass]="{'is-invalid': appRouteInput.invalid && (appRouteInput.dirty || appRouteInput.touched)}">
          <div *ngIf="appRouteInput.invalid && (appRouteInput.dirty || appRouteInput.touched)" class="invalid-feedback">
            <div *ngIf="appRouteInput.errors?.['pattern']">App route should contain only alphanumeric characters and slashes.</div>
          </div>
        </div>
        <div class="col-12 mt-4 d-flex justify-content-end gap-2">
          <button type="button" class="btn btn-secondary" (click)="closeCreateModal()">
            Cancel
          </button>
          <button type="submit" class="btn adani-btn" [disabled]="createForm.invalid || createLoading">
            <span *ngIf="!createLoading"><i class="bi bi-plus-circle-fill me-1"></i> Create Feature</span>
            <span *ngIf="createLoading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
            <span *ngIf="createLoading"> Creating...</span>
          </button>
        </div>
      </div>
    </form>
  </div>
</app-offcanvas>

<app-offcanvas [title]="'Edit Feature'" *ngIf="isEditModalOpen" (onClickCross)="closeEditModal()">
  <div class="edit-container p-3">
    <form #editForm="ngForm" (ngSubmit)="submitEditForm()">
      <div class="row g-3">
        <div class="col-12">
          <label class="form-label" for="editFeatureMasterId">Feature Master</label>
          <input type="text" id="editFeatureMasterId" class="form-control"
            [ngModel]="editedFeatureData.featureMaster?.title" name="featureMasterTitle" [disabled]="true">
        </div>
        <div class="col-12">
          <label class="form-label d-block mb-2">Enabled Status</label>
          <app-switch [(checked)]="editedFeatureData.enabled" name="enabled" onLabel="Active" offLabel="Inactive">
          </app-switch>
        </div>
        <div class="col-12">
          <label class="form-label" for="editAdminRoute">Admin Route</label>
          <input type="text" id="editAdminRoute" class="form-control" [(ngModel)]="editedFeatureData.adminRoute" name="adminRoute"
            #editAdminRouteInput="ngModel" pattern="^[a-zA-Z0-9\/]*$"
            [ngClass]="{'is-invalid': editAdminRouteInput.invalid && (editAdminRouteInput.dirty || editAdminRouteInput.touched)}">
          <div *ngIf="editAdminRouteInput.invalid && (editAdminRouteInput.dirty || editAdminRouteInput.touched)" class="invalid-feedback">
            <div *ngIf="editAdminRouteInput.errors?.['pattern']">Admin route should contain only alphanumeric characters and slashes.</div>
          </div>
        </div>
        <div class="col-12">
          <label class="form-label" for="editAppRoute">App Route</label>
          <input type="text" id="editAppRoute" class="form-control" [(ngModel)]="editedFeatureData.appRoute" name="appRoute"
            #editAppRouteInput="ngModel" pattern="^[a-zA-Z0-9\/]*$"
            [ngClass]="{'is-invalid': editAppRouteInput.invalid && (editAppRouteInput.dirty || editAppRouteInput.touched)}">
          <div *ngIf="editAppRouteInput.invalid && (editAppRouteInput.dirty || editAppRouteInput.touched)" class="invalid-feedback">
            <div *ngIf="editAppRouteInput.errors?.['pattern']">App route should contain only alphanumeric characters and slashes.</div>
          </div>
        </div>
        <div class="col-12 mt-4 d-flex justify-content-end gap-2">
          <button type="button" class="btn btn-secondary" (click)="closeEditModal()">
            Cancel
          </button>
          <button type="submit" class="btn adani-btn" [disabled]="editForm.invalid || editLoading">
            <span *ngIf="!editLoading"><i class="bi bi-save me-1"></i> Update Feature</span>
            <span *ngIf="editLoading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
            <span *ngIf="editLoading"> Updating...</span>
          </button>
        </div>
      </div>
    </form>
  </div>
</app-offcanvas>