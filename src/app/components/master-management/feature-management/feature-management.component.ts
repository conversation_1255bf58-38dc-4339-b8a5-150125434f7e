
interface FeatureFilter {
  featureMasterTitle?: string | null;
  businessUnitId?: number | null;
  enabled?: string | null;
  sortField?: string | null;
  sortDirection?: 'ASC' | 'DESC';
}

import { Component, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, NgForm } from '@angular/forms';
import { FeatureManagementService } from '../../../services/master-management/feature-management/feature-management.service';
import { BusinessUnitService } from '../../../services/business-unit/business-unit.service';
import { Feature } from '../../../model/feature.model';
import { BusinessUnit } from '../../../model/business-unit.model';
import { createAxiosConfig } from '../../../core/utilities/axios-param-config';
import { SwitchComponent } from '../../../shared/switch/switch.component';
import { PaginationComponent } from '../../../shared/pagination/pagination.component';
import { OffcanvasComponent } from '../../../shared/offcanvas/offcanvas.component';
import { ToastMessageComponent } from '../../../shared/toast-message/toast-message.component';
import { UpdateService } from '../../../services/update/update.service';
import { AuthService } from '../../../services/auth.service';
import { FeatureMasterService } from '../../../services/master-management/feature-master/feature-master.service';

interface FeatureFilter {
  featureMasterTitle?: string | null;
  businessUnitId?: number | null;
  enabled?: string | null;
  sortField?: string | null;
  sortDirection?: 'ASC' | 'DESC';
}

interface NewFeatureData {
  featureMasterId: number | null;
  enabled: boolean;
  adminRoute: string | null;
  appRoute: string | null;
}

@Component({
  selector: 'app-feature-management',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    SwitchComponent,
    PaginationComponent,
    OffcanvasComponent,
    ToastMessageComponent
  ],
  templateUrl: './feature-management.component.html',
  styleUrl: './feature-management.component.scss'
})
export class FeatureManagementComponent implements OnInit {
  @ViewChild('filterForm') filterForm?: NgForm;
  @ViewChild('createForm') createForm?: NgForm;
  @ViewChild(ToastMessageComponent) toast!: ToastMessageComponent;

  isFilterModalOpen = false;
  isCreateModalOpen = false;
  isEditModalOpen = false;

  featureList: Feature[] = [];
  businessUnits: BusinessUnit[] = [];
  featureMasters: { id: number; title: string }[] = []; // For the dropdown in create form
  listLoading = false;
  createLoading = false;
  editLoading = false;

  currentPage = 1;
  itemsPerPage = 10;
  totalItems = 0;

  filters: FeatureFilter = {
    featureMasterTitle: null,
    businessUnitId: null,
    enabled: null, // Set to null so it's not applied by default
    sortField: 'createdTimestamp',
    sortDirection: 'DESC'
  };

  newFeatureData: NewFeatureData = {
    featureMasterId: null,
    enabled: true,
    adminRoute: null,
    appRoute: null,
  };

  editedFeatureData: Feature = {
    id: 0,
    enabled: false,
    featureMasterId: 0,
    businessUnitId: 0,
    privilege: false,
    adminRoute: null,
    appRoute: null,
    priority: null,
    createdBy: null,
    createdTimestamp: '',
    updatedBy: null,
    updatedTimestamp: '',
    featureMaster: {
      id: 0,
      enabled: false,
      title: '',
      priority: null,
      createdBy: null,
      createdTimestamp: '',
      updatedBy: null,
      updatedTimestamp: '',
    },
    businessUnit: {
      id: 0,
      enabled: false,
      isDeleted: false,
      title: '',
      icon: null,
      priority: null,
      createdBy: null,
      createdTimestamp: '',
      updatedBy: null,
      updatedTimestamp: '',
    },
  };

  availableSortFields = [
    { value: 'id', label: 'ID' },
    { value: 'featureMaster.title', label: 'Feature Name' },
    { value: 'businessUnit.title', label: 'Business Unit' },
    { value: 'enabled', label: 'Enabled Status' },
    { value: 'adminRoute', label: 'Admin Route' },
    { value: 'appRoute', label: 'App Route' },
    { value: 'createdTimestamp', label: 'Created Date' }
  ];

  constructor(
    private featureManagementService: FeatureManagementService,
    private businessUnitService: BusinessUnitService,
    private updateService: UpdateService,
    private authService: AuthService,
    private featureMasterService: FeatureMasterService
  ) { }

  ngOnInit() {
    this.loadFeatures(this.currentPage);
    this.loadBusinessUnits();
    // Call loadFeatureMasters after features are loaded to ensure filtering works correctly
    // However, given the async nature, it's better to ensure featureList is populated first.
    // For now, keep it here, but if issues arise, consider a chained promise or event.
    this.loadFeatureMasters();
  }

  openFilterModal(): void { this.isFilterModalOpen = true; }
  closeFilterModal(): void { this.isFilterModalOpen = false; }

  openCreateModal(): void {
    this.newFeatureData = {
      featureMasterId: null,
      enabled: false, // Set to false to reflect disabled state by default
      adminRoute: null,
      appRoute: null,
    };
    this.createForm?.resetForm({ enabled: false, adminRoute: null, appRoute: null }); // Ensure form reset also sets to false
    this.isCreateModalOpen = true;
    this.createLoading = false;
  }

  closeCreateModal(): void {
    this.isCreateModalOpen = false;
  }

  openEditModal(feature: Feature): void {
    this.editedFeatureData = { ...feature };
    this.isEditModalOpen = true;
    this.editLoading = false;
  }

  closeEditModal(): void {
    this.isEditModalOpen = false;
  }

  async submitEditForm(): Promise<void> {
    if (this.createForm?.invalid) { // Use createForm to check validity for now, will add editForm later
      Object.values(this.createForm.controls).forEach(control => control.markAsTouched());
      this.toast?.showErrorToast("Please correct the validation errors before updating.");
      return;
    }

    this.editLoading = true;

    try {
      const updatePayload = {
        tableName: 'feature',
        id: this.editedFeatureData.id,
        data: {
          adminRoute: this.editedFeatureData.adminRoute,
          appRoute: this.editedFeatureData.appRoute,
          // enabled: this.editedFeatureData.enabled // Enabled is handled by switch
        }
      };
      await this.updateService.update(updatePayload);
      this.toast?.showSuccessToast(`Feature updated successfully!`);
      this.closeEditModal();
      this.loadFeatures(this.currentPage);
    } catch (error) {
      console.error("Error updating feature:", error);
      this.toast?.showErrorToast('Failed to update feature. Please try again.');
    } finally {
      this.editLoading = false;
    }
  }

  async submitCreateForm(): Promise<void> {
    if (this.createForm?.invalid) {
      Object.values(this.createForm.controls).forEach(control => control.markAsTouched());
      this.toast?.showErrorToast("Please correct the validation errors before creating.");
      return;
    }

    this.createLoading = true;

    try {
      const createdFeature = await this.featureManagementService.createFeature(this.newFeatureData);
      this.toast?.showSuccessToast(`Feature created successfully!`);
      this.closeCreateModal();
      this.currentPage = 1;
      this.loadFeatures(this.currentPage);
    } catch (error) {
      console.error("Error creating feature:", error);
      this.toast?.showErrorToast('Failed to create feature. Please try again.');
    } finally {
      this.createLoading = false;
    }
  }

  async loadFeatures(page: number): Promise<void> {
    this.listLoading = true;
    const filterParams: string[] = [];

    if (this.filters.featureMasterTitle) {
      filterParams.push(`featureMaster.title||$contL||${this.filters.featureMasterTitle}`);
    }
    if (this.filters.businessUnitId !== null) {
      filterParams.push(`businessUnitId||$eq||${this.filters.businessUnitId}`);
    }
    if (this.filters.enabled !== null && this.filters.enabled !== undefined && this.filters.enabled !== '') {
      filterParams.push(`enabled||$eq||${this.filters.enabled}`);
    }

    const requestData = {
      page: page,
      limit: this.itemsPerPage,
      sort: `${this.filters.sortField || 'createdTimestamp'},${this.filters.sortDirection || 'DESC'}`,
      filter: filterParams
    };

    try {
      const params = createAxiosConfig(requestData);
      const response = await this.featureManagementService.getFeatures(params);
      this.featureList = response?.data ?? [];
      this.totalItems = response?.total ?? 0;
      // Now that featureList is populated, load feature masters for the dropdown
      await this.loadFeatureMasters();
    } catch (error) {
      console.error("Error fetching features:", error);
      this.featureList = [];
      this.totalItems = 0;
    } finally {
      this.listLoading = false;
    }
  }

  async loadBusinessUnits(): Promise<void> {
    try {
      const response = await this.businessUnitService.getBusinessUnits(createAxiosConfig({
        limit: 1000,
        sort: 'title,ASC'
      }));
      this.businessUnits = response?.data ?? [];
    } catch (error) {
      console.error("Error fetching business units:", error);
      this.businessUnits = [];
    }
  }

  async loadFeatureMasters(): Promise<void> {
    try {
      const response = await this.featureMasterService.getFeatureMaster(createAxiosConfig({
        limit: 1000,
        sort: 'title,ASC'
      }));
      const allFeatureMasters = response ?? [];

      // Filter out feature masters that are already in the featureList
      this.featureMasters = allFeatureMasters.filter((fm: { id: number; }) =>
        !this.featureList.some(f => f.featureMaster.id === fm.id)
      );
    } catch (error) {
      console.error("Error fetching feature masters:", error);
      this.featureMasters = [];
    }
  }

  applyFilters(): void {
    if (this.filterForm?.invalid) {
      this.toast?.showErrorToast("Please correct the validation errors before applying filters.");
      return;
    }
    if (this.filters.featureMasterTitle) {
      this.filters.featureMasterTitle = this.filters.featureMasterTitle.trim();
    }
    this.currentPage = 1;
    this.loadFeatures(this.currentPage);
    this.closeFilterModal();
  }

  resetFilters(): void {
    this.filters = {
      featureMasterTitle: null,
      businessUnitId: null,
      enabled: null,
      sortField: 'createdTimestamp',
      sortDirection: 'DESC'
    };
    this.currentPage = 1;
    this.loadFeatures(this.currentPage);
  }

  onPageChange(page: number): void {
    if (page !== this.currentPage) {
      this.currentPage = page;
      this.loadFeatures(this.currentPage);
    }
  }

  async onSwitchToggle(value: boolean, feature: Feature, field: 'enabled'): Promise<void> {
    const originalValue = feature[field];
    feature[field] = value;

    const updatePayload = {
      tableName: 'feature',
      id: feature.id,
      data: {
        [field]: value
      }
    };

    try {
      await this.updateService.update(updatePayload);
      this.toast?.showSuccessToast(`Feature ${field} updated successfully!`);
    } catch (error) {
      feature[field] = originalValue;
      const index = this.featureList.findIndex(f => f.id === feature.id);
      if (index !== -1) {
        this.featureList[index] = { ...this.featureList[index], [field]: originalValue };
      }
      console.error(`Error updating feature ${field}:`, error);
      this.toast?.showErrorToast(`Failed to update feature ${field}.`);
    }
  }
}