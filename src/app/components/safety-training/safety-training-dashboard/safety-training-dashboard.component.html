<div class="container-fluid mt-3">
  <div *ngIf="isLoading" class="text-center p-5">
    <span class="spinner-border text-primary" role="status"></span>
    <p class="mt-2">Loading dashboard data...</p>
  </div>

  <div *ngIf="!isLoading">
    <div class="card custom-card">
      <div class="card-header">
         <div class="row align-items-center">
            <div class="col">
        <h5 class="mb-0">Safety Training Dashboard</h5>
        </div>
        <div class="col text-end d-flex align-items-center justify-content-end">
          <button *ngIf="roleType  === 'plantadmin'" type="button" class="btn btn-secondary ms-3 " style="font-size:smaller;" title="Filter Records">
            <b>{{selectedPlant}} </b>
          </button>
          <!-- <div *ngIf="roleType  === 'plantadmin'">
            <select [disabled]="true" id="filterPlant" class="form-select" [(ngModel)]="selectedPlant" name="plantFilter">
              <option *ngFor="let plant of availablePlants" [ngValue]="plant.id">{{ plant.name }}</option>
            </select>
          </div> -->
          <button type="button" class="btn btn-light ms-3 p-1" (click)="openFilterModal()" title="Filter Records">
            <img src="../../../assets/svg/filter.svg" class="filter-button" alt="Filter" />
          </button>
        </div>
      </div>
      </div>
      <div class="card-body">
        <div class="row g-3">
          <!-- Monthly Trainings Chart -->
          <div class="col-md-4">
            <div id="monthlyTrainingsChart"  class="chart-container"   *ngIf="monthlyTrainingsChartOptions?.series?.length">
              <apx-chart
                [series]="monthlyTrainingsChartOptions.series"
                [chart]="monthlyTrainingsChartOptions.chart"
                [xaxis]="monthlyTrainingsChartOptions.xaxis"
                [colors]="monthlyTrainingsChartOptions.colors"
                [dataLabels]="monthlyTrainingsChartOptions.dataLabels"
                [title]="monthlyTrainingsChartOptions.title"
              ></apx-chart>
            </div>
          </div>

          <!-- Gender Distribution Chart -->
          <div class="col-md-4">
            <div id="genderDistributionChart" class="chart-container" *ngIf="genderDistributionChartOptions?.series?.length">
              <apx-chart
                [series]="genderDistributionChartOptions.series"
                [chart]="genderDistributionChartOptions.chart"
                [xaxis]="genderDistributionChartOptions.xaxis"
                [plotOptions]="genderDistributionChartOptions.plotOptions"
                [colors]="genderDistributionChartOptions.colors"
                [legend]="genderDistributionChartOptions.legend"
                [dataLabels]="genderDistributionChartOptions.dataLabels"
                [title]="genderDistributionChartOptions.title"
              ></apx-chart>
            </div>
          </div>

          <!-- Training Type Chart -->
          <div class="col-md-4">
            <div id="trainingTypeChart" class="chart-container"  *ngIf="trainingTypeChartOptions?.series?.length">
              <apx-chart
                [series]="trainingTypeChartOptions.series"
                [chart]="trainingTypeChartOptions.chart"
                [xaxis]="trainingTypeChartOptions.xaxis"
                [plotOptions]="trainingTypeChartOptions.plotOptions"
                [colors]="trainingTypeChartOptions.colors"
                [legend]="trainingTypeChartOptions.legend"
                [labels]="trainingTypeChartOptions.labels"
                [dataLabels]="trainingTypeChartOptions.dataLabels"
                [title]="trainingTypeChartOptions.title"
              ></apx-chart>
            </div>
          </div>

          <!-- Manhours Chart -->
          <div class="col-md-4">
            <div id="manhoursChart" class="chart-container  m-0 p-0" *ngIf="manhoursChartOptions?.series?.length">
              <apx-chart
                [series]="manhoursChartOptions.series"
                [chart]="manhoursChartOptions.chart"
                [plotOptions]="manhoursChartOptions.plotOptions"
                [xaxis]="manhoursChartOptions.xaxis"
                [yaxis]="manhoursChartOptions.yaxis"
                [colors]="manhoursChartOptions.colors"
                [legend]="manhoursChartOptions.legend"
                [fill]="manhoursChartOptions.fill"
                [dataLabels]="manhoursChartOptions.dataLabels"
                [title]="manhoursChartOptions.title"
              ></apx-chart>
            </div>
          </div>

          <!-- Participant Type Chart -->
          <div class="col-md-4">
            <div id="participantTypeChart" class="chart-container"  *ngIf="participantTypeChartOptions?.series?.length">
              <apx-chart
                [series]="participantTypeChartOptions.series"
                [chart]="participantTypeChartOptions.chart"
                [xaxis]="participantTypeChartOptions.xaxis"
                [plotOptions]="participantTypeChartOptions.plotOptions"
                [colors]="participantTypeChartOptions.colors"
                [legend]="participantTypeChartOptions.legend"
                [labels]="participantTypeChartOptions.labels"
                [dataLabels]="participantTypeChartOptions.dataLabels"
                [title]="participantTypeChartOptions.title"
              ></apx-chart>
            </div>
          </div>

          <!-- Average Participants Chart -->
           <div class="col-md-4">
            <div id="avgParticipantsChart" class="chart-container"  *ngIf="avgParticipantsChartOptions?.series?.length">
              <apx-chart
                [series]="avgParticipantsChartOptions.series"
                [chart]="avgParticipantsChartOptions.chart"
                [xaxis]="avgParticipantsChartOptions.xaxis"
                [yaxis]="avgParticipantsChartOptions.yaxis"
                [colors]="avgParticipantsChartOptions.colors"
                [stroke]="avgParticipantsChartOptions.stroke"
                [dataLabels]="avgParticipantsChartOptions.dataLabels"
                [title]="avgParticipantsChartOptions.title"
              ></apx-chart>
            </div>
          </div>

        </div> <!-- End row -->
      </div> <!-- End outer card-body -->
    </div> <!-- End outer card -->
  </div> <!-- End *ngIf="!isLoading" -->
</div> <!-- End container-fluid -->
<app-offcanvas [title]="'Filter Safety Training Records'" [width]="'512px'" *ngIf="isFilterModalOpen" (onClickCross)="closeFilterModal()">
    <div class="filter-container p-3">
        <form #filterForm="ngForm" (ngSubmit)="applyFilters()">
            <div class="row g-3">
                <!-- Filter form elements -->
                <!-- <div class="col-md-12">
                    <label for="filterMonth" class="form-label">Month</label>
                    <input type="date" id="filterMonth" class="form-control" [(ngModel)]="filters.month"
                        name="monthFilter">
                </div> -->
                <div class="col-md-6" *ngIf="roleType =='superadmin'">
                    <label for="filterFacility" class="form-label">Facility</label>
                    <select id="filterFacility" class="form-select" [(ngModel)]="filters.plantTypeId" name="facilityFilter"  (change)="onFacilityChange(filters.plantTypeId)">
                        <option [ngValue]="null">All Facilities</option>
                        <option *ngFor="let facility of availableFacilities" [ngValue]="facility.id">{{ facility.title
                            }}</option>
                    </select>
                </div>
                <!-- <div class="col-md-6">
                    <label for="filterCluster" class="form-label">Cluster</label>
                    <select id="filterCluster" class="form-select" [(ngModel)]="filters.clusterId" name="clusterFilter">
                        <option [ngValue]="null">All Clusters</option>
                        <option *ngFor="let cluster of availableClusters" [ngValue]="cluster.id">{{ cluster.title }}
                        </option>
                    </select>
                </div> -->
                <!-- <div class="col-md-6">
                    <label for="filterCompanyName" class="form-label">Name of Company</label>
                    <select id="filterCompanyName" class="form-select" [(ngModel)]="filters.companyName"
                        name="companyNameFilter">
                        <option [ngValue]="null">All Companies</option>
                        <option *ngFor="let company of availableCompanies" [ngValue]="company">{{ company }}</option>
                    </select>
                </div> -->
               <div class="col-md-6">
                    <label for="filterPlant" class="form-label">Plant</label>
                    <select id="filterPlant" class="form-select" [disabled]="filters.plantTypeId ==null && roleType =='superadmin'" [(ngModel)]="filters.plantId" name="plantFilter" (change)="onPlantSelect(filters.plantId)">
                        <option *ngIf="filters.plantTypeId ==null" [ngValue]="null">All Plants</option>
                        <option *ngIf="filters.plantTypeId !=null" [ngValue]="null">Select Plants</option>
                        <option *ngFor="let plant of filteredPlants" [ngValue]="plant.id">{{ plant.name }}</option>
                    </select>
                </div>
                   <div class="col-6">
                    <label for="filterPlants" class="form-label">Select Month(s)</label>
                    <ng-select
                        [items]="filteredMonths"
                        bindValue="id"
                        bindLabel="name"
                        [multiple]="true"
                        [closeOnSelect]="false"
                        [clearable]="false"
                        [(ngModel)]="filters.months"
                        (change)="updateSelectAllState()"
                        name="monthFilter"
                        placeholder="Select Month(s)">
                        <ng-template ng-header-tmp>
                            <div class="form-check mb-1 ms-2">
                                <input class="form-check-input" type="checkbox"
                                       id="selectAllPlantsCheckbox"
                                       [checked]="isAllMonthsSelected"
                                       (change)="toggleSelectAllPlants($event)">
                                <label class="form-check-label small" for="selectAllPlantsCheckbox">
                                    Select All / Deselect All
                                </label>
                            </div>
                        </ng-template>
                        <ng-template ng-option-tmp let-item="item" let-index="index">
                            {{ item.name }}
                        </ng-template>
                    </ng-select>
                </div>
                 <div class="col-6" >
                        <label class="form-label" for="createYear">Year</label>
                        <select id="createYear" class="form-select p-2" (ngModelChange)="onYearChange($event)"
                            [(ngModel)]="year" name="selectedYear" required
                            >
                            <option [ngValue]="null" disabled> Year </option>
                            <option *ngFor="let year of uniqueYearsForFilter" [value]="year">{{ year }}</option>
                        </select>
                    </div>
            </div>
             <div class="row p-3 mt-4 justify-content-center">
                    <button type="submit" class="btn adani-btn col-4 m-1" (click)="applyFilters()">
                        <i class="bi bi-search me-1"></i> Apply Filters
                    </button>
                    <button type="button" class="btn btn-secondary col-4 m-1" (click)="resetFilters()">
                        <i class="bi bi-arrow-clockwise me-1"></i> Reset Filters
                    </button>
                </div>
        </form>
    </div>
</app-offcanvas>
