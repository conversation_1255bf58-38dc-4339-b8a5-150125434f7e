// Add styles to visually separate the charts within the main dashboard card

// Target the row containing the charts inside the main card body
.card > .card-body > .row.g-3 {
  > .col-md-4,.col-12, .col-md-4,.col-5, .col-lg-4 ,.col-md-5, .col-sm-4 ,.col-lg-6{ // Target the direct children columns
    border: 1px solid #e0e0e0; // Add a light border
    border-radius: 0.25rem; // Optional: slightly rounded corners
    padding: 0.8rem; // Add some internal padding around the chart div
    background-color: #fdfdfd; // Optional: slightly off-white background

    // Ensure the chart div itself doesn't have extra margin/padding interfering
    > div[id$="Chart"] { // Target divs with IDs ending in "Chart"
        margin: 0;
        padding: 0;
    }
  }
}
.chart-container {
  width: 100%;
  height: 350px; /* Set initial height, but allow resizing */
  max-width: 100%;
  display: flex;
  justify-content: center;
}
.chart-title {
  font-size: 11px !important;  /* Forces font size change */
}
.btn-secondary{
  font-size: 12px;
}