.card-body {
  height: 70vh;
  overflow: auto;
}

/* Table header styling */
.table-header th {
  font-size: 12px;
  background-color: #0B74B0 !important;
  color: white !important;
  text-align: center;
  padding: 10px;
}

/* Table cell styling */
td {
  font-size: 12px;
  padding: 10px;
  vertical-align: top;
}

i.edit {
  font-size: 12px;
}

/* Actions column styling */
.actions {
  text-align: center !important;
  vertical-align: middle !important;
  width: 120px;
  min-width: 120px;
}

/* Table container styling */
.table-container {
  width: 100%;
  overflow-x: auto;
}

.table-responsive {
  overflow-x: auto;
  max-width: 100%;
}

/* Table column width adjustments */
.table td:nth-child(3) {
  width: 25%;
  max-width: 250px;
  white-space: normal;
  word-wrap: break-word;
}

.table td:nth-child(4) {
  width: 40%;
  max-width: 40%;
  white-space: normal;
  word-wrap: break-word;
}

/* Modal header styling */
.modal-header {
  background-image: linear-gradient(90deg, #0b74b0, #75479c, #bd3681) !important;
  color: white;
}

/* Section title styling */
.heading-title {
  font-weight: 700;
  color: #333;
  margin-bottom: 10px;
  font-size: 14px;
  padding: 6px 0;
}

/* Info group styling */
.info-group {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  margin-bottom: 6px;
  padding-bottom: 6px;
  border-bottom: 1px dashed #eee;
  line-height: 1.4;
  flex-wrap: wrap;
}

.info-group:last-child {
  margin-bottom: 5px;
  border-bottom: none;
}

/* Label styling */
.info-label {
  font-weight: normal;
  color: #666;
  margin-right: 8px;
  min-width: 90px;
  width: auto;
  flex: 0 0 auto;
  font-size: 12px;
}

/* Value styling */
.value-text {
  font-weight: 600;
  color: #333;
  flex: 1;
  word-break: break-word;
  font-size: 12px;
}

/* Section container styling */
.section-container {
  margin-bottom: 15px;
  padding-bottom: 10px;
}

.section-container + .section-container {
  border-top: 1px dashed #ddd;
  padding-top: 10px;
}

/* Detail text styling */
.detail-text {
  width: 100%;
  word-wrap: break-word;
  white-space: normal;
}

.detail-text .value-text,
.detail-text b {
  display: block;
  padding-top: 4px;
}

/* Bold text styling */
td b,
td .info-group > span:not(.info-label),
td .info-group > div > span:not(.info-label) {
  font-weight: 600;
  color: #333;
  font-size: 12px;
}

/* Modal table styling */
.modal-body table {
  width: 100%;
  border-collapse: collapse;
}

.modal-body table th {
  font-weight: 600;
  background-color: #f5f7fa;
  color: #333;
  text-align: center;
  padding: 8px;
}

.modal-body table td {
  font-weight: 500;
  padding: 8px;
  border-bottom: 1px dashed #eee;
}

/* Table row styling */
.table-bordered tbody tr {
  border-bottom: 1px dashed #eee;
}

/* Button styling */
.btn-victim-data {
  background: #964576 !important;
  border-color: #964576 !important;
  text-align: center;
  color: white;
  display: flex;
  justify-content: center;
}

.btn-view-capa {
  background-color: #17a2b8 !important;
  border-color: #17a2b8 !important;
  color: white;
}

.btn-accept {
  background-color: #28a745 !important;
  border-color: #28a745 !important;
  color: white;
}

.btn-capa-status {
  background-color: #28a745 !important;
  border-color: #28a745 !important;
  color: white;
}

/* Filter button styling */
.filter-button {
  width: 35px;
  cursor: pointer;
}

/* Responsive adjustments for small screens */
@media (max-width: 768px) {
  .info-label {
    min-width: 80px;
  }

  .table td:nth-child(3) {
    max-width: 200px;
  }

  .actions {
    width: 100px;
    min-width: 100px;
  }
}