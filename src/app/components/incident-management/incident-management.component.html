<app-toast-message></app-toast-message>
<app-tab [tabs]="tabs" [selectedTabIndex]="selectedTabIndex" (tabSelected)="onTabSelected($event)">
</app-tab>
<div *ngIf="selectedTabIndex == 0" class="card custom-card">
  <div class="card-header">
    <div class="row align-items-center">
      <div class="col">
        <h6 class="mb-0">New Incident List</h6>
      </div>
      <div class="col text-end d-flex align-items-center justify-content-end">
        <!-- *** REPLACE the existing simple Download button with this Dropdown *** -->
        <div ngbDropdown class="d-inline-block me-2">
          <button type="button" class="btn btn-sm adani-btn dropdown-toggle"
            [id]="'downloadIncExcelDropdown' + selectedTabIndex" ngbDropdownToggle
            [disabled]="isDownloadingExcel || listLoading">
            <span *ngIf="!isDownloadingExcel">
              <i class="bi bi-file-earmark-excel me-1"></i> Download Excel
            </span>
            <span *ngIf="isDownloadingExcel">
              <span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
              Downloading {{ downloadType === 'current' ? 'Page' : (downloadType === 'all' ? 'All' : '') }}...
            </span>
          </button>
          <ul ngbDropdownMenu [attr.aria-labelledby]="'downloadIncExcelDropdown' + selectedTabIndex">
            <li>
              <button type="button" ngbDropdownItem (click)="downloadExcel('current')"
                [disabled]="isDownloadingExcel || listLoading || (getCurrentListData()?.length ?? 0) === 0">
                <i class="bi bi-download me-1"></i> Download Current Page ({{ getCurrentListData()?.length ?? 0 }})
              </button>
            </li>
            <li>
              <button type="button" ngbDropdownItem (click)="downloadExcel('all')"
                [disabled]="isDownloadingExcel || listLoading || totalItems === 0">
                <i class="bi bi-cloud-download me-1"></i> Download All Filtered ({{ totalItems }})
              </button>
            </li>
          </ul>
        </div>
        <img src="../../../assets/svg/filter.svg" class="filter-button ms-3" (click)="openFilterModal()" alt="Filter"
          tabindex="0" (keyup.enter)="openFilterModal()" />
      </div>
    </div>
  </div>
  <div class="card-body">
    <div class="table-responsive">
      <table class="table table-bordered custom-table">
        <thead class="table-header">
          <tr>
            <th scope="col" class="text-center">Actions</th>
            <th scope="col" class="text-center">Incident By</th>
            <th scope="col" class="text-center">Incident Location</th>
            <th scope="col" class="text-center">Incident Details</th>
            <th scope="col" class="text-center" *ngIf="showCreatedDate">Created Date</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngIf="listLoading">
            <td [attr.colspan]="showCreatedDate ? 6 : 5" class="text-center p-4">
              <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
              Loading {{ tabs[selectedTabIndex]?.title?.toLowerCase() ?? 'incidents' }}...
            </td>
          </tr>
          <tr *ngIf="!listLoading && getCurrentListData()?.length === 0">
            <td [attr.colspan]="showCreatedDate ? 6 : 5" class="text-center p-4 text-muted">
              No {{ tabs[selectedTabIndex]?.title?.toLowerCase() ?? 'incidents' }} found matching the criteria.
            </td>
          </tr>
          <tr *ngFor="let item of newIncidentList">
            <td class="text-center align-middle actions">
              <a class="btn adani-btn" (click)="openAssignTeamModal(item)">Assign Team</a>
              <br /><br />
              <button type="button" class="btn btn-danger theme-button btn-sm" (click)="openRejectConfirmation(item)">Reject</button>
            </td>

            <!-- INCIDENT BY COLUMN -->
            <td class="align-middle">
              <div class="section-container">
                <p class="heading-title">Reported By</p>
                <div class="info-group">
                  <span class="info-label">Name:</span>
                  <span class="value-text">{{ item.admin?.firstName }} {{ item.admin?.lastName }}</span>
                </div>
                <div class="info-group">
                  <span class="info-label">Email:</span> <span class="value-text">{{ item.admin?.email }}</span>
                </div>
              </div>

              <div class="section-container" *ngIf="item.teamLeader">
                <p class="heading-title">Team Leader</p>
                <div class="info-group">
                  <span class="info-label">Name:</span>
                  <span class="value-text">{{ item.teamLeader?.firstName }} {{ item.teamLeader?.lastName }}</span>
                </div>
                <div class="info-group">
                  <span class="info-label">Email:</span> <span class="value-text">{{ item.teamLeader?.email }}</span>
                </div>
              </div>
            </td>

            <!-- INCIDENT LOCATION COLUMN -->
            <td class="align-middle">
              <div class="info-group">
                <span class="info-label">Plant:</span>
                <span class="value-text">{{ item.plant?.name || 'N/A' }}</span>
              </div>
              <div class="info-group">
                <span class="info-label">Department:</span>
                <span class="value-text">{{ item.department?.title || 'N/A' }}</span>
              </div>
              <div class="info-group">
                <span class="info-label">Zone:</span>
                <span class="value-text">{{ item.zone?.zoneName || 'N/A' }}</span>
              </div>
              <div class="info-group">
                <span class="info-label">Zone Area:</span>
                <span class="value-text">{{ item.zone?.zoneArea || 'N/A' }}</span>
              </div>
              <div class="info-group">
                <span class="info-label">Exact Area:</span>
                <span class="value-text">{{ item.exactArea || 'N/A' }}</span>
              </div>
            </td>

            <!-- INCIDENT DETAILS COLUMN -->
            <td class="align-middle">
              <div class="info-group">
                <span class="info-label">Incident Date:</span>
                <span class="value-text">{{ item.date || 'N/A' }}</span>
              </div>
              <div class="info-group">
                <span class="info-label">Incident Time:</span>
                <span class="value-text">{{ item.time || 'N/A' }}</span>
              </div>
              <div class="info-group">
                <span class="info-label">Incident:</span>
                <span class="value-text">{{ item.incidentTypeMaster?.incidentMaster?.title || 'N/A' }}</span>
              </div>
              <div class="info-group">
                <span class="info-label">Incident Type:</span>
                <span class="value-text">{{ item.incidentTypeMaster?.title || 'N/A' }}</span>
              </div>
              <div class="info-group">
                <span class="info-label">Equipments:</span>
                <span class="value-text">{{ item.equipement?.title || 'N/A' }}</span>
              </div>
              <div class="info-group">
                <span class="info-label">Detail:</span>
                <div class="detail-text">
                  <span class="value-text">{{ item.incidentDetail || 'N/A' }}</span>
                </div>
              </div>
            </td>

            <td class="text-center align-middle" *ngIf="showCreatedDate">
              <span class="text-danger value-text">
                {{ item.createdTimestamp | date : 'dd-MM-yyyy' }}
              </span>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
  <div class="card-footer text-muted text-center">
    <app-pagination [currentPage]="currentPage" [totalItems]="totalItems" [itemsPerPage]="itemsPerPage"
      (pageChange)="onPageChange($event)"></app-pagination>
  </div>
</div>
<div *ngIf="selectedTabIndex == 1" class="card custom-card">
  <div class="card-header">
    <div class="row align-items-center">
      <div class="col">
        <h6 class="mb-0">Pending CAPA List</h6>
      </div>
      <div class="col text-end d-flex align-items-center justify-content-end">
        <!-- *** REPLACE the existing simple Download button with this Dropdown *** -->
        <div ngbDropdown class="d-inline-block me-2">
          <button type="button" class="btn btn-sm adani-btn dropdown-toggle"
            [id]="'downloadIncExcelDropdown' + selectedTabIndex" ngbDropdownToggle
            [disabled]="isDownloadingExcel || listLoading">
            <span *ngIf="!isDownloadingExcel">
              <i class="bi bi-file-earmark-excel me-1"></i> Download Excel
            </span>
            <span *ngIf="isDownloadingExcel">
              <span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
              Downloading {{ downloadType === 'current' ? 'Page' : (downloadType === 'all' ? 'All' : '') }}...
            </span>
          </button>
          <ul ngbDropdownMenu [attr.aria-labelledby]="'downloadIncExcelDropdown' + selectedTabIndex">
            <li>
              <button type="button" ngbDropdownItem (click)="downloadExcel('current')"
                [disabled]="isDownloadingExcel || listLoading || (getCurrentListData()?.length ?? 0) === 0">
                <i class="bi bi-download me-1"></i> Download Current Page ({{ getCurrentListData()?.length ?? 0 }})
              </button>
            </li>
            <li>
              <button type="button" ngbDropdownItem (click)="downloadExcel('all')"
                [disabled]="isDownloadingExcel || listLoading || totalItems === 0">
                <i class="bi bi-cloud-download me-1"></i> Download All Filtered ({{ totalItems }})
              </button>
            </li>
          </ul>
        </div>
        <button type="button" class="btn btn-link p-0 ms-3" (click)="openFilterModal()">
          <img src="../../../assets/svg/filter.svg" class="filter-button" alt="Filter" />
        </button>
      </div>
    </div>
  </div>
  <div class="card-body">
    <div class="table-responsive">
      <table class="table table-bordered custom-table">
        <thead class="table-header">
          <tr>
            <th scope="col" class="text-center">Actions</th>
            <th scope="col" class="text-center">Incident By</th>
            <th scope="col" class="text-center">Incident Location</th>
            <th scope="col" class="text-center">Incident Details</th>
            <th scope="col" class="text-center" *ngIf="showCreatedDate">Created Date</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngIf="listLoading">
            <td [attr.colspan]="showCreatedDate ? 6 : 5" class="text-center p-4">
              <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
              Loading {{ tabs[selectedTabIndex]?.title?.toLowerCase() ?? 'incidents' }}...
            </td>
          </tr>
          <tr *ngIf="!listLoading && getCurrentListData()?.length === 0">
            <td [attr.colspan]="showCreatedDate ? 6 : 5" class="text-center p-4 text-muted">
              No {{ tabs[selectedTabIndex]?.title?.toLowerCase() ?? 'incidents' }} found matching the criteria.
            </td>
          </tr>
          <tr *ngFor="let item of pendingCAPAList">
            <td class="text-center align-middle actions">
              <button type="button" class="btn adani-btn" (click)="openViewMembersModal(item)">Members</button>
              <br /><br />
            </td>

            <td class="align-middle">
              <div class="section-container">
                <p class="heading-title">Reported By</p>
                <div class="info-group">
                  <span class="info-label">Name:</span>
                  <span class="value-text">{{ item.admin?.firstName }} {{ item.admin?.lastName }}</span>
                </div>
                <div class="info-group">
                  <span class="info-label">Email:</span> <span class="value-text">{{ item.admin?.email }}</span>
                </div>
              </div>

              <div class="section-container">
                <p class="heading-title">Plant Admin</p>
                <div class="info-group">
                  <span class="info-label">Name:</span>
                  <span class="value-text">{{ item.engineerIncharge?.firstName }} {{ item.engineerIncharge?.lastName }}</span>
                </div>
                <div class="info-group">
                  <span class="info-label">Email:</span> <span class="value-text">{{ item.engineerIncharge?.email }}</span>
                </div>
              </div>

              <div class="section-container">
                <p class="heading-title">Team Leader</p>
                <div class="info-group">
                  <span class="info-label">Name:</span>
                  <span class="value-text">{{ item.teamLeader?.firstName }} {{ item.teamLeader?.lastName }}</span>
                </div>
                <div class="info-group">
                  <span class="info-label">Email:</span> <span class="value-text">{{ item.teamLeader?.email }}</span>
                </div>
              </div>
            </td>

            <td class="align-middle">
              <div class="info-group">
                <span class="info-label">Plant:</span>
                <span class="value-text">{{ item.plant?.name || 'N/A' }}</span>
              </div>
              <div class="info-group">
                <span class="info-label">Department:</span>
                <span class="value-text">{{ item.department?.title || 'N/A' }}</span>
              </div>
              <div class="info-group">
                <span class="info-label">Zone:</span>
                <span class="value-text">{{ item.zone?.zoneName || 'N/A' }}</span>
              </div>
              <div class="info-group">
                <span class="info-label">Zone Area:</span>
                <span class="value-text">{{ item.zone?.zoneArea || 'N/A' }}</span>
              </div>
              <div class="info-group">
                <span class="info-label">Exact Area:</span>
                <span class="value-text">{{ item.exactArea || 'N/A' }}</span>
              </div>
            </td>

            <td class="align-middle">
              <div class="info-group">
                <span class="info-label">Incident Date:</span> <span>{{ item.date }}</span>
              </div>
              <div class="info-group">
                <span class="info-label">Incident Time:</span> <span>{{ item.time }}</span>
              </div>
              <div class="info-group">
                <span class="info-label">Incident:</span> <span>{{ item.incidentTypeMaster?.incidentMaster?.title
                  }}</span>
              </div>
              <div class="info-group">
                <span class="info-label">Incident Type:</span>
                <span>{{ item.incidentTypeMaster?.title }}</span>
              </div>
              <div class="info-group">
                <span class="info-label">Equipments:</span> <span>{{ item.equipement?.title }}</span>
              </div>
              <div class="info-group">
                <span class="info-label">Detail:</span>
                <div class="detail-text">
                  <span class="value-text">{{ item.incidentDetail }}</span>
                </div>
              </div>
              <div class="info-group">
                <span class="info-label">Zone:</span> <span>{{ item.zone?.zoneName }}</span>
              </div>
              <div class="info-group">
                <span class="info-label">Zone Area:</span> <span>{{ item.zone?.zoneArea }}</span>
              </div>
              <div class="info-group">
                <span class="info-label">Exact Area:</span> <span>{{ item.exactArea }}</span>
              </div>
              <div class="info-group">
                <span class="info-label">Deadline Date:</span> <span>{{ item.deadline }}</span>
              </div>
              <div *ngIf="item.incidentMasterId == 2">
                <button type="button" class="btn theme-button btn-sm btn-victim-data">Victim Data</button>
              </div>
            </td>

            <td class="text-center" *ngIf="showCreatedDate">
              <span class="text-danger">
                {{ item.createdTimestamp | date : 'dd-MM-yyyy' }}
              </span>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
  <div class="card-footer text-muted text-center">
    <app-pagination [currentPage]="currentPage" [totalItems]="totalItems" [itemsPerPage]="itemsPerPage"
      (pageChange)="onPageChange($event)"></app-pagination>
  </div>
</div>
<div *ngIf="selectedTabIndex == 2" class="card custom-card">
  <div class="card-header">
    <div class="row align-items-center">
      <div class="col">
        <h6 class="mb-0">Revised List</h6>
      </div>
      <div class="col text-end d-flex align-items-center justify-content-end">
        <!-- *** REPLACE the existing simple Download button with this Dropdown *** -->
        <div ngbDropdown class="d-inline-block me-2">
          <button type="button" class="btn btn-sm adani-btn dropdown-toggle"
            [id]="'downloadIncExcelDropdown' + selectedTabIndex" ngbDropdownToggle
            [disabled]="isDownloadingExcel || listLoading">
            <span *ngIf="!isDownloadingExcel">
              <i class="bi bi-file-earmark-excel me-1"></i> Download Excel
            </span>
            <span *ngIf="isDownloadingExcel">
              <span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
              Downloading {{ downloadType === 'current' ? 'Page' : (downloadType === 'all' ? 'All' : '') }}...
            </span>
          </button>
          <ul ngbDropdownMenu [attr.aria-labelledby]="'downloadIncExcelDropdown' + selectedTabIndex">
            <li>
              <button type="button" ngbDropdownItem (click)="downloadExcel('current')"
                [disabled]="isDownloadingExcel || listLoading || (getCurrentListData()?.length ?? 0) === 0">
                <i class="bi bi-download me-1"></i> Download Current Page ({{ getCurrentListData()?.length ?? 0 }})
              </button>
            </li>
            <li>
              <button type="button" ngbDropdownItem (click)="downloadExcel('all')"
                [disabled]="isDownloadingExcel || listLoading || totalItems === 0">
                <i class="bi bi-cloud-download me-1"></i> Download All Filtered ({{ totalItems }})
              </button>
            </li>
          </ul>
        </div>
        <button type="button" class="btn btn-link p-0 ms-3" (click)="openFilterModal()">
          <img src="../../../assets/svg/filter.svg" class="filter-button" alt="Filter" />
        </button>
      </div>
    </div>
  </div>
  <div class="card-body">
    <div class="table-responsive">
      <table class="table table-bordered custom-table">
        <thead class="table-header">
          <tr>
            <th scope="col" class="text-center">Actions</th>
            <th scope="col" class="text-center">Incident By</th>
            <th scope="col" class="text-center">Incident Location</th>
            <th scope="col" class="text-center">Incident Details</th>
            <th scope="col" class="text-center" *ngIf="showCreatedDate">Created Date</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngIf="listLoading">
            <td [attr.colspan]="showCreatedDate ? 6 : 5" class="text-center p-4">
              <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
              Loading {{ tabs[selectedTabIndex]?.title?.toLowerCase() ?? 'incidents' }}...
            </td>
          </tr>
          <tr *ngIf="!listLoading && getCurrentListData()?.length === 0">
            <td [attr.colspan]="showCreatedDate ? 6 : 5" class="text-center p-4 text-muted">
              No {{ tabs[selectedTabIndex]?.title?.toLowerCase() ?? 'incidents' }} found matching the criteria.
            </td>
          </tr>
          <tr *ngFor="let item of revisedList">
            <td class="text-center align-middle actions">
              <button type="button" class="btn theme-button btn-sm" (click)="openViewMembersModal(item)">Members</button>
              <br /><br />
              <button type="button" class="btn theme-button btn-warning btn-sm" (click)="openAssignTeamModal(item)">Assign Team</button>
            </td>

            <td class="align-middle">
              <div class="section-container">
                <p class="heading-title">Reported By</p>
                <div class="info-group">
                  <span class="info-label">Name:</span>
                  <span class="value-text">{{ item.admin?.firstName }} {{ item.admin?.lastName }}</span>
                </div>
                <div class="info-group">
                  <span class="info-label">Email:</span> <span class="value-text">{{ item.admin?.email }}</span>
                </div>
              </div>

              <div class="section-container">
                <p class="heading-title">Plant Admin</p>
                <div class="info-group">
                  <span class="info-label">Name:</span>
                  <span class="value-text">{{ item.engineerIncharge?.firstName }} {{ item.engineerIncharge?.lastName }}</span>
                </div>
                <div class="info-group">
                  <span class="info-label">Email:</span> <span class="value-text">{{ item.engineerIncharge?.email }}</span>
                </div>
              </div>

              <div class="section-container">
                <p class="heading-title">Team Leader</p>
                <div class="info-group">
                  <span class="info-label">Name:</span>
                  <span class="value-text">{{ item.teamLeader?.firstName }} {{ item.teamLeader?.lastName }}</span>
                </div>
                <div class="info-group">
                  <span class="info-label">Email:</span> <span class="value-text">{{ item.teamLeader?.email }}</span>
                </div>
              </div>
            </td>

            <td class="align-middle">
              <div class="info-group">
                <span class="info-label">Plant:</span> <b>{{ item.plant?.name }}</b>
              </div>
              <div class="info-group">
                <span class="info-label">Department:</span> <b>{{ item.department?.title }}</b>
              </div>
              <div class="info-group">
                <span class="info-label">Zone:</span> <b>{{ item.zone?.zoneName }}</b>
              </div>
              <div class="info-group">
                <span class="info-label">Zone Area:</span> <b>{{ item.zone?.zoneArea }}</b>
              </div>
              <div class="info-group">
                <span class="info-label">Exact Area:</span> <b>{{ item.exactArea }}</b>
              </div>
            </td>

            <td class="align-middle">
              <div class="info-group">
                <span class="info-label">Incident Date:</span> <b>{{ item.date }}</b>
              </div>
              <div class="info-group">
                <span class="info-label">Incident Time:</span> <b>{{ item.time }}</b>
              </div>
              <div class="info-group">
                <span class="info-label">Incident:</span> <b>{{ item.incidentTypeMaster?.incidentMaster?.title }}</b>
              </div>
              <div class="info-group">
                <span class="info-label">Incident Type:</span>
                <b>{{ item.incidentTypeMaster?.title }}</b>
              </div>
              <div class="info-group">
                <span class="info-label">Equipments:</span> <b>{{ item.equipement?.title }}</b>
              </div>
              <div class="info-group">
                <span class="info-label">Detail:</span>
                <div class="detail-text">
                  <span class="value-text">{{ item.incidentDetail }}</span>
                </div>
              </div>
              <div class="info-group">
                <span class="info-label">Zone:</span> <b>{{ item.zone?.zoneName }}</b>
              </div>
              <div class="info-group">
                <span class="info-label">Zone Area:</span> <b>{{ item.zone?.zoneArea }}</b>
              </div>
              <div class="info-group">
                <span class="info-label">Exact Area:</span> <b>{{ item.exactArea }}</b>
              </div>
              <div class="info-group">
                <span class="info-label">Deadline Date:</span> <b>{{ item.deadline }}</b>
              </div>
              <div *ngIf="item.incidentMasterId == 2">
                <button type="button" class="btn theme-button btn-sm btn-victim-data">Victim Data</button>
              </div>
            </td>

            <td class="text-center" *ngIf="showCreatedDate">
              <span class="text-danger">
                {{ item.createdTimestamp | date : 'dd-MM-yyyy' }}
              </span>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
  <div class="card-footer text-muted text-center">
    <app-pagination [currentPage]="currentPage" [totalItems]="totalItems" [itemsPerPage]="itemsPerPage"
      (pageChange)="onPageChange($event)"></app-pagination>
  </div>
</div>
<div *ngIf="selectedTabIndex == 3" class="card custom-card">
  <div class="card-header">
    <div class="row align-items-center">
      <div class="col">
        <h6 class="mb-0">CAPA Submitted List</h6>
      </div>
      <div class="col text-end d-flex align-items-center justify-content-end">
        <!-- *** REPLACE the existing simple Download button with this Dropdown *** -->
        <div ngbDropdown class="d-inline-block me-2">
          <button type="button" class="btn btn-sm adani-btn dropdown-toggle"
            [id]="'downloadIncExcelDropdown' + selectedTabIndex" ngbDropdownToggle
            [disabled]="isDownloadingExcel || listLoading">
            <span *ngIf="!isDownloadingExcel">
              <i class="bi bi-file-earmark-excel me-1"></i> Download Excel
            </span>
            <span *ngIf="isDownloadingExcel">
              <span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
              Downloading {{ downloadType === 'current' ? 'Page' : (downloadType === 'all' ? 'All' : '') }}...
            </span>
          </button>
          <ul ngbDropdownMenu [attr.aria-labelledby]="'downloadIncExcelDropdown' + selectedTabIndex">
            <li>
              <button type="button" ngbDropdownItem (click)="downloadExcel('current')"
                [disabled]="isDownloadingExcel || listLoading || (getCurrentListData()?.length ?? 0) === 0">
                <i class="bi bi-download me-1"></i> Download Current Page ({{ getCurrentListData()?.length ?? 0 }})
              </button>
            </li>
            <li>
              <button type="button" ngbDropdownItem (click)="downloadExcel('all')"
                [disabled]="isDownloadingExcel || listLoading || totalItems === 0">
                <i class="bi bi-cloud-download me-1"></i> Download All Filtered ({{ totalItems }})
              </button>
            </li>
          </ul>
        </div>
        <button type="button" class="btn btn-link p-0 ms-3" (click)="openFilterModal()">
          <img src="../../../assets/svg/filter.svg" class="filter-button" alt="Filter" />
        </button>
      </div>
    </div>
  </div>
  <div class="card-body">
    <div class="table-responsive">
      <table class="table table-bordered custom-table">
        <thead class="table-header">
          <tr>
            <th scope="col" class="text-center">Actions</th>
            <th scope="col" class="text-center">Incident By</th>
            <th scope="col" class="text-center">Incident Location</th>
            <th scope="col" class="text-center">Incident Details</th>
            <th scope="col" class="text-center" *ngIf="showCreatedDate">Created Date</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngIf="listLoading">
            <td [attr.colspan]="showCreatedDate ? 6 : 5" class="text-center p-4">
              <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
              Loading {{ tabs[selectedTabIndex]?.title?.toLowerCase() ?? 'incidents' }}...
            </td>
          </tr>
          <tr *ngIf="!listLoading && getCurrentListData()?.length === 0">
            <td [attr.colspan]="showCreatedDate ? 6 : 5" class="text-center p-4 text-muted">
              No {{ tabs[selectedTabIndex]?.title?.toLowerCase() ?? 'incidents' }} found matching the criteria.
            </td>
          </tr>
          <tr *ngFor="let item of capaSubmittedList">
            <td class="text-center align-middle actions">
              <button type="button" class="btn theme-button btn-sm" (click)="openViewMembersModal(item)">Members</button>
              <br /><br />
              <a href="/incident/capa/{{ item.id }}?tab=submit">
                <button type="button" class="btn theme-button btn-sm btn-view-capa">View CAPA</button>
              </a>
              <br /><br />
              <button type="button" class="btn theme-button btn-danger btn-sm">Reject</button>
              <br /><br />
              <button type="button" class="btn theme-button btn-sm btn-accept">Accept</button>
            </td>

            <td class="align-middle">
              <div class="section-container">
                <p class="heading-title">Reported By</p>
                <div class="info-group">
                  <span class="info-label">Name:</span>
                  <span class="value-text">{{ item.admin?.firstName }} {{ item.admin?.lastName }}</span>
                </div>
                <div class="info-group">
                  <span class="info-label">Email:</span> <span class="value-text">{{ item.admin?.email }}</span>
                </div>
              </div>

              <div class="section-container">
                <p class="heading-title">Plant Admin</p>
                <div class="info-group">
                  <span class="info-label">Name:</span>
                  <span class="value-text">{{ item.engineerIncharge?.firstName }} {{ item.engineerIncharge?.lastName }}</span>
                </div>
                <div class="info-group">
                  <span class="info-label">Email:</span> <span class="value-text">{{ item.engineerIncharge?.email }}</span>
                </div>
              </div>

              <div class="section-container">
                <p class="heading-title">Team Leader</p>
                <div class="info-group">
                  <span class="info-label">Name:</span>
                  <span class="value-text">{{ item.teamLeader?.firstName }} {{ item.teamLeader?.lastName }}</span>
                </div>
                <div class="info-group">
                  <span class="info-label">Email:</span> <span class="value-text">{{ item.teamLeader?.email }}</span>
                </div>
              </div>
            </td>

            <td class="align-middle">
              <div class="info-group">
                <span class="info-label">Plant:</span> <b>{{ item.plant?.name }}</b>
              </div>
              <div class="info-group">
                <span class="info-label">Department:</span> <b>{{ item.department?.title }}</b>
              </div>
              <div class="info-group">
                <span class="info-label">Zone:</span> <b>{{ item.zone?.zoneName }}</b>
              </div>
              <div class="info-group">
                <span class="info-label">Zone Area:</span> <b>{{ item.zone?.zoneArea }}</b>
              </div>
              <div class="info-group">
                <span class="info-label">Exact Area:</span> <b>{{ item.exactArea }}</b>
              </div>
            </td>

            <td class="align-middle">
              <div class="info-group">
                <span class="info-label">Incident Date:</span> <b>{{ item.date }}</b>
              </div>
              <div class="info-group">
                <span class="info-label">Incident Time:</span> <b>{{ item.time }}</b>
              </div>
              <div class="info-group">
                <span class="info-label">Incident:</span> <b>{{ item.incidentTypeMaster?.incidentMaster?.title }}</b>
              </div>
              <div class="info-group">
                <span class="info-label">Incident Type:</span>
                <b>{{ item.incidentTypeMaster?.title }}</b>
              </div>
              <div class="info-group">
                <span class="info-label">Equipments:</span> <b>{{ item.equipement?.title }}</b>
              </div>
              <div class="info-group">
                <span class="info-label">Detail:</span> <b>{{ item.incidentDetail }}</b>
              </div>
              <div class="info-group">
                <span class="info-label">Zone:</span> <b>{{ item.zone?.zoneName }}</b>
              </div>
              <div class="info-group">
                <span class="info-label">Zone Area:</span> <b>{{ item.zone?.zoneArea }}</b>
              </div>
              <div class="info-group">
                <span class="info-label">Exact Area:</span> <b>{{ item.exactArea }}</b>
              </div>
              <div class="info-group">
                <span class="info-label">Deadline Date:</span> <b>{{ item.deadline }}</b>
              </div>
              <div *ngIf="item.incidentMasterId == 2">
                <button type="button" class="btn theme-button btn-sm btn-victim-data">Victim Data</button>
              </div>
            </td>

            <td class="text-center" *ngIf="showCreatedDate">
              <span class="text-danger">
                {{ item.createdTimestamp | date : 'dd-MM-yyyy' }}
              </span>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
  <div class="card-footer text-muted text-center">
    <app-pagination [currentPage]="currentPage" [totalItems]="totalItems" [itemsPerPage]="itemsPerPage"
      (pageChange)="onPageChange($event)"></app-pagination>
  </div>
</div>
<div *ngIf="selectedTabIndex == 4" class="card custom-card">
  <div class="card-header">
    <div class="row align-items-center">
      <div class="col">
        <h6 class="mb-0">Open Incident List</h6>
      </div>
      <div class="col text-end d-flex align-items-center justify-content-end">
        <!-- *** REPLACE the existing simple Download button with this Dropdown *** -->
        <div ngbDropdown class="d-inline-block me-2">
          <button type="button" class="btn btn-sm adani-btn dropdown-toggle"
            [id]="'downloadIncExcelDropdown' + selectedTabIndex" ngbDropdownToggle
            [disabled]="isDownloadingExcel || listLoading">
            <span *ngIf="!isDownloadingExcel">
              <i class="bi bi-file-earmark-excel me-1"></i> Download Excel
            </span>
            <span *ngIf="isDownloadingExcel">
              <span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
              Downloading {{ downloadType === 'current' ? 'Page' : (downloadType === 'all' ? 'All' : '') }}...
            </span>
          </button>
          <ul ngbDropdownMenu [attr.aria-labelledby]="'downloadIncExcelDropdown' + selectedTabIndex">
            <li>
              <button type="button" ngbDropdownItem (click)="downloadExcel('current')"
                [disabled]="isDownloadingExcel || listLoading || (getCurrentListData()?.length ?? 0) === 0">
                <i class="bi bi-download me-1"></i> Download Current Page ({{ getCurrentListData()?.length ?? 0 }})
              </button>
            </li>
            <li>
              <button type="button" ngbDropdownItem (click)="downloadExcel('all')"
                [disabled]="isDownloadingExcel || listLoading || totalItems === 0">
                <i class="bi bi-cloud-download me-1"></i> Download All Filtered ({{ totalItems }})
              </button>
            </li>
          </ul>
        </div>
        <button type="button" class="btn btn-link p-0 ms-3" (click)="openFilterModal()">
          <img src="../../../assets/svg/filter.svg" class="filter-button" alt="Filter" />
        </button>
      </div>
    </div>
  </div>
  <div class="card-body">
    <div class="table-responsive">
      <table class="table table-bordered custom-table">
        <thead class="table-header">
          <tr>
            <th scope="col" class="text-center">Actions</th>
            <th scope="col" class="text-center">Incident By</th>
            <th scope="col" class="text-center">Incident Location</th>
            <th scope="col" class="text-center">Incident Details</th>
            <th scope="col" class="text-center" *ngIf="showCreatedDate">Created Date</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngIf="listLoading">
            <td [attr.colspan]="showCreatedDate ? 6 : 5" class="text-center p-4">
              <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
              Loading {{ tabs[selectedTabIndex]?.title?.toLowerCase() ?? 'incidents' }}...
            </td>
          </tr>
          <tr *ngIf="!listLoading && getCurrentListData()?.length === 0">
            <td [attr.colspan]="showCreatedDate ? 6 : 5" class="text-center p-4 text-muted">
              No {{ tabs[selectedTabIndex]?.title?.toLowerCase() ?? 'incidents' }} found matching the criteria.
            </td>
          </tr>
          <tr *ngFor="let item of capaSubmittedList">
            <td class="text-center align-middle actions">
              <button type="button" class="btn theme-button btn-sm" (click)="openViewMembersModal(item)">Members</button>
              <br /><br />
              <a href="/incident/capaStatus/{{ item.id }}?tab=open">
                <button type="button" class="btn theme-button btn-sm btn-capa-status">View CAPA Status</button>
              </a>
            </td>

            <td class="align-middle">
              <p class="heading-title"><b>Reported By:</b></p>
              <div class="info-group">
                <span class="info-label">Id:</span> <span>{{ item.admin?.id }}</span>
              </div>
              <div class="info-group">
                <span class="info-label">Name:</span>
                <span>{{ item.admin?.firstName }} {{ item.admin?.lastName }}</span>
              </div>
              <div class="info-group">
                <span class="info-label">Email:</span> <span>{{ item.admin?.email }}</span>
              </div>

              <p class="heading-title"><b>Plant Admin :</b></p>
              <div class="info-group">
                <span class="info-label">Id:</span> <span>{{ item.engineerIncharge?.id }}</span>
              </div>
              <div class="info-group">
                <span class="info-label">Name:</span>
                <span>{{ item.engineerIncharge?.firstName }} {{ item.engineerIncharge?.lastName }}</span>
              </div>
              <div class="info-group">
                <span class="info-label">Email:</span> <span>{{ item.engineerIncharge?.email }}</span>
              </div>

              <p class="heading-title"><b>Team Leader :</b></p>
              <div class="info-group">
                <span class="info-label">Id:</span> <span>{{ item.teamLeader?.id }}</span>
              </div>
              <div class="info-group">
                <span class="info-label">Name:</span>
                <span>{{ item.teamLeader?.firstName }} {{ item.teamLeader?.lastName }}</span>
              </div>
              <div class="info-group">
                <span class="info-label">Email:</span> <span>{{ item.teamLeader?.email }}</span>
              </div>
            </td>

            <td class="align-middle">
              <div class="info-group">
                <span class="info-label">Plant:</span> <b>{{ item.plant?.name }}</b>
              </div>
              <div class="info-group">
                <span class="info-label">Department:</span> <b>{{ item.department?.title }}</b>
              </div>
              <div class="info-group">
                <span class="info-label">Zone:</span> <b>{{ item.zone?.zoneName }}</b>
              </div>
              <div class="info-group">
                <span class="info-label">Zone Area:</span> <b>{{ item.zone?.zoneArea }}</b>
              </div>
              <div class="info-group">
                <span class="info-label">Exact Area:</span> <b>{{ item.exactArea }}</b>
              </div>
            </td>

            <td class="align-middle">
              <div class="info-group">
                <span class="info-label">Incident Date:</span> <b>{{ item.date }}</b>
              </div>
              <div class="info-group">
                <span class="info-label">Incident Time:</span> <b>{{ item.time }}</b>
              </div>
              <div class="info-group">
                <span class="info-label">Incident:</span> <b>{{ item.incidentTypeMaster?.incidentMaster?.title }}</b>
              </div>
              <div class="info-group">
                <span class="info-label">Incident Type:</span>
                <b>{{ item.incidentTypeMaster?.title }}</b>
              </div>
              <div class="info-group">
                <span class="info-label">Equipments:</span> <b>{{ item.equipement?.title }}</b>
              </div>
              <div class="info-group">
                <span class="info-label">Detail:</span> <b>{{ item.incidentDetail }}</b>
              </div>
              <div class="info-group">
                <span class="info-label">Zone:</span> <b>{{ item.zone?.zoneName }}</b>
              </div>
              <div class="info-group">
                <span class="info-label">Zone Area:</span> <b>{{ item.zone?.zoneArea }}</b>
              </div>
              <div class="info-group">
                <span class="info-label">Exact Area:</span> <b>{{ item.exactArea }}</b>
              </div>
              <div class="info-group">
                <span class="info-label">Deadline Date:</span> <b>{{ item.deadline }}</b>
              </div>
              <div *ngIf="item.incidentMasterId == 2">
                <button type="button" class="btn theme-button btn-sm btn-victim-data">Victim Data</button>
              </div>
            </td>

            <td class="text-center" *ngIf="showCreatedDate">
              <span class="text-danger">
                {{ item.createdTimestamp | date : 'dd-MM-yyyy' }}
              </span>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
  <div class="card-footer text-muted text-center">
    <app-pagination [currentPage]="currentPage" [totalItems]="totalItems" [itemsPerPage]="itemsPerPage"
      (pageChange)="onPageChange($event)"></app-pagination>
  </div>
</div>
<div *ngIf="selectedTabIndex == 5" class="card custom-card">
  <div class="card-header">
    <div class="row align-items-center">
      <div class="col">
        <h6 class="mb-0">Closed Incident List</h6>
      </div>
      <div class="col text-end d-flex align-items-center justify-content-end">
        <!-- *** REPLACE the existing simple Download button with this Dropdown *** -->
        <div ngbDropdown class="d-inline-block me-2">
          <button type="button" class="btn btn-sm adani-btn dropdown-toggle"
            [id]="'downloadIncExcelDropdown' + selectedTabIndex" ngbDropdownToggle
            [disabled]="isDownloadingExcel || listLoading">
            <span *ngIf="!isDownloadingExcel">
              <i class="bi bi-file-earmark-excel me-1"></i> Download Excel
            </span>
            <span *ngIf="isDownloadingExcel">
              <span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
              Downloading {{ downloadType === 'current' ? 'Page' : (downloadType === 'all' ? 'All' : '') }}...
            </span>
          </button>
          <ul ngbDropdownMenu [attr.aria-labelledby]="'downloadIncExcelDropdown' + selectedTabIndex">
            <li>
              <button type="button" ngbDropdownItem (click)="downloadExcel('current')"
                [disabled]="isDownloadingExcel || listLoading || (getCurrentListData()?.length ?? 0) === 0">
                <i class="bi bi-download me-1"></i> Download Current Page ({{ getCurrentListData()?.length ?? 0 }})
              </button>
            </li>
            <li>
              <button type="button" ngbDropdownItem (click)="downloadExcel('all')"
                [disabled]="isDownloadingExcel || listLoading || totalItems === 0">
                <i class="bi bi-cloud-download me-1"></i> Download All Filtered ({{ totalItems }})
              </button>
            </li>
          </ul>
        </div>
        <button type="button" class="btn btn-link p-0 ms-3" (click)="openFilterModal()">
          <img src="../../../assets/svg/filter.svg" class="filter-button" alt="Filter" />
        </button>
      </div>
    </div>
  </div>
  <div class="card-body">
    <div class="table-responsive">
      <table class="table table-bordered custom-table">
        <thead class="table-header">
          <tr>
            <th scope="col" class="text-center">Actions</th>
            <th scope="col" class="text-center">Incident By</th>
            <th scope="col" class="text-center">Incident Location</th>
            <th scope="col" class="text-center">Incident Details</th>
            <th scope="col" class="text-center" *ngIf="showCreatedDate">Created Date</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngIf="listLoading">
            <td [attr.colspan]="showCreatedDate ? 6 : 5" class="text-center p-4">
              <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
              Loading {{ tabs[selectedTabIndex]?.title?.toLowerCase() ?? 'incidents' }}...
            </td>
          </tr>
          <tr *ngIf="!listLoading && getCurrentListData()?.length === 0">
            <td [attr.colspan]="showCreatedDate ? 6 : 5" class="text-center p-4 text-muted">
              No {{ tabs[selectedTabIndex]?.title?.toLowerCase() ?? 'incidents' }} found matching the criteria.
            </td>
          </tr>
          <tr *ngFor="let item of closedIncidentsList">
            <td class="text-center align-middle actions">
              <button type="button" class="btn theme-button btn-sm" (click)="openViewMembersModal(item)">Members</button>
              <br /><br />
              <a href="/incident/capaStatus/{{ item.id }}?tab=close">
                <button type="button" class="btn theme-button btn-sm btn-capa-status">View CAPA Status</button>
              </a>
            </td>

            <td class="align-middle">
              <p class="heading-title"><b>Reported By:</b></p>
              <div class="info-group">
                <span class="info-label">Id:</span> <span>{{ item.admin?.id }}</span>
              </div>
              <div class="info-group">
                <span class="info-label">Name:</span>
                <span>{{ item.admin?.firstName }} {{ item.admin?.lastName }}</span>
              </div>
              <div class="info-group">
                <span class="info-label">Email:</span> <span>{{ item.admin?.email }}</span>
              </div>

              <p class="heading-title"><b>Plant Admin :</b></p>
              <div class="info-group">
                <span class="info-label">Id:</span> <span>{{ item.engineerIncharge?.id }}</span>
              </div>
              <div class="info-group">
                <span class="info-label">Name:</span>
                <span>{{ item.engineerIncharge?.firstName }} {{ item.engineerIncharge?.lastName }}</span>
              </div>
              <div class="info-group">
                <span class="info-label">Email:</span> <span>{{ item.engineerIncharge?.email }}</span>
              </div>

              <p class="heading-title"><b>Team Leader :</b></p>
              <div class="info-group">
                <span class="info-label">Id:</span> <span>{{ item.teamLeader?.id }}</span>
              </div>
              <div class="info-group">
                <span class="info-label">Name:</span>
                <span>{{ item.teamLeader?.firstName }} {{ item.teamLeader?.lastName }}</span>
              </div>
              <div class="info-group">
                <span class="info-label">Email:</span> <span>{{ item.teamLeader?.email }}</span>
              </div>
            </td>

            <td class="align-middle">
              <div class="info-group">
                <span class="info-label">Plant:</span> <b>{{ item.plant?.name }}</b>
              </div>
              <div class="info-group">
                <span class="info-label">Department:</span> <b>{{ item.department?.title }}</b>
              </div>
              <div class="info-group">
                <span class="info-label">Zone:</span> <b>{{ item.zone?.zoneName }}</b>
              </div>
              <div class="info-group">
                <span class="info-label">Zone Area:</span> <b>{{ item.zone?.zoneArea }}</b>
              </div>
              <div class="info-group">
                <span class="info-label">Exact Area:</span> <b>{{ item.exactArea }}</b>
              </div>
            </td>

            <td class="align-middle">
              <div class="info-group">
                <span class="info-label">Incident Date:</span> <b>{{ item.date }}</b>
              </div>
              <div class="info-group">
                <span class="info-label">Incident Time:</span> <b>{{ item.time }}</b>
              </div>
              <div class="info-group">
                <span class="info-label">Incident:</span> <b>{{ item.incidentTypeMaster?.incidentMaster?.title }}</b>
              </div>
              <div class="info-group">
                <span class="info-label">Incident Type:</span>
                <b>{{ item.incidentTypeMaster?.title }}</b>
              </div>
              <div class="info-group">
                <span class="info-label">Equipments:</span> <b>{{ item.equipement?.title }}</b>
              </div>
              <div class="info-group">
                <span class="info-label">Detail:</span> <b>{{ item.incidentDetail }}</b>
              </div>
              <div class="info-group">
                <span class="info-label">Zone:</span> <b>{{ item.zone?.zoneName }}</b>
              </div>
              <div class="info-group">
                <span class="info-label">Zone Area:</span> <b>{{ item.zone?.zoneArea }}</b>
              </div>
              <div class="info-group">
                <span class="info-label">Exact Area:</span> <b>{{ item.exactArea }}</b>
              </div>
              <div class="info-group">
                <span class="info-label">Deadline Date:</span> <b>{{ item.deadline }}</b>
              </div>
              <div *ngIf="item.incidentMasterId == 2">
                <button type="button" class="btn theme-button btn-sm btn-victim-data">Victim Data</button>
              </div>
            </td>

            <td class="text-center" *ngIf="showCreatedDate">
              <span class="text-danger">
                {{ item.createdTimestamp | date : 'dd-MM-yyyy' }}
              </span>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
  <div class="card-footer text-muted text-center">
    <app-pagination [currentPage]="currentPage" [totalItems]="totalItems" [itemsPerPage]="itemsPerPage"
      (pageChange)="onPageChange($event)"></app-pagination>
  </div>
</div>
<div *ngIf="selectedTabIndex == 6" class="card custom-card">
  <div class="card-header">
    <div class="row align-items-center">
      <div class="col">
        <h6 class="mb-0">Rejected Incident List</h6>
      </div>
      <div class="col text-end d-flex align-items-center justify-content-end">
        <!-- *** REPLACE the existing simple Download button with this Dropdown *** -->
        <div ngbDropdown class="d-inline-block me-2">
          <button type="button" class="btn btn-sm adani-btn dropdown-toggle"
            [id]="'downloadIncExcelDropdown' + selectedTabIndex" ngbDropdownToggle
            [disabled]="isDownloadingExcel || listLoading">
            <span *ngIf="!isDownloadingExcel">
              <i class="bi bi-file-earmark-excel me-1"></i> Download Excel
            </span>
            <span *ngIf="isDownloadingExcel">
              <span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
              Downloading {{ downloadType === 'current' ? 'Page' : (downloadType === 'all' ? 'All' : '') }}...
            </span>
          </button>
          <ul ngbDropdownMenu [attr.aria-labelledby]="'downloadIncExcelDropdown' + selectedTabIndex">
            <li>
              <button type="button" ngbDropdownItem (click)="downloadExcel('current')"
                [disabled]="isDownloadingExcel || listLoading || (getCurrentListData()?.length ?? 0) === 0">
                <i class="bi bi-download me-1"></i> Download Current Page ({{ getCurrentListData()?.length ?? 0 }})
              </button>
            </li>
            <li>
              <button type="button" ngbDropdownItem (click)="downloadExcel('all')"
                [disabled]="isDownloadingExcel || listLoading || totalItems === 0">
                <i class="bi bi-cloud-download me-1"></i> Download All Filtered ({{ totalItems }})
              </button>
            </li>
          </ul>
        </div>
        <button type="button" class="btn btn-link p-0 ms-3" (click)="openFilterModal()">
          <img src="../../../assets/svg/filter.svg" class="filter-button" alt="Filter" />
        </button>
      </div>
    </div>
  </div>
  <div class="card-body">
    <div class="table-responsive">
      <table class="table table-bordered custom-table">
        <thead class="table-header">
          <tr>
            <th scope="col" class="text-center">Actions</th>
            <th scope="col" class="text-center">Incident By</th>
            <th scope="col" class="text-center">Incident Location</th>
            <th scope="col" class="text-center">Incident Details</th>
            <th scope="col" class="text-center" *ngIf="showCreatedDate">Created Date</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngIf="listLoading">
            <td [attr.colspan]="showCreatedDate ? 6 : 5" class="text-center p-4">
              <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
              Loading {{ tabs[selectedTabIndex]?.title?.toLowerCase() ?? 'incidents' }}...
            </td>
          </tr>
          <tr *ngIf="!listLoading && getCurrentListData()?.length === 0">
            <td [attr.colspan]="showCreatedDate ? 6 : 5" class="text-center p-4 text-muted">
              No {{ tabs[selectedTabIndex]?.title?.toLowerCase() ?? 'incidents' }} found matching the criteria.
            </td>
          </tr>
          <tr *ngFor="let item of rejectedIncidentsList">
            <td class="text-center align-middle actions">
              <button type="button" class="btn adani-btn" (click)="openViewMembersModal(item)">Members</button>
              <br /><br />
              <a>
                <button type="button" class="btn theme-button btn-warning btn-sm" (click)="openAssignTeamModal(item)">Assign Team</button>
              </a>
            </td>

            <td class="align-middle">
              <div class="section-container">
                <p class="heading-title">Reported By</p>
                <div class="info-group">
                  <span class="info-label">Name:</span>
                  <span class="value-text">{{ item.admin?.firstName }} {{ item.admin?.lastName }}</span>
                </div>
                <div class="info-group">
                  <span class="info-label">Email:</span> <span class="value-text">{{ item.admin?.email }}</span>
                </div>
              </div>

              <div class="section-container" *ngIf="item.engineerIncharge">
                <p class="heading-title">Plant Admin</p>
                <div class="info-group">
                  <span class="info-label">Name:</span>
                  <span class="value-text">{{ item.engineerIncharge?.firstName }} {{ item.engineerIncharge?.lastName }}</span>
                </div>
                <div class="info-group">
                  <span class="info-label">Email:</span> <span class="value-text">{{ item.engineerIncharge?.email }}</span>
                </div>
              </div>

              <div class="section-container" *ngIf="item.teamLeader">
                <p class="heading-title">Team Leader</p>
                <div class="info-group">
                  <span class="info-label">Name:</span>
                  <span class="value-text">{{ item.teamLeader?.firstName }} {{ item.teamLeader?.lastName }}</span>
                </div>
                <div class="info-group">
                  <span class="info-label">Email:</span> <span class="value-text">{{ item.teamLeader?.email }}</span>
                </div>
              </div>
            </td>

            <td class="align-middle">
              <div class="info-group">
                <span class="info-label">Plant:</span> <b>{{ item.plant?.name }}</b>
              </div>
              <div class="info-group">
                <span class="info-label">Department:</span> <b>{{ item.department?.title }}</b>
              </div>
              <div class="info-group">
                <span class="info-label">Zone:</span> <b>{{ item.zone?.zoneName }}</b>
              </div>
              <div class="info-group">
                <span class="info-label">Zone Area:</span> <b>{{ item.zone?.zoneArea }}</b>
              </div>
              <div class="info-group">
                <span class="info-label">Exact Area:</span> <b>{{ item.exactArea }}</b>
              </div>
            </td>

            <td class="align-middle">
              <div class="info-group">
                <span class="info-label">Incident Date:</span> <b>{{ item.date }}</b>
              </div>
              <div class="info-group">
                <span class="info-label">Incident Time:</span> <b>{{ item.time }}</b>
              </div>
              <div class="info-group">
                <span class="info-label">Incident:</span> <b>{{ item.incidentTypeMaster?.incidentMaster?.title }}</b>
              </div>
              <div class="info-group">
                <span class="info-label">Incident Type:</span>
                <b>{{ item.incidentTypeMaster?.title }}</b>
              </div>
              <div class="info-group">
                <span class="info-label">Equipments:</span> <b>{{ item.equipement?.title }}</b>
              </div>
              <div class="info-group">
                <span class="info-label">Detail:</span>
                <div class="detail-text">
                  <span class="value-text">{{ item.incidentDetail }}</span>
                </div>
              </div>
              <div class="info-group">
                <span class="info-label">Zone:</span> <b>{{ item.zone?.zoneName }}</b>
              </div>
              <div class="info-group">
                <span class="info-label">Zone Area:</span> <b>{{ item.zone?.zoneArea }}</b>
              </div>
              <div class="info-group">
                <span class="info-label">Exact Area:</span> <b>{{ item.exactArea }}</b>
              </div>
              <div class="info-group">
                <span class="info-label">Deadline Date:</span> <b>{{ item.deadline }}</b>
              </div>
              <div *ngIf="item.incidentMasterId == 2">
                <button type="button" class="btn theme-button btn-sm btn-victim-data">Victim Data</button>
              </div>
            </td>

            <td class="text-center" *ngIf="showCreatedDate">
              <span class="text-danger">
                {{ item.createdTimestamp | date : 'dd-MM-yyyy' }}
              </span>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
  <div class="card-footer text-muted text-center">
    <app-pagination [currentPage]="currentPage" [totalItems]="totalItems" [itemsPerPage]="itemsPerPage"
      (pageChange)="onPageChange($event)"></app-pagination>
  </div>
</div>
<!-- Filter Offcanvas -->
<app-offcanvas [title]="'Filter Incidents'" *ngIf="isFilterModalOpen" (onClickCross)="closeFilterModal()">
  <div class="filter-container p-3"> <!-- Added padding -->
    <form #filterForm="ngForm" (ngSubmit)="applyFilters()"> <!-- Add form -->
      <div class="row g-3"> <!-- Use g-3 -->

        <div class="col-12">
          <label class="form-label" for="filterFirstName">Reporter First Name</label>
          <input type="text" id="filterFirstName" class="form-control" placeholder="First Name"
            [(ngModel)]="filters.firstName" name="firstName" maxlength="30" pattern="^[a-zA-Z\s]*$"
            #firstName="ngModel" [ngClass]="{'is-invalid': firstName.invalid && (firstName.dirty || firstName.touched)}"
            (blur)="trimInputField(filters, 'firstName')">
          <div *ngIf="firstName.invalid && (firstName.dirty || firstName.touched)" class="invalid-feedback">
            <div *ngIf="firstName.errors?.['maxlength']">First name cannot exceed 30 characters.</div>
            <div *ngIf="firstName.errors?.['pattern']">First name should contain only alphabets.</div>
          </div>
          <small *ngIf="filters && filters.firstName" class="text-muted">
            {{ filters.firstName.length }}/30 characters
          </small>
        </div>

        <div class="col-12">
          <label class="form-label" for="filterLastName">Reporter Last Name</label>
          <input type="text" id="filterLastName" class="form-control" placeholder="Last Name"
            [(ngModel)]="filters.lastName" name="lastName" maxlength="30" pattern="^[a-zA-Z\s]*$"
            #lastName="ngModel" [ngClass]="{'is-invalid': lastName.invalid && (lastName.dirty || lastName.touched)}"
            (blur)="trimInputField(filters, 'lastName')">
          <div *ngIf="lastName.invalid && (lastName.dirty || lastName.touched)" class="invalid-feedback">
            <div *ngIf="lastName.errors?.['maxlength']">Last name cannot exceed 30 characters.</div>
            <div *ngIf="lastName.errors?.['pattern']">Last name should contain only alphabets.</div>
          </div>
          <small *ngIf="filters && filters.lastName" class="text-muted">
            {{ filters.lastName.length }}/30 characters
          </small>
        </div>

        <div class="col-12">
          <label class="form-label" for="filterEmail">Reporter Email</label>
          <input type="email" id="filterEmail" class="form-control" placeholder="Email with &#64;adani.com domain"
            [(ngModel)]="filters.email" name="email" email pattern="[a-zA-Z0-9._%+-]+&#64;adani\.com$"
            #email="ngModel" [ngClass]="{'is-invalid': email.invalid && (email.dirty || email.touched)}"
            (blur)="trimInputField(filters, 'email')">
          <div *ngIf="email.invalid && (email.dirty || email.touched)" class="invalid-feedback">
            <div *ngIf="email.errors?.['email']">Please enter a valid email address.</div>
            <div *ngIf="email.errors?.['pattern']">Email must use the &#64;adani.com domain.</div>
          </div>
          <small *ngIf="filters && filters.email" class="text-muted">
            {{ filters.email.length }} characters
          </small>
        </div>

        <div class="col-12">
          <label class="form-label" for="filterContactNumber">Reporter Contact Number</label>
          <input type="text" id="filterContactNumber" class="form-control" placeholder="Contact Number"
            [(ngModel)]="filters.contactNumber" name="contactNumber" pattern="^[0-9]{10}$" maxlength="10"
            #contactNumber="ngModel" [ngClass]="{'is-invalid': contactNumber.invalid && (contactNumber.dirty || contactNumber.touched)}"
            (blur)="trimInputField(filters, 'contactNumber')">
          <div *ngIf="contactNumber.invalid && (contactNumber.dirty || contactNumber.touched)" class="invalid-feedback">
            <div *ngIf="contactNumber.errors?.['pattern']">Please enter a valid 10-digit number.</div>
          </div>
          <small *ngIf="filters && filters.contactNumber" class="text-muted">
            {{ filters.contactNumber.length }}/10 characters
          </small>
        </div>

        <div class="col-12">
          <label class="form-label" for="filterStartDate">Date Range (Created)</label>
          <div class="input-group">
            <input type="date" id="filterStartDate" class="form-control" aria-label="Start Date" [(ngModel)]="filters.startDate"
              name="startDate" #startDate="ngModel"
              [ngClass]="{'is-invalid': startDate.invalid && (startDate.dirty || startDate.touched)}" />
            <span class="input-group-text">to</span>
            <input type="date" id="filterEndDate" class="form-control" aria-label="End Date" [(ngModel)]="filters.endDate"
              name="endDate" #endDate="ngModel"
              [ngClass]="{'is-invalid': endDate.invalid && (endDate.dirty || endDate.touched)}" />
          </div>
          <div *ngIf="endDate.value && startDate.value && endDate.value < startDate.value" class="text-danger small mt-1">
            End date cannot be earlier than start date.
          </div>
          <small class="text-muted">Filters incidents created within this date range.</small>
        </div>

        <div class="col-12">
          <label class="form-label" for="filterPlantInc">
             <!-- *** UPDATED Label based on role *** -->
            {{ currentUserRole === componentRoles.PLANT_ADMIN ? 'Select Your Plant' : 'Select Plant' }}
          </label>
          <select id="filterPlantInc" class="form-select"
                  [(ngModel)]="filters.plantId"
                  (ngModelChange)="onFilterPlantSelectionChange($event)"
                  name="plantId">
             <!-- *** REMOVED [disabled]="isPlantFilterDisabled" *** -->

             <!-- *** UPDATED Default Options *** -->
            <!-- Show 'All Plants' only for Super Admin -->
            <option [ngValue]="null" *ngIf="currentUserRole !== componentRoles.PLANT_ADMIN">All Plants</option>
            <!-- Default selection prompt for Plant Admin -->
            <option [ngValue]="null" *ngIf="currentUserRole === componentRoles.PLANT_ADMIN">-- Select Your Plant (or All Assigned) --</option>

            <!-- availablePlants is filtered correctly in TS -->
            <option *ngFor="let plant of availablePlants" [value]="plant.id">{{ plant.name }}</option>

            <!-- Message if Plant Admin has no plants -->
            <option [ngValue]="null" *ngIf="currentUserRole === componentRoles.PLANT_ADMIN && availablePlants.length === 0" disabled>No plants assigned or available</option>
          </select>
        </div>

        <div class="col-12">
          <label class="form-label" for="filterZoneInc">Select Zone</label>
          <!-- *** UPDATED [disabled] condition for Zone *** -->
          <select id="filterZoneInc" class="form-select"
                  [(ngModel)]="filters.zoneId"
                  name="zone"
                  [disabled]="!filters.plantId"> <!-- Disable only if NO plant is selected in the filter -->
             <!-- *** UPDATED Default Option Text *** -->
            <option [ngValue]="null">
                {{ !filters.plantId ? 'Select Plant First' : (availableZones.length > 0 ? '-- All Zones --' : '-- No zones found --') }}
            </option>
            <option *ngFor="let zone of availableZones" [value]="zone.id">{{ zone.zoneName }}</option>
          </select>
        </div>

        <div class="col-12">
          <label class="form-label" for="filterDeptInc">Select Department</label>
          <select id="filterDeptInc" class="form-select" [(ngModel)]="filters.departmentId" name="department">
            <option [ngValue]="null">All Departments</option>
            <option *ngFor="let dept of availableDepartments" [value]="dept.id">{{ dept.title }}</option>
            <!-- Adjust value if needed -->
          </select>
        </div>

        <div class="col-12">
          <label class="form-label" for="filterEnabledInc">Enabled Status</label>
          <select id="filterEnabledInc" class="form-select" [(ngModel)]="filters.enabled" name="enabled">
            <option [ngValue]="null">Any</option>
            <option value="true">Yes</option>
            <option value="false">No</option>
          </select>
        </div>

        <div class="col-12">
          <label class="form-label" for="filterSortByInc">Sort By</label>
          <select id="filterSortByInc" class="form-select" [(ngModel)]="filters.sortField" name="sortField">
            <option [ngValue]="null">Default Sort (ID DESC)</option>
            <option *ngFor="let field of availableSortFields" [value]="field.value">{{ field.label }}</option>
          </select>
          <label class="form-label mt-2" for="filterSortDirInc">Sort Direction</label>
          <select id="filterSortDirInc" class="form-select" [(ngModel)]="filters.sortDirection" name="sortDirection">
            <option value="DESC">Descending</option>
            <option value="ASC">Ascending</option>
          </select>
        </div>

        <div class="col-12 mt-4 d-grid gap-2">
          <button type="submit" class="btn adani-btn" [disabled]="filterForm.invalid || (endDate.value && startDate.value && endDate.value < startDate.value)">
            <i class="bi bi-search me-1"></i> Search
          </button>
          <button type="button" class="btn btn-secondary" (click)="resetFilters()">
            <i class="bi bi-arrow-clockwise me-1"></i> Reset Filters
          </button>
        </div>
      </div>
    </form>
  </div>
</app-offcanvas>

<!-- *** UPDATED: Assign Team Offcanvas *** -->
<app-offcanvas [title]="'Assign Incident Team'" [width]="'500'" *ngIf="isAssignTeamModalOpen"
  (onClickCross)="closeAssignTeamModal()">
  <div class="assign-team-container p-3">
    <div *ngIf="!selectedIncidentForAssignment && isAssignTeamModalOpen" class="text-center p-5">
      <div class="spinner-border spinner-border-sm"></div> Loading...
    </div>

    <form *ngIf="selectedIncidentForAssignment as incident" #assignTeamForm="ngForm"
      (ngSubmit)="submitAssignTeamForm()">
      <h6 class="mb-1">Incident ID: {{ incident.id }}</h6>
      <p class="mb-3 small text-muted">Reported By: {{ incident.admin?.firstName }} {{ incident.admin?.lastName }}</p>

      <div class="row g-3">

        <!-- Member Multi-Select -->
        <div class="col-12">
          <label for="assignTeamMembersSelect" class="form-label">Select Team Members <span
              class="text-danger">*</span></label>
          <ng-select id="assignTeamMembersSelect" [items]="availableAdminsForAssign"
            bindLabel="firstName" bindValue="id" [multiple]="true" [closeOnSelect]="false" placeholder="Select Members"
            required [(ngModel)]="assignmentData.teamMemberIds" name="teamMemberIds" #teamMemberIdsCtrl="ngModel"
            (change)="handleMemberSelectionChange($event)"> <!-- Pass selected items array -->
            <!-- Custom Template for Option Display -->
            <ng-template ng-option-tmp let-item="item" let-index="index" let-search="searchTerm">
              <div>{{item.firstName}} {{item.lastName}}</div>
              <small class="text-muted">{{item.email}}</small>
            </ng-template>
          </ng-select>
          <div class="form-text">Hold Ctrl/Cmd to select multiple. At least one member (including leader) is required.
          </div>
          <!-- Use the template variable for error display -->
          <div *ngIf="teamMemberIdsCtrl.invalid && (teamMemberIdsCtrl.dirty || teamMemberIdsCtrl.touched)"
            class="text-danger small mt-1">
            <!-- Check specifically for the 'required' error -->
            <div *ngIf="teamMemberIdsCtrl.errors?.['required']">
              At least one member selection is required.
            </div>
            <!-- Add checks for other potential errors if needed -->
          </div>
        </div>

        <!-- Deadline -->
        <div class="col-12">
          <label for="assignDeadline" class="form-label">Investigation Deadline</label>
          <input type="date" id="assignDeadline" name="deadline" class="form-control"
            [(ngModel)]="assignmentData.deadline">
        </div>

        <!-- Assigned Members Table (Ensure it uses assignedTeamMembers) -->
        <div class="col-12 mt-3"
          *ngIf="assignmentData.assignedTeamMembers && assignmentData.assignedTeamMembers.length > 0">
          <!-- Check length -->
          <label class="form-label">Selected Team (Mark one as Leader)</label>
          <div class="table-responsive">
            <table class="table table-sm table-bordered table-hover">
              <thead class="table-light">
                <tr>
                  <th>Name</th>
                  <th>Email</th>
                  <th class="text-center">Action</th>
                </tr>
              </thead>
              <tbody>
                <!-- Iterate over assignedTeamMembers -->
                <tr *ngFor="let member of assignmentData.assignedTeamMembers" [class.table-info]="member.isLead">
                  <td>
                    {{ member.firstName }} {{ member.lastName }}
                    <span *ngIf="member.isLead" class="badge bg-primary ms-2">Lead</span>
                  </td>
                  <td>{{ member.email || 'N/A' }}</td>
                  <td class="text-center">
                    <button type="button" class="btn btn-sm btn-outline-primary" *ngIf="!member.isLead"
                      (click)="assignTeamLead(member.id)" title="Mark as Team Lead">
                      Mark as Lead
                    </button>
                    <!-- Optional: Add remove button? -->
                    <!-- <button type="button" class="btn btn-sm btn-outline-danger ms-1" (click)="removeMember(member.id)" title="Remove Member">X</button> -->
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
        <div *ngIf="!assignmentData.assignedTeamMembers || assignmentData.assignedTeamMembers.length === 0"
          class="col-12 mt-3">
          <p class="text-muted text-center">No members selected yet.</p>
        </div>


        <!-- Action Buttons -->
        <div class="col-12 mt-4 d-flex justify-content-end gap-2">
          <button type="button" class="btn btn-secondary" (click)="closeAssignTeamModal()">Cancel</button>
          <button type="submit" class="btn adani-btn"
            [disabled]="assignTeamForm.invalid || assignTeamLoading || !assignmentData.teamLeaderId">
            <!-- Disable if no leader -->
            <span *ngIf="!assignTeamLoading"><i class="bi bi-people-fill me-1"></i> Assign Team</span>
            <span *ngIf="assignTeamLoading" class="spinner-border spinner-border-sm"></span><span
              *ngIf="assignTeamLoading"> Assigning...</span>
          </button>
        </div>
      </div>
    </form>
  </div>
</app-offcanvas>
<!-- View Members Modal -->
<div class="modal fade" tabindex="-1" id="viewMembersModal" #viewMembersModalElement
  aria-labelledby="viewMembersModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg modal-dialog-centered"> <!-- Use modal-lg for more space -->
    <div class="modal-content">
      <div class="modal-header text-white">
        <h5 class="modal-title" id="viewMembersModalLabel">
          <i class="bi bi-people-fill me-2"></i> Assigned Team Members
        </h5>
        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"
          (click)="closeViewMembersModal()"></button>
      </div>
      <div class="modal-body">

        <div *ngIf="viewMembersLoading" class="text-center p-5">
          <div class="spinner-border text-info" role="status">
            <span class="visually-hidden">Loading...</span>
          </div>
          <p class="mt-2 text-muted">Loading member details...</p>
        </div>

        <div *ngIf="!viewMembersLoading">
          <p class="mb-3" *ngIf="incidentForViewingMembers">
            Viewing members for Incident ID: <strong>{{ incidentForViewingMembers.id }}</strong>
          </p>

          <div *ngIf="membersToDisplay && membersToDisplay.length > 0; else noMembersAssigned">
            <div class="table-responsive">
              <table class="table table-sm table-bordered table-hover">
                <thead class="table-light">
                  <tr>
                    <th>Name</th>
                    <th>Email</th>
                    <th>Contact Number</th>
                    <th class="text-center">Role</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let member of membersToDisplay" [class.table-info]="member.isLead">
                    <td>{{ member.firstName }} {{ member.lastName }}</td>
                    <td>{{ member.email || 'N/A' }}</td>
                    <td>{{ member.contactNumber || 'N/A' }}</td>
                    <td class="text-center">
                      <span *ngIf="member.isLead" class="badge bg-primary">Lead</span>
                      <span *ngIf="!member.isLead" class="badge bg-secondary">Member</span>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>

          <ng-template #noMembersAssigned>
            <div class="alert alert-warning text-center" role="alert">
              <i class="bi bi-exclamation-triangle me-2"></i> No team members found assigned to this incident.
            </div>
          </ng-template>
        </div>

      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"
          (click)="closeViewMembersModal()">Close</button>
      </div>
    </div>
  </div>
</div>
<!-- End View Members Modal -->
 <!-- Reject Confirmation Modal -->
<div class="modal fade" #rejectConfirmationModalElement id="rejectConfirmationModal" tabindex="-1" aria-labelledby="rejectConfirmationModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header bg-danger text-white"> <!-- Header styled for rejection -->
        <h5 class="modal-title" id="rejectConfirmationModalLabel">
           <i class="bi bi-exclamation-triangle-fill me-2"></i> Confirm Rejection
        </h5>
        <button type="button" class="btn-close btn-close-white" aria-label="Close" (click)="closeRejectConfirmation()"></button>
        <!-- Use (click) binding -->
      </div>
      <div class="modal-body">
        <p>Are you sure you want to <strong>reject</strong> Incident ID <strong>{{ incidentToReject?.id }}</strong>?</p>
        <!-- Display incident ID for clarity -->
         <p class="small text-muted" *ngIf="incidentToReject">Reported by: {{ incidentToReject?.admin?.firstName }} {{ incidentToReject?.admin?.lastName }}</p> <!-- Optional detail -->
      </div>
      <div class="modal-footer">
         <!-- Using standard Bootstrap buttons for confirmation -->
        <button type="button" class="btn btn-secondary" (click)="closeRejectConfirmation()">
            <i class="bi bi-x-circle me-1"></i> Cancel
        </button>
        <button type="button" class="btn btn-danger" (click)="confirmReject()">
            <i class="bi bi-check-circle me-1"></i> Yes, Reject
        </button>
      </div>
    </div>
  </div>
</div>
<!-- End Reject Confirmation Modal -->