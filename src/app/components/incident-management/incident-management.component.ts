import { CommonModule } from '@angular/common';
import { Component, OnInit, ViewChild, ChangeDetectorRef, inject, ElementRef, On<PERSON><PERSON>roy, AfterViewInit } from '@angular/core';
import { FormsModule, NgForm } from '@angular/forms';
import { IncidentManagementService } from '../../services/incident-management/incident-management.service';
import { createAxiosConfig } from '../../core/utilities/axios-param-config';
import { PaginationComponent } from "../../shared/pagination/pagination.component";
import { OffcanvasComponent } from "../../shared/offcanvas/offcanvas.component";
import { PlantManagementService } from '../../services/plant-management/plant-management.service';
import { ZoneService } from '../../services/zone/zone.service';
import { AdminService } from '../../services/admin/admin.service';
import { UpdateService } from '../../services/update/update.service';
import { NgSelectModule } from '@ng-select/ng-select';
import { TabComponent } from '../../shared/tab/tab.component';
import { ToastMessageComponent } from '../../shared/toast-message/toast-message.component';
import { NgbDropdownModule } from '@ng-bootstrap/ng-bootstrap';
import * as XLSX from 'xlsx';
import { Modal } from 'bootstrap'; // Ensure Modal from bootstrap is imported
import { DepartmentService } from '../../services/master-management/department/department.service';

// --- Define ROLES constant (adjust values if needed) ---
const ROLES = {
    SUPER_ADMIN: 'super_admin',
    PLANT_ADMIN: 'plant_admin',
};

// --- Interfaces ---

interface IncidentFilter {
    firstName?: string | null;
    lastName?: string | null;
    email?: string | null;
    contactNumber?: string | null;
    startDate?: string | null;
    endDate?: string | null;
    plantId?: number | null; // User selected plant (from filtered list if Plant Admin)
    zoneId?: number | null;
    departmentId?: number | null;
    enabled?: string | null;
    sortField?: string | null;
    sortDirection?: 'ASC' | 'DESC';
}

interface Plant {
    id: number;
    name: string;
}

interface Zone {
    id: number;
    zoneName: string;
    plantId?: number;
    zoneArea?: string | null
}

interface SimpleMaster {
    id: number;
    title: string;
}

interface AdminUser {
    id: number;
    firstName: string;
    lastName: string;
    contactNumber?: string | null;
    email?: string | null;
}

interface IncidentTypeMaster extends SimpleMaster {
    incidentMaster?: SimpleMaster | null;
}

interface Incident {
    member?: AdminUser[] | null; // Updated based on response example
    id: number;
    admin?: AdminUser | null; // Reported By
    engineerIncharge?: AdminUser | null;
    teamLeader?: AdminUser | null;
    teamLeaderId?: number | null;
    memberIds?: number[];
    plant?: Plant | null;
    department?: SimpleMaster | null;
    zone?: Zone | null;
    incidentTypeMaster?: IncidentTypeMaster | null;
    equipement?: SimpleMaster | null;
    date?: string | null;
    time?: string | null;
    exactArea?: string | null;
    incidentDetail?: string | null;
    deadline?: string | null;
    incidentMasterId?: number | null;
    status: IncidentStatus;
    createdTimestamp: string | Date;
    plantId?: number;
    adminId?: number;
    enabled?: boolean;
    image?: string[];
    updatedTimestamp?: string | Date;
    showFullDetail?: boolean; // For UI toggle of detail text
}

interface AssignedMember extends AdminUser {
    isLead?: boolean;
}

interface AssignTeamData {
    incidentId: number | null;
    teamLeaderId: number | null;
    teamMemberIds: number[] | null;
    deadline: string | null;
    assignedTeamMembers: AssignedMember[];
}

enum IncidentStatus {
    New = 0,
    PendingCAPA = 1,
    Rejected = 2,
    CAPASubmitted = 3,
    Open = 4,
    Revised = 5,
    Closed = 6
}


@Component({
    selector: 'app-incident-management',
    standalone: true,
    imports: [
        CommonModule,
        FormsModule,
        TabComponent,
        PaginationComponent,
        OffcanvasComponent,
        NgbDropdownModule,
        ToastMessageComponent,
        NgSelectModule
    ],
    templateUrl: './incident-management.component.html',
    styleUrls: ['./incident-management.component.scss']
})
export class IncidentManagementComponent implements OnInit, AfterViewInit, OnDestroy {
    // --- Expose ROLES constant to the template ---
    public componentRoles = ROLES;

    // --- ViewChild References ---
    @ViewChild('assignTeamForm') assignTeamForm?: NgForm;
    @ViewChild(ToastMessageComponent) toast!: ToastMessageComponent;
    @ViewChild('viewMembersModalElement') viewMembersModalElement!: ElementRef;
    @ViewChild('rejectConfirmationModalElement') rejectConfirmationModalElement!: ElementRef;

    // --- Modal Instances ---
    viewMembersModalInstance: Modal | null = null;
    rejectConfirmationModalInstance: Modal | null = null;

    // --- Component State ---
    listLoading = false;
    assignTeamLoading = false;
    viewMembersLoading = false;
    showCreatedDate: boolean = true; // Or set based on logic if needed
    isDownloadingExcel = false;
    downloadType: 'current' | 'all' | null = null;

    // Data Lists per Tab
    newIncidentList: Incident[] = [];
    pendingCAPAList: Incident[] = [];
    revisedList: Incident[] = [];
    capaSubmittedList: Incident[] = [];
    openIncidentsList: Incident[] = [];
    closedIncidentsList: Incident[] = [];
    rejectedIncidentsList: Incident[] = [];

    // Common Pagination & State
    currentPage = 1;
    itemsPerPage = 10;
    totalItems = 0;

    // Tab Configuration
    selectedTabIndex = 0;
    tabs = [
        { title: 'New Incident', status: IncidentStatus.New, listKey: 'newIncidentList' as const },
        { title: 'Pending CAPA', status: IncidentStatus.PendingCAPA, listKey: 'pendingCAPAList' as const },
        { title: 'Revised', status: IncidentStatus.Revised, listKey: 'revisedList' as const },
        { title: 'CAPA Submitted', status: IncidentStatus.CAPASubmitted, listKey: 'capaSubmittedList' as const },
        { title: 'Open Incidents', status: IncidentStatus.Open, listKey: 'openIncidentsList' as const },
        { title: 'Closed Incidents', status: IncidentStatus.Closed, listKey: 'closedIncidentsList' as const },
        { title: 'Rejected Incidents', status: IncidentStatus.Rejected, listKey: 'rejectedIncidentsList' as const }
    ];

    // Modal States
    isFilterModalOpen = false;
    isAssignTeamModalOpen = false;
    isViewMembersModalOpen = false;

    // --- Filter Properties ---
    filters: IncidentFilter = {
        firstName: null, lastName: null, email: null, contactNumber: null,
        startDate: null, endDate: null, plantId: null, zoneId: null, departmentId: null,
        enabled: 'true', // Defaulting to 'true' based on original filter
        sortField: 'id', sortDirection: 'DESC'
    };

    // --- Role-Based Access Control ---
    currentUserRole: string = '';
    loggedInAdminId: number | null = null;
    loggedInPlantIds: number[] = []; // Store all assigned plant IDs for Plant Admin

    // --- Data for Dropdowns ---
    availablePlants: Plant[] = []; // Filtered based on role
    availableZones: Zone[] = [];
    availableDepartments: SimpleMaster[] = []; // Using SimpleMaster interface
    availableAdminsForAssign: AdminUser[] = [];
    availableSortFields = [
        { value: 'id', label: 'Incident ID' }, { value: 'admin.firstName', label: 'Reporter Name' }, // Adjusted label
        { value: 'plant.name', label: 'Plant Name' }, { value: 'createdTimestamp', label: 'Created Date' }
    ];

    // --- Assignment State ---
    selectedIncidentForAssignment: Incident | null = null;
    assignmentData: AssignTeamData = this.getInitialAssignmentData();

    // --- View Members State ---
    incidentForViewingMembers: Incident | null = null;
    membersToDisplay: AssignedMember[] = []; // For view members modal

    // --- Rejection State ---
    incidentToReject: Incident | null = null;


    // --- Inject Services ---
    private incidentManagementService = inject(IncidentManagementService);
    private plantService = inject(PlantManagementService);
    private zoneService = inject(ZoneService);
    private adminService = inject(AdminService);
    private updateService = inject(UpdateService);
    private departmentService = inject(DepartmentService)
    private cdr = inject(ChangeDetectorRef);

    constructor() { }

    ngOnInit() {
        this.setCurrentUserRoleAndDetailsById();
        this.getPlants();
        this.getDepartments();
        this.loadIncidentsForCurrentTab();
    }

    ngAfterViewInit(): void {
        if (this.viewMembersModalElement) {
            this.viewMembersModalInstance = new Modal(this.viewMembersModalElement.nativeElement);
        } else { console.error("View Members modal element not found!"); }

        if (this.rejectConfirmationModalElement) {
            this.rejectConfirmationModalInstance = new Modal(this.rejectConfirmationModalElement.nativeElement);
        } else { console.error("Reject confirmation modal element not found!"); }
    }

    ngOnDestroy(): void {
        this.viewMembersModalInstance?.dispose();
        this.rejectConfirmationModalInstance?.dispose();
    }

    private setCurrentUserRoleAndDetailsById(): void {
        try {
            const userString = localStorage.getItem('user');
            if (!userString || userString.trim() === "" || ['null', 'undefined', '""', '" "'].includes(userString.trim().toLowerCase())) {
                console.error("User data not found or is invalid in localStorage.");
                this.currentUserRole = '';
                this.loggedInAdminId = null;
                this.loggedInPlantIds = [];
                this.toast?.showErrorToast("User session invalid. Please log in again.");
                return;
            }

            const currentUser = JSON.parse(userString);
            console.log('Current User Parsed for Role Check (Incident Mgmt):', currentUser);

            this.loggedInAdminId = currentUser?.id ?? null;

            this.loggedInPlantIds = (Array.isArray(currentUser?.plantIds) && currentUser.plantIds.length > 0)
                                      ? currentUser.plantIds.map((id: any) => Number(id)).filter((id: number) => !isNaN(id))
                                      : [];

            const roleId = currentUser?.adminsRoleId;

            if (roleId === 1 || roleId === 6) {
                this.currentUserRole = this.componentRoles.SUPER_ADMIN;
            } else if (roleId === 2) {
                this.currentUserRole = this.componentRoles.PLANT_ADMIN;
                if (this.loggedInPlantIds.length === 0) {
                    console.error(`Plant Admin (Role ID ${roleId}, User ID ${this.loggedInAdminId}) has no plants assigned!`);
                    this.toast?.showErrorToast("User configuration error: Plant Admin has no assigned plants.");
                }
            } else {
                console.error(`Invalid or missing adminsRoleId (${roleId}) for User ID ${this.loggedInAdminId}.`);
                this.currentUserRole = '';
                this.toast?.showErrorToast("User configuration error: Invalid role.");
            }

            console.log(`User Role Determined: ${this.currentUserRole || 'None'}, Plant IDs: [${this.loggedInPlantIds.join(', ')}]`);

        } catch (error) {
            console.error("Error parsing user data from localStorage:", error);
            this.currentUserRole = '';
            this.loggedInAdminId = null;
            this.loggedInPlantIds = [];
            this.toast?.showErrorToast("Error reading user session. Please log in again.");
        }
    }

    async getPlants() {
        const data = { sort: 'name,ASC', filter: ['enabled||eq||true'], limit: 1000 };
        const param = createAxiosConfig(data);
        try {
            console.log("Fetching plants for Incident Management...");
            const response = await this.plantService.getPlants(param);
            let allEnabledPlants: Plant[] = response?.data ?? response ?? [];
            console.log("Fetched all enabled plants:", allEnabledPlants.length);

            if (this.currentUserRole === this.componentRoles.PLANT_ADMIN && this.loggedInPlantIds.length > 0) {
                this.availablePlants = allEnabledPlants.filter(plant =>
                    this.loggedInPlantIds.includes(plant.id)
                );
                console.log(`Plant Admin: Filtered ${this.availablePlants.length} plants.`);
                 if (this.availablePlants.length !== this.loggedInPlantIds.length) {
                      console.warn(`Some assigned plants might be disabled or not found.`);
                 }
            } else if (this.currentUserRole === this.componentRoles.SUPER_ADMIN) {
                this.availablePlants = allEnabledPlants;
                console.log(`Super Admin: Showing all ${this.availablePlants.length} enabled plants.`);
            } else {
                this.availablePlants = [];
                console.log("No plants available for current user role/assignment.");
            }
        } catch (e) {
            console.error("Error fetching plants:", e);
            this.availablePlants = [];
            this.toast?.showErrorToast('Failed to load plants.');
        }
    }

    async getDepartments() {
        const requestData = { sort: 'title,ASC', filter: ['enabled||eq||true'], limit: 1000 };
        try {
            const params = createAxiosConfig(requestData);
            const response = await this.departmentService.getDepartments(params);
            this.availableDepartments = response?.data?.map((dept: any) => ({ id: dept.id, title: dept.title }))
                                       ?? response?.map((dept: any) => ({ id: dept.id, title: dept.title }))
                                       ?? [];
            console.log(`Loaded ${this.availableDepartments.length} departments.`);
        } catch (error) {
            console.error("Error fetching departments:", error);
            this.availableDepartments = [];
            this.toast?.showErrorToast('Failed to load departments.');
        }
    }

    async loadZones(plantId: number) {
        this.availableZones = [];
        if (!plantId) return;
        console.log(`Loading zones for selected plant: ${plantId}`);
        const data = { sort: 'zoneName,ASC', filter: [`plantId||eq||${plantId}`, 'enabled||eq||true'], limit: 1000 };
        const params = createAxiosConfig(data);
        try {
            const response = await this.zoneService.getZone(params);
            this.availableZones = response ?? [];
            console.log(`Loaded ${this.availableZones.length} zones for plant ${plantId}.`);
        } catch (e) {
            console.error(`Error fetching zones for plant ${plantId}:`, e);
            this.availableZones = [];
            this.toast?.showErrorToast('Failed to load zones for the selected plant.');
        }
    }

    async loadAdminsForAssignmentDropdown(plantId: number): Promise<void> {
        console.log('Loading admins for assignment for Plant ID:', plantId);
        this.availableAdminsForAssign = [];
        if (!plantId) return;

        const filterParams: string[] = [
            'enabled||eq||true',
            'status||eq||1',
            'adminsRoleId||ne||1',
            `plant.id||$eq||${plantId}`
        ];
        const data = { limit: 1000, sort: 'firstName,ASC', filter: filterParams };
        const param = createAxiosConfig(data);

        try {
            console.log("Fetching Admins for Assignment Params:", param);
            const response = await this.adminService.getAdmin(param);
            this.availableAdminsForAssign = response?.data ?? response ?? [];
            console.log(`Loaded ${this.availableAdminsForAssign.length} admins for assignment.`);
        } catch (error) {
            console.error("Error fetching admins for assignment dropdown:", error);
            this.availableAdminsForAssign = [];
            this.toast?.showErrorToast('Failed to load eligible admins for assignment.');
        }
    }

    openFilterModal(): void {
        this.isFilterModalOpen = true;
        if (this.filters.plantId) {
            this.loadZones(this.filters.plantId);
        }
    }

    closeFilterModal(): void { this.isFilterModalOpen = false; }

    applyFilters(): void {
        // Trim string values to prevent whitespace-only searches
        if (this.filters.firstName) {
            this.filters.firstName = this.filters.firstName.trim();
        }
        if (this.filters.lastName) {
            this.filters.lastName = this.filters.lastName.trim();
        }
        if (this.filters.email) {
            this.filters.email = this.filters.email.trim();
        }
        if (this.filters.contactNumber) {
            this.filters.contactNumber = this.filters.contactNumber.trim();
        }

        // Check for date range validity
        if (this.filters.startDate && this.filters.endDate) {
            const startDate = new Date(this.filters.startDate);
            const endDate = new Date(this.filters.endDate);
            if (endDate < startDate) {
                this.toast?.showErrorToast("End date cannot be earlier than start date.");
                return;
            }
        }

        // Validate first name contains only alphabets if provided
        if (this.filters.firstName && !this.isValidName(this.filters.firstName)) {
            this.toast?.showErrorToast("First name should contain only alphabets.");
            return;
        }

        // Validate last name contains only alphabets if provided
        if (this.filters.lastName && !this.isValidName(this.filters.lastName)) {
            this.toast?.showErrorToast("Last name should contain only alphabets.");
            return;
        }

        // Validate email format if provided
        if (this.filters.email) {
            // First check basic email format
            const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
            if (!emailRegex.test(this.filters.email)) {
                this.toast?.showErrorToast("Please enter a valid email address.");
                return;
            }

            // Then check for adani.com domain
            if (!this.filters.email.toLowerCase().endsWith('@adani.com')) {
                this.toast?.showErrorToast("Email must use the @adani.com domain.");
                return;
            }
        }

        // Validate contact number if provided
        if (this.filters.contactNumber && !this.isValidContactNumber(this.filters.contactNumber)) {
            this.toast?.showErrorToast("Contact number must be 10 digits.");
            return;
        }

        this.currentPage = 1;
        this.loadIncidentsForCurrentTab();
        this.closeFilterModal();
    }

    resetFilters(): void {
        this.filters = {
            firstName: null, lastName: null, email: null, contactNumber: null,
            startDate: null, endDate: null,
            plantId: null,
            zoneId: null, departmentId: null,
            enabled: 'true',
            sortField: 'id', sortDirection: 'DESC'
        };
        this.availableZones = [];
        this.currentPage = 1;
        this.loadIncidentsForCurrentTab();
    }

    onFilterPlantSelectionChange(selectedPlantId: number | null): void {
        this.filters.zoneId = null;
        this.availableZones = [];
        if (selectedPlantId !== null) {
            this.loadZones(selectedPlantId);
        }
    }

    getInitialAssignmentData(): AssignTeamData {
        return { incidentId: null, teamLeaderId: null, teamMemberIds: [], deadline: null, assignedTeamMembers: [] };
    }

    async openAssignTeamModal(incident: Incident): Promise<void> {
        console.log('Opening Assign Team Modal for Incident:', incident);
        if (!incident || !incident.id) { this.toast?.showErrorToast("Invalid Incident Data."); return; }
        const incidentPlantId = incident.plant?.id || incident.plantId;
        if (!incidentPlantId) { this.toast?.showErrorToast("Incident plant information is missing."); return; }

        this.selectedIncidentForAssignment = incident;
        this.assignmentData = this.getInitialAssignmentData();
        this.assignmentData.incidentId = incident.id;
        this.isAssignTeamModalOpen = true;
        this.assignTeamLoading = false;
        this.availableAdminsForAssign = [];

        try {
            await this.loadAdminsForAssignmentDropdown(incidentPlantId);

            const availableAdminsMap = new Map(this.availableAdminsForAssign.map(admin => [admin.id, admin]));
            const validAdminIds = new Set(availableAdminsMap.keys());

            let preSelectedLeaderId: number | null = null;
            const preSelectedMemberIdsForNgSelect: number[] = [];
            const preSelectedMembersForTable: AssignedMember[] = [];

            const leaderFromIncident = incident.teamLeader;
            if (leaderFromIncident && validAdminIds.has(leaderFromIncident.id)) {
                preSelectedLeaderId = leaderFromIncident.id;
                 if (!preSelectedMemberIdsForNgSelect.includes(preSelectedLeaderId)) { preSelectedMemberIdsForNgSelect.push(preSelectedLeaderId); }
                 if (!preSelectedMembersForTable.some(m => m.id === preSelectedLeaderId)) { preSelectedMembersForTable.push({ ...leaderFromIncident, isLead: true }); }
            } else if (incident.teamLeaderId && validAdminIds.has(incident.teamLeaderId)) {
                preSelectedLeaderId = incident.teamLeaderId;
                const leaderAdmin = availableAdminsMap.get(preSelectedLeaderId);
                if (leaderAdmin) {
                    if (!preSelectedMemberIdsForNgSelect.includes(preSelectedLeaderId)) { preSelectedMemberIdsForNgSelect.push(preSelectedLeaderId); }
                    if (!preSelectedMembersForTable.some(m => m.id === preSelectedLeaderId)) { preSelectedMembersForTable.push({ ...leaderAdmin, isLead: true }); }
                } else { preSelectedLeaderId = null; }
            }

            const membersFromIncident = incident.member ?? [];
            console.log('Members array from incident:', membersFromIncident);
             for (const memberObj of membersFromIncident) {
                 if (memberObj && memberObj.id && validAdminIds.has(memberObj.id)) {
                     const memberId = memberObj.id;
                      if (!preSelectedMemberIdsForNgSelect.includes(memberId)) {
                          preSelectedMemberIdsForNgSelect.push(memberId);
                      }
                      if (!preSelectedMembersForTable.some(m => m.id === memberId)) {
                          preSelectedMembersForTable.push({
                              ...memberObj,
                              isLead: memberId === preSelectedLeaderId
                          });
                          console.log(`Pre-filling member from member array: ${memberObj.firstName} (ID: ${memberId})`);
                      } else {
                         const existingTableMember = preSelectedMembersForTable.find(m => m.id === memberId);
                         if (existingTableMember && preSelectedLeaderId === memberId) {
                              existingTableMember.isLead = true;
                         }
                      }
                 } else if(memberObj) {
                     console.warn(`Member ID ${memberObj.id} from incident.member is not in the available admins list or member object invalid.`);
                 }
             }

            this.assignmentData.deadline = incident.deadline ? new Date(incident.deadline).toISOString().split('T')[0] : null;
            this.assignmentData.teamLeaderId = preSelectedLeaderId;
            this.assignmentData.teamMemberIds = preSelectedMemberIdsForNgSelect;
            this.assignmentData.assignedTeamMembers = preSelectedMembersForTable;

            setTimeout(() => {
                this.assignTeamForm?.resetForm(this.assignmentData);
                this.assignmentData.teamMemberIds = [...preSelectedMemberIdsForNgSelect];
                console.log('Form reset complete. Final assignmentData state:', JSON.parse(JSON.stringify(this.assignmentData)));
                this.cdr.detectChanges();
            }, 0);

        } catch (error) {
            console.error("Error during openAssignTeamModal preparation:", error);
            this.toast?.showErrorToast("Failed to prepare assignment form.");
            this.closeAssignTeamModal();
        }
    }

    closeAssignTeamModal(): void {
        this.isAssignTeamModalOpen = false;
        this.selectedIncidentForAssignment = null;
        this.assignmentData = this.getInitialAssignmentData();
        this.assignTeamForm?.resetForm();
        this.availableAdminsForAssign = [];
    }

    handleMemberSelectionChange(selectedAdmins: AdminUser[] | null): void {
        if (!this.assignmentData || !this.availableAdminsForAssign) return;

        const currentMembersMap = new Map(this.assignmentData.assignedTeamMembers.map(m => [m.id, m]));
        const newMemberList: AssignedMember[] = [];
        const selectedAdminObjects = selectedAdmins || [];

        for (const admin of selectedAdminObjects) {
             if (!admin || typeof admin.id === 'undefined') continue;
             let existingMember = currentMembersMap.get(admin.id);
             newMemberList.push(existingMember ? existingMember : { ...admin, isLead: false });
        }

        this.assignmentData.assignedTeamMembers = newMemberList;

        const currentLeaderId = this.assignmentData.teamLeaderId;
        const leaderStillSelected = newMemberList.some(m => m.id === currentLeaderId);
        if (currentLeaderId && !leaderStillSelected) {
            this.assignmentData.teamLeaderId = null;
            this.assignmentData.assignedTeamMembers.forEach(m => m.isLead = false);
        } else if (currentLeaderId && leaderStillSelected) {
            this.assignmentData.assignedTeamMembers.forEach(m => m.isLead = (m.id === currentLeaderId));
        }

        console.log('AFTER handler - assignmentData.teamMemberIds:', this.assignmentData.teamMemberIds);
        this.cdr.detectChanges();
    }

    assignTeamLead(memberId: number): void {
        if (!this.assignmentData?.assignedTeamMembers) return;
        if (!this.assignmentData.assignedTeamMembers.some(m => m.id === memberId)) {
             this.toast?.showErrorToast("Selected leader is not in the current team selection.");
             return;
        }
        this.assignmentData.teamLeaderId = memberId;
        this.assignmentData.assignedTeamMembers = this.assignmentData.assignedTeamMembers.map(m => ({
            ...m, isLead: m.id === memberId
        }));
        this.cdr.detectChanges();
    }

    async submitAssignTeamForm(): Promise<void> {
        if (!this.assignmentData.incidentId) { this.toast?.showErrorToast("Missing Incident ID."); return; }
        if (!this.assignmentData.teamLeaderId) { this.toast?.showErrorToast('Please mark one member as Team Leader.'); return; }
        if (!this.assignmentData.assignedTeamMembers || this.assignmentData.assignedTeamMembers.length === 0) { this.toast?.showErrorToast('Please select at least one team member.'); return; }
        if (!this.assignmentData.assignedTeamMembers.some(m => m.id === this.assignmentData.teamLeaderId)) { this.toast?.showErrorToast('Team Leader inconsistency. Please re-select members.'); return; }
        if (this.assignTeamForm?.invalid) {
            Object.values(this.assignTeamForm.controls).forEach(c => c.markAsTouched());
            this.toast?.showErrorToast('Please ensure all required fields are filled correctly.');
            return;
        }

        this.assignTeamLoading = true;
        const uniqueMemberIds = Array.from(new Set(this.assignmentData.assignedTeamMembers.map(m => m.id)));

        const currentUserId = this.loggedInAdminId;
        if (!currentUserId) {
            this.toast?.showErrorToast('Authentication error. Cannot save assignment.');
            this.assignTeamLoading = false;
            return;
        }

        const updatePayload = {
            tableName: "incident",
            id: this.assignmentData.incidentId,
            data: {
                teamLeaderId: this.assignmentData.teamLeaderId,
                memberIds: uniqueMemberIds,
                deadline: this.assignmentData.deadline || null
            },
            createdBy: currentUserId
        };

        console.log('Submitting Team Assignment Payload:', JSON.stringify(updatePayload, null, 2));

        try {
            await this.updateService.update(updatePayload);
            this.toast?.showSuccessToast(`Team assigned successfully for Incident ID: ${this.assignmentData.incidentId}`);
            this.closeAssignTeamModal();
            this.loadIncidentsForCurrentTab();
        } catch (error: any) {
            console.error("Error assigning team:", error);
            const errorMsg = error?.response?.data?.message || 'Failed to assign team.';
            this.toast?.showErrorToast(errorMsg);
        } finally {
            this.assignTeamLoading = false;
        }
    }

    openViewMembersModal(incident: Incident): void {
        if (!incident || !incident.id) { this.toast?.showErrorToast("Invalid incident data."); return; }
        this.incidentForViewingMembers = incident;
        this.isViewMembersModalOpen = true;
        this.viewMembersLoading = false;
        this.membersToDisplay = [];
        const addedMemberIds = new Set<number>();

        this.viewMembersModalInstance?.show();

        const leaderId = incident.teamLeaderId;

        if (incident.teamLeader && incident.teamLeader.id) {
             if (!addedMemberIds.has(incident.teamLeader.id)) {
                this.membersToDisplay.push({ ...incident.teamLeader, isLead: true });
                addedMemberIds.add(incident.teamLeader.id);
             }
        }

        if (incident.engineerIncharge && incident.engineerIncharge.id) {
             if (!addedMemberIds.has(incident.engineerIncharge.id)) {
                this.membersToDisplay.push({ ...incident.engineerIncharge, isLead: incident.engineerIncharge.id === leaderId });
                addedMemberIds.add(incident.engineerIncharge.id);
             }
        }

        if (incident.member && Array.isArray(incident.member)) {
            for (const memberObj of incident.member) {
                 if (memberObj && memberObj.id) {
                    if (!addedMemberIds.has(memberObj.id)) {
                        this.membersToDisplay.push({ ...memberObj, isLead: memberObj.id === leaderId });
                        addedMemberIds.add(memberObj.id);
                    }
                 }
            }
        }

        this.membersToDisplay.sort((a, b) => {
            if (a.isLead && !b.isLead) return -1;
            if (!a.isLead && b.isLead) return 1;
            return (a.firstName || '').localeCompare(b.firstName || '');
        });

        console.log("Final Members for Display:", this.membersToDisplay);
        this.cdr.detectChanges();
    }

    closeViewMembersModal(): void {
        this.viewMembersModalInstance?.hide();
        this.isViewMembersModalOpen = false;
        this.incidentForViewingMembers = null;
        this.membersToDisplay = [];
    }

    openRejectConfirmation(item: Incident): void {
        if (!item || !item.id) { this.toast?.showErrorToast("Invalid incident data."); return; }
        this.incidentToReject = item;
        this.rejectConfirmationModalInstance?.show();
    }

    closeRejectConfirmation(): void {
        this.rejectConfirmationModalInstance?.hide();
        this.incidentToReject = null;
    }

    async confirmReject(): Promise<void> {
        if (!this.incidentToReject) { this.toast?.showErrorToast("An internal error occurred."); return; }

        const itemToReject = this.incidentToReject;
        const originalTabIndex = this.selectedTabIndex;
        this.closeRejectConfirmation();

        const currentUserId = this.loggedInAdminId;
        if (!currentUserId) {
            this.toast?.showErrorToast("Authentication error. Cannot reject incident.");
            return;
        }

        const payload = {
            tableName: 'incident',
            id: itemToReject.id,
            data: { status: IncidentStatus.Rejected },
            createdBy: currentUserId
        };

        this.listLoading = true;
        try {
            await this.updateService.update(payload);
            this.toast?.showSuccessToast(`Incident ID ${itemToReject.id} rejected successfully.`);
            if (this.selectedTabIndex === originalTabIndex) {
                this.loadIncidentsForCurrentTab();
            }
        } catch (error: any) {
            console.error("Error rejecting incident:", error);
            const errorMsg = error?.response?.data?.message || `Failed to reject Incident ID ${itemToReject.id}.`;
            this.toast?.showErrorToast(errorMsg);
        } finally {
            this.listLoading = false;
            this.incidentToReject = null;
        }
    }

    async fetchIncidents(page: number, status: IncidentStatus, currentFilters: IncidentFilter): Promise<{ data: Incident[], total: number } | null> {
        this.listLoading = true;
        const filterParams: string[] = [`status||eq||${status}`];

        // --- Apply Role-Based Plant Filtering ---
        // --- Apply Role-Based Plant Filtering ---
        if (this.currentUserRole === this.componentRoles.PLANT_ADMIN) {
            if (currentFilters.plantId !== null && currentFilters.plantId !== undefined) {
                // Plant Admin selected a specific plant from their list
                // Always filter by the selected plant ID if one is explicitly chosen.
                // The backend should enforce that the selected plant is one of their assigned plants.
                filterParams.push(`plant.id||eq||${currentFilters.plantId}`);
                console.log("Plant Admin: Filtering incidents by selected plant ID:", currentFilters.plantId);
            } else if (this.loggedInPlantIds.length > 0) {
                // Plant Admin selected "All My Plants" (or default) - filter by all their assigned plants
                filterParams.push(`plant.id||$in||${this.loggedInPlantIds.join(',')}`);
                console.log("Plant Admin: Filtering incidents by assigned plant IDs:", this.loggedInPlantIds);
            } else {
                // Plant Admin has no plants assigned - this case should ideally be prevented
                // or result in no data. Adding a filter that likely returns nothing.
                console.warn("Plant Admin has no assigned plants. Applying impossible filter.");
                filterParams.push(`plant.id||eq||-1`); // Or handle as needed
            }
        } else if (this.currentUserRole === this.componentRoles.SUPER_ADMIN) {
            // Super Admin: Only filter if they explicitly selected a plant
            if (currentFilters.plantId !== null && currentFilters.plantId !== undefined) {
                filterParams.push(`plant.id||eq||${currentFilters.plantId}`);
                console.log("Super Admin: Filtering incidents by selected plant ID:", currentFilters.plantId);
            } else {
                 console.log("Super Admin: No specific plant selected, showing all.");
                 // No plant filter added
            }
        } else {
            // No role or unknown role - potentially restrict or show all? Showing all for now.
            console.warn("Unknown user role, not applying specific plant filters.");
            // No plant filter added by default
        }
        // --- End Role-Based Plant Filtering ---

        if (currentFilters.enabled !== null && currentFilters.enabled !== '') filterParams.push(`enabled||eq||${currentFilters.enabled}`);

        // Trim string values before applying filters
        if (currentFilters.firstName) {
            const trimmedFirstName = currentFilters.firstName.trim();
            if (trimmedFirstName) {
                filterParams.push(`admin.firstName||$contL||${trimmedFirstName}`);
            }
        }

        if (currentFilters.lastName) {
            const trimmedLastName = currentFilters.lastName.trim();
            if (trimmedLastName) {
                filterParams.push(`admin.lastName||$contL||${trimmedLastName}`);
            }
        }

        if (currentFilters.email) {
            const trimmedEmail = currentFilters.email.trim();
            if (trimmedEmail) {
                filterParams.push(`admin.email||$contL||${trimmedEmail}`);
            }
        }

        if (currentFilters.contactNumber) {
            const trimmedContactNumber = currentFilters.contactNumber.trim();
            if (trimmedContactNumber) {
                filterParams.push(`admin.contactNumber||$contL||${trimmedContactNumber}`);
            }
        }
        if (currentFilters.startDate) filterParams.push(`createdTimestamp||$gte||${currentFilters.startDate}T00:00:00Z`);
        if (currentFilters.endDate) filterParams.push(`createdTimestamp||$lte||${currentFilters.endDate}T23:59:59Z`);
        if (currentFilters.zoneId && (currentFilters.plantId || (this.currentUserRole === this.componentRoles.PLANT_ADMIN && this.loggedInPlantIds.length > 0))) {
            filterParams.push(`zone.id||eq||${currentFilters.zoneId}`);
        }
        if (currentFilters.departmentId) filterParams.push(`department.id||eq||${currentFilters.departmentId}`);

        const data = {
            page: page, limit: this.itemsPerPage,
            sort: `${currentFilters.sortField || 'id'},${currentFilters.sortDirection || 'DESC'}`,
            filter: filterParams,
            join: [
                'admin', 'plant', 'department', 'zone',
                'incidentTypeMaster.incidentMaster', 'equipement',
                'engineerIncharge', 'teamLeader', 'member'
            ]
        };

        console.log("API Request Params (fetchIncidents):", JSON.stringify(data, null, 2));

        try {
            const param = createAxiosConfig(data);
            const response = await this.incidentManagementService.getIncidents(param);
            const incidents = (response?.data ?? response ?? []).map((inc: any) => ({
                ...inc, createdTimestamp: new Date(inc.createdTimestamp)
            }));
            return { data: incidents, total: response?.total ?? 0 };
        } catch (error: any) {
            console.error("Error fetching incidents:", error);
            this.toast?.showErrorToast(error?.response?.data?.message || "Failed to load incidents.");
            this.clearCurrentTabData();
            this.totalItems = 0;
            return null;
        } finally {
            this.listLoading = false;
        }
    }

    async loadIncidentsForCurrentTab() {
        const currentTab = this.tabs[this.selectedTabIndex];
        if (!currentTab) return;

        console.log(`Loading data for tab: ${currentTab.title}`);
        const result = await this.fetchIncidents(this.currentPage, currentTab.status, this.filters);

        this.clearCurrentTabData();
        this.totalItems = 0;

        if (result) {
            this.totalItems = result.total;
            (this as any)[currentTab.listKey] = result.data;
            console.log(`Loaded ${result.data.length} incidents for tab: ${currentTab.title}, Total: ${this.totalItems}`);
        }
        this.cdr.detectChanges();
    }

    clearCurrentTabData() {
        const listKey = this.tabs[this.selectedTabIndex]?.listKey;
        if (listKey && (this as any)[listKey]) {
            (this as any)[listKey] = [];
        }
    }

    onTabSelected(index: number) {
        if (this.selectedTabIndex === index || this.listLoading) return;
        this.selectedTabIndex = index;
        this.currentPage = 1;
        this.loadIncidentsForCurrentTab();
    }

    onPageChange(page: number) {
        if (this.currentPage === page || this.listLoading) return;
        this.currentPage = page;
        this.loadIncidentsForCurrentTab();
    }

    async fetchAllFilteredIncidents(): Promise<Incident[] | null> {
        const currentTab = this.tabs[this.selectedTabIndex];
        if (!currentTab) { console.error("Invalid tab selected."); return null; }

        this.listLoading = true;
        const status = currentTab.status;
        const filterParams: string[] = [`status||eq||${status}`];

        if (this.currentUserRole === this.componentRoles.PLANT_ADMIN) {
            if (this.loggedInPlantIds.length > 0) {
                if (this.filters.plantId && this.loggedInPlantIds.includes(this.filters.plantId)) {
                    filterParams.push(`plant.id||eq||${this.filters.plantId}`);
                } else {
                    filterParams.push(`plant.id||$in||${this.loggedInPlantIds.join(',')}`);
                     if(this.filters.plantId && !this.loggedInPlantIds.includes(this.filters.plantId)) {
                          this.filters.plantId = null; // Correct visual filter if needed
                          this.filters.zoneId = null;
                          this.availableZones = [];
                     }
                }
            } else {
                filterParams.push(`plant.id||eq||-1`);
            }
        } else if (this.currentUserRole === this.componentRoles.SUPER_ADMIN) {
            if (this.filters.plantId) {
                filterParams.push(`plant.id||eq||${this.filters.plantId}`);
            }
        }

        if (this.filters.enabled !== null && this.filters.enabled !== '') filterParams.push(`enabled||eq||${this.filters.enabled}`);

        // Trim string values before applying filters
        if (this.filters.firstName) {
            const trimmedFirstName = this.filters.firstName.trim();
            if (trimmedFirstName) {
                filterParams.push(`admin.firstName||$contL||${trimmedFirstName}`);
            }
        }

        if (this.filters.lastName) {
            const trimmedLastName = this.filters.lastName.trim();
            if (trimmedLastName) {
                filterParams.push(`admin.lastName||$contL||${trimmedLastName}`);
            }
        }

        if (this.filters.email) {
            const trimmedEmail = this.filters.email.trim();
            if (trimmedEmail) {
                filterParams.push(`admin.email||$contL||${trimmedEmail}`);
            }
        }

        if (this.filters.contactNumber) {
            const trimmedContactNumber = this.filters.contactNumber.trim();
            if (trimmedContactNumber) {
                filterParams.push(`admin.contactNumber||$contL||${trimmedContactNumber}`);
            }
        }
        if (this.filters.startDate) filterParams.push(`createdTimestamp||$gte||${this.filters.startDate}T00:00:00Z`);
        if (this.filters.endDate) filterParams.push(`createdTimestamp||$lte||${this.filters.endDate}T23:59:59Z`);
        if (this.filters.zoneId && (this.filters.plantId || (this.currentUserRole === this.componentRoles.PLANT_ADMIN && this.loggedInPlantIds.length > 0))) {
            filterParams.push(`zone.id||eq||${this.filters.zoneId}`);
        }
        if (this.filters.departmentId) filterParams.push(`department.id||eq||${this.filters.departmentId}`);

        const data = {
            limit: 10000,
            sort: `${this.filters.sortField || 'id'},${this.filters.sortDirection || 'DESC'}`,
            filter: filterParams,
            join: [
                'admin', 'plant', 'department', 'zone',
                'incidentTypeMaster.incidentMaster', 'equipement',
                'engineerIncharge', 'teamLeader', 'member'
            ]
        };

        try {
            const param = createAxiosConfig(data);
            console.log('API Request Params (Incidents Download - All Data):', JSON.stringify(param, null, 2));
            const response = await this.incidentManagementService.getIncidents(param);
            // Handle potential response structures
             if (response && Array.isArray(response.data)) {
                 return response.data;
             } else if (response && Array.isArray(response)) {
                 return response; // Handle direct array response
             } else {
                 console.warn("Received unexpected response structure when fetching all incidents:", response);
                 return null;
             }
        } catch (error: any) {
            console.error("Error fetching all incidents for download:", error);
            this.toast?.showErrorToast(error?.response?.data?.message || "Failed to retrieve full data for download.");
            return null;
        } finally {
            this.listLoading = false;
        }
    }

    getCurrentListData(): Incident[] | undefined {
        const currentTab = this.tabs[this.selectedTabIndex];
        if (currentTab && currentTab.listKey) {
            const list = (this as any)[currentTab.listKey];
            if (Array.isArray(list)) {
                return list;
            }
        }
        console.warn("Could not get list data for download for tab index:", this.selectedTabIndex);
        return undefined;
    }

    async downloadExcel(type: 'current' | 'all') {
        const currentTab = this.tabs[this.selectedTabIndex];
        if (!currentTab || this.isDownloadingExcel || this.listLoading) return;

        this.isDownloadingExcel = true;
        this.downloadType = type;
        this.toast?.showSuccessToast(`Preparing download for ${type === 'current' ? 'current page' : 'all filtered'} incidents...`);

        let dataToExport: Incident[] | null = null;

        try {
            if (type === 'all') {
                dataToExport = await this.fetchAllFilteredIncidents();
            } else {
                dataToExport = this.getCurrentListData() ?? null;
                if (dataToExport === undefined) { dataToExport = null; }
            }

            if (dataToExport === null) { this.toast?.showErrorToast("Failed to retrieve data for download."); return; }
            if (!dataToExport || dataToExport.length === 0) { this.toast?.showErrorToast(`No ${currentTab.title.toLowerCase()} incidents available to download.`); return; }

            console.log(`Fetched ${dataToExport.length} incidents for Excel export (${type}).`);

            const dataForExcel = dataToExport.map(inc => ({
                'Incident ID': inc.id,
                'Status': this.getIncidentStatusText(inc.status),
                'Reported By ID': inc.admin?.id,
                'Reported By Name': `${inc.admin?.firstName ?? ''} ${inc.admin?.lastName ?? ''}`.trim(),
                'Reported By Email': inc.admin?.email,
                'Plant': inc.plant?.name,
                'Department': inc.department?.title,
                'Zone Name': inc.zone?.zoneName,
                'Zone Area': inc.zone?.zoneArea,
                'Exact Area': inc.exactArea,
                'Incident Date': inc.date,
                'Incident Time': inc.time,
                'Incident Category': inc.incidentTypeMaster?.incidentMaster?.title,
                'Incident Type': inc.incidentTypeMaster?.title,
                'Equipment': inc.equipement?.title,
                'Incident Detail': inc.incidentDetail,
                'Deadline Date': inc.deadline ? new Date(inc.deadline).toLocaleDateString() : '',
                'Team Leader Name': `${inc.teamLeader?.firstName ?? ''} ${inc.teamLeader?.lastName ?? ''}`.trim(),
                'Team Leader ID': inc.teamLeaderId,
                'Plant Admin Name': `${inc.engineerIncharge?.firstName ?? ''} ${inc.engineerIncharge?.lastName ?? ''}`.trim(),
                'Plant Admin ID': inc.engineerIncharge?.id,
                'Created Timestamp': inc.createdTimestamp ? new Date(inc.createdTimestamp).toLocaleString() : '',
                'Updated Timestamp': inc.updatedTimestamp ? new Date(inc.updatedTimestamp).toLocaleString() : '',
            }));

            const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(dataForExcel);
            const wb: XLSX.WorkBook = XLSX.utils.book_new();
            const safeSheetName = currentTab.title.replace(/[^a-zA-Z0-9]/g, '').substring(0, 30);
            XLSX.utils.book_append_sheet(wb, ws, safeSheetName || 'Incidents');

            const dateStr = new Date().toISOString().slice(0, 10);
            const typeStr = type === 'current' ? `Page${this.currentPage}` : 'All';
            const fileName = `Incidents_${safeSheetName}_${typeStr}_${dateStr}.xlsx`;

            XLSX.writeFile(wb, fileName);
            this.toast?.showSuccessToast(`Excel file download started (${type}).`);

        } catch (error) {
            console.error(`Error generating Excel file (${type}):`, error);
            this.toast?.showErrorToast(`An error occurred while generating the Excel file (${type}).`);
        } finally {
            this.isDownloadingExcel = false;
            this.downloadType = null;
        }
    }

    getIncidentStatusText(status: IncidentStatus): string {
        switch (status) {
            case IncidentStatus.New: return 'New';
            case IncidentStatus.PendingCAPA: return 'Pending CAPA';
            case IncidentStatus.Rejected: return 'Rejected';
            case IncidentStatus.CAPASubmitted: return 'CAPA Submitted';
            case IncidentStatus.Open: return 'Open';
            case IncidentStatus.Revised: return 'Revised';
            case IncidentStatus.Closed: return 'Closed';
            default: return 'Unknown';
        }
    }

    getSortClass(key: string): string {
        if (this.filters.sortField === key) {
            return this.filters.sortDirection === 'ASC' ? 'sort-asc' : 'sort-desc';
        }
        return '';
    }

    sortBy(field: string) {
        if (this.listLoading) return;
        if (this.filters.sortField === field) {
            this.filters.sortDirection = this.filters.sortDirection === 'ASC' ? 'DESC' : 'ASC';
        } else {
            this.filters.sortField = field;
            this.filters.sortDirection = 'DESC';
        }
        this.applyFilters(); // Re-load data with new sort order
    }

    // --- Validation Helper Methods ---
    private isValidName(name: string): boolean {
        const nameRegex = /^[a-zA-Z\s]*$/;
        return nameRegex.test(name);
    }

    private isValidEmail(email: string): boolean {
        // First check basic email format
        const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
        if (!emailRegex.test(email)) {
            return false;
        }

        // Then check for adani.com domain
        return email.toLowerCase().endsWith('@adani.com');
    }

    private isValidContactNumber(contactNumber: string): boolean {
        const contactRegex = /^[0-9]{10}$/;
        return contactRegex.test(contactNumber);
    }

    // --- Helper to trim input fields ---
    trimInputField(formData: any, fieldName: string): void {
        if (formData && formData[fieldName] && typeof formData[fieldName] === 'string') {
            formData[fieldName] = formData[fieldName].trim();
        }
    }

    // Toggle detail text expansion
    toggleDetailText(item: Incident): void {
        if (item) {
            item.showFullDetail = !item.showFullDetail;
        }
    }

} // --- End of IncidentManagementComponent Class ---