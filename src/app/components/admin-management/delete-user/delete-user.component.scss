/* Card styling */
.card {
  background-color: #fff;
  border-radius: 10px;
  border: 1px solid rgba(0, 0, 0, 0.125);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  min-width: 0;
  word-wrap: break-word;
  margin-top: 10px;
  overflow: hidden;
}

.card-body {
  height: 70vh;
  overflow: auto;
  padding: 1.25rem;
}

.card-header {
  padding: 0.75rem 1.25rem;
  margin-bottom: 0;
  background-color: rgba(0, 0, 0, 0.03);
  border-bottom: 1px solid rgba(0, 0, 0, 0.125);
  font-size: 12px;
  text-align: center;
}

/* Table styling */
.custom-table {
  width: 100%;
  border-collapse: collapse;
  border-radius: 10px;
  margin-top: 10px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  table-layout: fixed;
}

.custom-table th,
.custom-table td {
  padding: 10px 12px;
  text-align: left;
  font-size: 12px;
  vertical-align: middle;
}

.custom-table thead th,
.table-header th {
  font-size: 12px;
  background-color: #0B74B0 !important;
  color: white !important;
  text-align: center;
  font-weight: bold;
}

/* Column width definitions */
th.col-actions {
  width: 120px;
}

th.col-user-details {
  width: 320px; /* Wider column for user details */
}

th.col-plant-info {
  width: 280px;
}

th.col-deleted-by {
  width: 280px;
}

td {
  font-size: 12px;
  text-align: left;
  vertical-align: middle;
  word-wrap: break-word; /* Allow text to wrap */
  overflow-wrap: break-word;
}

/* Add bottom border to table rows */
.custom-table tbody tr {
  border-bottom: 1px dotted #eee;
}

.img-thumbnail {
    width: 80px;
    height: 80px;
    object-fit: cover;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.btn-sm {
    border-radius: 6px;
    font-size: 12px;
}

/* Details container styling */
.details-container {
  display: table;
  width: 100%;
  text-align: left;
  padding: 5px;
  border-spacing: 0 8px;
}

.label-value {
  margin-bottom: 8px;
  line-height: 1.5;
  display: flex;
  align-items: baseline;
  padding: 2px 0;
  border-bottom: 1px dotted #eee;
  padding-bottom: 8px;
}

.label-value:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.label-value strong {
  font-weight: 500;
  color: #777;
  margin-right: 8px;
  min-width: 110px;
  font-size: 12px;
  display: inline-block;
  vertical-align: top;
}

.value-text {
  font-weight: 600;
  color: #222;
  font-size: 12px;
  display: inline-block;
  word-break: break-word;
  overflow-wrap: break-word;
  flex: 1;
  letter-spacing: normal;
  line-height: 1.5;
}

/* User details styling - for backward compatibility */
.user-details-container {
  display: table;
  width: 100%;
  text-align: left;
  padding: 5px;
  border-spacing: 0 8px;
}

.user-details-row {
  margin-bottom: 8px;
  line-height: 1.5;
  display: flex;
  align-items: baseline;
  padding: 2px 0;
  border-bottom: 1px dotted #eee;
  padding-bottom: 8px;
}

.user-details-row:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.user-details-label {
  font-weight: 500;
  color: #777;
  margin-right: 8px;
  min-width: 110px;
  font-size: 12px;
  display: inline-block;
  vertical-align: top;
}

.user-details-value {
  font-weight: 600;
  color: #222;
  font-size: 12px;
  display: inline-block;
  word-break: break-word;
  overflow-wrap: break-word;
  flex: 1;
  letter-spacing: normal;
  line-height: 1.5;
}

/* Plant badges styling */
.plant-badges-container {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  margin-bottom: 8px;
  align-items: center;
}

.plant-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 16px; /* Rounded corners for chip-like appearance */
  font-size: 11px;
  font-weight: 500;
  margin: 2px 4px 4px 0;
  white-space: normal;
  text-align: center;
  line-height: 1.2;
  color: white !important; /* Make text color white for better contrast */
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1); /* Subtle shadow for depth */
}

.cursor-pointer {
    cursor: pointer;
}

.filter-button {
    width: 35px;
    cursor: pointer;
}

.text-warning {
    color: #eb6f33 !important;
}
