import { Component, OnInit, ViewChild, ElementRef, inject, AfterViewInit, OnDestroy } from '@angular/core'; // Added inject, AfterViewInit, OnDestroy
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, Validators, AbstractControl, ValidationErrors, ValidatorFn } from '@angular/forms';
import { TabComponent } from '../../../shared/tab/tab.component';
import { AdminService } from '../../../services/admin/admin.service';
import { createAxiosConfig } from '../../../core/utilities/axios-param-config';
import { PaginationComponent } from "../../../shared/pagination/pagination.component";
import { OffcanvasComponent } from "../../../shared/offcanvas/offcanvas.component";
import { DepartmentService } from '../../../services/master-management/department/department.service';
import { DesignationService } from '../../../services/master-management/designation/designation.service';
import { PlantManagementService } from '../../../services/plant-management/plant-management.service';
import { UpdateService } from '../../../services/update/update.service';
import { UploadService } from '../../../services/upload/upload.service';
import { SwitchComponent } from '../../../shared/switch/switch.component';
import { ToastMessageComponent } from "../../../shared/toast-message/toast-message.component";
import { NgbDropdownModule } from '@ng-bootstrap/ng-bootstrap';
import { Modal } from 'bootstrap';
import * as XLSX from 'xlsx';

// --- Define ROLES constant ---
const ROLES = {
    SUPER_ADMIN: 'super_admin',
    PLANT_ADMIN: 'plant_admin',
};

// --- Interfaces (Defined for clarity and type safety) ---
interface UserFilter {
    firstName?: string | null;
    lastName?: string | null;
    email?: string | null;
    mobile?: string | null;
    plantId?: number | null; // Plant selected in filter
    designationId?: number | null;
    departmentId?: number | null;
    adminRoleId?: number | null;
    sortField?: string | null;
    sortDirection?: 'ASC' | 'DESC';
}

// Interface for User data received from the main list API
interface User {
    id: number;
    firstName: string;
    lastName: string;
    email: string;
    contactNumber: string | null;
    dob: string | Date | null; // Allow Date object too
    gender: number | string | null; // Allow number or string (adjust as needed)
    profilePicture: string | null;
    status: number; // 0=Pending, 1=Active, 2=Rejected
    department?: SimpleMaster | null;
    designation?: SimpleMaster | null;
    adminsRole?: AdminRole | null;
    plant?: Plant[]; // Array of Plant objects as received from API (using 'plant' as key based on join)
    createdTimestamp?: string | Date | null;
    updatedTimestamp?: string | Date | null; // Add if needed for export
}

// Define InactiveUserItem based on User for consistency
type InactiveUserItem = User;

interface SimpleMaster { // Used for Department, Designation
    id: number;
    title: string;
}

interface Plant {
    id: number;
    name: string;
}

interface AdminRole {
    id: number;
    name: string;
}

// Custom validator for adani.com email domain
export function adaniEmailValidator(): ValidatorFn {
  return (control: AbstractControl): ValidationErrors | null => {
    if (!control.value) {
      return null; // Let required validator handle empty values
    }

    const email = control.value.toLowerCase();
    const isAdaniEmail = email.endsWith('@adani.com');

    return isAdaniEmail ? null : { adaniDomain: true };
  };
}

@Component({
    selector: 'app-inactive-user',
    standalone: true,
    imports: [
        CommonModule,
        FormsModule,
        ReactiveFormsModule,
        TabComponent,
        PaginationComponent,
        OffcanvasComponent,
        SwitchComponent,
        ToastMessageComponent,
        NgbDropdownModule
    ],
    templateUrl: './inactive-user.component.html',
    styleUrl: './inactive-user.component.scss'
})
export class InactiveUserComponent implements OnInit, AfterViewInit, OnDestroy { // Added AfterViewInit, OnDestroy
    // --- ViewChild References ---
    @ViewChild(ToastMessageComponent) toast!: ToastMessageComponent;
    @ViewChild('fileInput') fileInput!: ElementRef;
    @ViewChild('userActionConfirmationModalElement') userActionConfirmationModalElement!: ElementRef;
    @ViewChild('filterForm') filterForm: any;

    public componentRoles = ROLES; // <--- ADD THIS LINE

    // --- State Variables ---
    isFilterModalOpen = false;
    userActionConfirmationModalInstance: Modal | null = null;
    isEditModalOpen = false;
    listLoading = false;
    editLoading = false;
    submitted = false;

    // --- State Variables for Confirmation ---
    userToAction: User | null = null;
    actionToConfirm: 'accept' | 'reject' | 'activate' | null = null;
    targetStatus: number | null = null;
    modalTitle = '';
    modalMessage = '';
    confirmButtonText = '';
    confirmButtonClass = '';

    // --- Lists & Pagination ---
    pendingUserList: InactiveUserItem[] = [];
    rejectedUserList: InactiveUserItem[] = [];
    currentPage = 1;
    itemsPerPage = 10;
    totalItems = 0;
    isDownloadingExcel = false;
    downloadType: 'current' | 'all' | null = null;

    // --- Tab Configuration ---
    selectedTabIndex = 0;
    tabs = [
        { title: 'Pending', status: 0, listKey: 'pendingUserList' as const },
        { title: 'Rejected', status: 2, listKey: 'rejectedUserList' as const },
    ];

    // --- Filter State ---
    filters: UserFilter = {
        firstName: null, lastName: null, email: null, mobile: null,
        plantId: null, designationId: null, departmentId: null, adminRoleId: null,
        sortField: 'id', sortDirection: 'DESC'
    };

    // --- Role-Based Access Control Properties ---
    currentUserRole: string = '';
    loggedInAdminId: number | null = null;
    loggedInPlantIds: number[] = [];

    // --- Data sources for filter/edit dropdowns ---
    availablePlants: Plant[] = []; // Filtered by role
    availableDesignations: SimpleMaster[] = [];
    availableDepartments: SimpleMaster[] = [];
    availableAdminRoles: AdminRole[] = [
        { id: 3, name: 'User' }, { id: 2, name: 'Plant Admin' }, { id: 1, name: 'Super Admin' }
    ];
    availableSortFields = [
        { value: 'id', label: 'ID' }, { value: 'firstName', label: 'First Name' },
        { value: 'lastName', label: 'Last Name' }, { value: 'email', label: 'Email' },
        { value: 'createdAt', label: 'Created Date' } // Assuming 'createdAt' exists or map to createdTimestamp
    ];

    // --- Editing State ---
    editUserForm!: FormGroup;
    selectedUserForEdit: User | null = null;
    profileImageUrl: string | ArrayBuffer | null = null;
    selectedFile: File | null = null;

    // --- Service Injection using inject() ---
    private fb = inject(FormBuilder);
    private adminService = inject(AdminService);
    private plantService = inject(PlantManagementService);
    private designationService = inject(DesignationService);
    private departmentService = inject(DepartmentService);
    private updateService = inject(UpdateService);
    private uploadService = inject(UploadService);

    constructor() { } // Keep constructor empty if using inject()

    ngOnInit(): void {
        this.setCurrentUserRoleAndDetailsById(); // Set role first
        this.initializeEditForm();
        // Fetch dropdown data *after* role is set to filter plants correctly
        this.getPlants();
        this.getDesignations();
        this.getDepartments();
        this.loadUsersForCurrentTab(); // Initial load applies role filter
    }

    ngAfterViewInit(): void {
        if (this.userActionConfirmationModalElement) {
            this.userActionConfirmationModalInstance = new Modal(this.userActionConfirmationModalElement.nativeElement);
        } else {
            console.error("User action confirmation modal element not found!");
        }
        // Initialize edit modal if needed (depends on implementation, offcanvas usually doesn't need separate JS init)
    }

    ngOnDestroy(): void {
        this.userActionConfirmationModalInstance?.dispose();
        // Dispose other modals if necessary
    }

    // --- RBAC Setup ---
    private setCurrentUserRoleAndDetailsById() {
        try {
            const userString = localStorage.getItem('user');
            if (!userString || userString.trim() === "" || ['null', 'undefined', '""', '" "'].includes(userString.trim().toLowerCase())) {
                this.currentUserRole = ''; this.loggedInAdminId = null; this.loggedInPlantIds = [];
                this.toast?.showErrorToast("User session invalid."); return;
            }
            const currentUser = JSON.parse(userString);
            this.loggedInAdminId = currentUser?.id ?? null;
            this.loggedInPlantIds = (Array.isArray(currentUser?.plantIds) && currentUser.plantIds.length > 0)
                ? currentUser.plantIds.map((id: any) => Number(id)).filter((id: number) => !isNaN(id)) : [];
            const roleId = currentUser?.adminsRoleId;
            if (roleId === 1 || roleId === 6) { this.currentUserRole = ROLES.SUPER_ADMIN; }
            else if (roleId === 2) {
                this.currentUserRole = ROLES.PLANT_ADMIN;
                if (this.loggedInPlantIds.length === 0) {
                    console.warn("Plant Admin has no assigned plants. They won't see any plant-specific data.");
                    // this.toast?.showWarningToast("You have no assigned plants."); // Optional warning
                }
            } else {
                 this.currentUserRole = '';
                 // Optionally handle 'User' role or other roles if they can access this component
                 console.warn(`User has role ID ${roleId}, which may not have permissions here.`);
                 // this.toast?.showErrorToast("Your user role does not have permission to view this page.");
            }
            console.log(`InactiveUser - Role: ${this.currentUserRole}, UserID: ${this.loggedInAdminId}, Plants: [${this.loggedInPlantIds.join(', ')}]`);
        } catch (error) {
            console.error("Error parsing user data:", error);
            this.currentUserRole = ''; this.loggedInAdminId = null; this.loggedInPlantIds = [];
            this.toast?.showErrorToast("Error reading user session.");
        }
    }

    // --- Confirmation Modal Logic ---
    openUserActionConfirmation(user: User, action: 'accept' | 'reject' | 'activate'): void {
        if (!user || user.id === undefined) {
            this.toast?.showErrorToast("Invalid user data provided.");
            console.error("Invalid user data for action confirmation:", user);
            return;
        }

        // --- RBAC Check for Plant Admin ---
        if (this.currentUserRole === ROLES.PLANT_ADMIN) {
             const userPlantIds = user.plant?.map(p => p.id).filter(id => id != null) as number[] || [];
             const canAccess = userPlantIds.some(pid => this.loggedInPlantIds.includes(pid));
             if (!canAccess && userPlantIds.length > 0) { // Only deny if user has plants, just not the admin's
                 this.toast?.showErrorToast(`You do not have permission to ${action} users for the assigned plant(s).`);
                 return;
             }
             // Decide policy for users with NO plants: allow Plant Admin action? Assuming NO for now.
             if (userPlantIds.length === 0) {
                 this.toast?.showErrorToast(`Cannot ${action} users with no assigned plant.`);
                 return;
             }
        }
        // --- End RBAC Check ---


        this.userToAction = user;
        this.actionToConfirm = action;

        switch (action) {
            case 'accept':
            case 'activate': // Treat 'accept' and 'activate' similarly for status update
                this.modalTitle = `Confirm ${action.charAt(0).toUpperCase() + action.slice(1)}`;
                this.modalMessage = `Are you sure you want to ${action} this user? They will be moved to the Active list.`;
                this.confirmButtonText = `Yes, ${action.charAt(0).toUpperCase() + action.slice(1)}`;
                this.confirmButtonClass = 'btn-success';
                this.targetStatus = 1; // Status for Active
                break;
            case 'reject':
                this.modalTitle = 'Confirm Rejection';
                this.modalMessage = `Are you sure you want to reject this user?`;
                this.confirmButtonText = 'Yes, Reject';
                this.confirmButtonClass = 'btn-danger';
                this.targetStatus = 2; // Status for Rejected
                break;
            default:
                console.error("Unknown action type for confirmation:", action);
                return;
        }

        this.userActionConfirmationModalInstance?.show();
    }

    closeUserActionConfirmation(): void {
        this.userActionConfirmationModalInstance?.hide();
        this.userToAction = null;
        this.actionToConfirm = null;
        this.targetStatus = null;
    }

    confirmUserAction(): void {
        if (!this.userToAction || this.targetStatus === null) {
            console.error("Confirmation failed: Missing user or target status.");
            this.toast?.showErrorToast("An internal error occurred. Please try again.");
            this.closeUserActionConfirmation();
            return;
        }
        // RBAC check again before proceeding (belt and suspenders)
        if (this.currentUserRole === ROLES.PLANT_ADMIN) {
             const userPlantIds = this.userToAction.plant?.map(p => p.id).filter(id => id != null) as number[] || [];
             const canAccess = userPlantIds.some(pid => this.loggedInPlantIds.includes(pid));
              if (!canAccess) {
                 this.toast?.showErrorToast(`Permission denied to perform this action.`);
                 this.closeUserActionConfirmation(); // Close modal
                 return;
             }
        }

        const userToUpdate = this.userToAction;
        const statusToSet = this.targetStatus;
        this.closeUserActionConfirmation();
        this.updateAdmin(userToUpdate, statusToSet);
    }

    // --- Excel Download Logic ---
    getCurrentListData(): InactiveUserItem[] | undefined {
        const currentTab = this.tabs[this.selectedTabIndex];
        if (currentTab && currentTab.listKey) {
            const list = (this as any)[currentTab.listKey];
            if (Array.isArray(list)) { return list; }
        }
        console.warn("Could not get list data for download for tab index:", this.selectedTabIndex);
        return undefined;
    }

    async fetchAllFilteredInactiveUsers(): Promise<InactiveUserItem[] | null> {
        const currentTab = this.tabs[this.selectedTabIndex];
        if (!currentTab) { console.error("Cannot fetch all data: Invalid tab selected."); return null; }

        this.listLoading = true;
        const status = currentTab.status;
        const filterParams: string[] = [ 'id||ne||1', `status||eq||${status}` ];
        // *** CHANGE: Use 'plant.id' consistently ***
        const plantFilterPath = 'plant.id';

        // --- RBAC Plant Filtering for API (ALL Data) ---
        if (this.currentUserRole === ROLES.PLANT_ADMIN) {
            if (this.loggedInPlantIds.length > 0) {

                // *** START FIX ***
                // Convert the selected filter plant ID to a number for reliable comparison
                const selectedPlantIdNum = this.filters.plantId != null ? Number(this.filters.plantId) : null;

                // Add logging to verify types and values just before the check
                console.log('[fetchAllFiltered - Inactive] Checking Plant Filter:');
                console.log('  Raw this.filters.plantId:', this.filters.plantId, typeof this.filters.plantId);
                console.log('  Converted selectedPlantIdNum:', selectedPlantIdNum, typeof selectedPlantIdNum);
                console.log('  loggedInPlantIds:', this.loggedInPlantIds);
                console.log('  Includes Check Result:', selectedPlantIdNum != null && this.loggedInPlantIds.includes(selectedPlantIdNum));

                // Check if a specific plant filter is applied AND it's one the admin manages (using the number)
                if (selectedPlantIdNum != null && this.loggedInPlantIds.includes(selectedPlantIdNum)) {
                    // A specific, valid plant IS selected - filter ONLY by this plant
                    filterParams.push(`${plantFilterPath}||eq||${selectedPlantIdNum}`); // Use the number
                    console.log(`[fetchAllFiltered - Inactive] Applying EQ filter for plant: ${selectedPlantIdNum}`);
                } else {
                    // No specific valid plant selected -> filter by ALL plants the admin has access to.
                    filterParams.push(`${plantFilterPath}||$in||${this.loggedInPlantIds.join(',')}`);
                    console.log(`[fetchAllFiltered - Inactive] Applying IN filter for plants: ${this.loggedInPlantIds.join(',')}`);
                    // No need to reset UI filter state here as it's just for fetching data
                }
                // *** END FIX ***

            } else {
                // Plant Admin has no assigned plants, block results
                filterParams.push(`${plantFilterPath}||eq||-1`);
                console.log('[fetchAllFiltered - Inactive] Plant Admin has no plants, blocking results.');
            }
        } else if (this.currentUserRole === ROLES.SUPER_ADMIN) {
            // Super Admin logic
            if (this.filters.plantId != null) {
                filterParams.push(`${plantFilterPath}||eq||${this.filters.plantId}`);
                console.log(`[fetchAllFiltered - Inactive] Super Admin applying EQ filter for plant: ${this.filters.plantId}`);
            } else {
                 console.log('[fetchAllFiltered - Inactive] Super Admin showing all plants.');
            }
        }
        // --- End RBAC Plant Filtering ---

        // Append other dynamic filters...
        if (this.filters.firstName) filterParams.push(`firstName||$contL||${this.filters.firstName}`);
        if (this.filters.lastName) filterParams.push(`lastName||$contL||${this.filters.lastName}`);
        if (this.filters.email) filterParams.push(`email||$contL||${this.filters.email}`);
        if (this.filters.mobile) filterParams.push(`contactNumber||$contL||${this.filters.mobile}`);
        // Plant filter handled above by RBAC
        if (this.filters.designationId != null) filterParams.push(`designationId||eq||${this.filters.designationId}`);
        if (this.filters.departmentId != null) filterParams.push(`departmentId||eq||${this.filters.departmentId}`);
        if (this.filters.adminRoleId != null) filterParams.push(`adminsRoleId||eq||${this.filters.adminRoleId}`);

        const data = {
            limit: 10000, // Large limit for "all"
            sort: `${this.filters.sortField || 'id'},${this.filters.sortDirection || 'DESC'}`,
            filter: filterParams,
            // Ensure 'plant' join is correct for filtering by plant.id
            join: ['plant', 'department', 'designation', 'adminsRole']
        };

        try {
            const param = createAxiosConfig(data);
            console.log("[fetchAllFiltered - Inactive] Final API Request Params:", JSON.stringify(data, null, 2));
            const response = await this.adminService.getAdmin(param);
            return response?.data ?? response ?? [];
        } catch (error: any) {
            console.error("Error fetching all inactive users for download:", error);
            this.toast?.showErrorToast(error?.response?.data?.message || "Failed to retrieve full data for download.");
            return null;
        } finally {
             this.listLoading = false;
        }
    }

    async downloadExcel(type: 'current' | 'all') {
        const currentTab = this.tabs[this.selectedTabIndex];
        if (!currentTab || this.isDownloadingExcel || this.listLoading) return;

        this.isDownloadingExcel = true;
        this.downloadType = type;
        this.toast?.showSuccessToast(`Preparing download for ${type === 'current' ? 'current page' : 'all filtered'} ${currentTab.title.toLowerCase()} users...`);

        let dataToExport: InactiveUserItem[] | null = null;

        try {
            if (type === 'all') { dataToExport = await this.fetchAllFilteredInactiveUsers(); }
            else { dataToExport = this.getCurrentListData() ?? null; }

            if (dataToExport === null) { this.toast?.showErrorToast("Failed to retrieve data for download."); return; }
            if (!dataToExport || dataToExport.length === 0) { this.toast?.showErrorToast(`No ${currentTab.title.toLowerCase()} users available to download.`); return; }

            console.log(`Fetched ${dataToExport.length} users for Excel export (${type}).`);

            const dataForExcel = dataToExport.map(user => ({
                'User ID': user.id,
                'First Name': user.firstName,
                'Last Name': user.lastName,
                'Email': user.email,
                'Contact Number': user.contactNumber ?? 'N/A',
                'Role': user.adminsRole?.name ?? 'N/A',
                'DOB': user.dob ? new Date(user.dob).toLocaleDateString() : 'N/A',
                 'Gender': typeof user.gender === 'number' ? (user.gender === 1 ? 'Male' : (user.gender === 0 ? 'Female' : (user.gender === 2 ? 'Other' : 'N/A'))) : (user.gender ?? 'N/A'),
                'Department': user.department?.title ?? 'N/A',
                'Designation': user.designation?.title ?? 'N/A',
                'Assigned Plants': user.plant && user.plant.length > 0
                    ? user.plant.map(p => p.name).filter(Boolean).join(', ') : 'N/A',
                'Status': user.status === 0 ? 'Pending' : (user.status === 2 ? 'Rejected' : 'Unknown'),
                'Created At': user.createdTimestamp ? new Date(user.createdTimestamp).toLocaleString() : 'N/A',
                'Last Updated At': user.updatedTimestamp ? new Date(user.updatedTimestamp).toLocaleString() : 'N/A',
            }));

            const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(dataForExcel);
            const wb: XLSX.WorkBook = XLSX.utils.book_new();
            const safeSheetName = currentTab.title.replace(/[^a-zA-Z0-9]/g, '').substring(0, 30);
            XLSX.utils.book_append_sheet(wb, ws, safeSheetName || 'InactiveUsers');

            const dateStr = new Date().toISOString().slice(0, 10);
            const typeStr = type === 'current' ? `Page${this.currentPage}` : 'All';
            let fileNamePrefix = '';
            if (this.selectedTabIndex === 0) {
                fileNamePrefix = 'inactive_pendingUser';
            } else if (this.selectedTabIndex === 1) {
                fileNamePrefix = 'inactive_rejectedUser';
            }
            const fileName = `${fileNamePrefix}_${typeStr}_${dateStr}.xlsx`;

            XLSX.writeFile(wb, fileName);
            this.toast?.showSuccessToast(`Excel file download started (${type}).`);

        } catch (error) {
            console.error(`Error generating Excel file (${type}):`, error);
            this.toast?.showErrorToast(`An error occurred while generating the Excel file (${type}).`);
        } finally {
            this.isDownloadingExcel = false;
            this.downloadType = null;
        }
    }

    // --- Initialize Reactive Edit Form ---
    initializeEditForm(): void {
        this.editUserForm = this.fb.group({
            adminsRoleId: ['', Validators.required],
            firstName: ['', [Validators.required, Validators.pattern(/^[a-zA-Z\s]{1,30}$/)]],
            lastName: ['', [Validators.required, Validators.pattern(/^[a-zA-Z\s]{1,30}$/)]],
            gender: ['', Validators.required], // Form select uses string values ('0', '1', '2' or 'Male', 'Female', 'Other') - check template
            email: [{ value: '', disabled: true }, [Validators.required, Validators.email, adaniEmailValidator()]],
            contactNumber: ['', [Validators.pattern(/^[0-9]{10}$/)]],
            dob: [''],
            departmentId: [null],
            designationId: [null],
            // plantIds: [[]] // plantIds is NOT part of this edit form (handled elsewhere or not editable here)
            // profilePicture is handled separately
        });
    }

    // --- Data Fetching Methods (with RBAC for Plants) ---
    async getDesignations() {
        const data = { sort: 'title,ASC', filter: ['enabled||eq||true'], limit: 1000 };
        try {
            const param = createAxiosConfig(data);
            const response = await this.designationService.getDesignation(param);
            // Adjust based on actual response structure (response.data or response directly)
            this.availableDesignations = Array.isArray(response) ? response : (response?.data ?? []);
        } catch(error) {
             console.error("Error fetching designations:", error);
             this.availableDesignations = [];
             this.toast?.showErrorToast("Failed to load designations.");
        }
    }

    async getDepartments() {
        const data = { sort: 'title,ASC', filter: ['enabled||eq||true'], limit: 1000 };
         try {
            const param = createAxiosConfig(data);
            const response = await this.departmentService.getDepartments(param);
            // Adjust based on actual response structure
            this.availableDepartments = Array.isArray(response) ? response : (response?.data ?? []);
        } catch(error) {
             console.error("Error fetching departments:", error);
             this.availableDepartments = [];
             this.toast?.showErrorToast("Failed to load departments.");
        }
    }

    async getPlants() {
        const data = { sort: 'name,ASC', filter: ['enabled||eq||true'], limit: 1000 }; // Fetch all enabled initially
        let allEnabledPlants: Plant[] = [];
        try {
            const param = createAxiosConfig(data);
            const response = await this.plantService.getPlants(param);
            // Adjust based on actual response structure
            allEnabledPlants = Array.isArray(response) ? response : (response?.data ?? []);
        } catch(error) {
             console.error("Error fetching plants:", error);
             this.availablePlants = [];
             this.toast?.showErrorToast("Failed to load plants.");
             return; // Stop if plants failed to load
        }

        // --- Filter available plants based on role ---
        if (this.currentUserRole === ROLES.PLANT_ADMIN && this.loggedInPlantIds.length > 0) {
            this.availablePlants = allEnabledPlants.filter(plant => this.loggedInPlantIds.includes(plant.id));
        } else if (this.currentUserRole === ROLES.SUPER_ADMIN) {
            this.availablePlants = allEnabledPlants; // Super admin sees all enabled plants
        } else {
            // No role or other role - show no plants or all? Decide policy. Showing none for now.
            this.availablePlants = [];
        }
         console.log(`Loaded ${this.availablePlants.length} plants accessible to current user for filter.`);
    }

    // --- Filter Modal Methods ---
    openFilterModal(): void { this.isFilterModalOpen = true; }
    closeFilterModal(): void { this.isFilterModalOpen = false; }

    applyFilters(): void {
        // Check if the form is valid before applying filters
        if (this.filterForm && this.filterForm.invalid) {
            // Mark all fields as touched to trigger validation display
            Object.keys(this.filterForm.controls).forEach(key => {
                const control = this.filterForm.controls[key];
                control.markAsTouched();
            });

            // Show error toast
            this.toast?.showErrorToast('Please correct the validation errors in the filter form.');
            return;
        }

        // Trim all string values to prevent whitespace-only searches
        if (this.filters.firstName) this.filters.firstName = this.filters.firstName.trim();
        if (this.filters.lastName) this.filters.lastName = this.filters.lastName.trim();
        if (this.filters.email) this.filters.email = this.filters.email.trim();
        if (this.filters.mobile) this.filters.mobile = this.filters.mobile.trim();

        // Validate email format if provided
        if (this.filters.email) {
            // First check basic email format
            const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
            if (!emailRegex.test(this.filters.email)) {
                this.toast?.showErrorToast("Please enter a valid email address.");
                return;
            }

            // Then check for adani.com domain
            if (!this.filters.email.toLowerCase().endsWith('@adani.com')) {
                this.toast?.showErrorToast("Email must use the @adani.com domain.");
                return;
            }
        }

        this.currentPage = 1;
        this.loadUsersForCurrentTab();
        this.closeFilterModal();
    }

    resetFilters(): void {
        this.filters = {
            firstName: null, lastName: null, email: null, mobile: null,
            plantId: null, designationId: null, departmentId: null, adminRoleId: null,
            sortField: 'id', sortDirection: 'DESC'
        };

        // Reset form validation states if form exists
        if (this.filterForm) {
            this.filterForm.resetForm();
            this.filterForm.form.patchValue(this.filters);
        }

        this.currentPage = 1;
        this.loadUsersForCurrentTab();
        this.closeFilterModal();
    }

    // --- Edit Modal Methods (with RBAC) ---
    openEditModal(user: User): void {
        if (!user) {
            console.error("Cannot edit: User data provided is invalid.");
            this.toast?.showErrorToast("Could not load user data for editing.");
            return;
        }

        // --- RBAC Check for Plant Admin ---
        if (this.currentUserRole === ROLES.PLANT_ADMIN) {
             const userPlantIds = user.plant?.map(p => p.id).filter(id => id != null) as number[] || [];
             const canAccess = userPlantIds.some(pid => this.loggedInPlantIds.includes(pid));
             if (!canAccess && userPlantIds.length > 0) {
                 this.toast?.showErrorToast("You do not have permission to edit users for the assigned plant(s).");
                 return;
             }
             if (userPlantIds.length === 0) {
                 this.toast?.showErrorToast("Cannot edit users with no assigned plant.");
                 return;
             }
        }
        // --- End RBAC Check ---

        console.log("Opening edit modal for user:", user);
        this.selectedUserForEdit = user;
        this.submitted = false; this.editLoading = false; this.selectedFile = null;
        this.resetFileInput();
        this.profileImageUrl = user.profilePicture || '../../../assets/svg/Avatar.svg';

        let formattedDob: string | null = null;
        if (user.dob) {
            try {
                const date = new Date(user.dob);
                if (!isNaN(date.getTime())) { formattedDob = date.toISOString().split('T')[0]; }
                else if (typeof user.dob === 'string' && user.dob.match(/^\d{4}-\d{2}-\d{2}/)) { formattedDob = user.dob.split('T')[0]; }
            } catch (e) { console.error("Error formatting DOB:", user.dob, e); }
        }

        this.editUserForm.reset({}, { emitEvent: false });
        // Map numeric gender to string for select preselection
        let genderValue = '';
        if (user.gender !== undefined && user.gender !== null) {
            if (user.gender === 1 || user.gender === '1') {
                genderValue = 'Male';
            } else if (user.gender === 0 || user.gender === '0') {
                genderValue = 'Female';
            } else if (user.gender === 2 || user.gender === '2') {
                genderValue = 'Other';
            } else if (typeof user.gender === 'string') {
                genderValue = user.gender;
            }
        }
        this.editUserForm.patchValue({
            adminsRoleId: user.adminsRole?.id ?? null, // Use null for default if needed
            firstName: user.firstName,
            lastName: user.lastName,
            gender: genderValue,
            email: user.email,
            contactNumber: user.contactNumber,
            dob: formattedDob,
            departmentId: user.department?.id ?? null,
            designationId: user.designation?.id ?? null,
             // plantIds are NOT patched here as they aren't in this form
        });

        // Disable role dropdown for plant admin
        if (this.currentUserRole === ROLES.PLANT_ADMIN) {
            this.editUserForm.get('adminsRoleId')?.disable();
        } else {
            this.editUserForm.get('adminsRoleId')?.enable();
        }

        this.isEditModalOpen = true;
    }

    closeEditModal(): void {
        this.isEditModalOpen = false;
        this.selectedUserForEdit = null;
        this.editUserForm.reset();
        this.profileImageUrl = null;
        this.selectedFile = null;
        this.submitted = false;
        this.editLoading = false;
    }

    onFileChange(event: Event): void {
        const element = event.currentTarget as HTMLInputElement;
        const fileList: FileList | null = element.files;

        if (fileList && fileList.length > 0) {
            const file = fileList[0];
            if (!file.type.startsWith('image/')) {
                this.toast?.showErrorToast('Please select a valid image file.');
                this.resetFileInput();
                return;
            }
            this.selectedFile = file;
            const reader = new FileReader();
            reader.onload = (e) => { this.profileImageUrl = e.target?.result ?? null; };
            reader.readAsDataURL(this.selectedFile);
        } else {
            this.resetFileInput(); // Resets file and preview
        }
    }

    private resetFileInput(): void {
        if (this.fileInput) { this.fileInput.nativeElement.value = ""; }
        this.selectedFile = null;
        this.profileImageUrl = this.selectedUserForEdit?.profilePicture || '../../../assets/svg/Avatar.svg';
    }

    resetEditForm() {
        if (this.selectedUserForEdit) { this.openEditModal(this.selectedUserForEdit); }
    }

    // Reusable async email existence check for add/edit forms
    async checkEmailExistActiveUser(form: FormGroup, field: string) {
        const control = form.get(field);
        if (control?.valid) {
            const email = control.value.toLowerCase().trim();
            try {
                const response: any = await this.adminService.checkEmailAndRole({ email });
                if (response?.responseCode === 200) {
                    // If backend says "admin found", show custom message
                    const customMsg = response?.message === 'admin found'
                        ? 'Email already used, try a different one.'
                        : (response.message || 'This email is already registered.');
                    control.setErrors({ ...control.errors, emailTaken: true, emailTakenMsg: customMsg });
                    this.toast?.showErrorToast(customMsg);
                } else {
                    // Remove only the emailTaken error if present
                    if (control.hasError('emailTaken')) {
                        const errors = { ...control.errors };
                        delete errors['emailTaken'];
                        delete errors['emailTakenMsg'];
                        control.setErrors(Object.keys(errors).length ? errors : null);
                    }
                }
            } catch (err) {
                // On error or not found, clear the error
                if (control.hasError('emailTaken')) {
                    const errors = { ...control.errors };
                    delete errors['emailTaken'];
                    delete errors['emailTakenMsg'];
                    control.setErrors(Object.keys(errors).length ? errors : null);
                }
                // Optionally log error
                console.error("Error checking email:", err);
            }
        }
    }

    // Reusable async contact existence check for add/edit forms
    async checkContactExistActiveUser(form: FormGroup, field: string) {
        const control = form.get(field);
        if (control?.valid) {
            const contactNumber = control.value.toLowerCase().trim();

            // If in edit mode and the contact number is unchanged for the current user, skip uniqueness check
            if (
                this.isEditModalOpen &&
                this.selectedUserForEdit &&
                this.selectedUserForEdit.contactNumber &&
                this.selectedUserForEdit.contactNumber.toLowerCase().trim() === contactNumber
            ) {
                // Remove only the contactTaken error if present
                if (control.hasError('contactTaken')) {
                    const errors = { ...control.errors };
                    delete errors['contactTaken'];
                    delete errors['contactTakenMsg'];
                    control.setErrors(Object.keys(errors).length ? errors : null);
                }
                return;
            }

            try {
                const response: any = await this.adminService.checkContactNumber({ contactNumber });
                if (response?.responseCode === 300) {
                    control.setErrors({ ...control.errors, contactTaken: true, contactTakenMsg: response.message || 'This contact number is already registered.' });
                    this.toast?.showErrorToast(response.message || 'This contact number is already registered.');
                } else {
                    // Remove only the contactTaken error if present
                    if (control.hasError('contactTaken')) {
                        const errors = { ...control.errors };
                        delete errors['contactTaken'];
                        delete errors['contactTakenMsg'];
                        control.setErrors(Object.keys(errors).length ? errors : null);
                    }
                }
            } catch (err) {
                // On error or not found, clear the error
                if (control.hasError('contactTaken')) {
                    const errors = { ...control.errors };
                    delete errors['contactTaken'];
                    delete errors['contactTakenMsg'];
                    control.setErrors(Object.keys(errors).length ? errors : null);
                }
                // Optionally log error
                console.error("Error checking contact number:", err);
            }
        }
    }

    async submitEditForm(): Promise<void> {
        this.submitted = true;
        if (this.editUserForm.invalid) {
            console.warn('Reactive Edit form is invalid.');
            this.editUserForm.markAllAsTouched();
            this.toast?.showErrorToast('Please review the form for errors.');
            return;
        }

        if (!this.selectedUserForEdit || !this.selectedUserForEdit.id) {
            console.error('No user selected or user ID missing for update.');
            this.toast?.showErrorToast('Cannot update: User information is missing.');
            return;
        }

        // --- RBAC Check before submitting ---
         if (this.currentUserRole === ROLES.PLANT_ADMIN) {
             const userPlantIds = this.selectedUserForEdit.plant?.map(p => p.id).filter(id => id != null) as number[] || [];
             const canAccess = userPlantIds.some(pid => this.loggedInPlantIds.includes(pid));
              if (!canAccess) {
                 this.toast?.showErrorToast(`Permission denied to save changes for this user.`);
                 return;
             }
        }
        // --- End RBAC Check ---

        this.editLoading = true;
        let avatarUrlToUpdate: string | undefined | null = this.selectedUserForEdit?.profilePicture;

        if (this.selectedFile) {
            const fileFormData = new FormData();
            fileFormData.append('file', this.selectedFile, this.selectedFile.name);
            try {
                const uploadResponse = await this.uploadService.upload(fileFormData);
                if (uploadResponse && typeof uploadResponse === 'string') { avatarUrlToUpdate = uploadResponse; }
                else { throw new Error('Invalid upload response'); }
            } catch (uploadError) {
                console.error('Error uploading avatar:', uploadError);
                this.toast?.showErrorToast('Failed to upload new avatar.');
                this.editLoading = false; return;
            }
        }

        const formValue = this.editUserForm.getRawValue();
        const updateData: { [key: string]: any } = {
            adminsRoleId: formValue.adminsRoleId, firstName: formValue.firstName, lastName: formValue.lastName,
            gender: formValue.gender, // Send the string value ('0', '1', '2' or 'Male' etc.)
            contactNumber: formValue.contactNumber || null, dob: formValue.dob || null,
            departmentId: formValue.departmentId || null, designationId: formValue.designationId || null,
            email: formValue.email, profilePicture: avatarUrlToUpdate ?? null
            // Status and Plant assignment are NOT updated here
        };

        try {
            const updatePayload = {
                tableName: 'admins', id: this.selectedUserForEdit.id, data: updateData,
                createdBy: this.loggedInAdminId // Pass logged-in user ID if required by backend
            };
            await this.updateService.update(updatePayload);
            this.toast?.showSuccessToast('User details saved successfully!');
            this.closeEditModal();
            this.loadUsersForCurrentTab();
        } catch (updateError: any) {
            console.error('Error updating user record:', updateError);
            this.toast?.showErrorToast(updateError?.response?.data?.message || 'Failed to save user details.');
        } finally {
            this.editLoading = false;
        }
    }

    // --- Centralized Data Fetching (with RBAC) ---
    async fetchUsers(page: number, status: number, currentFilters: UserFilter): Promise<{ data: User[], total: number } | null> {
        this.listLoading = true;
        const filterParams: string[] = [ 'id||ne||1', `status||eq||${status}` ];
        // *** CHANGE: Use 'plant.id' consistently ***
        const plantFilterPath = 'plant.id';

        // --- RBAC Plant Filtering for API (Paged Data) ---
        let appliedPlantFilter = false;
        if (this.currentUserRole === ROLES.PLANT_ADMIN) {
            if (this.loggedInPlantIds.length > 0) {

                // *** START FIX ***
                // Convert the selected filter plant ID to a number for reliable comparison
                const selectedPlantIdNum = currentFilters.plantId != null ? Number(currentFilters.plantId) : null;

                // Add logging to verify types and values just before the check
                console.log('[fetchUsers - Inactive] Checking Plant Filter:');
                console.log('  Raw currentFilters.plantId:', currentFilters.plantId, typeof currentFilters.plantId);
                console.log('  Converted selectedPlantIdNum:', selectedPlantIdNum, typeof selectedPlantIdNum);
                console.log('  loggedInPlantIds:', this.loggedInPlantIds);
                console.log('  Includes Check Result:', selectedPlantIdNum != null && this.loggedInPlantIds.includes(selectedPlantIdNum));

                // Check if a specific plant filter is applied AND it's one the admin manages (using the number)
                if (selectedPlantIdNum != null && this.loggedInPlantIds.includes(selectedPlantIdNum)) {
                    // A specific, valid plant IS selected - filter ONLY by this plant
                    filterParams.push(`${plantFilterPath}||eq||${selectedPlantIdNum}`); // Use the number
                    appliedPlantFilter = true;
                    console.log(`[fetchUsers - Inactive] Applying EQ filter for plant: ${selectedPlantIdNum}`);
                } else {
                    // No specific valid plant selected -> filter by ALL plants the admin has access to.
                    filterParams.push(`${plantFilterPath}||$in||${this.loggedInPlantIds.join(',')}`);
                    appliedPlantFilter = true;
                    console.log(`[fetchUsers - Inactive] Applying IN filter for plants: ${this.loggedInPlantIds.join(',')}`);

                    // Reset the UI filter state if an invalid/unmanaged plant was selected
                    if (currentFilters.plantId != null && (selectedPlantIdNum == null || !this.loggedInPlantIds.includes(selectedPlantIdNum))) {
                         console.warn(`[fetchUsers - Inactive] Invalid or non-managed plant filter (${currentFilters.plantId}) detected, resetting UI filter and showing all accessible plants.`);
                         this.filters.plantId = null; // Reset the filter state in the component
                     }
                }
                // *** END FIX ***

            } else {
                // Plant Admin has no assigned plants, block results
                filterParams.push(`${plantFilterPath}||eq||-1`);
                appliedPlantFilter = true;
                console.log('[fetchUsers - Inactive] Plant Admin has no plants, blocking results.');
            }
        } else if (this.currentUserRole === ROLES.SUPER_ADMIN) {
            // Super Admin logic
            if (currentFilters.plantId != null) {
                filterParams.push(`${plantFilterPath}||eq||${currentFilters.plantId}`);
                appliedPlantFilter = true;
                console.log(`[fetchUsers - Inactive] Super Admin applying EQ filter for plant: ${currentFilters.plantId}`);
            } else {
                 console.log('[fetchUsers - Inactive] Super Admin showing all plants.');
            }
        }
        // --- End RBAC Plant Filtering ---

        // Add other filters...
        if (currentFilters.firstName) filterParams.push(`firstName||$contL||${currentFilters.firstName}`);
        if (currentFilters.lastName) filterParams.push(`lastName||$contL||${currentFilters.lastName}`);
        if (currentFilters.email) filterParams.push(`email||$contL||${currentFilters.email}`);
        if (currentFilters.mobile) filterParams.push(`contactNumber||$contL||${currentFilters.mobile}`);
        // Plant filter handled above by RBAC logic
        if (currentFilters.designationId != null) filterParams.push(`designationId||eq||${currentFilters.designationId}`);
        if (currentFilters.departmentId != null) filterParams.push(`departmentId||eq||${currentFilters.departmentId}`);
        if (currentFilters.adminRoleId != null) filterParams.push(`adminsRoleId||eq||${currentFilters.adminRoleId}`);

        const data = {
            page: page, limit: this.itemsPerPage,
            sort: `${currentFilters.sortField || 'id'},${currentFilters.sortDirection || 'DESC'}`,
            filter: filterParams,
            // Ensure 'plant' join is correct for filtering by plant.id
            join: ['plant', 'department', 'designation', 'adminsRole']
        };

        try {
            const param = createAxiosConfig(data);
            console.log("[fetchUsers - Inactive] Final API Request Params:", JSON.stringify(data, null, 2));
            const response = await this.adminService.getAdmin(param);
            return { data: response?.data ?? [], total: response?.total ?? 0 };
        } catch (error: any) {
            console.error("Error fetching inactive users:", error);
            this.toast?.showErrorToast(error?.response?.data?.message || 'Failed to load user list.');
            return null;
        } finally {
            this.listLoading = false;
        }
    }

    // Helper to load data into the correct list based on the current tab
    async loadUsersForCurrentTab() {
        const currentTab = this.tabs[this.selectedTabIndex];
        if (!currentTab) return;
        const result = await this.fetchUsers(this.currentPage, currentTab.status, this.filters);
        this.pendingUserList = []; // Reset lists
        this.rejectedUserList = [];
        this.totalItems = 0;
        if (result) {
            this.totalItems = result.total;
             // Assign to correct list property based on listKey ('pendingUserList' or 'rejectedUserList')
             this[currentTab.listKey] = result.data;
        }
    }

    // --- Tab and Pagination Handlers ---
    onTabSelected(index: number) {
        if (this.selectedTabIndex === index || this.listLoading) return; // Prevent changing tab while loading
        this.selectedTabIndex = index;
        this.currentPage = 1;
        this.totalItems = 0; // Reset total items
        // Reset or keep filters? Resetting might be clearer.
        // this.resetFilters(); // Uncomment if filters should reset on tab change
        this.loadUsersForCurrentTab();
    }

    onPageChange(page: number) {
        if (this.currentPage === page || this.listLoading) return; // Prevent changing page while loading
        this.currentPage = page;
        this.loadUsersForCurrentTab();
    }

    // --- Table Sorting ---
    getSortClass(key: string): string {
        if (this.filters.sortField === key) {
            return this.filters.sortDirection === 'ASC' ? 'sort-asc' : 'sort-desc';
        }
        return 'sort-none';
    }

    sortBy(field: string) {
        if (this.listLoading) return; // Prevent sorting while loading
        if (this.filters.sortField === field) {
            this.filters.sortDirection = this.filters.sortDirection === 'ASC' ? 'DESC' : 'ASC';
        } else {
            this.filters.sortField = field;
            this.filters.sortDirection = 'DESC'; // Default to DESC on new field
        }
        this.currentPage = 1; // Go back to first page on sort change
        this.loadUsersForCurrentTab();
    }

    // --- User Actions (Accept/Reject/Delete - with RBAC implicitly handled by confirmation modal open logic) ---
    async updateAdmin(user: User, newStatus: number) {
        // RBAC check already done in openUserActionConfirmation -> confirmUserAction
        if (!user || user.id === undefined) return;

        const actionDesc = newStatus === 1 ? 'activate/accept' : 'reject';
        const payload = {
            tableName: 'admins', id: user.id, data: { status: newStatus },
            createdBy: this.loggedInAdminId // Add if needed
        };
        this.listLoading = true;
        try {
            await this.updateService.update(payload);
            this.toast?.showSuccessToast(`User status updated successfully.`);
            // Refresh the list, ensuring pagination is handled if last item on page is moved
            if (this[this.tabs[this.selectedTabIndex].listKey].length === 1 && this.currentPage > 1) {
                 // If the user was the only one on the current page (and not page 1),
                 // go to the previous page after the update causes them to be removed.
                 this.currentPage--;
            }
            this.loadUsersForCurrentTab(); // Refresh the current tab's view
        } catch (error: any) {
            console.error(`Error updating status to ${newStatus} for user ${user.id}:`, error);
             const errorMsg = error?.response?.data?.message || `Failed to ${actionDesc} user.`;
            this.toast?.showErrorToast(errorMsg);
        } finally {
            this.listLoading = false;
        }
    }

    // --- Display Helper ---
    showMorePlants(user: User): void {
        const plantNames = user.plant?.map((p: Plant) => p.name).filter(Boolean).join('\n') || 'None assigned';
        // Use a more robust modal/popover instead of alert for better UX in real app
        alert(`Assigned Plants for ${user.firstName} ${user.lastName}:\n\n${plantNames}`);
    }

    // Helper method to safely get plant name
    getPlantName(plant: any): string {
        return plant?.name || 'Unknown Plant';
    }

}
