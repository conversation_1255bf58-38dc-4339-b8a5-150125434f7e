<app-toast-message></app-toast-message>
<app-tab [tabs]="tabs" [selectedTabIndex]="selectedTabIndex" (tabSelected)="onTabSelected($event)">
</app-tab>

<!-- Pending Users -->
<div *ngIf="selectedTabIndex == 0" class="card custom-card" id="pending-user-list">
    <div class="card-header">
        <div class="row align-items-center">
            <div class="col d-flex align-items-center">
                <h6 class="mb-0">Pending Admin List</h6> <!-- Updated Title -->
            </div>
            <div class="col text-end d-flex align-items-center justify-content-end">
                <!-- *** REPLACE the existing simple Download button with this Dropdown *** -->
                <div ngbDropdown class="d-inline-block me-2">
                    <button type="button" class="btn btn-sm adani-btn dropdown-toggle"
                        [id]="'downloadExcelDropdown' + selectedTabIndex" ngbDropdownToggle
                        [disabled]="isDownloadingExcel || listLoading">
                        <span *ngIf="!isDownloadingExcel">
                            <i class="bi bi-file-earmark-excel me-1"></i> Download Excel
                        </span>
                        <span *ngIf="isDownloadingExcel">
                            <span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
                            Downloading {{ downloadType === 'current' ? 'Page' : (downloadType === 'all' ? 'All' : '')
                            }}...
                        </span>
                    </button>
                    <ul ngbDropdownMenu [attr.aria-labelledby]="'downloadExcelDropdown' + selectedTabIndex">
                        <li>
                            <button ngbDropdownItem (click)="downloadExcel('current')"
                                [disabled]="isDownloadingExcel || listLoading || (getCurrentListData()?.length ?? 0) === 0">
                                <i class="bi bi-download me-1"></i> Download Current Page ({{
                                getCurrentListData()?.length ?? 0 }})
                            </button>
                        </li>
                        <li>
                            <button ngbDropdownItem (click)="downloadExcel('all')"
                                [disabled]="isDownloadingExcel || listLoading || totalItems === 0">
                                <i class="bi bi-cloud-download me-1"></i> Download All Filtered ({{ totalItems }})
                            </button>
                        </li>
                    </ul>
                </div>
                <!-- *** END REPLACEMENT *** -->
                <img src="../../../assets/svg/filter.svg" class="filter-button ms-3" (click)="openFilterModal()"
                    alt="Filter" style="width: 35px;" />
            </div>
        </div>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered custom-table">
                <thead class="table-header">
                    <tr>
                        <th scope="col" class="col-avatar">Avatar</th>
                        <th scope="col" class="col-admin-details">Admin Details</th>
                        <th scope="col" class="col-plant-info">Plant Information</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- Loading State -->
                    <tr *ngIf="listLoading">
                        <td colspan="3" class="text-center p-4">
                            <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                            Loading pending users...
                        </td>
                    </tr>
                    <!-- No Data State -->
                    <tr *ngIf="!listLoading && (!pendingUserList || pendingUserList.length === 0)">
                        <td colspan="3" class="text-center p-4 text-muted">
                            No pending users found.
                        </td>
                    </tr>
                    <!-- Data Rows -->
                    <tr *ngFor="let item of pendingUserList">
                        <td class="text-center">
                            <div class="user-id mb-2 fw-bold">ID: {{item.id}}</div>
                            <img [src]="item.profilePicture || '../../../assets/svg/Avatar.svg'"
                                class="img-thumbnail rounded-circle" alt="Avatar">
                            <div class="d-flex flex-row gap-1 justify-content-center mt-2">
                                <button type="button" class="btn btn-success btn-sm"
                                    (click)="openUserActionConfirmation(item, 'accept')" title="Accept User">
                                    <i class="bi bi-check-lg"></i>
                                </button>
                                <button type="button" class="btn btn-danger btn-sm"
                                    (click)="openUserActionConfirmation(item, 'reject')" title="Reject User">
                                    <i class="bi bi-x-lg"></i>
                                </button>
                                <button type="button" class="btn btn-warning btn-sm" title="Edit User Details"
                                    (click)="openEditModal(item)">
                                    <i class="bi bi-pencil"></i>
                                </button>
                            </div>
                        </td>
                        <td>
                            <div class="details-container">
                                <p class="label-value"><strong>Name:</strong> <span class="value-text">{{item.firstName}} {{item.lastName}}</span></p>
                                <p class="label-value"><strong>Email:</strong> <span class="value-text">{{item.email}}</span></p>
                                <p class="label-value"><strong>Contact:</strong> <span class="value-text">{{item.contactNumber || 'N/A'}}</span></p>
                                <p class="label-value"><strong>DOB:</strong> <span class="value-text">{{item.dob ? (item.dob | date:'yyyy-MM-dd') : 'N/A'}}</span></p>
                                <p class="label-value">
                                    <strong>Gender:</strong>
                                    <span class="value-text">
                                        <span [ngSwitch]="+(item.gender ?? -1)">
                                            <span *ngSwitchCase="0">Female</span>
                                            <span *ngSwitchCase="1">Male</span>
                                            <span *ngSwitchCase="2">Other</span>
                                            <span *ngSwitchDefault>{{ item.gender || 'N/A' }}</span>
                                        </span>
                                    </span>
                                </p>
                                <p class="label-value" *ngIf="item.createdTimestamp != null">
                                    <strong>Created:</strong> <span class="value-text">{{item.createdTimestamp | date:'short'}}</span>
                                </p>
                                <p class="label-value">
                                    <strong>Role:</strong>
                                    <span class="value-text">
                                        <span *ngIf="item.adminsRole?.id === 1" class="badge bg-primary">Super Admin</span>
                                        <span *ngIf="item.adminsRole?.id === 2" class="badge bg-warning">Plant Admin</span>
                                        <span *ngIf="item.adminsRole?.id === 3" class="badge bg-secondary">User</span>
                                        <span *ngIf="!item.adminsRole?.id">{{ item.adminsRole?.name || 'N/A' }}</span>
                                    </span>
                                </p>
                                <p class="label-value"><strong>Designation:</strong> <span class="value-text">{{item.designation?.title || 'N/A'}}</span></p>
                            </div>
                        </td>
                        <td>
                            <div class="details-container">
                                <p class="label-value"><strong>Department:</strong> <span class="value-text">{{ item.department?.title || 'N/A' }}</span></p>
                                <p class="label-value"><strong>Designation:</strong> <span class="value-text">{{ item.designation?.title || 'N/A' }}</span></p>
                                <p class="label-value">
                                    <strong>Plants:</strong>
                                    <span class="value-text">
                                        <div class="plant-badges-container" *ngIf="(item.plant?.length ?? 0) > 0">
                                            <span class="plant-badge bg-primary" *ngFor="let plant of item.plant | slice:0:5">{{ plant?.name ||
                                                'Unknown Plant' }}</span>
                                            <a *ngIf="(item.plant?.length ?? 0) > 5" (click)="showMorePlants(item)"
                                                class="text-success cursor-pointer">
                                                ...and {{(item.plant?.length ?? 0) - 5}} more
                                            </a>
                                        </div>
                                        <span *ngIf="!item.plant || item.plant.length === 0" class="text-muted">No plants
                                            assigned</span>
                                    </span>
                                </p>
                            </div>
                        </td>

                    </tr>
                </tbody>
            </table>
        </div>
    </div>
    <div class="card-footer text-muted text-center">
        <app-pagination [currentPage]="currentPage" [totalItems]="totalItems" [itemsPerPage]="itemsPerPage"
            (pageChange)="onPageChange($event)"></app-pagination>
    </div>
</div>

<!-- Rejected Users -->
<div *ngIf="selectedTabIndex == 1" class="card custom-card" id="rejected-user-list">
    <div class="card-header">
        <div class="row align-items-center">
            <div class="col d-flex align-items-center">
                <h6 class="mb-0">Rejected Admin List</h6> <!-- Updated Title -->
            </div>
            <div class="col text-end d-flex align-items-center justify-content-end">
                <!-- *** REPLACE the existing simple Download button with this Dropdown *** -->
                <div ngbDropdown class="d-inline-block me-2">
                    <button type="button" class="btn btn-sm adani-btn dropdown-toggle"
                        [id]="'downloadExcelDropdown' + selectedTabIndex" ngbDropdownToggle
                        [disabled]="isDownloadingExcel || listLoading">
                        <span *ngIf="!isDownloadingExcel">
                            <i class="bi bi-file-earmark-excel me-1"></i> Download Excel
                        </span>
                        <span *ngIf="isDownloadingExcel">
                            <span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
                            Downloading {{ downloadType === 'current' ? 'Page' : (downloadType === 'all' ? 'All' : '')
                            }}...
                        </span>
                    </button>
                    <ul ngbDropdownMenu [attr.aria-labelledby]="'downloadExcelDropdown' + selectedTabIndex">
                        <li>
                            <button ngbDropdownItem (click)="downloadExcel('current')"
                                [disabled]="isDownloadingExcel || listLoading || (getCurrentListData()?.length ?? 0) === 0">
                                <i class="bi bi-download me-1"></i> Download Current Page ({{
                                getCurrentListData()?.length ?? 0 }})
                            </button>
                        </li>
                        <li>
                            <button ngbDropdownItem (click)="downloadExcel('all')"
                                [disabled]="isDownloadingExcel || listLoading || totalItems === 0">
                                <i class="bi bi-cloud-download me-1"></i> Download All Filtered ({{ totalItems }})
                            </button>
                        </li>
                    </ul>
                </div>
                <!-- *** END REPLACEMENT *** -->
                <img src="../../../assets/svg/filter.svg" class="filter-button ms-3" (click)="openFilterModal()"
                    alt="Filter" style="width: 35px;" />
            </div>
        </div>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered custom-table">
                <thead class="table-header">
                    <tr>
                        <th scope="col" class="col-avatar">Avatar</th>
                        <th scope="col" class="col-admin-details">Admin Details</th>
                        <th scope="col" class="col-plant-info">Plant Information</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- Loading State -->
                    <tr *ngIf="listLoading">
                        <td colspan="3" class="text-center p-4">
                            <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                            Loading rejected users...
                        </td>
                    </tr>
                    <!-- No Data State -->
                    <tr *ngIf="!listLoading && (!rejectedUserList || rejectedUserList.length === 0)">
                        <td colspan="3" class="text-center p-4 text-muted">
                            No rejected users found.
                        </td>
                    </tr>
                    <!-- Data Rows -->
                    <tr *ngFor="let item of rejectedUserList">
                        <td class="text-center">
                            <div class="user-id mb-2 fw-bold">ID: {{ item.id }}</div>
                            <img [src]="item.profilePicture || '../../../assets/svg/Avatar.svg'"
                                class="img-thumbnail rounded-circle" alt="Avatar">
                            <div class="d-flex flex-row gap-1 justify-content-center mt-2">
                                <!-- Activate Button: Only Super Admin can activate from Rejected -->
                                <button *ngIf="currentUserRole === componentRoles.SUPER_ADMIN" type="button"
                                    class="btn btn-success btn-sm"
                                    (click)="openUserActionConfirmation(item, 'activate')" title="Move to Active">
                                    <i class="bi bi-arrow-up-circle"></i>
                                </button>
                                <!-- Edit Button: Super Admin OR Plant Admin -->
                                <button
                                    *ngIf="currentUserRole === componentRoles.PLANT_ADMIN || currentUserRole === componentRoles.SUPER_ADMIN"
                                    type="button" class="btn btn-warning btn-sm" title="Edit User Details"
                                    (click)="openEditModal(item)">
                                    <i class="bi bi-pencil"></i>
                                </button>
                                <!-- Delete Button was removed as per the task -->
                            </div>
                        </td>
                        <td>
                            <div class="admin-details-container">
                                <div class="admin-details-row">
                                    <span class="admin-details-label">Name:</span>
                                    <span class="admin-details-value">{{ item.firstName }} {{ item.lastName }}</span>
                                </div>
                                <div class="admin-details-row">
                                    <span class="admin-details-label">Email:</span>
                                    <span class="admin-details-value">
                                        <a [href]="'mailto:' + item.email" class="text-primary">{{ item.email }}</a>
                                    </span>
                                </div>
                                <div class="admin-details-row">
                                    <span class="admin-details-label">Contact:</span>
                                    <span class="admin-details-value">{{ item.contactNumber || 'N/A' }}</span>
                                </div>
                                <div class="admin-details-row">
                                    <span class="admin-details-label">DOB:</span>
                                    <span class="admin-details-value">{{ item.dob ? (item.dob | date: 'yyyy-MM-dd') :
                                        'N/A' }}</span>
                                </div>
                                <div class="admin-details-row">
                                    <span class="admin-details-label">Gender:</span>
                                    <span class="admin-details-value">
                                        <span [ngSwitch]="+(item.gender ?? -1)">
                                            <span *ngSwitchCase="0">Female</span>
                                            <span *ngSwitchCase="1">Male</span>
                                            <span *ngSwitchCase="2">Other</span>
                                            <span *ngSwitchDefault>{{ item.gender || 'N/A' }}</span>
                                        </span>
                                    </span>
                                </div>
                                <div class="admin-details-row" *ngIf="item.createdTimestamp != null">
                                    <span class="admin-details-label">Created:</span>
                                    <span class="admin-details-value">{{item.createdTimestamp | date:'short'}}</span>
                                </div>
                                <div class="admin-details-row">
                                    <span class="admin-details-label">Role:</span>
                                    <span class="admin-details-value">
                                        <span *ngIf="item.adminsRole?.id === 1" class="badge bg-primary">Super Admin</span>
                                        <span *ngIf="item.adminsRole?.id === 2" class="badge bg-warning">Plant Admin</span>
                                        <span *ngIf="item.adminsRole?.id === 3" class="badge bg-secondary">User</span>
                                        <span *ngIf="!item.adminsRole?.id">{{ item.adminsRole?.name || 'N/A' }}</span>
                                    </span>
                                </div>
                                <div class="admin-details-row">
                                    <span class="admin-details-label">Designation:</span>
                                    <span class="admin-details-value">{{item.designation?.title || 'N/A'}}</span>
                                </div>
                            </div>
                        </td>
                        <td>
                            <div class="admin-details-container">
                                <div class="admin-details-row">
                                    <span class="admin-details-label">Department:</span>
                                    <span class="admin-details-value">{{ item.department?.title || 'N/A' }}</span>
                                </div>
                                <div class="admin-details-row">
                                    <span class="admin-details-label">Designation:</span>
                                    <span class="admin-details-value">{{ item.designation?.title || 'N/A' }}</span>
                                </div>
                                <div class="admin-details-row">
                                    <span class="admin-details-label">Plants:</span>
                                    <span class="admin-details-value">
                                        <div class="plant-badges-container" *ngIf="(item.plant?.length ?? 0) > 0">
                                            <span class="plant-badge bg-primary" *ngFor="let plant of item.plant | slice:0:5">{{ plant.name }}</span>
                                            <a *ngIf="(item.plant?.length ?? 0) > 5" (click)="showMorePlants(item)"
                                                class="text-success cursor-pointer">
                                                ...and {{(item.plant?.length ?? 0) - 5}} more
                                            </a>
                                        </div>
                                        <span *ngIf="!item.plant || item.plant.length === 0" class="text-muted">No plants
                                            assigned</span>
                                    </span>
                                </div>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
    <div class="card-footer text-muted text-center">
        <app-pagination [currentPage]="currentPage" [totalItems]="totalItems" [itemsPerPage]="itemsPerPage"
            (pageChange)="onPageChange($event)"></app-pagination>
    </div>
</div>

<!-- Filter Offcanvas (No changes needed for Edit implementation) -->
<app-offcanvas [title]="'Filter Inactive Users'" *ngIf="isFilterModalOpen" (onClickCross)="closeFilterModal()">
    <div class="filter-container p-3">
        <form #filterForm="ngForm" (ngSubmit)="applyFilters()">
            <div class="row g-3">
                <!-- Existing Filter Fields -->
                <div class="col-12">
                    <label class="form-label" for="filterFirstName">First Name</label>
                    <input type="text" id="filterFirstName" class="form-control" placeholder="First Name"
                        [(ngModel)]="filters.firstName" name="firstName" #firstName="ngModel" pattern="^[a-zA-Z\s]*$"
                        maxlength="30" [class.is-invalid]="firstName.invalid && (firstName.dirty || firstName.touched)">
                    <div *ngIf="firstName.invalid && (firstName.dirty || firstName.touched)" class="invalid-feedback">
                        <div *ngIf="firstName.errors?.['pattern']">First name should contain only alphabets.</div>
                    </div>
                    <small class="form-text text-muted" *ngIf="filters.firstName">{{filters.firstName.length}}/30
                        characters</small>
                </div>
                <div class="col-12">
                    <label class="form-label" for="filterLastName">Last Name</label>
                    <input type="text" id="filterLastName" class="form-control" placeholder="Last Name"
                        [(ngModel)]="filters.lastName" name="lastName" #lastName="ngModel" pattern="^[a-zA-Z\s]*$"
                        maxlength="30" [class.is-invalid]="lastName.invalid && (lastName.dirty || lastName.touched)">
                    <div *ngIf="lastName.invalid && (lastName.dirty || lastName.touched)" class="invalid-feedback">
                        <div *ngIf="lastName.errors?.['pattern']">Last name should contain only alphabets.</div>
                    </div>
                    <small class="form-text text-muted" *ngIf="filters.lastName">{{filters.lastName.length}}/30
                        characters</small>
                </div>
                <div class="col-12">
                    <label class="form-label" for="filterEmail">Email</label>
                    <input type="email" id="filterEmail" class="form-control"
                        placeholder="Email with &#64;adani.com domain" [(ngModel)]="filters.email" name="email"
                        #email="ngModel" pattern="[a-zA-Z0-9._%+-]+&#64;adani\.com$"
                        [class.is-invalid]="email.invalid && (email.dirty || email.touched)">
                    <div *ngIf="email.invalid && (email.dirty || email.touched)" class="invalid-feedback">
                        <div *ngIf="email.errors?.['pattern']">Email must use the &#64;adani.com domain.</div>
                    </div>
                </div>
                <div class="col-12">
                    <label class="form-label" for="filterMobile">Mobile Number</label>
                    <input type="text" id="filterMobile" class="form-control" placeholder="Mobile Number"
                        [(ngModel)]="filters.mobile" name="mobile" #mobile="ngModel" pattern="^[0-9]*$" maxlength="10"
                        [class.is-invalid]="mobile.invalid && (mobile.dirty || mobile.touched)">
                    <div *ngIf="mobile.invalid && (mobile.dirty || mobile.touched)" class="invalid-feedback">
                        <div *ngIf="mobile.errors?.['pattern']">Mobile number should contain only digits.</div>
                    </div>
                </div>
                <div class="col-12">
                    <label class="form-label" for="filterPlant">Select Plant</label>
                    <select id="filterPlant" class="form-select" [(ngModel)]="filters.plantId" name="plantId">
                        <option [ngValue]="null">All Plants</option>
                        <option *ngFor="let plant of availablePlants" [value]="plant.id">{{ plant.name }}</option>
                    </select>
                </div>
                <div class="col-12">
                    <label class="form-label" for="filterDesignation">Filter by Designation</label>
                    <select id="filterDesignation" class="form-select" [(ngModel)]="filters.designationId"
                        name="designationId">
                        <option [ngValue]="null">All Designations</option>
                        <option *ngFor="let desg of availableDesignations" [value]="desg.id">{{ desg.title }}</option>
                    </select>
                </div>
                <div class="col-12">
                    <label class="form-label" for="filterDepartment">Filter by Department</label>
                    <select id="filterDepartment" class="form-select" [(ngModel)]="filters.departmentId"
                        name="departmentId">
                        <option [ngValue]="null">All Departments</option>
                        <option *ngFor="let dept of availableDepartments" [value]="dept.id">{{ dept.title }}</option>
                    </select>
                </div>
                <div class="col-12">
                    <label class="form-label" for="filterAdminRole">Search by Admin Role</label>
                    <select id="filterAdminRole" class="form-select" [(ngModel)]="filters.adminRoleId"
                        name="adminRoleId">
                        <option [ngValue]="null">All Roles</option>
                        <option *ngFor="let role of availableAdminRoles" [value]="role.id">{{ role.name }}</option>
                    </select>
                </div>
                <div class="col-12">
                    <label class="form-label" for="filterSortBy">Sort By</label>
                    <select id="filterSortBy" class="form-select" [(ngModel)]="filters.sortField" name="sortField">
                        <option [ngValue]="null">Default Sort (ID DESC)</option>
                        <option *ngFor="let field of availableSortFields" [value]="field.value">{{ field.label }}
                        </option>
                    </select>
                    <label class="form-label mt-2" for="filterSortDir">Sort Direction</label>
                    <select id="filterSortDir" class="form-select" [(ngModel)]="filters.sortDirection"
                        name="sortDirection">
                        <option value="DESC">Descending</option>
                        <option value="ASC">Ascending</option>
                    </select>
                </div>
                <div class="col-12 mt-4 d-grid gap-2">
                    <button type="submit" class="btn adani-btn" [disabled]="filterForm.invalid">
                        <i class="bi bi-search me-1"></i> Search
                    </button>
                    <button type="button" class="btn btn-secondary" (click)="resetFilters()">
                        <i class="bi bi-arrow-clockwise me-1"></i> Reset Filters
                    </button>
                </div>
            </div>
        </form>
    </div>
</app-offcanvas>

<!-- *** NEW: Edit Offcanvas (Reactive Forms) *** -->
<app-offcanvas [title]="'Edit User Details'" *ngIf="isEditModalOpen" (onClickCross)="closeEditModal()">
    <div class="edit-container p-3">

        <!-- Show loading spinner if selectedUserForEdit is being prepared (optional) -->
        <div *ngIf="!editUserForm && isEditModalOpen" class="text-center p-5">
            Loading form...
        </div>

        <!-- Reactive Form -->
        <form *ngIf="editUserForm" [formGroup]="editUserForm" (ngSubmit)="submitEditForm()">
            <div class="row g-3">

                <!-- Profile Picture Upload/Preview -->
                <div class="col-12 mb-3 text-center">
                    <img [src]="profileImageUrl || '../../../assets/svg/Avatar.svg'" alt="Profile Picture Preview"
                        class="img-thumbnail rounded-circle mb-2" width="100" height="100" style="object-fit: cover;" />
                    <label for="editAvatar" class="form-label d-block">Change Avatar</label>
                    <!-- File input triggers onFileChange -->
                    <input type="file" class="form-control form-control-sm" id="editAvatar" #fileInput
                        (change)="onFileChange($event)" accept="image/*" />
                    <!-- No formControlName needed for file input in this pattern -->
                </div>

                <!-- User ID (Display Only) -->
                <div class="col-12">
                    <label class="form-label" for="editUserIdDisplay">User ID</label>
                    <input type="text" id="editUserIdDisplay" class="form-control" [value]="selectedUserForEdit?.id"
                        readonly disabled>
                </div>

                <!-- Email (Readonly) -->
                <div class="col-12">
                    <label class="form-label" for="editUserEmail">Email</label>
                    <!-- Bind to the disabled form control -->
                    <input type="email" id="editUserEmail" class="form-control" formControlName="email" readonly
                        [class.is-invalid]="submitted && editUserForm.controls['email'].errors"
                        (blur)="checkEmailExistActiveUser(editUserForm, 'email')">
                    <!-- Validation shown if needed (e.g., initial data bad), but usually not necessary -->
                    <div *ngIf="submitted && editUserForm.controls['email'].errors" class="invalid-feedback">
                        <div *ngIf="editUserForm.controls['email'].errors?.['required']">Email is required.</div>
                        <div *ngIf="editUserForm.controls['email'].errors?.['email']">Please enter a valid email address.</div>
                        <div *ngIf="editUserForm.controls['email'].errors?.['adaniDomain']">Email must use the &#64;adani.com domain.</div>
                        <div *ngIf="editUserForm.controls['email'].errors?.['emailTaken']">
                            {{ editUserForm.controls['email'].errors?.['emailTakenMsg'] || 'This email is already registered.' }}
                        </div>
                    </div>
                </div>

                <!-- First Name (Required) -->
                <div class="col-12">
                    <label class="form-label" for="editUserFirstName">First Name <span
                            class="text-danger">*</span></label>
                    <input type="text" id="editUserFirstName" class="form-control" formControlName="firstName"
                        placeholder="Enter first name"
                        [class.is-invalid]="submitted && editUserForm.controls['firstName'].errors">
                    <!-- Validation Message -->
                    <div *ngIf="submitted && editUserForm.controls['firstName'].errors" class="invalid-feedback">
                        <div *ngIf="editUserForm.controls['firstName'].errors?.['required']">First name is required.
                        </div>
                        <div *ngIf="editUserForm.controls['firstName'].errors?.['pattern']">Invalid characters or
                            length.</div>
                    </div>
                </div>

                <!-- Last Name (Required) -->
                <div class="col-12">
                    <label class="form-label" for="editUserLastName">Last Name <span
                            class="text-danger">*</span></label>
                    <input type="text" id="editUserLastName" class="form-control" formControlName="lastName"
                        placeholder="Enter last name"
                        [class.is-invalid]="submitted && editUserForm.controls['lastName'].errors">
                    <!-- Validation Message -->
                    <div *ngIf="submitted && editUserForm.controls['lastName'].errors" class="invalid-feedback">
                        <div *ngIf="editUserForm.controls['lastName'].errors?.['required']">Last name is required.</div>
                        <div *ngIf="editUserForm.controls['lastName'].errors?.['pattern']">Invalid characters or length.
                        </div>
                    </div>
                </div>

                <!-- Contact Number (Optional, Validated) -->
                <div class="col-12">
                    <label class="form-label" for="editUserContact">Contact Number</label>
                    <input type="text" id="editUserContact" class="form-control" formControlName="contactNumber"
                        placeholder="10-digit number" maxlength="10"
                        [class.is-invalid]="(editUserForm.get('contactNumber')?.touched || editUserForm.get('contactNumber')?.dirty) && editUserForm.controls['contactNumber'].errors"
                        (blur)="checkContactExistActiveUser(editUserForm, 'contactNumber')">
                    <!-- Validation Message -->
                    <div *ngIf="(editUserForm.get('contactNumber')?.touched || editUserForm.get('contactNumber')?.dirty) && editUserForm.controls['contactNumber'].errors" class="invalid-feedback">
                        <div *ngIf="editUserForm.controls['contactNumber'].errors?.['pattern']">Enter a valid 10-digit
                            number.</div>
                        <div *ngIf="editUserForm.controls['contactNumber'].errors?.['contactTaken']">
                            {{ editUserForm.controls['contactNumber'].errors?.['contactTakenMsg'] || 'This contact number is already registered.' }}
                        </div>
                    </div>
                </div>

                <!-- Date of Birth (Optional) -->
                <div class="col-12">
                    <label class="form-label" for="editUserDob">Date of Birth</label>
                    <input type="date" id="editUserDob" class="form-control" formControlName="dob">
                    <!-- Add validation if needed -->
                </div>

                <!-- Gender (Required) -->
                <div class="col-12">
                    <label class="form-label" for="editUserGender">Gender <span class="text-danger">*</span></label>
                    <select id="editUserGender" class="form-select" formControlName="gender"
                        [class.is-invalid]="submitted && editUserForm.controls['gender'].errors">
                        <option value="" disabled>Select Gender</option> <!-- Use empty string for default -->
                        <option value="Male">Male</option>
                        <option value="Female">Female</option>
                        <option value="Other">Other</option>
                    </select>
                    <div *ngIf="submitted && editUserForm.controls['gender'].errors?.['required']"
                        class="invalid-feedback">
                        Gender is required.
                    </div>
                </div>

                <!-- Department (Optional) -->
                <div class="col-12">
                    <label class="form-label" for="editUserDepartment">Department</label>
                    <select id="editUserDepartment" class="form-select" formControlName="departmentId">
                        <option [ngValue]="null">-- No Department --</option> <!-- Use null for 'no selection' -->
                        <option *ngFor="let dept of availableDepartments" [value]="dept.id">{{ dept.title }}</option>
                    </select>
                    <!-- Add required validation if needed -->
                </div>

                <!-- Designation (Optional) -->
                <div class="col-12">
                    <label class="form-label" for="editUserDesignation">Designation</label>
                    <select id="editUserDesignation" class="form-select" formControlName="designationId">
                        <option [ngValue]="null">-- No Designation --</option> <!-- Use null for 'no selection' -->
                        <option *ngFor="let desg of availableDesignations" [value]="desg.id">{{ desg.title }}</option>
                    </select>
                    <!-- Add required validation if needed -->
                </div>

                <!-- Admin Role (Required) -->
                <div class="col-12">
                    <label class="form-label" for="editUserAdminRole">Admin Role <span
                            class="text-danger">*</span></label>
                    <select id="editUserAdminRole" class="form-select" formControlName="adminsRoleId"
                        [class.is-invalid]="submitted && editUserForm.controls['adminsRoleId'].errors"
                        [disabled]="currentUserRole === componentRoles.PLANT_ADMIN">
                        <option value="" disabled>Select Role</option> <!-- Use empty string for default -->
                        <option *ngFor="let role of availableAdminRoles" [value]="role.id">{{ role.name }}</option>
                    </select>
                    <div *ngIf="submitted && editUserForm.controls['adminsRoleId'].errors?.['required']"
                        class="invalid-feedback">
                        Admin role is required.
                    </div>
                </div>


                <!-- Action Buttons -->
                <div class="col-12 mt-4 d-flex justify-content-end gap-2">
                    <button type="button" class="btn btn-secondary" (click)="closeEditModal()" [disabled]="editLoading">
                        Cancel
                    </button>
                    <!-- Disable submit if form is invalid OR currently submitting -->
                    <button type="submit" class="btn adani-btn" [disabled]="editUserForm.invalid || editLoading">
                        <span *ngIf="!editLoading"><i class="bi bi-save me-1"></i> Save Changes</span>
                        <!-- Loading Indicator -->
                        <span *ngIf="editLoading" class="spinner-border spinner-border-sm" role="status"
                            aria-hidden="true"></span>
                        <span *ngIf="editLoading"> Saving...</span>
                    </button>
                </div>
            </div> <!-- End row -->
        </form> <!-- End form -->
    </div> <!-- End container -->
</app-offcanvas>
<!-- ********** END: Edit User Offcanvas ********** -->
<!-- User Action Confirmation Modal -->
<div class="modal fade" #userActionConfirmationModalElement id="userActionConfirmationModal" tabindex="-1"
    aria-labelledby="userActionConfirmationModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header"
                [ngClass]="confirmButtonClass === 'btn-success' ? 'bg-success text-white' : (confirmButtonClass === 'btn-danger' ? 'bg-danger text-white' : 'bg-primary text-white')">
                <h5 class="modal-title" id="userActionConfirmationModalLabel">
                    <i [ngClass]="confirmButtonClass === 'btn-success' ? 'bi bi-check-circle-fill' : (confirmButtonClass === 'btn-danger' ? 'bi bi-exclamation-triangle-fill' : 'bi bi-question-circle-fill')"
                        class="me-2"></i>
                    {{ modalTitle }} <!-- Dynamic Title -->
                </h5>
                <button type="button" class="btn-close btn-close-white" aria-label="Close"
                    (click)="closeUserActionConfirmation()"></button>
            </div>
            <div class="modal-body">
                <p>{{ modalMessage }}</p> <!-- Dynamic Message -->
                <p *ngIf="userToAction" class="small text-muted">
                    User: {{ userToAction.firstName }} {{ userToAction.lastName }} (ID: {{ userToAction.id }})
                </p>
            </div>
            <div class="modal-footer justify-content-center"> <!-- Center buttons -->
                <button type="button" class="btn btn-secondary" (click)="closeUserActionConfirmation()">
                    <i class="bi bi-x-lg me-1"></i> Cancel
                </button>
                <button type="button" class="btn" [ngClass]="confirmButtonClass" (click)="confirmUserAction()">
                    <!-- Dynamic Icon and Text -->
                    <i [ngClass]="confirmButtonClass === 'btn-success' ? 'bi bi-check-lg' : (confirmButtonClass === 'btn-danger' ? 'bi bi-x-lg' : 'bi bi-arrow-up-circle')"
                        class="me-1"></i>
                    {{ confirmButtonText }}
                </button>
            </div>
        </div>
    </div>
</div>
<!-- End User Action Confirmation Modal -->
