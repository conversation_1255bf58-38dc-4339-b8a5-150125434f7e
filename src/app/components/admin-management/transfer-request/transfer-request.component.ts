import { Component, ElementRef, OnInit, ViewChild, inject, AfterViewInit, OnDestroy } from '@angular/core'; // Added inject, AfterViewInit, OnDestroy
import { NgForm } from '@angular/forms';
import { TabComponent } from '../../../shared/tab/tab.component';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { AdminService } from '../../../services/admin/admin.service';
import { createAxiosConfig } from '../../../core/utilities/axios-param-config';
import { OffcanvasComponent } from "../../../shared/offcanvas/offcanvas.component";
import { PaginationComponent } from "../../../shared/pagination/pagination.component";
import { PlantManagementService } from '../../../services/plant-management/plant-management.service';
import { DepartmentService } from '../../../services/master-management/department/department.service';
import { DesignationService } from '../../../services/master-management/designation/designation.service';
import { NgbDropdownModule } from '@ng-bootstrap/ng-bootstrap';
import * as XLSX from 'xlsx';
import { ToastMessageComponent } from '../../../shared/toast-message/toast-message.component';
import { UpdateService } from '../../../services/update/update.service';
import { Modal } from 'bootstrap';

// --- Define ROLES constant ---
const ROLES = {
  SUPER_ADMIN: 'super_admin',
  PLANT_ADMIN: 'plant_admin',
};

// Interfaces for dropdown data (adjust properties as needed)
interface Plant { id: number; name: string; }
interface Designation { id: number; title: string; } // Adjusted to 'title' based on typical service response
interface Department { id: number; title: string; } // Adjusted to 'title' based on typical service response
interface AdminRole { id: number; name: string; } // Adjusted to 'name' for consistency

// --- Updated TransferRequestItem Interface ---
interface TransferRequestItem {
  id: number; // ID of the transfer request itself
  adminId: number; // ID of the user being transferred
  plantId: number; // ID of the TARGET plant
  transferedByAdminId: number; // ID of the admin who requested
  approvedByAdminId?: number | null; // ID of the admin who approved/rejected
  status: number; // 0=Pending, 1=Transferred, 2=Rejected
  createdAt?: string | Date;
  updatedAt?: string | Date;

  // Joined Data (adjust based on actual API response structure)
  admin?: { // User being transferred
    id: number;
    profilePicture?: string | null;
    firstName: string;
    lastName: string;
    dob?: string | null;
    gender?: number | string | null; // Allow both for flexibility
    adminsRole?: { id: number; name: string }; // Role object with ID
    email: string;
    contactNumber: string | null;
    // Consider adding current plant(s) info if available and needed for display/logic
    // plantUsers?: { plant: { id: number; name: string } }[];
  };
  plant?: { // Plant being transferred TO
      id: number;
      name: string;
  };
  transferedByAdmin?: { // Admin who requested
    id: number;
    firstName: string;
    lastName: string;
    email: string;
  };
  approvedByAdmin?: { // Admin who approved/rejected
    id: number;
    firstName: string;
    lastName: string;
    email: string;
  };
  // Current plant info might need a separate join or logic if not directly on the request
  // currentPlant?: { name: string }; // Example
}


@Component({
  selector: 'app-transfer-request',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    TabComponent,
    OffcanvasComponent,
    PaginationComponent,
    NgbDropdownModule,
    ToastMessageComponent
  ],
  templateUrl: './transfer-request.component.html',
  styleUrl: './transfer-request.component.scss'
})
export class TransferRequestComponent implements OnInit, AfterViewInit, OnDestroy { // Implement interfaces
  // --- Expose ROLES to the template ---
  public componentRoles = ROLES;

  @ViewChild(ToastMessageComponent) toast!: ToastMessageComponent;
  @ViewChild('transferActionConfirmationModalElement') transferActionConfirmationModalElement!: ElementRef;
  @ViewChild('filterForm') filterForm!: NgForm;

  transferActionConfirmationModalInstance: Modal | null = null;

  // --- State Variables for Confirmation ---
  itemToConfirmAction: TransferRequestItem | null = null;
  actionToConfirm: 'accept' | 'reject' | null = null;
  modalTitle = '';
  modalMessage = '';
  confirmButtonText = '';
  confirmButtonClass = '';
  confirmIconClass = '';

  // Data Lists per Tab
  pendingTransferList: any = [];
  transferredTransferList: any = [];
  rejectedTransferList: any = [];

  // Modal State
  isFilterOffcanvasOpen: boolean = false; // Renamed for clarity

  // Loading States
  isLoadingData: boolean = false;
  isLoadingFilters: boolean = false;
  isDownloadingExcel = false;
  downloadType: 'current' | 'all' | null = null;

  // Filter Dropdown Data
  plantsList: Plant[] = [];           // Filtered by role
  designationsList: Designation[] = []; // Use updated interface
  departmentsList: Department[] = [];   // Use updated interface
  // rolesList: AdminRole[] = []; // Use updated interface if fetching roles dynamically
  // Hardcoded roles for filter dropdown (Adjust IDs/names as needed)
  rolesList: AdminRole[] = [
    { id: 3, name: 'User' },
    { id: 2, name: 'Plant Admin' },
    { id: 1, name: 'Super Admin' }
  ];
  sortOptions: any[] = [
    { value: 'id,DESC', label: 'Request Date (Newest First)' },
    { value: 'id,ASC', label: 'Request Date (Oldest First)' },
    { value: 'admin.firstName,ASC', label: 'User First Name (A-Z)' },
    { value: 'admin.lastName,ASC', label: 'User Last Name (A-Z)' },
    { value: 'plant.name,ASC', label: 'Target Plant (A-Z)' },
  ];

  // --- Filter Models (Bound to inputs in offcanvas) ---
  filterFirstName: string = '';
  filterLastName: string = '';
  filterEmail: string = '';
  filterMobile: string = '';
  filterPlantId: number | null = null;         // Target Plant ID
  // filterDesignationId: number | null = null; // Not typically filtered on transfer req
  // filterDepartmentId: number | null = null;  // Not typically filtered on transfer req
  filterRoleId: number | null = null;           // User's Role ID
  filterSortBy: string = 'id,DESC'; // Default sort

  // --- Applied Filters (Used by API calls) ---
  currentFirstName: string = '';
  currentLastName: string = '';
  currentEmail: string = '';
  currentMobile: string = '';
  currentPlantId: number | null = null;
  // currentDesignationId: number | null = null; // Remove if not used
  // currentDepartmentId: number | null = null; // Remove if not used
  currentRoleId: number | null = null;
  currentSortBy: string = 'id,DESC';

  // Pagination State
  currentPage: number = 1;
  totalItems: number = 0;
  itemsPerPage: number = 10;

  // --- RBAC Properties ---
  currentUserRole: string = '';
  loggedInAdminId: number | null = null;
  loggedInPlantIds: number[] = [];
  // loggedInUserPlantName: any; // Can be removed if not used directly

  // Tab State
  tabs = [
    { title: 'Pending', status: 0, listKey: 'pendingTransferList' as const },
    { title: 'Transferred', status: 1, listKey: 'transferredTransferList' as const },
    { title: 'Rejected', status: 2, listKey: 'rejectedTransferList' as const },
  ];
  selectedTabIndex = 0;

  // Service Injection
  private adminService = inject(AdminService);
  private plantService = inject(PlantManagementService);
  private departmentService = inject(DepartmentService); // Keep if needed elsewhere, otherwise remove
  private designationService = inject(DesignationService); // Keep if needed elsewhere, otherwise remove
  private updateService = inject(UpdateService); // Corrected variable name

  constructor() { } // Keep constructor empty

  ngOnInit() {
    this.setCurrentUserRoleAndDetailsById(); // Set role FIRST
    this.fetchDataForCurrentTab();          // Fetch data based on role
    this.fetchAllFilterData();              // Fetch dropdowns (plants will be filtered)
    // this.getCurrentLoggedInAdminDetail(); // Redundant, handled by setCurrentUserRoleAndDetailsById
  }

  ngAfterViewInit(): void {
    if (this.transferActionConfirmationModalElement) {
      this.transferActionConfirmationModalInstance = new Modal(this.transferActionConfirmationModalElement.nativeElement);
    } else {
      console.error("Transfer action confirmation modal element not found!");
    }
  }

  ngOnDestroy(): void {
    this.transferActionConfirmationModalInstance?.dispose();
  }

  // --- RBAC Setup ---
  private setCurrentUserRoleAndDetailsById() {
    try {
      const userString = localStorage.getItem('user');
      if (!userString || userString.trim() === "" || ['null', 'undefined', '""', '" "'].includes(userString.trim().toLowerCase())) {
        this.currentUserRole = ''; this.loggedInAdminId = null; this.loggedInPlantIds = [];
        this.toast?.showErrorToast("User session invalid."); return;
      }
      const currentUser = JSON.parse(userString);
      this.loggedInAdminId = currentUser?.id ?? null;
      // Ensure plantIds are numbers
      this.loggedInPlantIds = (Array.isArray(currentUser?.plantIds) && currentUser.plantIds.length > 0)
        ? currentUser.plantIds.map((id: any) => Number(id)).filter((id: number) => !isNaN(id)) : [];

      const roleId = currentUser?.adminsRoleId;
      if (roleId === 1 || roleId === 6) { this.currentUserRole = ROLES.SUPER_ADMIN; }
      else if (roleId === 2) {
        this.currentUserRole = ROLES.PLANT_ADMIN;
        if (this.loggedInPlantIds.length === 0) {
          console.warn("Plant Admin has no assigned plants. Transfer request visibility might be limited.");
        }
      } else {
        this.currentUserRole = ''; // Or handle other roles if necessary
        console.warn(`User has role ID ${roleId}, which may not have permissions for transfer requests.`);
      }
      console.log(`TransferRequest - Role: ${this.currentUserRole}, UserID: ${this.loggedInAdminId}, Plants: [${this.loggedInPlantIds.join(', ')}]`);
    } catch (error) {
      console.error("Error parsing user data:", error);
      this.currentUserRole = ''; this.loggedInAdminId = null; this.loggedInPlantIds = [];
      this.toast?.showErrorToast("Error reading user session.");
    }
  }

  // --- Confirmation Modal ---
  openConfirmationModal(item: TransferRequestItem, action: 'accept' | 'reject'): void {
    if (!item || !item.id || !item.admin) {
      this.toast?.showErrorToast("Invalid request data provided.");
      console.error("Invalid item data for action confirmation:", item);
      return;
    }

    // --- RBAC Check: Plant Admin can only act on requests TO their plants ---
    if (this.currentUserRole === ROLES.PLANT_ADMIN) {
        // Check if the TARGET plant ID (item.plantId) is in the admin's list
        if (!item.plantId || !this.loggedInPlantIds.includes(item.plantId)) {
            this.toast?.showErrorToast(`You do not have permission to ${action} requests for plant ${item.plant?.name ?? item.plantId ?? 'N/A'}.`);
            return;
        }
    }
    // --- End RBAC Check ---

    this.itemToConfirmAction = item;
    this.actionToConfirm = action;

    if (action === 'accept') {
      this.modalTitle = 'Confirm Acceptance';
      this.modalMessage = `Accept transfer request for ${item.admin.firstName} ${item.admin.lastName} to plant ${item.plant?.name ?? 'N/A'}?`;
      this.confirmButtonText = 'Yes, Accept';
      this.confirmButtonClass = 'bg-success';
      this.confirmIconClass = 'bi bi-check-circle-fill';
    } else { // reject
      this.modalTitle = 'Confirm Rejection';
      this.modalMessage = `Reject transfer request for ${item.admin.firstName} ${item.admin.lastName} (Target: ${item.plant?.name ?? 'N/A'})?`;
      this.confirmButtonText = 'Yes, Reject';
      this.confirmButtonClass = 'bg-danger';
      this.confirmIconClass = 'bi bi-x-octagon-fill';
    }

    this.transferActionConfirmationModalInstance?.show();
  }

  closeConfirmationModal(): void {
    this.transferActionConfirmationModalInstance?.hide();
    this.itemToConfirmAction = null;
    this.actionToConfirm = null;
  }

  confirmAction(): void {
    if (!this.itemToConfirmAction || !this.actionToConfirm || !this.loggedInAdminId) {
      console.error("Confirmation failed: Missing item, action, or logged-in admin ID.");
      this.toast?.showErrorToast("An internal error occurred. Please try again.");
      this.closeConfirmationModal();
      return;
    }

    // --- RBAC Check (again before API call) ---
    if (this.currentUserRole === ROLES.PLANT_ADMIN) {
        if (!this.itemToConfirmAction.plantId || !this.loggedInPlantIds.includes(this.itemToConfirmAction.plantId)) {
            this.toast?.showErrorToast(`Permission denied to perform this action.`);
            this.closeConfirmationModal();
            return;
        }
    }
    // --- End RBAC Check ---

    const itemToAction = this.itemToConfirmAction;
    const action = this.actionToConfirm;
    this.closeConfirmationModal(); // Close modal first

    if (action === 'accept') {
      this.approveTransfer(itemToAction);
    } else if (action === 'reject') {
      this.rejectTransfer(itemToAction);
    }
  }

  // --- Excel Download ---
  getCurrentListData(): TransferRequestItem[] | undefined {
    const currentTab = this.tabs[this.selectedTabIndex];
    if (!currentTab) return undefined;
    // Use listKey defined in tabs array
    if (currentTab.listKey) {
        const list = (this as any)[currentTab.listKey];
        if (Array.isArray(list)) { return list; }
    }
    return undefined;
  }

  async fetchAllFilteredTransferRequests(): Promise<TransferRequestItem[] | null> {
    const currentTab = this.tabs[this.selectedTabIndex];
    if (!currentTab) { console.error("Invalid tab selected."); return null; }

    this.isLoadingData = true;
    const filters = this.buildFilterParams(true); // Pass flag to indicate fetching ALL

    const data = {
      // page: 1, // Not needed if API supports no pagination with large limit
      limit: 10000, // Large limit
      sort: this.currentSortBy,
      filter: filters,
      join: ['admin.adminsRole', 'plant', 'transferedByAdmin', 'approvedByAdmin'] // Add necessary joins
    };
    const param = createAxiosConfig(data);

    try {
      console.log('API Request Params (Transfer Download - All Data):', JSON.stringify(param, null, 2));
      // Ensure service method returns the correct structure, possibly response.data
      const response = await this.adminService.getPlantTransferRequest(param);
      return response?.data ?? response ?? []; // Adapt based on actual response
    } catch (error: any) {
      console.error("Error fetching all transfer requests for download:", error);
      this.toast?.showErrorToast(error?.response?.data?.message || "Failed to retrieve full data for download.");
      return null;
    } finally {
      this.isLoadingData = false;
    }
  }

  async downloadExcel(type: 'current' | 'all') {
    const currentTab = this.tabs[this.selectedTabIndex];
    if (!currentTab || this.isDownloadingExcel || this.isLoadingData) return;

    this.isDownloadingExcel = true;
    this.downloadType = type;
    this.toast?.showSuccessToast(`Preparing download for ${type === 'current' ? 'current page' : 'all filtered'} ${currentTab.title.toLowerCase()} requests...`);

    let dataToExport: TransferRequestItem[] | null = null;

    try {
      if (type === 'all') { dataToExport = await this.fetchAllFilteredTransferRequests(); }
      else { dataToExport = this.getCurrentListData() ?? null; }

      if (dataToExport === null) { this.toast?.showErrorToast("Failed to retrieve data for download."); return; }
      if (!dataToExport || dataToExport.length === 0) { this.toast?.showErrorToast(`No ${currentTab.title.toLowerCase()} requests available to download.`); return; }

      console.log(`Fetched ${dataToExport.length} requests for Excel export (${type}).`);

      const dataForExcel = dataToExport.map(item => {
        const baseData: { [key: string]: any } = { // Define index signature
          'Request ID': item.id,
          'User ID': item.admin?.id ?? item.adminId,
          'User Name': `${item.admin?.firstName ?? ''} ${item.admin?.lastName ?? ''}`.trim(),
          'User Email': item.admin?.email ?? 'N/A',
          'User Contact': item.admin?.contactNumber ?? 'N/A',
          'User Role': item.admin?.adminsRole?.name ?? 'N/A',
          'Target Plant': item.plant?.name ?? item.plantId ?? 'N/A', // Display name or ID
          'Requested By': `${item.transferedByAdmin?.firstName ?? ''} ${item.transferedByAdmin?.lastName ?? ''}`.trim() || item.transferedByAdminId || 'N/A',
          'Request Date': item.createdAt ? new Date(item.createdAt).toLocaleString() : 'N/A',
          'Status': currentTab.title, // Status based on the tab being exported
        };

        // Add details specific to Transferred/Rejected tabs
        if (currentTab.status === 1) { // Transferred
          baseData['Approved By'] = `${item.approvedByAdmin?.firstName ?? ''} ${item.approvedByAdmin?.lastName ?? ''}`.trim() || item.approvedByAdminId || 'N/A';
          baseData['Approval Date'] = item.updatedAt ? new Date(item.updatedAt).toLocaleString() : 'N/A';
        } else if (currentTab.status === 2) { // Rejected
          // Assuming approvedByAdminId holds the rejector ID if status is 2
          baseData['Rejected By'] = `${item.approvedByAdmin?.firstName ?? ''} ${item.approvedByAdmin?.lastName ?? ''}`.trim() || item.approvedByAdminId || 'N/A';
          baseData['Rejection Date'] = item.updatedAt ? new Date(item.updatedAt).toLocaleString() : 'N/A';
        }
        return baseData;
      });

      const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(dataForExcel);
      const wb: XLSX.WorkBook = XLSX.utils.book_new();
      const safeSheetName = currentTab.title.replace(/[^a-zA-Z0-9]/g, '').substring(0, 30);
      XLSX.utils.book_append_sheet(wb, ws, safeSheetName || 'TransferRequests');

      const dateStr = new Date().toISOString().slice(0, 10);
      const typeStr = type === 'current' ? `Page${this.currentPage}` : 'All';
      const fileName = `TransferRequests_${safeSheetName}_${typeStr}_${dateStr}.xlsx`;

      XLSX.writeFile(wb, fileName);
      this.toast?.showSuccessToast(`Excel file download started (${type}).`);

    } catch (error) {
      console.error(`Error generating Excel file (${type}):`, error);
      this.toast?.showErrorToast(`An error occurred while generating the Excel file (${type}).`);
    } finally {
      this.isDownloadingExcel = false;
      this.downloadType = null;
    }
  }

  // --- Modal Handling ---
  closeFilterModal() {
    this.isFilterOffcanvasOpen = false;
  }

  openFilterModal() {
    // Restore filter form state from currently applied filters
    this.filterFirstName = this.currentFirstName;
    this.filterLastName = this.currentLastName;
    this.filterEmail = this.currentEmail;
    this.filterMobile = this.currentMobile;
    this.filterPlantId = this.currentPlantId;
    // this.filterDesignationId = this.currentDesignationId; // Remove if not used
    // this.filterDepartmentId = this.currentDepartmentId; // Remove if not used
    this.filterRoleId = this.currentRoleId;
    this.filterSortBy = this.currentSortBy;

    this.isFilterOffcanvasOpen = true;
  }

  // --- Filter Data Fetching (with RBAC for Plants) ---
  async fetchAllFilterData() {
    this.isLoadingFilters = true;
    try {
      await Promise.all([
        this.fetchPlantsForFilter(),
        // this.fetchDesignationsForFilter(), // Remove if not needed for this component
        // this.fetchDepartmentsForFilter(),  // Remove if not needed for this component
        // this.fetchRolesForFilter() // Roles are hardcoded now
      ]);
    } catch (error) {
      console.error("Error loading filter data:", error);
      // Show specific error toasts if needed
    } finally {
      this.isLoadingFilters = false;
    }
  }

  async fetchPlantsForFilter() {
    const data = { sort: 'name,ASC', filter: ['enabled||eq||true'], limit: 1000 };
    let allEnabledPlants: Plant[] = [];
    try {
        const param = createAxiosConfig(data);
        const response = await this.plantService.getPlants(param);
        allEnabledPlants = response?.data ?? response ?? []; // Adjust based on API response
    } catch (error) {
        console.error("Error fetching plants:", error);
        this.plantsList = [];
        this.toast?.showErrorToast('Failed to load plants for filter.');
        return; // Stop if plants failed
    }

    // Filter based on role (similar to ActiveUserComponent)
    if (this.currentUserRole === ROLES.PLANT_ADMIN && this.loggedInPlantIds.length > 0) {
        this.plantsList = allEnabledPlants.filter(plant => this.loggedInPlantIds.includes(plant.id));
    } else if (this.currentUserRole === ROLES.SUPER_ADMIN) {
        this.plantsList = allEnabledPlants;
    } else {
        this.plantsList = []; // Or show all if required for other roles
    }
    console.log(`Loaded ${this.plantsList.length} plants accessible to current user for filter.`);
  }

  // Removed fetchDesignationsForFilter, fetchDepartmentsForFilter, fetchRolesForFilter
  // as they seem unused in the filter UI/logic for transfer requests

  // --- Filter Application ---
  applyFilter() {
    // Check if form is valid before applying filters
    if (this.filterForm && this.filterForm.invalid) {
      this.toast?.showErrorToast("Please correct the validation errors before applying filters.");
      return;
    }

    // Validate email format if provided
    if (this.filterEmail) {
      // First check basic email format
      const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
      if (!emailRegex.test(this.filterEmail)) {
        this.toast?.showErrorToast("Please enter a valid email address.");
        return;
      }

      // Then check for adani.com domain
      if (!this.filterEmail.toLowerCase().endsWith('@adani.com')) {
        this.toast?.showErrorToast("Email must use the @adani.com domain.");
        return;
      }
    }

    // Apply filters from the form models to the current filter state
    this.currentFirstName = this.filterFirstName ? this.filterFirstName.trim() : '';
    this.currentLastName = this.filterLastName ? this.filterLastName.trim() : '';
    this.currentEmail = this.filterEmail ? this.filterEmail.trim() : '';
    this.currentMobile = this.filterMobile ? this.filterMobile.trim() : '';
    this.currentPlantId = this.filterPlantId;
    // this.currentDesignationId = this.filterDesignationId; // Remove if not used
    // this.currentDepartmentId = this.filterDepartmentId; // Remove if not used
    this.currentRoleId = this.filterRoleId;
    this.currentSortBy = this.filterSortBy;

    this.currentPage = 1; // Reset to page 1 when filters change
    this.fetchDataForCurrentTab();
    this.closeFilterModal();
  }

  resetFilters() {
    // 1. Reset Applied Filters
    this.currentFirstName = ''; this.currentLastName = ''; this.currentEmail = ''; this.currentMobile = '';
    this.currentPlantId = null;
    // this.currentDesignationId = null; // Remove if not used
    // this.currentDepartmentId = null; // Remove if not used
    this.currentRoleId = null;
    this.currentSortBy = 'id,DESC'; // Reset to default sort

    // 2. Reset Form Models
    this.filterFirstName = ''; this.filterLastName = ''; this.filterEmail = ''; this.filterMobile = '';
    this.filterPlantId = null;
    // this.filterDesignationId = null; // Remove if not used
    // this.filterDepartmentId = null; // Remove if not used
    this.filterRoleId = null;
    this.filterSortBy = 'id,DESC';

    // 3. Reset Pagination & Fetch
    this.currentPage = 1;
    this.fetchDataForCurrentTab();
    // Optionally close modal: this.closeFilterModal();
  }


  // --- Data Fetching Logic (with RBAC) ---
  fetchDataForCurrentTab() {
    const currentTab = this.tabs[this.selectedTabIndex];
    if (!currentTab) return;
    this.getTransferRequest(currentTab.status, this.currentPage);
  }

  // --- Build Filter Params with RBAC ---
    // --- Build Filter Params with RBAC ---
    buildFilterParams(isFetchingAll = false): string[] {
      const filters: string[] = [];
      const currentStatus = this.tabs[this.selectedTabIndex].status;
      filters.push(`status||eq||${currentStatus}`);
      // filters.push('admin.applicationId||ne||2'); // Adjust if needed

      // *** CHANGE: Define plant filter path ***
      const plantFilterPath = 'plantId'; // Direct field on the transfer request table

      // --- RBAC Plant Filtering (TARGET PLANT) ---
      let appliedPlantFilter = false;
      if (this.currentUserRole === ROLES.PLANT_ADMIN) {
          if (this.loggedInPlantIds.length > 0) {

              // *** START FIX ***
              // Convert the selected filter plant ID to a number for reliable comparison
              const selectedPlantIdNum = this.currentPlantId != null ? Number(this.currentPlantId) : null;

              // Add logging to verify types and values just before the check
              console.log('[buildFilterParams - Transfer] Checking Plant Filter:');
              console.log('  Raw currentPlantId:', this.currentPlantId, typeof this.currentPlantId);
              console.log('  Converted selectedPlantIdNum:', selectedPlantIdNum, typeof selectedPlantIdNum);
              console.log('  loggedInPlantIds:', this.loggedInPlantIds);
              console.log('  Includes Check Result:', selectedPlantIdNum != null && this.loggedInPlantIds.includes(selectedPlantIdNum));

              // Check if a specific plant is selected in the filter AND the admin manages it (using the number)
              if (selectedPlantIdNum != null && this.loggedInPlantIds.includes(selectedPlantIdNum)) {
                  // A specific, valid plant IS selected - filter ONLY by this target plant
                  filters.push(`${plantFilterPath}||eq||${selectedPlantIdNum}`); // Use the number
                  appliedPlantFilter = true;
                   console.log(`[buildFilterParams - Transfer] Applying EQ filter for plant: ${selectedPlantIdNum}`);
              } else {
                  // Otherwise, show requests targeting ANY of the admin's plants
                  filters.push(`${plantFilterPath}||$in||${this.loggedInPlantIds.join(',')}`);
                  appliedPlantFilter = true;
                   console.log(`[buildFilterParams - Transfer] Applying IN filter for plants: ${this.loggedInPlantIds.join(',')}`);

                  // Reset visual filter if a non-managed plant was selected
                  if (this.currentPlantId != null && (selectedPlantIdNum == null || !this.loggedInPlantIds.includes(selectedPlantIdNum))) {
                      if (!isFetchingAll) {
                          console.warn(`[buildFilterParams - Transfer] Invalid or non-managed plant filter (${this.currentPlantId}) detected, resetting UI filter.`);
                          this.currentPlantId = null; // Reset only for paged view
                      }
                      // Also reset form model for consistency when opening filter again
                      this.filterPlantId = null;
                  }
              }
               // *** END FIX ***

          } else {
              // Block data if Plant Admin has no plants
              filters.push(`${plantFilterPath}||eq||-1`);
              appliedPlantFilter = true;
              console.log('[buildFilterParams - Transfer] Plant Admin has no plants, blocking results.');
          }
      } else if (this.currentUserRole === ROLES.SUPER_ADMIN) {
          // Super Admin can filter by any specific target plant if selected
          if (this.currentPlantId != null) { // Check specifically for not null
              filters.push(`${plantFilterPath}||eq||${this.currentPlantId}`);
              appliedPlantFilter = true;
              console.log(`[buildFilterParams - Transfer] Super Admin applying EQ filter for plant: ${this.currentPlantId}`);
          } else {
               console.log('[buildFilterParams - Transfer] Super Admin showing all plants.');
          }
      }
      // --- End RBAC Plant Filtering ---


      // Add other filters (User details are nested under 'admin')
      if (this.currentFirstName) filters.push(`admin.firstName||$contL||${this.currentFirstName}`);
      if (this.currentLastName) filters.push(`admin.lastName||$contL||${this.currentLastName}`);
      if (this.currentEmail) filters.push(`admin.email||$contL||${this.currentEmail}`);
      if (this.currentMobile) filters.push(`admin.contactNumber||$contL||${this.currentMobile}`);

      // Plant filter handled above by RBAC logic

      // Filter by User's Role (nested under admin)
      if (this.currentRoleId) filters.push(`admin.adminsRoleId||eq||${this.currentRoleId}`);

      // Add designation/department filters if needed (nested under admin)
      // if (this.currentDesignationId) filters.push(`admin.designationId||eq||${this.currentDesignationId}`);
      // if (this.currentDepartmentId) filters.push(`admin.departmentId||eq||${this.currentDepartmentId}`);

      return filters;
  }

  async getTransferRequest(status: number, page: number) {
    this.isLoadingData = true;
    const filters = this.buildFilterParams();

    const data = {
        page: page,
        limit: this.itemsPerPage,
        sort: this.currentSortBy,
        filter: filters,
        // Define necessary joins to get nested data (admin, plant, etc.)
        join: ['admin.adminsRole', 'plant', 'transferedByAdmin', 'approvedByAdmin']
    };
    const param = createAxiosConfig(data);

    try {
      // console.log("Fetching requests with params:", JSON.stringify(param, null, 2)); // Debug log
      const response = await this.adminService.getPlantTransferRequest(param);
      const listData: TransferRequestItem[] = response?.data ?? [];
      const total: number = response?.total ?? 0;

      // Assign data to the correct list based on the current tab's status
      const currentTab = this.tabs.find(tab => tab.status === status);
      if (currentTab && currentTab.listKey) {
          // Reset all lists before assigning to the current one
          this.pendingTransferList = [];
          this.transferredTransferList = [];
          this.rejectedTransferList = [];
          // Assign fetched data
          (this as any)[currentTab.listKey] = listData;
      } else {
          console.warn("Could not find listKey for status:", status);
          // Clear lists as a fallback
          this.pendingTransferList = [];
          this.transferredTransferList = [];
          this.rejectedTransferList = [];
      }

      this.totalItems = total;
    } catch (error: any) {
      console.error("Error fetching transfer requests:", error);
      this.pendingTransferList = []; this.transferredTransferList = []; this.rejectedTransferList = [];
      this.totalItems = 0;
      this.toast?.showErrorToast(error?.response?.data?.message || 'Failed to load transfer requests.');
    } finally {
      this.isLoadingData = false;
    }
  }

  // --- Tab Handling ---
  onTabSelected(index: number) {
    if (this.selectedTabIndex === index || this.isLoadingData) return; // Prevent switch while loading
    this.selectedTabIndex = index;
    this.currentPage = 1;
    this.totalItems = 0; // Reset pagination info
    // Filters could be reset here if desired: this.resetFilters();
    this.fetchDataForCurrentTab();
  }

  // --- Pagination Handling ---
  onPageChange(page: number) {
    if (page === this.currentPage || this.isLoadingData) return; // Prevent change while loading
    this.currentPage = page;
    this.fetchDataForCurrentTab();
  }

  // --- Action Handling (Approve/Reject - RBAC checked in confirmation) ---
  async approveTransfer(item: TransferRequestItem) {
    if (!item || !item.id || !this.loggedInAdminId) {
      this.toast?.showErrorToast("Invalid item data or missing admin ID for approval.");
      return;
    }
    // API requires ID of the request and the approving admin ID
    const data = {
      id: item.id,
      approvedByAdminId: this.loggedInAdminId
      // Backend should handle setting status to 1 (Transferred)
    };
    this.isLoadingData = true;
    try {
      // Use the specific update method for transfer requests
      await this.adminService.updatePantTransferRequest(data); // Corrected method name if needed
      this.toast?.showSuccessToast("Transfer request approved successfully.");
      this.fetchDataForCurrentTab(); // Refresh list
    } catch (error: any) {
      console.error("Error approving transfer:", error);
      const errorMsg = error?.response?.data?.message || "Failed to approve transfer request.";
      this.toast?.showErrorToast(errorMsg);
    } finally {
      this.isLoadingData = false;
    }
  }

  async rejectTransfer(item: TransferRequestItem) {
    if (!item || !item.id || !this.loggedInAdminId) {
      this.toast?.showErrorToast("Invalid item data or missing admin ID for rejection.");
      return;
    }
    // Use UpdateService to set the status to Rejected (2)
    // The backend might automatically record who rejected based on logged-in user context,
    // or you might need to pass rejectedByAdminId explicitly. Here we assume setting status is enough.
    const body = {
      tableName: 'plant-transfer-request', // *** Verify actual table name ***
      id: item.id,
      data: {
        status: 2, // Status for Rejected
      }
    };
    this.isLoadingData = true;
    try {
      await this.updateService.update(body);
      this.toast?.showSuccessToast("Transfer request rejected successfully.");
      // Handle pagination if the last item on the page is rejected
        const currentList = this.getCurrentListData();
        if (currentList?.length === 1 && this.currentPage > 1 && this.tabs[this.selectedTabIndex].status === 0) {
             this.currentPage--; // Go to previous page only if rejecting from Pending list
        }
      this.fetchDataForCurrentTab(); // Refresh list
    } catch (error: any) {
      console.error("Error rejecting transfer:", error);
      const errorMsg = error?.response?.data?.message || "Failed to reject transfer request.";
      this.toast?.showErrorToast(errorMsg);
    } finally {
      this.isLoadingData = false;
    }
  }

  // --- Helper Function ---
  // This might not be needed if using Angular date pipe in template
  formatDate(dateString: string | Date | null): string {
    if (!dateString) return 'N/A';
    try {
      const date = new Date(dateString);
      // Check if date is valid before formatting
      if (isNaN(date.getTime())) return 'Invalid Date';
      return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
    } catch (e) { return 'Invalid Date'; }
  }
}