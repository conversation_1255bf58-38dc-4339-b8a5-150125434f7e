<app-toast-message></app-toast-message>
<div class="app-container">
    <div class="card custom-card">
        <div class="card-header">
            <div class="row align-items-center">
                <div class="col d-flex align-items-center">
                    <h6 class="mb-0">Roles List</h6>
                </div>
                <div class="col text-end d-flex align-items-center justify-content-end">
                    <div ngbDropdown class="d-inline-block me-2">
                        <button type="button" class="btn btn-sm adani-btn dropdown-toggle" id="downloadRolesExcelDropdown" ngbDropdownToggle
                            [disabled]="isDownloadingExcel || listLoading">
                            <span *ngIf="!isDownloadingExcel">
                                <i class="bi bi-file-earmark-excel me-1"></i> Download Excel
                            </span>
                            <span *ngIf="isDownloadingExcel">
                                <span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
                                Downloading {{ downloadType === 'current' ? 'Page' : (downloadType === 'all' ? 'All' : '') }}...
                            </span>
                        </button>
                        <ul ngbDropdownMenu aria-labelledby="downloadRolesExcelDropdown">
                            <li>
                                <button ngbDropdownItem (click)="downloadExcel('current')" [disabled]="isDownloadingExcel || listLoading || (rolesList?.length ?? 0) === 0">
                                    <i class="bi bi-download me-1"></i> Download Current Page ({{ rolesList?.length ?? 0 }})
                                </button>
                            </li>
                            <li>
                                <button ngbDropdownItem (click)="downloadExcel('all')" [disabled]="isDownloadingExcel || listLoading || totalItems === 0">
                                    <i class="bi bi-cloud-download me-1"></i> Download All Filtered ({{ totalItems }})
                                </button>
                            </li>
                        </ul>
                    </div>
                    <img src="../../../assets/svg/filter.svg" class="filter-button ms-3" (click)="openFilterModal()" alt="" style="width: 35px;" />
                </div>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered custom-table">
                    <thead class="table-header">
                        <tr>
                            <th scope="col">Id</th>
                            <th scope="col">Name</th>
                            <th scope="col" *ngIf="showCreatedDate">Created Date</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr *ngIf="listLoading">
                            <!-- Calculate colspan dynamically -->
                            <td colspan="4" class="text-center p-4">
                                <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                                Loading roles...
                            </td>
                        </tr>
                        <tr *ngIf="!listLoading && rolesList.length === 0">
                            <!-- Calculate colspan dynamically -->
                             <td colspan="4" class="text-center p-4 text-muted">
                                No roles found matching the criteria.
                            </td>
                        </tr>
                        <tr *ngFor="let item of rolesList">
                            <td class="text-center">{{ item.id }}</td>
                            <td class="text-center">{{ item.name }}</td>
                            <td *ngIf="showCreatedDate" class="text-center">
                                <span class="text-danger">{{ item.createdTimestamp | date: 'dd-MM-yyyy' }}</span>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
<!-- Filter Offcanvas -->
<app-offcanvas [title]="'Filter Roles'" *ngIf="isEditUserModalOpen" (onClickCross)="closeModal()">
    <div class="filter-container p-3">
        <form #filterForm="ngForm" (ngSubmit)="applyFilters()">
            <div class="row g-3"> <!-- Use g-3 for spacing -->

                <div class="col-12">
                    <label class="form-label" for="filterName">Name</label>
                    <input type="text" id="filterName" class="form-control" placeholder="Search by Role Name"
                           [(ngModel)]="filters.name" name="name" #name="ngModel"
                           pattern="^[a-zA-Z\s]*$" maxlength="30"
                           [ngClass]="{'is-invalid': name.invalid && (name.dirty || name.touched)}">
                    <div *ngIf="name.invalid && (name.dirty || name.touched)" class="invalid-feedback">
                        <div *ngIf="name.errors?.['pattern']">Name should contain only alphabets.</div>
                    </div>
                </div>

                <div class="col-12"> <!-- Adjusted col size based on original -->
                    <label class="form-label" for="filterEnabled">Enabled Status</label>
                    <select id="filterEnabled" class="form-select"
                            [(ngModel)]="filters.enabled" name="enabled">
                        <option [ngValue]="null">Any</option> <!-- Allow Any -->
                        <option value="true">Yes</option>
                        <option value="false">No</option>
                    </select>
                </div>

                <!-- Optional: Add Sort Field/Direction Dropdowns -->
                 <div class="col-12">
                    <label class="form-label" for="filterSortBy">Sort By</label>
                    <select id="filterSortBy" class="form-select" [(ngModel)]="filters.sortField" name="sortField">
                        <option [ngValue]="null">Default Sort (ID DESC)</option>
                        <option *ngFor="let field of availableSortFields" [value]="field.value">{{ field.label }}</option>
                    </select>
                </div>
                 <div class="col-12">
                     <label class="form-label" for="filterSortDir">Sort Direction</label>
                     <select id="filterSortDir" class="form-select" [(ngModel)]="filters.sortDirection" name="sortDirection">
                        <option value="DESC">Descending</option>
                        <option value="ASC">Ascending</option>
                    </select>
                </div>

                <div class="col-12 mt-4 d-grid gap-2">
                    <button type="submit" class="btn adani-btn" [disabled]="filterForm.invalid">
                        <i class="bi bi-search me-1"></i> Search
                    </button>
                    <button type="button" class="btn btn-secondary" (click)="resetFilters()">
                        <i class="bi bi-arrow-clockwise me-1"></i> Reset Filters
                    </button>
                </div>
            </div>
        </form>
    </div>
</app-offcanvas>

