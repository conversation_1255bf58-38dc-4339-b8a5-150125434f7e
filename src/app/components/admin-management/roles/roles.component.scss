.app-container {
    padding: 20px;
}

.custom-card {
    border-radius: 10px;
    overflow: hidden;
}
.card-body{
    height: 70vh;
    overflow: auto;
}
.table-responsive {
    overflow-x: auto;
    white-space: nowrap;
    max-width: 100%;
}

.custom-table {
    width: 100%;
    table-layout: auto;
    border-collapse: collapse;
    border-radius: 10px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.card-header {
    font-size: 12px;
    background-color: white !important;
    color: black !important;
    padding: 10px 15px;
}

.table-header th {
    font-size: 12px;
    background-color: #f5f7fa ;
    color: black ;
    text-align: center;
}

td {
    font-size: 12px;
    text-align: left;
    vertical-align: middle;
}

.img-thumbnail {
    width: 80px;
    height: 80px;
    object-fit: cover;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.btn-sm {
    border-radius: 6px;
    font-size: 12px;
}

.text-warning {
    color: #eb6f33 !important;
}
.actions{
    text-align: center !important;
    vertical-align: middle !important;
}