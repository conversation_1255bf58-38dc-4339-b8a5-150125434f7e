import { CommonModule, DatePipe } from '@angular/common';
import { Component, OnInit, ViewChild } from '@angular/core';
import { FormsModule, NgForm } from '@angular/forms'; // Import FormsModule and NgForm
import { AdminService } from '../../../services/admin/admin.service';
import { createAxiosConfig } from '../../../core/utilities/axios-param-config';
import { PaginationComponent } from "../../../shared/pagination/pagination.component"; // Import PaginationComponent
import { OffcanvasComponent } from "../../../shared/offcanvas/offcanvas.component";
import { ToastMessageComponent } from '../../../shared/toast-message/toast-message.component'; // Adjust path if needed
import { NgbDropdownModule } from '@ng-bootstrap/ng-bootstrap'; // For dropdown button
import * as XLSX from 'xlsx'; // For Excel generation

// Interface for filter structure
interface RoleFilter {
    name?: string | null;
    enabled?: string | null; // 'true'/'false' or null
    sortField?: string;
    sortDirection?: 'ASC' | 'DESC';
}

interface RoleItem {
    id: number;
    name: string;
    enabled?: boolean; // Add if returned by API and needed for export
    createdTimestamp?: string | Date;
    updatedTimestamp?: string | Date; // Add if returned by API and needed for export
}

@Component({
    selector: 'app-roles',
    standalone: true,
    imports: [
        CommonModule,
        FormsModule, // Add FormsModule
        DatePipe,// Add PaginationComponent
        OffcanvasComponent,
        NgbDropdownModule,
        ToastMessageComponent
    ],
    templateUrl: './roles.component.html',
    styleUrl: './roles.component.scss'
})
export class RolesComponent implements OnInit {
    @ViewChild(ToastMessageComponent) toast!: ToastMessageComponent;
    @ViewChild('filterForm') filterForm!: NgForm;
    isEditUserModalOpen = false; // Keep boolean type
    rolesList: any[] = [];
    listLoading: boolean = false;
    showCreatedDate: boolean = true; // Keep if used in template, otherwise remove
    // tableKey: number = 0; // Seems unused, can be removed

    // --- Pagination Properties ---
    currentPage = 1;
    itemsPerPage = 10; // Or your desired page size
    totalItems = 0;
    isDownloadingExcel = false;
    downloadType: 'current' | 'all' | null = null;

    // --- Filter Properties ---
    filters: RoleFilter = {
        name: null,
        enabled: null,       // Default to showing all (or set to 'true' if preferred)
        sortField: 'id',    // Default sort field
        sortDirection: 'DESC' // Default sort direction
    };

    // Mock data for dropdowns (if needed, e.g., for sorting field selection)
    availableSortFields = [
        { value: 'id', label: 'ID' },
        { value: 'name', label: 'Name' },
        { value: 'createdTimestamp', label: 'Created Date' }
    ];

    constructor(readonly adminService: AdminService) { }

    ngOnInit(): void {
        this.loadRoles(this.currentPage); // Load initial data
    }

    // Helper to get current list data
    getCurrentListData(): RoleItem[] | undefined {
        return this.rolesList;
    }

    // Fetch ALL roles matching current filters (no pagination)
    async fetchAllFilteredRoles(): Promise<RoleItem[] | null> {
        this.listLoading = true; // Optional: Indicate loading
        const filterParams: string[] = [];
        if (this.filters.name) { filterParams.push(`name||$contL||${this.filters.name}`); }
        if (this.filters.enabled !== null && this.filters.enabled !== undefined && this.filters.enabled !== '') {
             filterParams.push(`enabled||eq||${this.filters.enabled}`);
        }

        const data = {
            limit: 10000, // Large limit for "all"
            sort: `${this.filters.sortField || 'id'},${this.filters.sortDirection || 'DESC'}`,
            filter: filterParams
        };

        try {
            const param = createAxiosConfig(data);
            console.log('API Request Params (Roles Download - All Data):', JSON.stringify(param, null, 2));
            const response = await this.adminService.getAdminRoles(param); // Use existing service method
            return response ?? [];
        } catch (error: any) {
            console.error("Error fetching all roles for download:", error);
            this.toast?.showErrorToast(error?.response?.data?.message || "Failed to retrieve full data for download.");
            return null;
        } finally {
             this.listLoading = false; // Reset loading indicator
        }
    }

    // Main download function
    async downloadExcel(type: 'current' | 'all') {
        if (this.isDownloadingExcel || this.listLoading) return; // Prevent concurrent actions

        this.isDownloadingExcel = true;
        this.downloadType = type;
        this.toast?.showSuccessToast(`Preparing download for ${type === 'current' ? 'current page' : 'all filtered'} roles...`);

        let dataToExport: RoleItem[] | null = null;

        try {
            // 1. Get Data
            if (type === 'all') {
                dataToExport = await this.fetchAllFilteredRoles();
            } else { // 'current'
                dataToExport = this.getCurrentListData() ?? null;
                if (dataToExport === undefined) { dataToExport = null; }
            }

            // 2. Check data
            if (dataToExport === null) { this.toast?.showErrorToast("Failed to retrieve data for download."); return; }
            if (!dataToExport || dataToExport.length === 0) { this.toast?.showErrorToast(`No roles available to download.`); return; }

            console.log(`Fetched ${dataToExport.length} roles for Excel export (${type}).`);

            // 3. Transform data
            const dataForExcel = dataToExport.map(role => ({
                'Role ID': role.id,
                'Role Name': role.name,
                'Status': role.enabled ? 'Active' : 'Inactive', // Add if 'enabled' exists
                'Created At': role.createdTimestamp ? new Date(role.createdTimestamp).toLocaleString() : 'N/A',
                'Updated At': role.updatedTimestamp ? new Date(role.updatedTimestamp).toLocaleString() : 'N/A', // Add if exists
            }));

            // 4. Create Worksheet and Workbook
            const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(dataForExcel);
            const wb: XLSX.WorkBook = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(wb, ws, 'Roles'); // Sheet name

            // 5. Generate File Name
            const dateStr = new Date().toISOString().slice(0, 10);
            const typeStr = type === 'current' ? `Page${this.currentPage}` : 'All';
            const fileName = `AdminRoles_${typeStr}_${dateStr}.xlsx`;

            // 6. Trigger Download
            XLSX.writeFile(wb, fileName);
            this.toast?.showSuccessToast(`Excel file download started (${type}).`);

        } catch (error) {
            console.error(`Error generating Excel file (${type}):`, error);
            this.toast?.showErrorToast(`An error occurred while generating the Excel file (${type}).`);
        } finally {
            this.isDownloadingExcel = false;
            this.downloadType = null;
        }
    }

    // --- Filter Modal ---
    openFilterModal() {
        this.isEditUserModalOpen = true;
    }
    closeModal() {
        this.isEditUserModalOpen = false;
    }

    // --- Centralized Data Loading ---
    async loadRoles(page: number) {
        this.listLoading = true;
        this.rolesList = []; // Clear previous list

        const filterParams: string[] = [];

        // Add dynamic filters based on the 'filters' object
        if (this.filters.name) {
            filterParams.push(`name||$contL||${this.filters.name}`); // 'cn' for contains search
        }
        if (this.filters.enabled !== null && this.filters.enabled !== undefined && this.filters.enabled !== '') {
             // Assuming backend field is 'enabled' and accepts boolean string 'true'/'false'
            filterParams.push(`enabled||eq||${this.filters.enabled}`);
        }
        // Add other filters if the form expands

        const data = {
            page: page,
            limit: this.itemsPerPage,
            sort: `${this.filters.sortField || 'id'},${this.filters.sortDirection || 'DESC'}`,
            filter: filterParams // Pass dynamically built filters
        };

        try {
            const param = createAxiosConfig(data);
            console.log('API Request Params (Roles):', param);
            const response = await this.adminService.getAdminRoles(param);
            this.rolesList = response.data ?? [];
            // Assuming the response structure includes a 'total' count for pagination
            this.totalItems = response.total ?? response.data?.length ?? 0; // Adjust based on actual API response
        } catch (error) {
            console.error("Error fetching roles:", error);
            this.rolesList = [];
            this.totalItems = 0;
            // Show error message to user if needed
        } finally {
            this.listLoading = false;
        }
    }

    // --- Pagination ---
    onPageChange(page: number) {
        this.currentPage = page;
        this.loadRoles(this.currentPage); // Reload data for the new page
    }

    // --- Filter Actions ---
    applyFilters() {
        // Check if form is valid before applying filters
        if (this.filterForm && this.filterForm.invalid) {
            this.toast?.showErrorToast("Please correct the validation errors before applying filters.");
            return;
        }

        // Trim string values to prevent whitespace-only searches
        if (this.filters.name) {
            this.filters.name = this.filters.name.trim();
        }

        this.currentPage = 1; // Reset to first page
        this.loadRoles(this.currentPage);
        this.closeModal(); // Close filter modal
    }

    resetFilters() {
        // Reset filter object to default values
        this.filters = {
            name: null,
            enabled: null, // Or 'true' if that's the default view
            sortField: 'id',
            sortDirection: 'DESC'
        };
        this.currentPage = 1; // Reset page
        this.loadRoles(this.currentPage);
        // Optionally close modal: this.closeModal();
    }

    // --- Sorting Helpers ---
    getSortClass(key: string): string {
        if (this.filters.sortField === key) {
            return this.filters.sortDirection === 'ASC' ? 'sort-asc' : 'sort-desc';
        }
        return '';
    }

    sortBy(field: string) {
        if (this.filters.sortField === field) {
            this.filters.sortDirection = this.filters.sortDirection === 'ASC' ? 'DESC' : 'ASC';
        } else {
            this.filters.sortField = field;
            this.filters.sortDirection = 'DESC'; // Or 'ASC' as default
        }
        this.applyFilters(); // Reload data with new sort
    }

    // --- CRUD Action Placeholders ---
    handleCreate() {
        console.log('handleCreate triggered');
        // Implement logic to open a create/edit modal or navigate to a create page
        // Example: this.openRoleModal(); // Pass no item for create mode
    }

    handleUpdate(item: any) {
        console.log('handleUpdate triggered for:', item);
         // Implement logic to open a create/edit modal or navigate to an edit page
        // Example: this.openRoleModal(item); // Pass item for edit mode
    }

    handleDelete(item: any) {
        console.log('handleDelete triggered for:', item);
        if (confirm(`Are you sure you want to delete role "${item.name}" (ID: ${item.id})?`)) {
             // --- Add API call to delete the role ---
             // Example:
             // this.adminService.deleteAdminRole(item.id).subscribe({
             //    next: () => {
             //        console.log('Role deleted successfully');
             //        this.loadRoles(this.currentPage); // Refresh list
             //        // Show success message
             //    },
             //    error: (err) => {
             //        console.error('Error deleting role:', err);
             //        // Show error message
             //    }
             // });
             // --- End API Call ---
             alert(`Functionality to delete role ${item.id} not fully implemented.`);
        }
    }

    // sortChange() { } // Redundant if using sortBy
}