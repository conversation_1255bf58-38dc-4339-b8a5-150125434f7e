<div class="container">
    <!-- Tab Component -->
    <app-tab [tabs]="tabs" [selectedTabIndex]="selectedTabIndex" (tabSelected)="onTabSelected($event)">
    </app-tab>

    <!-- AAA Leaders Tab (Index 0) -->
    <div *ngIf="selectedTabIndex === 0" class="card mb-4">
        <div class="card-header">
            <div class="row align-items-center">
                <div class="col">
                    <h6 class="mb-0">{{ currentTabTitle }} - Top 3 Leaders</h6>
                </div>
                <div class="col-auto">
                    <button type="button" class="btn p-0" (click)="openFilterModal()" title="Filter">
                        <img src="../../../assets/svg/filter.svg" class="filter-button" alt="Filter" style="width: 35px;" />
                    </button>
                </div>
            </div>
        </div>
        <div class="card-body">
            <!-- Loading Indicator -->
            <div *ngIf="isLoading" class="text-center my-3">
                <div class="spinner-border text-primary spinner-border-sm">
                    <span class="visually-hidden">Loading...</span>
                </div>
            </div>

            <div *ngIf="!isLoading" class="table-responsive">
                <table class="table table-bordered table-hover custom-leaderboard-table">
                    <thead class="table-header">
                        <tr>
                            <th scope="col" class="text-center">Rank</th>
                            <th scope="col" class="text-center">User Details</th>
                            <th scope="col" class="text-center">Safety and Conditions</th>
                            <th scope="col" class="text-center">Tours and QR Data</th>
                        </tr>
                    </thead>
                    <tbody *ngIf="(currentTabData | slice:0:3).length > 0; else noTop3Data">
                        <tr *ngFor="let item of currentTabData | slice:0:3; let i = index">
                            <!-- Rank Column -->
                            <td class="text-center align-middle">
                                <div class="text-center">
                                    <img *ngIf="getRankImage(i)" [src]="getRankImage(i)" alt="Rank {{ i + 1 }}"
                                        width="44" height="69" />
                                    <span *ngIf="!getRankImage(i)">{{ i + 1 }}</span>
                                </div>
                            </td>
                            <!-- User Details Column -->
                            <td class="align-middle">
                                <div class="user-details">
                                    <p class="label-value"><strong>User ID:</strong> <span class="value-text">{{ item.adminId }}</span></p>
                                    <p class="label-value"><strong>User Name:</strong> <span class="value-text">{{ item.firstName }} {{ item.lastName }}</span></p>
                                    <p class="label-value"><strong>Email:</strong> <span class="value-text">{{ item.email }}</span></p>
                                    <p class="label-value"><strong>Contact:</strong> <span class="value-text">{{ item.contactNumber }}</span></p>
                                </div>
                            </td>
                            <!-- Safety and Conditions Column -->
                            <td class="align-middle">
                                <div class="safety-details">
                                    <p class="label-value"><strong>Safe Act:</strong> <span class="value-text">{{ item.safeobservationcount ?? 'N/A' }}</span></p>
                                    <p class="label-value"><strong>Unsafe Act:</strong> <span class="value-text">{{ item.unsafeobservationcount ?? 'N/A' }}</span></p>
                                    <p class="label-value"><strong>Unsafe Condition:</strong> <span class="value-text">{{ item.unsafeconditionobservationcount ?? 'N/A' }}</span></p>
                                    <p class="label-value"><strong>Total Observation:</strong> <span class="value-text">{{ item.totalobservationcount ?? 'N/A' }}</span></p>
                                </div>
                            </td>
                            <!-- Tours and QR Data Column -->
                            <td class="align-middle">
                                <div class="qr-scanned">
                                    <p class="label-value">
                                        <strong>QR Scanned:</strong>
                                        <span class="value-text">
                                            {{ getQrSum(item) }}
                                            <span class="tooltip-container">
                                                <i class="bi bi-info-circle info-icon"></i>
                                                <div class="tooltip-content">
                                                    <div class="qr-details-header">QR Scan Details:</div>
                                                    <ul>
                                                        <li><i class="bi bi-check-circle-fill"></i> <b>Permanent QR:</b> {{ item.permanentqrcount != null ? item.permanentqrcount : '' }}</li>
                                                        <li><i class="bi bi-clock-fill"></i> <b>Temporary QR:</b> {{ item.temporaryqrcount != null ? item.temporaryqrcount : '' }}</li>
                                                        <li><i class="bi bi-calculator"></i> <b>Total:</b> {{ getQrSum(item) }}</li>
                                                    </ul>
                                                </div>
                                            </span>
                                        </span>
                                    </p>
                                </div>
                                <div class="tour-details">
                                    <p class="label-value"><strong>Tours:</strong> <span class="value-text">{{ item.tour_count ?? 'N/A' }}</span></p>
                                    <p class="label-value"><strong>Tour Points:</strong> <span class="value-text">{{ item.tourpointcount ?? 'N/A' }}</span></p>
                                    <p class="label-value"><strong>Total Days:</strong> <span class="value-text">{{ item.totaldays ?? 'N/A' }}</span></p>
                                    <p class="label-value"><strong>Total Hours:</strong> <span class="value-text">{{ item.totalHours ?? 'N/A' }}</span></p>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                    <!-- Template for when no top 3 data exists -->
                    <ng-template #noTop3Data>
                        <tbody>
                            <tr>
                                <td colspan="4" class="text-center fst-italic text-muted py-3">No leaders found for the top 3 ranks.</td>
                            </tr>
                        </tbody>
                    </ng-template>
                </table>
            </div>
        </div>
    </div>

    <!-- Plant Leaders Tab (Index 1) -->
    <div *ngIf="selectedTabIndex === 1" class="card">
        <div class="card-header">
            <div class="row align-items-center">
                <div class="col">
                    <h6 class="mb-0">{{ currentTabTitle }}</h6>
                </div>
                <div class="col-auto">
                    <button type="button" class="btn p-0" (click)="openFilterModal()" title="Filter">
                        <img src="../../../assets/svg/filter.svg" class="filter-button" alt="Filter" style="width: 35px;" />
                    </button>
                </div>
            </div>
        </div>
        <div class="card-body">
            <!-- Loading Indicator -->
            <div *ngIf="isLoading" class="text-center my-3">
                <div class="spinner-border text-primary spinner-border-sm">
                    <span class="visually-hidden">Loading...</span>
                </div>
            </div>

            <div *ngIf="!isLoading" class="table-responsive">
                <table class="table table-bordered table-hover custom-leaderboard-table">
                    <thead class="table-header">
                        <tr>
                            <th scope="col" class="text-center">Rank</th>
                            <th scope="col" class="text-center">User Details</th>
                            <th scope="col" class="text-center">Safety and Conditions</th>
                            <th scope="col" class="text-center">Tours and QR Data</th>
                        </tr>
                    </thead>
                    <tbody *ngIf="currentTabData.length > 0; else noNext7Data">
                        <tr *ngFor="let item of currentTabData; let i = index">
                            <!-- Rank Column -->
                            <td class="text-center align-middle">
                                <span class="fw-bold">{{ i + 1 }}</span>
                            </td>
                            <!-- User Details Column -->
                            <td class="align-middle">
                                <div class="user-details">
                                    <p class="label-value"><strong>User ID:</strong> <span class="value-text">{{ item.adminId }}</span></p>
                                    <p class="label-value"><strong>User Name:</strong> <span class="value-text">{{ item.firstName }} {{ item.lastName }}</span></p>
                                    <p class="label-value"><strong>Email:</strong> <span class="value-text">{{ item.email }}</span></p>
                                    <p class="label-value"><strong>Contact:</strong> <span class="value-text">{{ item.contactNumber }}</span></p>
                                </div>
                            </td>
                            <!-- Safety and Conditions Column -->
                            <td class="align-middle">
                                <div class="safety-details">
                                    <p class="label-value"><strong>Safe Act:</strong> <span class="value-text">{{ item.safeobservationcount ?? 'N/A' }}</span></p>
                                    <p class="label-value"><strong>Unsafe Act:</strong> <span class="value-text">{{ item.unsafeobservationcount ?? 'N/A' }}</span></p>
                                    <p class="label-value"><strong>Unsafe Condition:</strong> <span class="value-text">{{ item.unsafeconditionobservationcount ?? 'N/A' }}</span></p>
                                    <p class="label-value"><strong>Total Observation:</strong> <span class="value-text">{{ item.totalobservationcount ?? 'N/A' }}</span></p>
                                </div>
                            </td>
                            <!-- Tours and QR Data Column -->
                            <td class="align-middle">
                                <div class="qr-scanned">
                                    <p class="label-value">
                                        <strong>QR Scanned:</strong>
                                        <span class="value-text">
                                            {{ getQrSum(item) }}
                                            <span class="tooltip-container">
                                                <i class="bi bi-info-circle info-icon"></i>
                                                <div class="tooltip-content">
                                                    <div class="qr-details-header">QR Scan Details:</div>
                                                    <ul>
                                                        <li><i class="bi bi-check-circle-fill"></i> <b>Permanent QR:</b> {{ item.permanentqrcount != null ? item.permanentqrcount : '' }}</li>
                                                        <li><i class="bi bi-clock-fill"></i> <b>Temporary QR:</b> {{ item.temporaryqrcount != null ? item.temporaryqrcount : '' }}</li>
                                                        <li><i class="bi bi-calculator"></i> <b>Total:</b> {{ getQrSum(item) }}</li>
                                                    </ul>
                                                </div>
                                            </span>
                                        </span>
                                    </p>
                                </div>
                                <div class="tour-details">
                                    <p class="label-value"><strong>Tours:</strong> <span class="value-text">{{ item.tour_count ?? 'N/A' }}</span></p>
                                    <p class="label-value"><strong>Tour Points:</strong> <span class="value-text">{{ item.tourpointcount ?? 'N/A' }}</span></p>
                                    <p class="label-value"><strong>Total Days:</strong> <span class="value-text">{{ item.totaldays ?? 'N/A' }}</span></p>
                                    <p class="label-value"><strong>Total Hours:</strong> <span class="value-text">{{ item.totalHours ?? 'N/A' }}</span></p>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                    <!-- Template for when no data exists -->
                    <ng-template #noNext7Data>
                        <tbody>
                            <tr>
                                <td colspan="4" class="text-center fst-italic text-muted py-3">No leaders found.</td>
                            </tr>
                        </tbody>
                    </ng-template>
                </table>
            </div>
        </div>
    </div>

    <!-- Filter Offcanvas -->
    <app-offcanvas [title]="'Filter ' + currentTabTitle" *ngIf="isFilterOffcanvasOpen" (onClickCross)="closeFilterOffcanvas()">
        <div class="container">
            <form #filterForm="ngForm" (ngSubmit)="applyFilters()">
                <div class="row g-3">
                    <!-- AAA Leaders Filters -->
                    <div *ngIf="selectedTabIndex === 0">
                        <!-- Month Filter -->
                        <div class="col-md-12">
                            <label for="aaaFilterMonth" class="form-label">Month</label>
                            <select id="aaaFilterMonth" class="form-select"
                                    [(ngModel)]="aaaLeaderFilters.month"
                                    name="aaaFilterMonth">
                                <option *ngFor="let month of monthOptions" [value]="month.value">{{ month.label }}</option>
                            </select>
                        </div>

                        <!-- Year Filter -->
                        <div class="col-md-12">
                            <label for="aaaFilterYear" class="form-label">Year</label>
                            <select id="aaaFilterYear" class="form-select"
                                    [(ngModel)]="aaaLeaderFilters.year"
                                    name="aaaFilterYear">
                                <option *ngFor="let year of yearOptions" [value]="year.value">{{ year.label }}</option>
                            </select>
                        </div>
                    </div>

                    <!-- Plant Leaders Filters -->
                    <div *ngIf="selectedTabIndex === 1">
                        <!-- Month Filter -->
                        <div class="col-md-12">
                            <label for="plantFilterMonth" class="form-label">Month</label>
                            <select id="plantFilterMonth" class="form-select"
                                    [(ngModel)]="plantLeaderFilters.month"
                                    name="plantFilterMonth">
                                <option *ngFor="let month of monthOptions" [value]="month.value">{{ month.label }}</option>
                            </select>
                        </div>

                        <!-- Year Filter -->
                        <div class="col-md-12">
                            <label for="plantFilterYear" class="form-label">Year</label>
                            <select id="plantFilterYear" class="form-select"
                                    [(ngModel)]="plantLeaderFilters.year"
                                    name="plantFilterYear">
                                <option *ngFor="let year of yearOptions" [value]="year.value">{{ year.label }}</option>
                            </select>
                        </div>

                        <!-- Plant Filter -->
                        <div class="col-md-12">
                            <label for="plantFilterPlant" class="form-label">Plant</label>
                            <ng-select
                                [items]="plantList"
                                bindLabel="name"
                                bindValue="id"
                                [multiple]="true"
                                [placeholder]="isLoadingPlants ? 'Loading plants...' : 'Select plants'"
                                [(ngModel)]="plantLeaderFilters.plantIds"
                                name="plantFilterPlant"
                                id="plantFilterPlant"
                                [closeOnSelect]="false"
                                [clearable]="true"
                                [loading]="isLoadingPlants"
                                [disabled]="isLoadingPlants">
                            </ng-select>
                            <small class="form-text text-muted">
                                <span *ngIf="isLoadingPlants">Loading plants...</span>
                                <span *ngIf="!isLoadingPlants && plantList.length === 0">No plants available</span>
                                <span *ngIf="!isLoadingPlants && plantList.length > 0">Select multiple plants for filtering ({{ plantList.length }} available)</span>
                            </small>
                        </div>
                    </div>


                    <!-- Filter Actions -->
                    <div class="col-12 mt-4 d-grid gap-2">
                        <button type="submit" class="btn adani-btn">
                            <i class="bi bi-search me-1"></i> Apply Filters
                        </button>
                        <button type="button" class="btn btn-secondary" (click)="resetFilters()">
                            <i class="bi bi-arrow-clockwise me-1"></i> Reset Filters
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </app-offcanvas>
</div>
