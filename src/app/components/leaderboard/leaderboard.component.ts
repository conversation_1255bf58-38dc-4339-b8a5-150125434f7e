import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { NgSelectModule } from '@ng-select/ng-select';
import { LeaderboardService } from '../../services/leaderboard/leaderboard.service';
import { OffcanvasComponent } from "../../shared/offcanvas/offcanvas.component";
import { TabComponent } from "../../shared/tab/tab.component";
import { AdminService } from '../../services/admin/admin.service';
import { PlantManagementService } from '../../services/plant-management/plant-management.service';
import { createAxiosConfig } from '../../core/utilities/axios-param-config';
import { AuthService } from '../../services/auth.service'; // Import AuthService

// Define ROLES constant
const ROLES = {
  SUPER_ADMIN: 'super_admin',
  PLANT_ADMIN: 'plant_admin',
};

// Define the interfaces for different filter criteria
export interface AAALeaderFilterCriteria {
  month: string | null;
  year: string | null;
}

export interface PlantLeaderFilterCriteria {
  month: string | null;
  year: string | null;
  plantIds: number[];
}

// Define interface for leaderboard item
export interface LeaderboardItem {
  adminId: string;
  firstName: string;
  lastName: string;
  email: string;
  contactNumber: string;
  safeobservationcount: number;
  unsafeobservationcount: number;
  unsafeconditionobservationcount: number;
  totalobservationcount: number;
  permanentqrcount: number;
  temporaryqrcount: number;
  tour_count: number;
  tourpointcount: number;
  totaldays: number;
  totalHours: number;
  [key: string]: any; // For any additional properties
}

@Component({
  selector: 'app-leaderboard',
  standalone: true,
  imports: [CommonModule, FormsModule, NgSelectModule, OffcanvasComponent, TabComponent],
  templateUrl: './leaderboard.component.html',
  styleUrl: './leaderboard.component.scss'
})
export class LeaderboardComponent implements OnInit {
  // --- Tab Configuration ---
  selectedTabIndex = 0;
  tabs = [
    { title: 'AAA Leaders' },
    { title: 'Plant Leaders' }
  ];

  // --- Filter Modal State ---
  isFilterOffcanvasOpen: boolean = false;

  // --- Filter Options Data ---
  plantList: any[] = [];
  adminRolesList: any[] = [];
  monthOptions = [
    { value: '01', label: 'January' },
    { value: '02', label: 'February' },
    { value: '03', label: 'March' },
    { value: '04', label: 'April' },
    { value: '05', label: 'May' },
    { value: '06', label: 'June' },
    { value: '07', label: 'July' },
    { value: '08', label: 'August' },
    { value: '09', label: 'September' },
    { value: '10', label: 'October' },
    { value: '11', label: 'November' },
    { value: '12', label: 'December' }
  ];
  yearOptions: { value: string, label: string }[] = [];

  // --- Separate Filter Criteria for Each Tab ---
  aaaLeaderFilters: AAALeaderFilterCriteria = {
    month: null,
    year: null
  };

  plantLeaderFilters: PlantLeaderFilterCriteria = {
    month: null,
    year: null,
    plantIds: [] as number[]
  };

  // --- Leaderboard Data ---
  aaaLeadersList: LeaderboardItem[] = [];
  plantLeadersList: LeaderboardItem[] = [];

  // --- Other State ---
  isLoading: boolean = false;
  isLoadingPlants: boolean = false;
  adminRole: any = 'superadmin'; // This might be redundant with currentUserRole

  // Role-Based Access Control Properties
  currentUserRole: string = '';
  loggedInAdminId: number | null = null;
  loggedInPlantIds: number[] = [];


  constructor(
    private leaderboardService: LeaderboardService,
    private adminService: AdminService,
    private plantService: PlantManagementService,
    private authService: AuthService // Inject AuthService
  ) {
    // Generate year options (current year and past 5 years)
    const currentYear = new Date().getFullYear();
    for (let i = 0; i < 6; i++) {
      const year = currentYear - i;
      this.yearOptions.push({ value: year.toString(), label: year.toString() });
    }
  }

  ngOnInit(): void {
    console.log('LeaderboardComponent initializing...');
    this.setCurrentUserRoleAndDetailsById(); // Set user role and plants

    // Set loading to true immediately to show loading indicator
    this.isLoading = true;

    // Initialize with fallback data first to ensure dropdown works
    this.plantList = [
      { id: 1, name: 'Loading plants...' }
    ];

    this.fetchFilterOptions().then(() => {
      console.log('Filter options loaded, plant list length:', this.plantList.length);
      console.log('Plant list data:', this.plantList);
      this.loadDataForCurrentTab();
    }).catch(error => {
      console.error('Error during initialization:', error);
      this.loadDataForCurrentTab();
    });
  }

  /**
   * Sets the current user's role and associated plant IDs from local storage.
   */
  private setCurrentUserRoleAndDetailsById(): void {
    try {
      const userString = localStorage.getItem('user');
      if (!userString || userString.trim() === "" || ['null', 'undefined', '""', '" "'].includes(userString.trim().toLowerCase())) {
        this.currentUserRole = ''; this.loggedInAdminId = null; this.loggedInPlantIds = [];
        console.warn("User session invalid or empty."); return;
      }
      const currentUser = JSON.parse(userString);
      this.loggedInAdminId = currentUser?.id ?? null;
      this.loggedInPlantIds = (Array.isArray(currentUser?.plantIds) && currentUser.plantIds.length > 0)
        ? currentUser.plantIds.map((id: any) => Number(id)).filter((id: number) => !isNaN(id)) : [];
      const roleId = currentUser?.adminsRoleId;
      if (roleId === 1 || roleId === 6) { this.currentUserRole = ROLES.SUPER_ADMIN; }
      else if (roleId === 2) {
        this.currentUserRole = ROLES.PLANT_ADMIN;
        if (this.loggedInPlantIds.length === 0) { console.warn("Plant Admin has no assigned plants."); }
      } else { this.currentUserRole = ''; console.warn("Invalid user role."); }
      console.log(`Leaderboard - Role: ${this.currentUserRole}, UserID: ${this.loggedInAdminId}, Plants: [${this.loggedInPlantIds.join(', ')}]`);
    } catch (error) {
      console.error("Error parsing user data:", error);
      this.currentUserRole = ''; this.loggedInAdminId = null; this.loggedInPlantIds = [];
    }
  }

  // --- Data Fetching ---
  async fetchFilterOptions() {
    this.isLoadingPlants = true;
    try {
      // Fetch plants using actual service
      const plantParams = createAxiosConfig({
        sort: 'name,ASC',
        filter: ['enabled||eq||true'],
        limit: 1000
      });
      const plantsResponse = await this.plantService.getPlants(plantParams);

      // Handle different response structures
      let plants = [];
      if (plantsResponse?.data) {
        plants = plantsResponse.data;
      } else if (Array.isArray(plantsResponse)) {
        plants = plantsResponse;
      } else {
        console.warn('Unexpected plants response structure:', plantsResponse);
        plants = [];
      }

      // Filter plants based on user role
      if (this.currentUserRole === ROLES.PLANT_ADMIN && this.loggedInPlantIds.length > 0) {
        this.plantList = plants.filter((p: any) => this.loggedInPlantIds.includes(p.id)).map((p: any) => ({
          id: p.id,
          name: p.name || p.title || `Plant ${p.id}`
        }));
        // Pre-select assigned plants for plant admin
        this.plantLeaderFilters.plantIds = this.plantList.map(p => p.id);
      } else if (this.currentUserRole === ROLES.SUPER_ADMIN) {
        this.plantList = plants.map((p: any) => ({
          id: p.id,
          name: p.name || p.title || `Plant ${p.id}`
        }));
      } else {
        this.plantList = []; // No plants for unknown roles or plant admin without assigned plants
      }

      console.log('Plants loaded:', this.plantList);
      console.log('Raw plants response:', plantsResponse);

      // Fetch admin roles
      const adminRolesParams = createAxiosConfig({});
      const adminRolesResponse = await this.adminService.getAdminRoles(adminRolesParams);
      this.adminRolesList = adminRolesResponse.data || [];
      console.log('Admin roles loaded:', this.adminRolesList);
    } catch (error) {
      console.error("Error fetching filter options:", error);
      // Set some default plants for testing if API fails
      this.plantList = [
        { id: 1, name: 'Plant Alpha' },
        { id: 2, name: 'Plant Beta' },
        { id: 3, name: 'Plant Gamma' }
      ];
      console.log('Using fallback plant data:', this.plantList);
    } finally {
      this.isLoadingPlants = false;
    }
  }

  async getAAALeaders(filters: AAALeaderFilterCriteria) {
    this.isLoading = true;

    // Convert month/year to startDate/endDate like Plant Leaders
    const startDate = this.getDateFromMonthYear(filters.month, filters.year, false) || "2023-04-20";
    const endDate = this.getDateFromMonthYear(filters.month, filters.year, true) || "2025-05-20";

    const requestParams = {
      startDate: startDate,
      endDate: endDate,
      // Add any specific sorting or limits for AAA leaders
    };

    console.log('AAA Leaders API Request:', requestParams);

    try {
      const response = await this.leaderboardService.getLeaderboard(requestParams);
      this.aaaLeadersList = (response.data || []).map((item: LeaderboardItem) => ({
        ...item,
        permanentqrcount: item.permanentqrcount != null ? Number(item.permanentqrcount) : null,
        temporaryqrcount: item.temporaryqrcount != null ? Number(item.temporaryqrcount) : null,
      }));
      console.log('AAA Leaders Response:', this.aaaLeadersList);
    } catch (error) {
      console.error("Error fetching AAA leaders:", error);
      this.aaaLeadersList = [];
    } finally {
      this.isLoading = false;
    }
  }

  async getPlantLeaders(filters: PlantLeaderFilterCriteria) {
    this.isLoading = true;

    // Convert month/year to startDate/endDate
    const startDate = this.getDateFromMonthYear(filters.month, filters.year, false) || "2023-04-20";
    const endDate = this.getDateFromMonthYear(filters.month, filters.year, true) || "2025-05-20";

    let plantIdsToSend: number[] = [];

    // Determine plant IDs based on role and filter selection
    if (this.currentUserRole === ROLES.PLANT_ADMIN) {
      if (this.loggedInPlantIds.length > 0) {
        // If plant admin has selected specific plants from their assigned list, use those
        if (filters.plantIds && filters.plantIds.length > 0) {
          // Ensure selected plants are within the assigned plants
          plantIdsToSend = filters.plantIds.filter(id => this.loggedInPlantIds.includes(id));
        } else {
          // Otherwise, use all assigned plants
          plantIdsToSend = this.loggedInPlantIds;
        }
      } else {
        console.warn("Plant Admin has no plants, skipping plant leaders fetch.");
        this.plantLeadersList = [];
        this.isLoading = false;
        return;
      }
    } else if (this.currentUserRole === ROLES.SUPER_ADMIN) {
      // Super admin uses selected plants, or empty array if none selected
      plantIdsToSend = filters.plantIds || [];
    } else {
      console.error("Unknown user role, cannot fetch plant leaders.");
      this.plantLeadersList = [];
      this.isLoading = false;
      return;
    }

    // Prepare request data in the format expected by the API
    const requestData = {
      pageSize: 1000, // Increased to fetch more results
      pageIndex: 1,
      startDate: startDate,
      endDate: endDate,
      plantIds: plantIdsToSend, // Use determined plant IDs
      adminsRoleIds: this.getSelectedAdminRoleIds() // Get admin role IDs
    };

    console.log('Plant Leaders API Request:', requestData);

    try {
      const response = await this.leaderboardService.getPlantLeaders(requestData);
      this.plantLeadersList = (response.data || []).map((item: LeaderboardItem) => ({
        ...item,
        permanentqrcount: item.permanentqrcount != null ? Number(item.permanentqrcount) : null,
        temporaryqrcount: item.temporaryqrcount != null ? Number(item.temporaryqrcount) : null,
      }));
      console.log('Plant Leaders Response:', this.plantLeadersList);
    } catch (error) {
      console.error("Error fetching plant leaders:", error);
      this.plantLeadersList = [];
    } finally {
      this.isLoading = false;
    }
  }

  // Helper method to get selected admin role IDs
  private getSelectedAdminRoleIds(): number[] {
    // Return default admin role IDs (1, 2, 3) or allow user selection
    // You can modify this to be configurable if needed
    return [1, 2, 3];
  }

  // --- Tab Handling ---
  onTabSelected(index: number) {
    if (this.selectedTabIndex === index || this.isLoading) return;
    this.selectedTabIndex = index;
    this.loadDataForCurrentTab();
  }

  loadDataForCurrentTab() {
    // Set loading to true immediately to show loading indicator
    this.isLoading = true;
    
    if (this.selectedTabIndex === 0) {
      this.getAAALeaders(this.aaaLeaderFilters);
    } else {
      this.getPlantLeaders(this.plantLeaderFilters);
    }
  }

  // --- Filter Actions ---
  applyFilters(): void {
    console.log('Applying filters for tab:', this.selectedTabIndex);
    this.loadDataForCurrentTab();
    this.isFilterOffcanvasOpen = false;
  }

  resetFilters(): void {
    if (this.selectedTabIndex === 0) {
      this.aaaLeaderFilters = {
        month: null,
        year: null
      };
    } else {
      this.plantLeaderFilters = {
        month: null,
        year: null,
        plantIds: []
      };
      // For plant admin, reset to their assigned plants
      if (this.currentUserRole === ROLES.PLANT_ADMIN && this.loggedInPlantIds.length > 0) {
        this.plantLeaderFilters.plantIds = [...this.loggedInPlantIds];
      }
    }
    console.log('Filters reset for tab:', this.selectedTabIndex);
    this.loadDataForCurrentTab(); // Load data without filters
  }

  // --- Filter Modal Controls ---
  openFilterModal() {
    this.isFilterOffcanvasOpen = true;
  }

  closeFilterOffcanvas(): void {
    this.isFilterOffcanvasOpen = false;
  }

  // --- Getters for Current Tab Data ---
  get currentTabData(): LeaderboardItem[] {
    return this.selectedTabIndex === 0 ? this.aaaLeadersList : this.plantLeadersList;
  }

  get currentTabTitle(): string {
    return this.tabs[this.selectedTabIndex]?.title || '';
  }

  get currentFilters(): AAALeaderFilterCriteria | PlantLeaderFilterCriteria {
    return this.selectedTabIndex === 0 ? this.aaaLeaderFilters : this.plantLeaderFilters;
  }

  // --- Utility Methods ---
  isAAALeadersTab(): boolean {
    return this.selectedTabIndex === 0;
  }

  isPlantLeadersTab(): boolean {
    return this.selectedTabIndex === 1;
  }

  hasFilterApplied(): boolean {
    if (this.isAAALeadersTab()) {
      return !!(this.aaaLeaderFilters.month || this.aaaLeaderFilters.year);
    } else {
      return !!(this.plantLeaderFilters.month || this.plantLeaderFilters.year || this.plantLeaderFilters.plantIds.length > 0);
    }
  }

  // Helper method to convert month/year to date string
  getDateFromMonthYear(month: string | null, year: string | null, isEndDate: boolean = false): string | null {
    if (!month || !year) return null;

    if (isEndDate) {
      // Get last day of the month
      const lastDay = new Date(parseInt(year), parseInt(month), 0).getDate();
      return `${year}-${month.padStart(2, '0')}-${lastDay.toString().padStart(2, '0')}`;
    } else {
      // Get first day of the month
      return `${year}-${month.padStart(2, '0')}-01`;
    }
  }

  // --- Utility/Display Methods ---
  getRankImage(index: number): string {
    const rankImages = [
      '../../../assets/img/rank1.png',
      '../../../assets/img/rank2.png',
      '../../../assets/img/rank3.png',
    ];
    return rankImages[index] || '';
  }

  getQrSum(item: LeaderboardItem): number | string {
    const pVal = item.permanentqrcount;
    const tVal = item.temporaryqrcount;

    // Convert to numbers, defaulting to 0 if null, undefined, or unparseable (NaN becomes 0 via || 0)
    const pNum = Number(pVal) || 0;
    const tNum = Number(tVal) || 0;

    const sum = pNum + tNum;

    // Determine if original values were effectively "empty" (null, undefined, or empty string)
    // This helps decide if a sum of 0 should be displayed as '0' or ''
    const pOriginalIsEmpty = pVal == null || String(pVal).trim() === '';
    const tOriginalIsEmpty = tVal == null || String(tVal).trim() === '';

    // If sum is 0 AND both original values were "empty", return empty string.
    // Otherwise, return the sum. This ensures 0 is shown if it's a legitimate sum from non-empty inputs.
    if (sum === 0 && pOriginalIsEmpty && tOriginalIsEmpty) {
      return '';
    }

    return sum;
  }

  // --- Remove potentially unused methods from previous version ---
  // filterUser($event: Event) { /* No longer needed if only applying on button click */ }
  // selectAdminRole($event: Event) { /* No longer needed if only applying on button click */ }
}
