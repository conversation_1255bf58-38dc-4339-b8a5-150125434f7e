.custom-leaderboard-table {
    width: 100%;
    table-layout: auto; /* Added for better table layout control */
    border-collapse: collapse; /* Ensures borders collapse properly */
    border-radius: 10px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1); /* Increased shadow for better visibility */
    overflow: hidden; /* Ensures borders are rounded */

    .table-header th {
      font-size: 12px;
      background-color: #0b74b0 !important; /* Consistent header background color */
      color: white !important;
      font-weight: bold;
      text-align: center;
      padding: 10px 12px;
    }

    td,
    th {
      text-align: left;
      vertical-align: middle; /* Vertically center cell content */
      padding: 10px 12px;
    }

    /* Specific styles for table cells */
    td {
      font-size: 12px;
    }

    /* Style for rank images */
    img {
      width: 44px;
      height: 69px;
      object-fit: contain;
    }

    /* Add bottom border to table rows */
    tbody tr {
      border-bottom: 1px dotted #eee;
    }

    /* Style for bold text in table cells */
    td b,
    td strong,
    td span:not(.info-label) {
      font-weight: 600;
      color: #222;
      font-size: 12px;
      letter-spacing: normal;
      line-height: 1.5;
    }
  }

/* Label-value pair styling */

.label-value {
  margin-bottom: 8px;
  line-height: 1.5;
  display: flex;
  align-items: baseline;
  padding: 2px 0;
  border-bottom: 1px dotted #eee;
  padding-bottom: 8px;
}

.label-value:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.label-value strong {
  font-weight: 500;
  color: #777;
  margin-right: 8px;
  min-width: 110px;
  font-size: 12px;
  display: inline-block;
  vertical-align: top;
}

.value-text {
  font-weight: 600;
  color: #222;
  font-size: 12px;
  display: inline-block;
  word-break: break-word;
  overflow-wrap: break-word;
  flex: 1;
  letter-spacing: normal;
  line-height: 1.5;
}

/* For backward compatibility */
.data-label {
  font-weight: 500;
  color: #777;
  margin-right: 8px;
  min-width: 110px;
  font-size: 12px;
  display: inline-block;
  vertical-align: top;
}

.data-value {
  font-weight: 600;
  color: #222;
  font-size: 12px;
  display: inline-block;
  word-break: break-word;
  overflow-wrap: break-word;
  flex: 1;
  letter-spacing: normal;
  line-height: 1.5;
}

/* Container styling */
.user-details, .safety-details, .tour-details, .qr-scanned {
  display: table;
  width: 100%;
  text-align: left;
  padding: 5px;
  border-spacing: 0 8px;
}

/* Add subtle separation between groups */
.qr-scanned {
  border-bottom: 1px dotted #eee;
  padding-bottom: 8px;
  margin-bottom: 8px;
}

/* Ensure consistent spacing */
.tour-details {
  padding-top: 8px;
}

/* Tooltip Styling */
.tooltip-container {
  position: relative;
  display: inline-block;
  margin-left: 5px;
}

.info-icon {
  cursor: pointer;
  color: #0b74b0;
  font-size: 14px;
}

.tooltip-content {
  display: none;
  position: absolute;
  z-index: 10;
  bottom: 125%;
  left: 50%;
  transform: translateX(-50%);
  background-color: white;
  color: #333;
  border: 1px solid #ccc;
  padding: 10px;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  width: 200px;
  text-align: left;
  font-size: 12px;
}

.tooltip-content::after {
  content: "";
  position: absolute;
  top: 100%;
  left: 50%;
  margin-left: -5px;
  border-width: 5px;
  border-style: solid;
  border-color: white transparent transparent transparent;
}

.tooltip-container:hover .tooltip-content {
  display: block;
}

.qr-details-header {
  font-weight: 600;
  margin-bottom: 8px;
  font-size: 12px;
  color: #222;
}

.tooltip-content ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.tooltip-content li {
  margin-bottom: 5px;
  display: flex;
  align-items: center;
  padding: 2px 0;
  border-bottom: 1px dotted #eee;
  padding-bottom: 5px;
}

.tooltip-content li:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.tooltip-content li i {
  margin-right: 8px;
  color: #0b74b0;
}

.tooltip-content li b {
  font-weight: 500;
  color: #777;
  margin-right: 5px;
}

  .qr-details-content {
    position: absolute;
    top: 100%;
    right: 0;
    background-color: #fff;
    border: 1px solid #ccc;
    border-radius: 4px;
    padding: 10px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    z-index: 10;
    min-width: 200px;
    visibility: hidden;
    opacity: 0;
    transition: visibility 0.3s, opacity 0.3s;

    ul {
      list-style-type: none;
      padding: 0;
      margin: 0;

      li {
        margin-bottom: 5px;
      }
    }

    .qr-details-header {
      margin-bottom: 10px;
    }
  }

  /* Card styling */
.card {
  background-color: #fff;
  border-radius: 10px;
  border: 1px solid rgba(0, 0, 0, 0.125);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  min-width: 0;
  word-wrap: break-word;
  margin-top: 10px;
  overflow: hidden;
}

.card-body {
  padding: 1.25rem;
}

.card-header {
  padding: 0.75rem 1.25rem;
  margin-bottom: 0;
  background-color: rgba(0, 0, 0, 0.03);
  border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}

/* Style for the user details */
.user-details {
  b {
    color: #222;
    font-size: 12px;
    font-weight: 600;
  }
}

/* Style for the safety details */
.safety-details {
  b {
    color: #222;
    font-size: 12px;
    font-weight: 600;
  }
}

.text-warning {
  color: #eb6f33 !important;
}
