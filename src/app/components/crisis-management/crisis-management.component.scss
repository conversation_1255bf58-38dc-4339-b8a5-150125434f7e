.custom-card {
  border-radius: 10px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.card-body {
  height: 70vh;
  overflow: auto;
}

.table-responsive {
  overflow-x: auto;
  white-space: nowrap; /* Keep this if horizontal scrolling is desired for narrow screens */
  max-width: 100%;
}

.custom-table {
  width: 100%;
  table-layout: auto; /* Or 'fixed' if you want columns to have equal width */
  border-collapse: collapse;
  border-radius: 10px; /* Note: border-radius on table might not show correctly without border-collapse: separate; */
  overflow: hidden; /* Helps clip corners if using border-radius */
}

.table-header th {
  font-size: 12px;
  background-color: #0b74b0 !important; /* Consistent header background color */
  color: white !important;
  font-weight: bold;
  text-align: center;
}

/* Style for general table cells */
.custom-table td {
  font-size: 12px;
  text-align: left;
  vertical-align: top; /* Align content to the top of the cell */
  padding: 0.75rem 0.5rem; /* Consistent padding */
  white-space: normal; /* Allow text wrapping within cells */
}

/* Target the table cells containing the detailed data rows */
.data-detail-table td {
   padding-top: 10px; /* Add some top padding to the cell */
   padding-bottom: 10px; /* Add some bottom padding to the cell */
}

/* Style for the rows within a table cell */
.data-row {
  /* Optional: adds a very subtle line between rows within a cell */
  /* border-bottom: 1px solid #eee; */
  /* padding-bottom: 4px; */ /* Space below the line if using border */
  /* margin-bottom: 4px; */ /* Space between rows if not using border */
  /* The mb-1/mb-2 classes handle spacing now */
   line-height: 1.5; /* Improve line spacing */
}

/* Remove bottom margin/padding/border from the last row in a group */
.data-group .data-row:last-child {
  margin-bottom: 0 !important;
  /* padding-bottom: 0; */
  /* border-bottom: none; */
}


/* Styling for the label part */
.data-label {
  font-weight: bold !important; /* Make label text explicitly bold */
  color: #555;      /* Dark grey color for label */
  padding-right: 5px; /* Small space between label and value columns */
  flex-shrink: 0; /* Prevent label column from shrinking too much */
}

/* Styling for the value part */
.data-value {
  color: #212529; /* Standard text color */
  word-break: break-word; /* Allow long words/emails to wrap */
}


/* Adjust button styles */
.data-row .btn-sm {
    padding: 0.2rem 0.4rem;
    font-size: 0.8em; /* Make buttons slightly smaller */
}


/* Ensure consistent vertical alignment within the row */
.data-row > div {
    display: flex;
    align-items: flex-start; /* Align items to the top of their line */
}


/* --- Existing styles --- */
.actions button {
  margin: 2px;
}

.img-thumbnail {
  width: 80px;
  height: 80px;
  object-fit: cover;
}

/* --- Filter Styles --- */
.filter-button {
    cursor: pointer;
    transition: opacity 0.2s ease-in-out;
}
.filter-button:hover {
    opacity: 0.8;
}

/* --- Adani Button Style --- */
.adani-btn:hover {
    background-color: #0056b3; /* Darker shade on hover */
    color: white;
}