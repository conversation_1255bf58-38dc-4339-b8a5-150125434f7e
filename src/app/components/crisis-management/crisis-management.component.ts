import { CommonModule } from '@angular/common';
import { Component, OnInit, ViewChild, ChangeDetectorRef, <PERSON><PERSON><PERSON><PERSON>, ElementRef, inject, AfterViewInit } from '@angular/core';
import { FormsModule, NgForm } from '@angular/forms';
import { Subject } from 'rxjs';

import { CrisisManagementService } from '../../services/crisis-management/crisis-management.service'; // Ensure correct path

import { NgbDropdownModule } from '@ng-bootstrap/ng-bootstrap';
import * as XLSX from 'xlsx';
import { Modal } from 'bootstrap'; // Import only Modal specifically
import { createAxiosConfig } from '../../core/utilities/axios-param-config';
import { AdminService } from '../../services/admin/admin.service';
import { PlantManagementService } from '../../services/plant-management/plant-management.service';
import { OffcanvasComponent } from '../../shared/offcanvas/offcanvas.component';
import { PaginationComponent } from '../../shared/pagination/pagination.component';
import { TabComponent } from '../../shared/tab/tab.component';
import { ToastMessageComponent } from '../../shared/toast-message/toast-message.component';
import { UpdateService } from '../../services/update/update.service';
import { UploadService } from '../../services/upload/upload.service';
import { Carousel } from '../../shared/carousel/carousel.component';
import { VideoPlayer } from '../../shared/video-player/video-player.component';


// Define ROLES constant
const ROLES = {
    SUPER_ADMIN: 'super_admin',
    PLANT_ADMIN: 'plant_admin',
};

// --- Interfaces & Enums ---

interface CrisisFilter {
  shortDescription?: string | null;
  plantId?: number | null;
  submittedById?: number | null;
  enabled?: string | null;
  sortDirection?: 'ASC' | 'DESC';
  sortField?: string;
  firstName?: string | null;
  lastName?: string | null;
}

interface Plant {
  id: number;
  name: string;
}
interface AdminUser {
    id: number;
    firstName: string;
    lastName: string;
}
interface SimpleOption {
    value: string;
    label: string;
}

interface SpocData {
    name: string | null;
    spocEmail: string | null;
    contactNumber: string | null;
}

interface Crisis {
    id: number;
    uniqueId: string;
    submittedByAdmin: AdminUser;
    plant: Plant;
    plantId?: number;
    location: string;
    Pertaining: string;
    criticality: string;
    email: string[] | string;
    image: string[];
    video: string[];
    desc: string;
    cause: string;
    actionTaken: string;
    detailDesc: string;
    expectedResolution: string;
    expectedResolutionTime: string;
    enabled: boolean;
    createdAt: string;
    updatedTimestamp?: string | Date;
}

interface SpocDetail {
  id?: number;
  name: string;
  spocEmail: string;
  contactNumber: string;
}

interface NewCrisisData {
  plantId: number | null;
  criticality: string | null;
  Pertaining: string | null;
  email: string | null;
  location: string | null;
  desc: string | null;
  detailDesc: string | null;
  cause: string | null;
  actionTaken: string | null;
  expectedResolution: string | null;
  expectedResolutionTime: string | null;
  date: string | null;
  time: string | null;
  spoc: SpocData[];
  imageFiles: File[];
  videoFiles: File[];
}

interface CreateCrisisPayload {
    id: number;
    date: string;
    time: string;
    plantId: number;
    submittedByAdminId: number;
    location: string;
    email: string[];
    spoc: { name: string; spocEmail: string; contactNumber: string; }[];
    image: string[];
    video: string[];
    criticality: string;
    Pertaining: string;
    desc: string;
    detailDesc: string | null;
    cause: string | null;
    actionTaken: string | null;
    expectedResolution: string | null;
    expectedResolutionTime: string | null;
    uploadFlag: number;
    createdBy: number;
}


@Component({
  selector: 'app-crisis-management',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    PaginationComponent,
    NgbDropdownModule,
    ToastMessageComponent,
    OffcanvasComponent,
    TabComponent,
    Carousel,
    VideoPlayer
  ],
  templateUrl: './crisis-management.component.html',
  styleUrls: ['./crisis-management.component.scss']
})
export class CrisisManagementComponent implements OnInit, AfterViewInit, OnDestroy {
  public componentRoles = ROLES;

  @ViewChild('createForm') createForm?: NgForm;
  @ViewChild('filterForm') filterForm?: NgForm;
  @ViewChild('spocDetailsModal') spocModalRef!: ElementRef;
  @ViewChild('mediaPreviewModal') mediaPreviewModalRef!: ElementRef;
  @ViewChild(ToastMessageComponent) toast!: ToastMessageComponent;

  crisisList: Crisis[] = [];
  listLoading: boolean = false;
  currentPage = 1;
  itemsPerPage = 10;
  totalItems = 0;
  isDownloadingExcel = false;
  downloadType: 'current' | 'all' | null = null;

  isFilterModalOpen = false;
  isCreateModalOpen = false;

  selectedSpocData: SpocDetail[] | null = null;
  spocLoading: boolean = false;
  spocError: string | null = null;
  private spocBootstrapModal: Modal | null = null;

  // Media preview properties
  mediaPreviewType: 'image' | 'video' = 'image';
  mediaPreviewUrls: string[] = [];
  mediaPreviewTitle: string = '';
  selectedVideoUrl: string | null = null;
  private mediaPreviewModal: Modal | null = null;

  filters: CrisisFilter = {
    shortDescription: null,
    plantId: null,
    submittedById: null,
    enabled: 'true',
    sortDirection: 'DESC',
    sortField: 'id'
  };

  currentUserRole: string = '';
  loggedInAdminId: number | null = null;
  loggedInPlantIds: number[] = [];

  availablePlants: Plant[] = [];
  availableSubmitters: AdminUser[] = []; // This will be filtered based on selected plant in filter
  criticalityOptions: SimpleOption[] = [ { value: 'High', label: 'High' }, { value: 'Medium', label: 'Medium' }, { value: 'Low', label: 'Low' } ];
  pertainingOptions: SimpleOption[] = [ { value: 'Employee', label: 'Employee' }, { value: 'Contractor', label: 'Contractor' }, { value: 'Other', label: 'Other' } ];
  availableSortFields = [ { value: 'id', label: 'ID' }, { value: 'createdAt', label: 'Submission Date' }, { value: 'criticality', label: 'Criticality' }, ];

  createLoading = false;
  newCrisisData: NewCrisisData = this.getInitialNewCrisisData();

  private crisisService = inject(CrisisManagementService);
  private plantService = inject(PlantManagementService);
  private adminService = inject(AdminService);
  private updateService = inject(UpdateService);
  private uploadService = inject(UploadService);
  private cdr = inject(ChangeDetectorRef);

  constructor() { }

  ngOnInit(): void {
    this.setCurrentUserRoleAndDetailsById();
    this.getPlants(); // Load plants based on role
    this.loadAdmins(); // Load initial admin list (will be filtered later)
    this.loadCrisisData(this.currentPage);
  }

  ngAfterViewInit(): void {
    // Initialize SPOC modal
    if (this.spocModalRef?.nativeElement) {
      this.spocBootstrapModal = new Modal(this.spocModalRef.nativeElement);
    } else {
      console.error("SPOC Modal element '#spocDetailsModal' not found in the template!");
    }

    // Initialize media preview modal
    if (this.mediaPreviewModalRef?.nativeElement) {
      this.mediaPreviewModal = new Modal(this.mediaPreviewModalRef.nativeElement);
    } else {
      console.error("Media Preview Modal element '#mediaPreviewModal' not found in the template!");
    }
  }

  ngOnDestroy(): void {
    this.spocBootstrapModal?.dispose();
    this.mediaPreviewModal?.dispose();
  }

   private setCurrentUserRoleAndDetailsById(): void {
        try {
            const userString = localStorage.getItem('user');
            if (!userString || userString.trim() === "" || ['null', 'undefined', '""', '" "'].includes(userString.trim().toLowerCase())) {
                this.currentUserRole = ''; this.loggedInAdminId = null; this.loggedInPlantIds = [];
                this.toast?.showErrorToast("User session invalid."); return;
            }
            const currentUser = JSON.parse(userString);
            this.loggedInAdminId = currentUser?.id ?? null;
            this.loggedInPlantIds = (Array.isArray(currentUser?.plantIds) && currentUser.plantIds.length > 0)
                ? currentUser.plantIds.map((id: any) => Number(id)).filter((id: number) => !isNaN(id)) : [];
            const roleId = currentUser?.adminsRoleId;
            if (roleId === 1 || roleId === 6) { this.currentUserRole = this.componentRoles.SUPER_ADMIN; }
            else if (roleId === 2) {
                this.currentUserRole = this.componentRoles.PLANT_ADMIN;
                if (this.loggedInPlantIds.length === 0) { this.toast?.showErrorToast("Plant Admin has no assigned plants."); }
            } else { this.currentUserRole = ''; this.toast?.showErrorToast("Invalid user role."); }
            console.log(`Role: ${this.currentUserRole}, UserID: ${this.loggedInAdminId}, Plants: [${this.loggedInPlantIds.join(', ')}]`);
        } catch (error) {
            console.error("Error parsing user data:", error);
            this.currentUserRole = ''; this.loggedInAdminId = null; this.loggedInPlantIds = [];
            this.toast?.showErrorToast("Error reading user session.");
        }
    }

  getInitialNewCrisisData(): NewCrisisData {
      return {
          plantId: null, criticality: null, Pertaining: null, email: null,
          location: null, desc: null, detailDesc: null, cause: null, actionTaken: null,
          expectedResolution: null, expectedResolutionTime: null, date: null, time: null,
          spoc: [{ name: null, spocEmail: null, contactNumber: null }],
          imageFiles: [], videoFiles: []
      };
  }

  getCurrentListData(): Crisis[] | undefined {
    return this.crisisList;
  }

    async fetchAllFilteredCrisisData(): Promise<Crisis[] | null> {
        const filterParams: string[] = [];

        if (this.currentUserRole === this.componentRoles.PLANT_ADMIN) {
            if (this.loggedInPlantIds.length > 0) {
                // Always use eq for selected plant, only use $in when showing all plants
                if (this.filters.plantId) {
                    filterParams.push(`plantId||eq||${this.filters.plantId}`);
                } else {
                    filterParams.push(`plantId||$in||${this.loggedInPlantIds.join(',')}`);
                }
            } else { filterParams.push(`plantId||eq||-1`); }
        } else if (this.currentUserRole === this.componentRoles.SUPER_ADMIN) {
            if (this.filters.plantId) { filterParams.push(`plantId||eq||${this.filters.plantId}`); }
        }

        if (this.filters.enabled !== null && this.filters.enabled !== '') filterParams.push(`enabled||eq||${this.filters.enabled}`);
        if (this.filters.shortDescription) filterParams.push(`desc||$contL||${this.filters.shortDescription}`);
        // **Filter by submitter ID needs to consider plant access if Plant Admin is filtering**
        if (this.filters.submittedById) {
             // If Plant Admin, ensure the submitter belongs to one of their plants? (Backend might handle this implicitly if joins are right)
             // For now, just add the filter. Revisit if incorrect results appear.
            filterParams.push(`submittedByAdminId||eq||${this.filters.submittedById}`);
        }


        const data = {
            limit: 10000,
            sort: `${this.filters.sortField || 'id'},${this.filters.sortDirection || 'DESC'}`,
            filter: filterParams,
            join: ['plant', 'submittedByAdmin']
        };

        this.listLoading = true;
        try {
            const param = createAxiosConfig(data);
            const response = await this.crisisService.getCrises(param);
            return response ?? [];
        } catch (error: any) {
            console.error("Error fetching all crisis data for download:", error);
            this.toast?.showErrorToast(error?.response?.data?.message || "Failed to retrieve full data for download.");
            return null;
        } finally {
             this.listLoading = false;
        }
    }

  async downloadExcel(type: 'current' | 'all') {
    if (this.isDownloadingExcel) return;

    this.isDownloadingExcel = true;
    this.downloadType = type;
    this.toast?.showSuccessToast(`Preparing download for ${type === 'current' ? 'current page' : 'all filtered'} crisis data...`);

    let dataToExport: Crisis[] | null = null;

    try {
        if (type === 'all') { dataToExport = await this.fetchAllFilteredCrisisData(); }
        else { dataToExport = this.getCurrentListData() ?? null; if (dataToExport === undefined) { dataToExport = null; } }

        if (dataToExport === null) { this.toast?.showErrorToast("Failed to retrieve data for download."); return; }
        if (!dataToExport || dataToExport.length === 0) { this.toast?.showErrorToast(`No crisis data available to download.`); return; }

        console.log(`Fetched ${dataToExport.length} crisis records for Excel export (${type}).`);

        const dataForExcel = dataToExport.map(item => ({
            'Crisis ID': item.id, 'Unique ID': item.uniqueId, 'Status': item.enabled ? 'Active' : 'Inactive',
            'Submitted By': `${item.submittedByAdmin?.firstName ?? ''} ${item.submittedByAdmin?.lastName ?? ''}`.trim(),
            'Plant': item.plant?.name ?? 'N/A', 'Location': item.location, 'Pertaining': item.Pertaining, 'Criticality': item.criticality,
            'Email(s)': Array.isArray(item.email) ? item.email.join(', ') : item.email, 'Short Description': item.desc, 'Cause': item.cause,
            'Action Taken': item.actionTaken, 'Detail Description': item.detailDesc, 'Expected Resolution': item.expectedResolution, 'Expected Resolution Time': item.expectedResolutionTime,
            'Image Count': item.image?.length ?? 0, 'Video Count': item.video?.length ?? 0,
            'Created At': item.createdAt ? new Date(item.createdAt).toLocaleString() : '',
        }));

        const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(dataForExcel);
        const wb: XLSX.WorkBook = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(wb, ws, 'CrisisData');

        const dateStr = new Date().toISOString().slice(0, 10);
        const typeStr = type === 'current' ? `Page${this.currentPage}` : 'All';
        const fileName = `CrisisData_${typeStr}_${dateStr}.xlsx`;

        XLSX.writeFile(wb, fileName);
        this.toast?.showSuccessToast(`Excel file download started (${type}).`);

    } catch (error) {
        console.error(`Error generating Excel file (${type}):`, error);
        this.toast?.showErrorToast(`An error occurred while generating the Excel file (${type}).`);
    } finally {
        this.isDownloadingExcel = false; this.downloadType = null;
    }
  }

  isArray(value: any): boolean {
      return Array.isArray(value);
  }

  async loadAdmins(plantId?: number | null) { // Accept optional plantId
    const filters = ['enabled||eq||true'];
    // If a plantId is provided (or selected in the filter), filter admins by that plant
    const targetPlantId = plantId ?? this.filters.plantId;
    if (targetPlantId) {
        filters.push(`plant.id||$eq||${targetPlantId}`); // Filter admins associated with the plant
    }
    // If Plant Admin is logged in and no specific plant is selected in filter,
    // load admins from *all* their assigned plants for the filter dropdown.
    else if (this.currentUserRole === this.componentRoles.PLANT_ADMIN && this.loggedInPlantIds.length > 0) {
         filters.push(`plant.id||$in||${this.loggedInPlantIds.join(',')}`);
    }
    // Super Admin with no plant selected in filter sees all active admins.

    const data = { sort: 'firstName,ASC', filter: filters, limit: 1000 }; // Sort by name
    const param = createAxiosConfig(data);
    console.log("Loading Admins with filters:", filters);
    try {
        const response = await this.adminService.getAdmin(param);
        this.availableSubmitters = response?.data ?? response ?? [];
        console.log(`Loaded ${this.availableSubmitters.length} submitters.`);
    } catch(error) {
        console.error("Error fetching admins:", error);
        this.availableSubmitters = [];
        this.toast?.showErrorToast("Failed to load submitter list.");
    }
  }

  async getPlants() {
    const data = { sort: 'name,ASC', filter: ['enabled||eq||true'], limit: 1000 };
    const param = createAxiosConfig(data);
    let allEnabledPlants: Plant[] = [];
     try {
        const response = await this.plantService.getPlants(param);
        allEnabledPlants = response?.data ?? response ?? [];
    } catch(error) {
        console.error("Error fetching plants:", error);
        this.availablePlants = [];
        this.toast?.showErrorToast("Failed to load plants list.");
        return;
    }

    if (this.currentUserRole === this.componentRoles.PLANT_ADMIN && this.loggedInPlantIds.length > 0) {
        this.availablePlants = allEnabledPlants.filter(plant => this.loggedInPlantIds.includes(plant.id));
    } else if (this.currentUserRole === this.componentRoles.SUPER_ADMIN) {
        this.availablePlants = allEnabledPlants;
    } else { this.availablePlants = []; }
    console.log(`Loaded ${this.availablePlants.length} plants accessible to current user.`);
  }

  // --- Filter Modal Methods (Offcanvas) ---
  openFilterModal(): void {
      this.isFilterModalOpen = true;
      // Reload admins based on the currently selected plant filter (if any)
      // Or based on Plant Admin's plants if no specific plant is selected
      this.loadAdmins(this.filters.plantId);
  }
  closeFilterModal(): void { this.isFilterModalOpen = false; }

  applyFilters(): void {
    // Check if form is valid before applying filters
    if (this.filterForm && this.filterForm.invalid) {
      this.toast?.showErrorToast("Please correct the validation errors before applying filters.");
      return;
    }

    // Trim string values to prevent whitespace-only searches
    if (this.filters.shortDescription) {
      this.trimInputField(this.filters, 'shortDescription');
    }
    if (this.filters.firstName) {
      this.trimInputField(this.filters, 'firstName');
    }
    if (this.filters.lastName) {
      this.trimInputField(this.filters, 'lastName');
    }

    this.currentPage = 1;
    this.loadCrisisData(this.currentPage);
    this.closeFilterModal();
  }

  resetFilters(): void {
    this.filters = {
      shortDescription: null, plantId: null, submittedById: null,
      enabled: 'true', sortDirection: 'DESC', sortField: 'id',
      firstName: null, lastName: null
    };
    // Reload admins for the default view (Super Admin sees all, Plant Admin sees theirs)
    this.loadAdmins();
    this.currentPage = 1;
    this.loadCrisisData(this.currentPage);
    this.closeFilterModal();
  }

  // --- Method called when plant selection changes IN THE FILTER modal ---
  onFilterPlantChange(plantId: number | null): void {
      this.filters.plantId = plantId; // Explicitly set plantId
      this.filters.submittedById = null; // Reset submitter when plant changes
      this.loadAdmins(plantId); // Load admins specific to the selected plant
  }


  openCreateModal(): void {
      if (this.currentUserRole === this.componentRoles.PLANT_ADMIN && this.loggedInPlantIds.length === 0) {
          this.toast?.showErrorToast("Cannot create crisis: You are not assigned to any plants."); return;
      }
      this.newCrisisData = this.getInitialNewCrisisData();
       if (this.currentUserRole === this.componentRoles.PLANT_ADMIN && this.availablePlants.length === 1) {
             this.newCrisisData.plantId = this.availablePlants[0].id;
       }
      setTimeout(() => { this.createForm?.resetForm(this.getInitialNewCrisisData()); }, 0);
      this.isCreateModalOpen = true;
      this.createLoading = false;
  }

  closeCreateModal(): void { this.isCreateModalOpen = false; }

  addSpoc(): void {
      this.newCrisisData.spoc.push({ name: null, spocEmail: null, contactNumber: null });
       this.cdr.detectChanges();
  }

  removeSpoc(index: number): void {
      if (this.newCrisisData.spoc.length > 1) {
          this.newCrisisData.spoc.splice(index, 1);
           this.cdr.detectChanges();
      }
  }

  handleImageChange(event: Event): void {
      const element = event.target as HTMLInputElement;
      this.newCrisisData.imageFiles = element.files ? Array.from(element.files) : [];
      console.log('Image files selected:', this.newCrisisData.imageFiles.length);
  }

  handleVideoChange(event: Event): void {
      const element = event.target as HTMLInputElement;
      this.newCrisisData.videoFiles = element.files ? Array.from(element.files) : [];
      console.log('Video files selected:', this.newCrisisData.videoFiles.length);
  }

  // Properties for file upload progress tracking
  uploadProgress: number = 0;
  uploadedFiles: number = 0;
  totalFiles: number = 0;
  uploadDialogVisible: boolean = false;
  imageUrl: string[] = [];
  videoUrl: string[] = [];

  // Method to update upload progress
  updateUploadProgress(): void {
    if (this.totalFiles > 0) {
      this.uploadProgress = Math.round((this.uploadedFiles / this.totalFiles) * 100);
    }
  }

  async submitCreateForm(): Promise<void> {
    if (this.createForm?.invalid) {
        Object.values(this.createForm.controls).forEach(control => { control.markAsDirty(); control.markAsTouched(); });
        this.toast?.showErrorToast('Please fill all required fields correctly.'); return;
    }

    // Trim string values before submission
    if (this.newCrisisData.location) {
      this.trimInputField(this.newCrisisData, 'location');
    }
    if (this.newCrisisData.desc) {
      this.trimInputField(this.newCrisisData, 'desc');
    }
    if (this.newCrisisData.detailDesc) {
      this.trimInputField(this.newCrisisData, 'detailDesc');
    }
    if (this.newCrisisData.cause) {
      this.trimInputField(this.newCrisisData, 'cause');
    }
    if (this.newCrisisData.actionTaken) {
      this.trimInputField(this.newCrisisData, 'actionTaken');
    }
    if (this.newCrisisData.expectedResolution) {
      this.trimInputField(this.newCrisisData, 'expectedResolution');
    }
    if (this.newCrisisData.expectedResolutionTime) {
      this.trimInputField(this.newCrisisData, 'expectedResolutionTime');
    }
    if (this.newCrisisData.email) {
      this.trimInputField(this.newCrisisData, 'email');
    }

    // Trim SPOC data
    this.newCrisisData.spoc.forEach(spoc => {
      if (spoc.name) {
        this.trimInputField(spoc, 'name');
      }
      if (spoc.spocEmail) {
        this.trimInputField(spoc, 'spocEmail');
      }
      if (spoc.contactNumber) {
        this.trimInputField(spoc, 'contactNumber');
      }
    });

    this.createLoading = true;

    const selectedPlantId = this.newCrisisData.plantId;
    if (!selectedPlantId) { this.toast?.showErrorToast("Plant is required."); this.createLoading = false; return; }

    const validSpocs = this.newCrisisData.spoc
      .filter(s => s.name && s.spocEmail && s.contactNumber)
      .map(s => ({ name: s.name!, spocEmail: s.spocEmail!, contactNumber: s.contactNumber! }));

    try {
        // Convert UTC date and time to IST
        let formattedDate = '';
        let formattedTime = '';

        try {
            if (this.newCrisisData.date) {
                // Create a proper date object with the selected date
                const dateObj = new Date(this.newCrisisData.date);
                if (isNaN(dateObj.getTime())) {
                    throw new Error("Invalid date format");
                }

                // Convert to IST
                const istDate = this.crisisService.convertUTCtoIST(dateObj);
                formattedDate = this.crisisService.formatDate(istDate.toISOString().split('T')[0]);
                console.log("Formatted date:", formattedDate);
            } else {
                throw new Error("Date required");
            }
        } catch (error) {
            console.error("Error processing date:", error);
            this.toast?.showErrorToast("Invalid date format. Please select a valid date.");
            this.createLoading = false;
            return;
        }

        try {
            if (this.newCrisisData.time) {
                // For time, we need to handle it differently since it's just a time string
                // First, extract hours and minutes from the time string (format: HH:MM)
                const timeMatch = this.newCrisisData.time.match(/^(\d{1,2}):(\d{1,2})$/);
                if (!timeMatch) {
                    throw new Error("Invalid time format");
                }

                const hours = parseInt(timeMatch[1], 10);
                const minutes = parseInt(timeMatch[2], 10);

                // Validate hours and minutes
                if (hours < 0 || hours > 23 || minutes < 0 || minutes > 59) {
                    throw new Error("Invalid time values");
                }

                // Create a date object with today's date and the specified time
                const timeDate = new Date();
                timeDate.setHours(hours);
                timeDate.setMinutes(minutes);
                timeDate.setSeconds(0);

                // Convert to IST
                const istTime = this.crisisService.convertUTCtoIST(timeDate);

                // Format as HH:MM:SS
                formattedTime = istTime.toTimeString().split(' ')[0];
                console.log("Formatted time:", formattedTime);
            } else {
                throw new Error("Time required");
            }
        } catch (error) {
            console.error("Error processing time:", error);
            this.toast?.showErrorToast("Invalid time format. Please enter a valid time.");
            this.createLoading = false;
            return;
        }

        const emailArray = this.newCrisisData.email ? this.newCrisisData.email.split(',').map(e => e.trim()).filter(e => e !== '') : [];

        if (!this.newCrisisData.criticality || !this.newCrisisData.Pertaining || !this.newCrisisData.location || !this.newCrisisData.desc) {
            this.toast?.showErrorToast('Internal error: Missing required data for submission.');
            this.createLoading = false; return;
        }

        if (!this.loggedInAdminId) {
            this.toast?.showErrorToast('Authentication error: Cannot identify user.');
            this.createLoading = false; return;
        }

        // Reset upload tracking variables
        this.uploadProgress = 0;
        this.uploadedFiles = 0;
        this.imageUrl = [];
        this.videoUrl = [];

        // Calculate total files to upload
        const totalFiles = (this.newCrisisData.imageFiles ? this.newCrisisData.imageFiles.length : 0) +
                          (this.newCrisisData.videoFiles ? this.newCrisisData.videoFiles.length : 0);
        this.totalFiles = totalFiles;

        // Determine upload flag
        let uploadFlag = 1; // Default: no files to upload
        if (this.newCrisisData.imageFiles.length > 0 || this.newCrisisData.videoFiles.length > 0) {
            // Show the dialog with the progress bar
            this.uploadDialogVisible = true;
            uploadFlag = 0; // Files will be uploaded
        }

        const payload: CreateCrisisPayload = {
            id: 0, date: formattedDate, time: formattedTime, plantId: this.newCrisisData.plantId!,
            submittedByAdminId: this.loggedInAdminId, location: this.newCrisisData.location!, email: emailArray,
            spoc: validSpocs, image: [], video: [], criticality: this.newCrisisData.criticality!,
            Pertaining: this.newCrisisData.Pertaining!, desc: this.newCrisisData.desc!,
            detailDesc: this.newCrisisData.detailDesc || null, cause: this.newCrisisData.cause || null,
            actionTaken: this.newCrisisData.actionTaken || null, expectedResolution: this.newCrisisData.expectedResolution || null,
            expectedResolutionTime: this.newCrisisData.expectedResolutionTime || null, uploadFlag: uploadFlag, createdBy: this.loggedInAdminId
        };

        console.log("Submitting Payload:", JSON.stringify(payload, null, 2));

        // Save the form data using createCrisis
        const res = await this.crisisService.createCrisis(payload);

        if (res && res.responseCode == 200) {
            console.log('Crisis creation successful', res);

            // Upload images if there are any
            if (this.newCrisisData.imageFiles.length > 0) {
                for (let i = 0; i < this.newCrisisData.imageFiles.length; i++) {
                    const file = this.newCrisisData.imageFiles[i];
                    const formData = new FormData();
                    formData.append('file', file);

                    try {
                        const uploadRes = await this.uploadService.upload(formData);
                        this.imageUrl.push(uploadRes);
                        this.uploadedFiles++;
                        this.updateUploadProgress();
                    } catch (uploadError) {
                        console.error('Error uploading image:', uploadError);
                    }
                }
            }

            // Upload videos if there are any
            if (this.newCrisisData.videoFiles.length > 0) {
                for (let i = 0; i < this.newCrisisData.videoFiles.length; i++) {
                    const file = this.newCrisisData.videoFiles[i];
                    const formData = new FormData();
                    formData.append('file', file);

                    try {
                        const uploadRes = await this.uploadService.upload(formData);
                        this.videoUrl.push(uploadRes);
                        this.uploadedFiles++;
                        this.updateUploadProgress();
                    } catch (uploadError) {
                        console.error('Error uploading video:', uploadError);
                    }
                }
            }

            // Update the crisis report with image and video URLs if any were uploaded
            if (this.imageUrl.length > 0 || this.videoUrl.length > 0) {
                const updateData = {
                    tableName: 'crisis-report',
                    id: res.data.id,
                    data: {
                        image: this.imageUrl,
                        video: this.videoUrl
                    }
                };

                try {
                    const updateResponse = await this.updateService.update(updateData);
                    console.log('Update response:', JSON.stringify(updateResponse));

                    // Send crisis email notification
                    await this.crisisService.sendCrisisEmail({ id: res.data.id });
                } catch (updateError) {
                    console.error('Error updating crisis with media:', updateError);
                }
            }

            // Hide the upload dialog
            this.uploadDialogVisible = false;

            // Show success message and close modal
            this.toast?.showSuccessToast('Crisis reported successfully!');
            this.closeCreateModal();
            this.currentPage = 1;
            this.loadCrisisData(this.currentPage);
        } else if (res && res.responseCode == 400) {
            this.toast?.showErrorToast(res.message || 'This month data present in database');
        } else {
            this.toast?.showErrorToast('Failed to create crisis report');
        }
    } catch (error: any) {
        console.error("Error creating crisis:", error);
        this.toast?.showErrorToast(error?.response?.data?.message || 'Failed to create crisis.');
        this.uploadDialogVisible = false;
    } finally {
        this.createLoading = false;
        this.cdr.detectChanges();
    }
}

  async loadCrisisData(page: number) {
    this.listLoading = true;
    this.crisisList = [];

    const filterParams: string[] = [];

    // --- Apply Role-Based Plant Filtering ---
    if (this.currentUserRole === this.componentRoles.PLANT_ADMIN) {
        if (this.loggedInPlantIds.length > 0) {
            if (this.filters.plantId !== null && this.filters.plantId !== undefined) {
                // Plant Admin selected a specific plant from their assigned list
                // Assuming UI prevents selecting unassigned plants, directly filter by the selected plant ID
                filterParams.push(`plantId||eq||${this.filters.plantId}`);
                console.log("Plant Admin: Filtering crisis data by selected plant ID:", this.filters.plantId);
            } else {
                // Plant Admin selected "All My Plants" (or default) - filter by all their assigned plants
                filterParams.push(`plantId||$in||${this.loggedInPlantIds.join(',')}`);
                console.log("Plant Admin: Filtering crisis data by assigned plant IDs:", this.loggedInPlantIds);
            }
        } else {
            // Plant Admin has no plants assigned - apply a filter that returns no data
            console.warn("Plant Admin has no assigned plants. Applying impossible filter.");
            filterParams.push(`plantId||eq||-1`);
        }
    } else if (this.currentUserRole === this.componentRoles.SUPER_ADMIN) {
        // Super Admin: Only filter if they explicitly selected a plant
        if (this.filters.plantId !== null && this.filters.plantId !== undefined) {
            filterParams.push(`plantId||eq||${this.filters.plantId}`);
            console.log("Super Admin: Filtering crisis data by selected plant ID:", this.filters.plantId);
        } else {
             console.log("Super Admin: No specific plant selected, showing all.");
             // No plant filter added
        }
    } else {
        // No role or unknown role - potentially restrict or show all? Showing all for now.
        console.warn("Unknown user role, not applying specific plant filters.");
        // No plant filter added by default
    }
    // --- End Role-Based Plant Filtering ---

    if (this.filters.enabled !== null && this.filters.enabled !== '') filterParams.push(`enabled||eq||${this.filters.enabled}`);
    if (this.filters.shortDescription) filterParams.push(`desc||$contL||${this.filters.shortDescription}`);
    if (this.filters.submittedById) filterParams.push(`submittedByAdminId||eq||${this.filters.submittedById}`);
    if (this.filters.firstName) filterParams.push(`submittedByAdmin.firstName||$contL||${this.filters.firstName}`);
    if (this.filters.lastName) filterParams.push(`submittedByAdmin.lastName||$contL||${this.filters.lastName}`);

    const data = {
      page: page, limit: this.itemsPerPage,
      sort: `${this.filters.sortField || 'id'},${this.filters.sortDirection || 'DESC'}`,
      filter: filterParams,
      join: ['plant', 'submittedByAdmin']
    };

    try {
      const param = createAxiosConfig(data);
      console.log("Crisis Management Filter Params:", filterParams);
      const response = await this.crisisService.getCrises(param);
      this.crisisList = response.data ?? [];
      this.totalItems = response.total ?? 0;
    } catch (error: any) {
      console.error("Error fetching crisis data:", error);
      this.crisisList = []; this.totalItems = 0;
      this.toast?.showErrorToast(error?.response?.data?.message || 'Failed to load crisis data.');
    } finally {
      this.listLoading = false;
    }
  }

  onPageChange(page: number) {
    if(this.currentPage === page) return;
    this.currentPage = page;
    this.loadCrisisData(this.currentPage);
  }

  getSortClass(key: string): string {
     if (this.filters.sortField === key) { return this.filters.sortDirection === 'ASC' ? 'sort-asc' : 'sort-desc'; } return '';
  }
  sortBy(field: string) {
     if (this.filters.sortField === field) { this.filters.sortDirection = this.filters.sortDirection === 'ASC' ? 'DESC' : 'ASC'; }
     else { this.filters.sortField = field; this.filters.sortDirection = 'DESC'; }
     this.currentPage = 1; this.loadCrisisData(this.currentPage);
  }

  // --- Helper to trim input fields ---
  trimInputField(formData: any, fieldName: string): void {
    if (formData && formData[fieldName] && typeof formData[fieldName] === 'string') {
      formData[fieldName] = formData[fieldName].trim();
    }
  }

  openImageDialog(images: string[]) {
    if (!images || images.length === 0) {
      this.toast?.showErrorToast('No images available to display.');
      return;
    }

    this.mediaPreviewType = 'image';
    this.mediaPreviewUrls = images;
    this.mediaPreviewTitle = 'Crisis Images';

    if (this.mediaPreviewModal) {
      this.mediaPreviewModal.show();
    } else {
      console.error("Media preview modal not initialized");
      this.toast?.showErrorToast('Could not open image preview.');
    }
  }

  openVideoDialog(videos: string[]) {
    if (!videos || videos.length === 0) {
      this.toast?.showErrorToast('No videos available to display.');
      return;
    }

    this.mediaPreviewType = 'video';
    this.mediaPreviewUrls = videos;
    this.mediaPreviewTitle = 'Crisis Videos';

    // Select the first video by default
    if (videos.length > 0) {
      this.selectedVideoUrl = videos[0];
    }

    if (this.mediaPreviewModal) {
      this.mediaPreviewModal.show();
    } else {
      console.error("Media preview modal not initialized");
      this.toast?.showErrorToast('Could not open video preview.');
    }
  }

  closeMediaPreviewModal(): void {
    if (this.mediaPreviewModal) {
      this.mediaPreviewModal.hide();
      this.mediaPreviewUrls = [];
      this.selectedVideoUrl = null;
      this.cdr.detectChanges();
    }
  }

  selectVideo(videoUrl: string): void {
    this.selectedVideoUrl = videoUrl;
    this.cdr.detectChanges();
  }

  async handleSpocData(uniqueId: string) {
    // Role check for SPOC details? Assume allowed for now.
    console.log('Fetching SPOC Data for uniqueId:', uniqueId);
    this.spocLoading = true; this.selectedSpocData = null; this.spocError = null;

    if (this.spocBootstrapModal) { this.spocBootstrapModal.show(); }
    else { console.error("Cannot show SPOC modal, instance not initialized."); this.spocLoading = false; return; }

    try {
      const data = { filter: [`uniqueId||eq||${uniqueId}`] };
      const param = createAxiosConfig(data);
      const response = await this.crisisService.getSpokeDetail(param);
      this.selectedSpocData = response ?? [];
      if (!this.selectedSpocData || this.selectedSpocData.length === 0) { console.warn(`No SPOC data returned for uniqueId: ${uniqueId}`); }
    } catch (error) {
      console.error(`Error fetching SPOC details for uniqueId ${uniqueId}:`, error);
      this.spocError = 'Failed to load SPOC details.'; this.selectedSpocData = [];
    } finally {
      this.spocLoading = false; this.cdr.detectChanges();
    }
  }

  closeSpocModal(): void {
    if (this.spocBootstrapModal) {
       this.spocBootstrapModal.hide();
       this.selectedSpocData = null; this.spocLoading = false; this.spocError = null;
       this.cdr.detectChanges();
    }
  }
}