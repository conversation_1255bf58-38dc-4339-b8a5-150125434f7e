<app-toast-message></app-toast-message>

<!-- Upload Progress Dialog -->
<div class="modal-backdrop fade show" *ngIf="uploadDialogVisible"></div>
<div class="modal fade" [class.show]="uploadDialogVisible" [style.display]="uploadDialogVisible ? 'block' : 'none'" tabindex="-1" role="dialog" aria-labelledby="uploadProgressModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="uploadProgressModalLabel">Uploading Files</h5>
      </div>
      <div class="modal-body">
        <p>Uploading media files. Please wait...</p>
        <div class="progress">
          <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" [style.width.%]="uploadProgress" aria-valuenow="uploadProgress" aria-valuemin="0" aria-valuemax="100">{{uploadProgress}}%</div>
        </div>
        <p class="mt-2 text-center">{{uploadedFiles}} of {{totalFiles}} files uploaded</p>
      </div>
    </div>
  </div>
</div>
<!-- End Upload Progress Dialog -->

<div class="app-container">
  <div class="card custom-card">
    <div class="card-header">
      <div class="row align-items-center">
        <div class="col">
          <h6 class="mb-0">Crisis Management</h6>
        </div>
        <div class="col text-end d-flex align-items-center justify-content-end">
          <!-- Add Crisis Button (Uses Offcanvas) -->
          <button class="btn-sm adani-btn me-2" (click)="openCreateModal()" title="Report New Crisis">
            <i class="bi bi-plus-circle"></i> Add Crisis
          </button>
          <!-- Download Button (Optional) -->
          <!-- *** REPLACE existing simple Download button with this Dropdown *** -->
          <div ngbDropdown class="d-inline-block me-2">
            <button type="button" class="btn btn-sm adani-btn dropdown-toggle" id="downloadCrisisExcelDropdown" ngbDropdownToggle
                [disabled]="isDownloadingExcel || listLoading">
                <span *ngIf="!isDownloadingExcel">
                    <i class="bi bi-file-earmark-excel me-1"></i> Download Excel
                </span>
                <span *ngIf="isDownloadingExcel">
                    <span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
                    Downloading {{ downloadType === 'current' ? 'Page' : (downloadType === 'all' ? 'All' : '') }}...
                </span>
            </button>
            <ul ngbDropdownMenu aria-labelledby="downloadCrisisExcelDropdown">
                <li>
                    <button ngbDropdownItem (click)="downloadExcel('current')" [disabled]="isDownloadingExcel || listLoading || (crisisList?.length ?? 0) === 0">
                        <i class="bi bi-download me-1"></i> Download Current Page ({{ crisisList?.length ?? 0 }})
                    </button>
                </li>
                <li>
                    <button ngbDropdownItem (click)="downloadExcel('all')" [disabled]="isDownloadingExcel || listLoading || totalItems === 0">
                        <i class="bi bi-cloud-download me-1"></i> Download All Filtered ({{ totalItems }})
                    </button>
                </li>
            </ul>
        </div>
          <!-- Filter Button (Uses Offcanvas) -->
          <img src="../../../assets/svg/filter.svg" class="filter-button ms-3" (click)="openFilterModal()" alt="Filter"
            style="width: 35px;" />
        </div>
      </div>
    </div>
    <div class="card-body">
      <div class="table-responsive">
        <table class="table table-bordered custom-table data-detail-table">
          <thead class="table-header text-center">
            <tr>
              <th scope="col">Id</th>
              <th scope="col">User Details</th>
              <th scope="col">Other Details</th>
              <th scope="col">Crisis Details</th>
              <th scope="col">Resolution Details</th>
            </tr>
          </thead>
          <tbody>
            <!-- Loading State -->
            <tr *ngIf="listLoading">
              <td colspan="5" class="text-center p-4">
                <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                Loading crisis data...
              </td>
            </tr>
            <!-- No Data State -->
            <tr *ngIf="!listLoading && (!crisisList || crisisList.length === 0)">
              <td colspan="5" class="text-center p-4 text-muted">
                No crisis data found.
              </td>
            </tr>
            <!-- Data Rows -->
            <tr *ngFor="let item of crisisList">
              <td class="text-center align-middle">{{ item.id }}</td>
              <td class="align-middle">
                <!-- User Details -->
                <div class="data-group">
                  <div class="row data-row mb-1">
                    <div class="col-5 data-label">Unique ID</div>
                    <div class="col-7 data-value">{{ item.uniqueId }}</div>
                  </div>
                  <div class="row data-row mb-1">
                    <div class="col-5 data-label">Submitted By</div>
                    <div class="col-7 data-value">
                      {{ item.submittedByAdmin?.firstName }} {{ item.submittedByAdmin?.lastName || 'N/A' }}
                    </div>
                  </div>
                  <div class="row data-row mb-1">
                    <div class="col-5 data-label">Plant</div>
                    <div class="col-7 data-value">{{ item.plant?.name || 'N/A' }}</div>
                  </div>
                  <div class="row data-row">
                    <div class="col-5 data-label">Location</div>
                    <div class="col-7 data-value">{{ item.location }}</div>
                  </div>
                </div>
              </td>
              <td class="align-middle">
                <!-- Other Details -->
                <div class="data-group">
                  <div class="row data-row mb-1">
                    <div class="col-5 data-label">Pertaining</div>
                    <div class="col-7 data-value">{{ item.Pertaining }}</div>
                  </div>
                  <div class="row data-row mb-1">
                    <div class="col-5 data-label">Criticality</div>
                    <div class="col-7 data-value">{{ item.criticality }}</div>
                  </div>
                  <div class="row data-row mb-2">
                    <div class="col-5 data-label">Email</div>
                    <div class="col-7 data-value">
                      <!-- Handle potential array or string for email display -->
                      <ng-container *ngIf="isArray(item.email)">
                        {{ item.email }}
                      </ng-container>
                      <ng-container *ngIf="!isArray(item.email)">
                        {{ item.email || 'N/A' }}
                      </ng-container>
                    </div>
                  </div>
                  <div class="row data-row mt-2">
                    <div class="col-12 d-flex gap-2 flex-wrap"> <!-- Added flex-wrap for responsiveness -->
                      <button *ngIf="item.image && item.image.length > 0" class="btn btn-outline-primary btn-sm"
                        (click)="openImageDialog(item.image)">
                        View Images
                      </button>
                      <button *ngIf="item.video && item.video.length > 0" class="btn btn-outline-primary btn-sm"
                        (click)="openVideoDialog(item.video)">
                        View Video
                      </button>
                      <!-- SPOC Button - Triggers Bootstrap Modal -->
                      <button class="btn btn-outline-info btn-sm" (click)="handleSpocData(item.uniqueId)">
                        View SPOC
                      </button>
                    </div>
                  </div>
                </div>
              </td>
              <td class="align-middle">
                <!-- Crisis Details -->
                <div class="data-group">
                  <div class="row data-row mb-1">
                    <div class="col-5 data-label">Short Desc</div>
                    <div class="col-7 data-value">{{ item.desc }}</div>
                  </div>
                  <div class="row data-row mb-1">
                    <div class="col-5 data-label">Cause</div>
                    <div class="col-7 data-value">{{ item.cause }}</div>
                  </div>
                  <div class="row data-row mb-1">
                    <div class="col-5 data-label">Action Taken</div>
                    <div class="col-7 data-value">{{ item.actionTaken }}</div>
                  </div>
                  <div class="row data-row">
                    <div class="col-5 data-label">Desc</div>
                    <div class="col-7 data-value">{{ item.detailDesc }}</div>
                  </div>
                </div>
              </td>
              <td class="align-middle">
                <!-- Resolution Details -->
                <div class="data-group">
                  <div class="row data-row mb-1">
                    <div class="col-5 data-label">Expected</div>
                    <div class="col-7 data-value">{{ item.expectedResolution }}</div>
                  </div>
                  <div class="row data-row">
                    <div class="col-5 data-label">Expected Time</div>
                    <div class="col-7 data-value">{{ item.expectedResolutionTime }}</div>
                  </div>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
    <div class="card-footer text-muted text-center">
      <!-- Pagination Component -->
      <app-pagination [currentPage]="currentPage" [totalItems]="totalItems" [itemsPerPage]="itemsPerPage"
        (pageChange)="onPageChange($event)"></app-pagination>
    </div>
  </div> <!-- /.card -->
</div> <!-- /.app-container -->

<!-- Filter Offcanvas (Existing - using app-offcanvas) -->
<app-offcanvas [title]="'Filter Crisis Data'" *ngIf="isFilterModalOpen" (onClickCross)="closeFilterModal()">
  <div class="filter-container p-3">
    <form #filterForm="ngForm" (ngSubmit)="applyFilters()">
      <!-- ... existing filter form fields ... -->
      <div class="row g-3">
        <!-- Existing filter fields -->
        <div class="col-12">
          <label class="form-label" for="filterEnabled">Enabled Status</label>
          <select id="filterEnabled" class="form-select" [(ngModel)]="filters.enabled" name="enabled">
            <option [ngValue]="null">Any</option>
            <option value="true">Yes</option>
            <option value="false">No</option>
          </select>
        </div>
        <div class="col-12">
          <label class="form-label" for="filterShortDesc">Short Description</label>
          <input type="text" id="filterShortDesc" class="form-control" placeholder="Search in Short Description"
            [(ngModel)]="filters.shortDescription" name="shortDescription" maxlength="200" #filterShortDesc="ngModel"
            pattern="^[a-zA-Z0-9\s.,!?-]*$" (blur)="trimInputField(filters, 'shortDescription')">
          <!-- Validation Message -->
          <div *ngIf="filterShortDesc.invalid && (filterShortDesc.dirty || filterShortDesc.touched)" class="text-danger small mt-1">
            <span *ngIf="filterShortDesc.errors?.['pattern']">Description should contain only alphanumeric characters and basic punctuation.</span>
          </div>
        </div>
        <div class="col-12">
          <label class="form-label" for="filterPlant">Plant</label>
          <select id="filterPlant" class="form-select" [(ngModel)]="filters.plantId" name="plantId" (change)="loadAdmins(filters.plantId)">
            <option [ngValue]="null">All Plants</option>
            <option *ngFor="let plant of availablePlants" [value]="plant.id">{{ plant.name }}</option>
          </select>
        </div>
        <div class="col-12">
          <label class="form-label" for="filterSubmittedBy">Submitted By</label>
          <select id="filterSubmittedBy" class="form-select" [(ngModel)]="filters.submittedById" name="submittedById">
            <option [ngValue]="null">All Submitters</option>
            <option *ngFor="let submitter of availableSubmitters" [value]="submitter.id">{{ submitter.firstName }} {{
              submitter.lastName }}</option>
          </select>
        </div>

        <div class="col-12">
          <label class="form-label" for="filterFirstName">First Name</label>
          <input type="text" id="filterFirstName" class="form-control" placeholder="Filter by first name"
            [(ngModel)]="filters.firstName" name="firstName" maxlength="30" pattern="^[a-zA-Z\s]*$"
            #firstName="ngModel" [ngClass]="{'is-invalid': firstName.invalid && (firstName.dirty || firstName.touched)}"
            (blur)="trimInputField(filters, 'firstName')">
          <div *ngIf="firstName.invalid && (firstName.dirty || firstName.touched)" class="invalid-feedback">
            <div *ngIf="firstName.errors?.['pattern']">First name should contain only alphabets.</div>
          </div>
        </div>

        <div class="col-12">
          <label class="form-label" for="filterLastName">Last Name</label>
          <input type="text" id="filterLastName" class="form-control" placeholder="Filter by last name"
            [(ngModel)]="filters.lastName" name="lastName" maxlength="30" pattern="^[a-zA-Z\s]*$"
            #lastName="ngModel" [ngClass]="{'is-invalid': lastName.invalid && (lastName.dirty || lastName.touched)}"
            (blur)="trimInputField(filters, 'lastName')">
          <div *ngIf="lastName.invalid && (lastName.dirty || lastName.touched)" class="invalid-feedback">
            <div *ngIf="lastName.errors?.['pattern']">Last name should contain only alphabets.</div>
          </div>
        </div>
        <div class="col-12">
          <label class="form-label" for="filterSortBy">Sort By</label>
          <select id="filterSortBy" class="form-select" [(ngModel)]="filters.sortField" name="sortField">
            <option *ngFor="let field of availableSortFields" [value]="field.value">{{ field.label }}</option>
          </select>
          <label class="form-label mt-2" for="filterSortDir">Sort Direction</label>
          <select id="filterSortDir" class="form-select" [(ngModel)]="filters.sortDirection" name="sortDirection">
            <option value="DESC">Descending</option>
            <option value="ASC">Ascending</option>
          </select>
        </div>
        <!-- Buttons -->
        <div class="col-12 mt-4 d-grid gap-2">
          <button type="submit" class="btn adani-btn"> <i class="bi bi-search me-1"></i> Search </button>
          <button type="button" class="btn btn-secondary" (click)="resetFilters()"> <i
              class="bi bi-arrow-clockwise me-1"></i> Reset Filters </button>
        </div>
      </div>
    </form>
  </div>
</app-offcanvas>

<!-- Create Crisis Offcanvas (Existing - using app-offcanvas) -->
<app-offcanvas [title]="'Add New Crisis'" [width]="'500'" *ngIf="isCreateModalOpen" (onClickCross)="closeCreateModal()">
  <div class="create-container p-3">
    <!-- Form for creating a new crisis -->
    <form #createForm="ngForm" (ngSubmit)="submitCreateForm()">
      <div class="row g-3">

        <!-- Row 1: Plant, Criticality, Pertaining -->
        <div class="col-md-4">
          <label class="form-label" for="createPlantId">Select Plant <span class="text-danger">*</span></label>
          <select id="createPlantId" class="form-select form-select-sm" [(ngModel)]="newCrisisData.plantId"
            name="plantId" required>
            <option [ngValue]="null" disabled>Select Plant</option>
            <option *ngFor="let plant of availablePlants" [value]="plant.id">{{ plant.name }}</option>
          </select>
          <!-- Validation Message -->
          <div
            *ngIf="createForm.controls['plantId']?.invalid && (createForm.controls['plantId']?.dirty || createForm.controls['plantId']?.touched)"
            class="text-danger small mt-1">
            Plant is required.
          </div>
        </div>
        <div class="col-md-4">
          <label class="form-label" for="createCriticality">Criticality <span class="text-danger">*</span></label>
          <select id="createCriticality" class="form-select form-select-sm" [(ngModel)]="newCrisisData.criticality"
            name="criticality" required>
            <option [ngValue]="null" disabled>Select Criticality</option>
            <option *ngFor="let item of criticalityOptions" [value]="item.value">{{ item.label }}</option>
          </select>
          <!-- Validation Message -->
          <div
            *ngIf="createForm.controls['criticality']?.invalid && (createForm.controls['criticality']?.dirty || createForm.controls['criticality']?.touched)"
            class="text-danger small mt-1">
            Criticality is required.
          </div>
        </div>
        <div class="col-md-4">
          <label class="form-label" for="createPertaining">Pertaining to <span class="text-danger">*</span></label>
          <select id="createPertaining" class="form-select form-select-sm" [(ngModel)]="newCrisisData.Pertaining"
            name="Pertaining" required>
            <option [ngValue]="null" disabled>Select Pertaining</option>
            <option *ngFor="let item of pertainingOptions" [value]="item.value">{{ item.label }}</option>
          </select>
          <!-- Validation Message -->
          <div
            *ngIf="createForm.controls['Pertaining']?.invalid && (createForm.controls['Pertaining']?.dirty || createForm.controls['Pertaining']?.touched)"
            class="text-danger small mt-1">
            Pertaining field is required.
          </div>
        </div>

        <!-- Row 2: Emails -->
        <div class="col-12">
          <label class="form-label" for="createEmail">Emails (comma-separated)</label>
          <textarea id="createEmail" class="form-control form-control-sm" rows="2" [(ngModel)]="newCrisisData.email"
            name="email" placeholder="Enter emails separated by commas" maxlength="200" #emailInput="ngModel"
            (blur)="trimInputField(newCrisisData, 'email')"></textarea>
          <!-- Character Count -->
          <div class="d-flex justify-content-end mt-1">
            <small class="text-muted">{{ newCrisisData.email?.length || 0 }}/200 characters</small>
          </div>
          <!-- Note: Email validation is handled in the component when processing the comma-separated list -->
        </div>

        <!-- Row 3: Location, Short Desc, Detail Desc -->
        <div class="col-md-4">
          <label class="form-label" for="createLocation">Crisis Location <span class="text-danger">*</span></label>
          <input type="text" id="createLocation" class="form-control form-control-sm"
            placeholder="Enter Crisis Location" [(ngModel)]="newCrisisData.location" name="location" required
            maxlength="200" #locationInput="ngModel" (blur)="trimInputField(newCrisisData, 'location')">
          <!-- Validation Message -->
          <div
            *ngIf="locationInput.invalid && (locationInput.dirty || locationInput.touched)"
            class="text-danger small mt-1">
            <span *ngIf="locationInput.errors?.['required']">Location is required.</span>
          </div>
        </div>
        <div class="col-md-4">
          <label class="form-label" for="createDesc">Short Description <span class="text-danger">*</span></label>
          <textarea id="createDesc" class="form-control form-control-sm" rows="1" placeholder="Short description"
            [(ngModel)]="newCrisisData.desc" name="desc" required maxlength="200" #descInput="ngModel"
            (blur)="trimInputField(newCrisisData, 'desc')"></textarea>
          <!-- Validation and Character Count -->
          <div class="d-flex justify-content-between mt-1">
            <div
              *ngIf="descInput.invalid && (descInput.dirty || descInput.touched)"
              class="text-danger small">
              <span *ngIf="descInput.errors?.['required']">Short description is required.</span>
            </div>
            <small class="text-muted ms-auto">{{ newCrisisData.desc?.length || 0 }}/200 characters</small>
          </div>
        </div>
        <div class="col-md-4">
          <label class="form-label" for="createDetailDesc">Detail Description</label>
          <textarea id="createDetailDesc" class="form-control form-control-sm" rows="1" placeholder="Detail description"
            [(ngModel)]="newCrisisData.detailDesc" name="detailDesc" maxlength="200" #detailDescInput="ngModel"
            (blur)="trimInputField(newCrisisData, 'detailDesc')"></textarea>
          <!-- Character Count -->
          <div class="d-flex justify-content-end mt-1">
            <small class="text-muted">{{ newCrisisData.detailDesc?.length || 0 }}/200 characters</small>
          </div>
        </div>

        <!-- Row 4: Cause, Action Taken, Expected Resolution -->
        <div class="col-md-4">
          <label class="form-label" for="createCause">Cause</label>
          <input type="text" id="createCause" class="form-control form-control-sm" placeholder="Enter Cause"
            [(ngModel)]="newCrisisData.cause" name="cause" maxlength="200"
            (blur)="trimInputField(newCrisisData, 'cause')">
        </div>
        <div class="col-md-4">
          <label class="form-label" for="createActionTaken">Action Taken / Underway</label>
          <input type="text" id="createActionTaken" class="form-control form-control-sm" placeholder="Action Taken"
            [(ngModel)]="newCrisisData.actionTaken" name="actionTaken" maxlength="200"
            (blur)="trimInputField(newCrisisData, 'actionTaken')">
        </div>
        <div class="col-md-4">
          <label class="form-label" for="createExpectedResolution">Expected Resolution</label>
          <input type="text" id="createExpectedResolution" class="form-control form-control-sm"
            placeholder="Expected resolution" [(ngModel)]="newCrisisData.expectedResolution" name="expectedResolution"
            maxlength="200" (blur)="trimInputField(newCrisisData, 'expectedResolution')">
        </div>

        <!-- Row 5: Expected Time, Date, Time -->
        <div class="col-md-4">
          <label class="form-label" for="createExpectedResolutionTime">Expected Resolution Time</label>
          <input type="text" id="createExpectedResolutionTime" class="form-control form-control-sm"
            placeholder="e.g., 2 hours, 1 day" [(ngModel)]="newCrisisData.expectedResolutionTime"
            name="expectedResolutionTime" maxlength="200"
            (blur)="trimInputField(newCrisisData, 'expectedResolutionTime')">
        </div>
        <div class="col-md-4">
          <label class="form-label" for="createDate">Crisis Date <span class="text-danger">*</span></label>
          <input type="date" id="createDate" class="form-control form-control-sm" [(ngModel)]="newCrisisData.date"
            name="date" required>
          <!-- Validation Message -->
          <div
            *ngIf="createForm.controls['date']?.invalid && (createForm.controls['date']?.dirty || createForm.controls['date']?.touched)"
            class="text-danger small mt-1">
            Crisis date is required.
          </div>
        </div>
        <div class="col-md-4">
          <label class="form-label" for="createTime">Crisis Time <span class="text-danger">*</span></label>
          <input type="time" id="createTime" class="form-control form-control-sm" [(ngModel)]="newCrisisData.time"
            name="time" required>
          <!-- Validation Message -->
          <div
            *ngIf="createForm.controls['time']?.invalid && (createForm.controls['time']?.dirty || createForm.controls['time']?.touched)"
            class="text-danger small mt-1">
            Crisis time is required.
          </div>
        </div>

        <!-- Row 6: File Uploads -->
        <div class="col-md-6">
          <label for="createImages" class="form-label">Upload Images (Multiple)</label>
          <input class="form-control form-control-sm" type="file" id="createImages" multiple accept="image/*"
            (change)="handleImageChange($event)">
          <!-- Display count of selected files -->
          <div *ngIf="newCrisisData.imageFiles.length > 0" class="mt-1 text-muted small">
            Selected: {{ newCrisisData.imageFiles.length }} image(s)
          </div>
        </div>
        <div class="col-md-6">
          <label for="createVideos" class="form-label">Upload Videos (Multiple)</label>
          <input class="form-control form-control-sm" type="file" id="createVideos" multiple accept="video/*"
            (change)="handleVideoChange($event)">
          <!-- Display count of selected files -->
          <div *ngIf="newCrisisData.videoFiles.length > 0" class="mt-1 text-muted small">
            Selected: {{ newCrisisData.videoFiles.length }} video(s)
          </div>
        </div>

        <!-- Row 7: SPOC Details -->
        <div class="col-12 mt-3">
          <div class="card border shadow-sm"> <!-- Added border and shadow for visual separation -->
            <div class="card-header py-2 bg-light"> <!-- Light background for header -->
              <h6 class="mb-0">SPOC Details</h6>
            </div>
            <div class="card-body pb-2"> <!-- Reduced bottom padding -->
              <!-- Loop through SPOC entries -->
              <div *ngFor="let spoc of newCrisisData.spoc; let i = index; let first = first; let last = last"
                class="spoc-entry mb-3" [class.border-bottom]="!last" [class.pb-3]="!last">
                <!-- Add border only between entries -->
                <div class="row g-2 align-items-end">
                  <div class="col-md-4">
                    <label class="form-label form-label-sm visually-hidden" [for]="'spocName' + i">Name</label>
                    <!-- Hide label visually, keep for accessibility -->
                    <input type="text" [id]="'spocName' + i" class="form-control form-control-sm"
                      placeholder="SPOC Name *" [(ngModel)]="spoc.name" [name]="'spocName' + i" required
                      pattern="^[a-zA-Z\s]*$" #spocName="ngModel"
                      (blur)="trimInputField(spoc, 'name')">
                    <!-- Validation Message -->
                    <div
                      *ngIf="spocName.invalid && (spocName.dirty || spocName.touched)"
                      class="text-danger small mt-1">
                      <span *ngIf="spocName.errors?.['required']">Name is required.</span>
                      <span *ngIf="spocName.errors?.['pattern']">Name should contain only alphabets.</span>
                    </div>
                  </div>
                  <div class="col-md-4">
                    <label class="form-label form-label-sm visually-hidden" [for]="'spocEmail' + i">Email</label>
                    <input type="email" [id]="'spocEmail' + i" class="form-control form-control-sm"
                      placeholder="SPOC Email *" [(ngModel)]="spoc.spocEmail" [name]="'spocEmail' + i" required email
                      (blur)="trimInputField(spoc, 'spocEmail')">
                    <!-- Validation Message -->
                    <div
                      *ngIf="createForm.controls['spocEmail' + i]?.invalid && (createForm.controls['spocEmail' + i]?.dirty || createForm.controls['spocEmail' + i]?.touched)"
                      class="text-danger small mt-1">
                      <span *ngIf="createForm.controls['spocEmail' + i]?.errors?.['required']">Email is required.</span>
                      <span *ngIf="createForm.controls['spocEmail' + i]?.errors?.['email']">Invalid email format.</span>
                    </div>
                  </div>
                  <div class="col-md-3">
                    <label class="form-label form-label-sm visually-hidden" [for]="'spocContact' + i">Contact</label>
                    <input type="text" [id]="'spocContact' + i" class="form-control form-control-sm"
                      placeholder="SPOC Contact *" [(ngModel)]="spoc.contactNumber" [name]="'spocContact' + i" required
                      pattern="[0-9\+\-\s()]*" maxlength="10" #spocContact="ngModel"
                      (blur)="trimInputField(spoc, 'contactNumber')"> <!-- Limit to 10 characters -->
                    <!-- Validation Message -->
                    <div
                      *ngIf="spocContact.invalid && (spocContact.dirty || spocContact.touched)"
                      class="text-danger small mt-1">
                      <span *ngIf="spocContact.errors?.['required']">Contact is required.</span>
                      <span *ngIf="spocContact.errors?.['pattern']">Invalid contact format.</span>
                    </div>
                  </div>
                  <div class="col-md-1 text-end"> <!-- Align button to the right -->
                    <!-- Remove Button -->
                    <button type="button" class="btn btn-sm btn-outline-danger" (click)="removeSpoc(i)"
                      [disabled]="newCrisisData.spoc.length <= 1" title="Remove SPOC">
                      <i class="bi bi-trash"></i>
                    </button>
                  </div>
                </div>
              </div>
              <!-- Add SPOC Button -->
              <button type="button" class="btn btn-sm btn-outline-primary mt-2" (click)="addSpoc()">
                <i class="bi bi-plus-circle me-1"></i> Add Another SPOC
              </button>
            </div>
          </div>
        </div>


        <!-- Action Buttons -->
        <div class="col-12 mt-4 d-flex justify-content-end gap-2">
          <!-- Cancel Button -->
          <button type="button" class="btn btn-secondary" (click)="closeCreateModal()">
            Cancel
          </button>
          <!-- Submit Button -->
          <button type="submit" class="btn adani-btn" [disabled]="createForm.invalid || createLoading">
            <!-- Text changes based on loading state -->
            <span *ngIf="!createLoading"><i class="bi bi-plus-circle-fill me-1"></i> Create Crisis</span>
            <!-- Loading Spinner -->
            <span *ngIf="createLoading">
              <span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
              Creating...
            </span>
          </button>
        </div>
      </div> <!-- /.row g-3 -->
    </form>
  </div> <!-- /.create-container -->
</app-offcanvas>
<!-- ********** END: Create Crisis Offcanvas ********** -->


<!-- ********** SPOC Details Bootstrap Modal ********** -->
<div class="modal fade" #spocDetailsModal id="spocDetailsModal" tabindex="-1" aria-labelledby="spocDetailsModalLabel"
  aria-hidden="true">
  <div class="modal-dialog modal-lg modal-dialog-centered"> <!-- Centered and Large -->
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="spocDetailsModalLabel">SPOC Details</h5>
        <!-- Header Close Button -->
        <button type="button" class="btn-close" aria-label="Close" (click)="closeSpocModal()"></button>
      </div>
      <div class="modal-body">

        <!-- Loading State -->
        <div *ngIf="spocLoading" class="text-center p-5"> <!-- Increased padding -->
          <div class="spinner-border text-primary" role="status"> <!-- Larger spinner -->
            <span class="visually-hidden">Loading...</span>
          </div>
          <p class="mt-2 mb-0">Loading SPOC details...</p>
        </div>

        <!-- Error State -->
        <div *ngIf="!spocLoading && spocError" class="alert alert-danger d-flex align-items-center" role="alert">
          <!-- Changed to alert-danger -->
          <i class="bi bi-x-octagon-fill me-2"></i>
          <div> {{ spocError }} </div>
        </div>

        <!-- Data Table -->
        <div *ngIf="!spocLoading && !spocError && selectedSpocData && selectedSpocData.length > 0"
          class="table-responsive">
          <table class="table table-sm table-bordered table-striped custom-table hover-table">
            <!-- Added hover-table class (define in CSS if needed) -->
            <thead class="table-header text-center"> <!-- Your existing header class -->
              <tr>
                <th scope="col">#</th>
                <th scope="col">Name</th>
                <th scope="col">Email</th>
                <th scope="col">Contact Number</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let spoc of selectedSpocData; let i = index">
                <td class="text-center align-middle">{{ i + 1 }}</td>
                <td class="align-middle">{{ spoc.name || 'N/A' }}</td>
                <td class="align-middle">{{ spoc.spocEmail || 'N/A' }}</td>
                <td class="align-middle">{{ spoc.contactNumber || 'N/A' }}</td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- No Data Message (when API returns empty array successfully) -->
        <div *ngIf="!spocLoading && !spocError && selectedSpocData && selectedSpocData.length === 0"
          class="text-center text-muted p-4">
          <i class="bi bi-info-circle fs-3 d-block mb-2"></i>
          No SPOC details were found for this crisis entry.
        </div>

      </div>
      <div class="modal-footer">
        <!-- Footer Close Button -->
        <button type="button" class="btn btn-secondary btn-sm" (click)="closeSpocModal()">Close</button>
      </div>
    </div>
  </div>
</div>
<!-- ********** END: SPOC Details Bootstrap Modal ********** -->

<!-- ********** Media Preview Modal ********** -->
<div class="modal fade" #mediaPreviewModal id="mediaPreviewModal" tabindex="-1" aria-labelledby="mediaPreviewModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="mediaPreviewModalLabel">{{ mediaPreviewTitle }}</h5>
        <button type="button" class="btn-close" aria-label="Close" (click)="closeMediaPreviewModal()"></button>
      </div>
      <div class="modal-body">
        <!-- Image Carousel -->
        <div *ngIf="mediaPreviewType === 'image'" class="text-center">
          <app-carousel [images]="mediaPreviewUrls"></app-carousel>
        </div>

        <!-- Video Carousel and Player -->
        <div *ngIf="mediaPreviewType === 'video'" class="text-center">
          <div class="row">
            <div class="col-md-5">
              <h6 class="mb-3">Select a Video</h6>
              <div class="video-thumbnails">
                <div *ngFor="let video of mediaPreviewUrls; let i = index"
                     class="video-thumbnail mb-2"
                     [class.active]="video === selectedVideoUrl"
                     (click)="selectVideo(video)">
                  <div class="d-flex align-items-center p-2 border rounded"
                       [class.bg-light]="video === selectedVideoUrl">
                    <i class="bi bi-film me-2"></i>
                    <span>Video {{ i + 1 }}</span>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-md-7">
              <h6 class="mb-3">Video Player</h6>
              <app-video-player [videoUrl]="selectedVideoUrl"></app-video-player>
            </div>
          </div>
          <div *ngIf="!selectedVideoUrl" class="mt-3 alert alert-info">
            <i class="bi bi-info-circle me-2"></i>
            Select a video from the list to play it.
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" (click)="closeMediaPreviewModal()">Close</button>
      </div>
    </div>
  </div>
</div>
<!-- ********** END: Media Preview Modal ********** -->