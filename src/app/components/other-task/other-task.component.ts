import { CommonModule } from '@angular/common';
import { Component, OnInit, ViewChild, ChangeDetector<PERSON>ef, <PERSON><PERSON><PERSON><PERSON>, ElementRef, inject, AfterViewInit } from '@angular/core';
import { FormsModule, NgForm } from '@angular/forms';
import { Subject } from 'rxjs';

import { NgbDropdownModule } from '@ng-bootstrap/ng-bootstrap';
import * as XLSX from 'xlsx';
import { Modal } from 'bootstrap';
import { TabComponent } from '../../shared/tab/tab.component';
import { createAxiosConfig } from '../../core/utilities/axios-param-config';
import { AdminService } from '../../services/admin/admin.service';
import { OtherTaskService } from '../../services/other-task/other-task.service';
import { PlantManagementService } from '../../services/plant-management/plant-management.service';
import { UpdateService } from '../../services/update/update.service';
import { UploadService } from '../../services/upload/upload.service';
import { ZoneService } from '../../services/zone/zone.service';
import { OffcanvasComponent } from '../../shared/offcanvas/offcanvas.component';
import { PaginationComponent } from '../../shared/pagination/pagination.component';
import { SwitchComponent } from '../../shared/switch/switch.component';
import { ToastMessageComponent } from '../../shared/toast-message/toast-message.component';


// Define ROLES constant
const ROLES = {
    SUPER_ADMIN: 'super_admin',
    PLANT_ADMIN: 'plant_admin',
};

// --- Interfaces & Enums ---

interface TaskFilter {
    assigneeId?: number | null;
    plantId?: number | null;
    enabled?: string | null;
    sortDirection?: 'ASC' | 'DESC';
    sortField?: string;
    // Removed 'id' filter as it's implicitly handled by role/plantId
}

interface Assignee {
    id: number;
    firstName: string;
    lastName: string;
}

interface Plant {
    id: number;
    name: string;
}

interface Zone {
    id: number;
    zoneName: string;
    zoneArea: string;
    plantId?: number;
}

interface OtherTask {
    id: number;
    enabled: boolean;
    assignToId?: number;
    plantId?: number;
    zoneId?: number;
    areaId?: number | null;
    qrId?: number | null;
    type?: number;
    assignTo?: Assignee | null;
    plant?: Plant | null;
    zone?: Zone | null;
    qr?: { lat?: number | string; lon?: number | string; } | null;
    desc?: string | null;
    imageUrl?: string | null;
    createdTimestamp: string | Date;
    status?: any;
    isQr?: boolean;
    deadline?: string;
    completeRemark?: string;
    updatedTimestamp?: string | Date;
}

interface NewTaskData {
    assignToId: number | null;
    plantId: number | null;
    zoneId: number | null;
    desc: string | null;
    enabled: boolean;
    imageFile?: File | null;
}

declare var bootstrap: any;

@Component({
    selector: 'app-other-task',
    standalone: true,
    imports: [
        CommonModule,
        FormsModule,
        TabComponent,
        PaginationComponent,
        OffcanvasComponent,
        SwitchComponent,
        NgbDropdownModule,
        ToastMessageComponent
    ],
    templateUrl: './other-task.component.html',
    styleUrls: ['./other-task.component.scss']
})
export class OtherTaskComponent implements OnInit, AfterViewInit, OnDestroy {
    public componentRoles = ROLES;

    @ViewChild('filterForm') filterForm?: NgForm;
    @ViewChild('createTaskForm') createTaskForm?: NgForm;
    @ViewChild(ToastMessageComponent) toast!: ToastMessageComponent;
    // Add ElementRefs for any Bootstrap modals if used

    isFilterModalOpen = false;
    isCreateModalOpen = false;
    isDownloadingExcel = false;
    downloadType: 'current' | 'all' | null = null;

    otherTaskList: OtherTask[] = [];
    currentPage = 1;
    itemsPerPage = 10;
    totalItems = 0;
    listLoading: boolean = false;
    showCreatedDate: boolean = true;

    filters: TaskFilter = {
        assigneeId: null,
        plantId: null,
        enabled: null,
        sortDirection: 'DESC',
        sortField: 'id'
    };

    currentUserRole: string = '';
    loggedInAdminId: number | null = null;
    loggedInPlantIds: number[] = [];

    availableAssignees: Assignee[] = [];
    availablePlants: Plant[] = [];
    availableZones: Zone[] = [];
    zonesLoading: boolean = false;

    createLoading: boolean = false;
    newTaskData: NewTaskData = this.getInitialNewTaskData();
    imagePreviewUrl: string | ArrayBuffer | null = null;

    previewImageUrl: string | null = null;

    private destroy$ = new Subject<void>();

    private otherTaskService = inject(OtherTaskService);
    private plantService = inject(PlantManagementService);
    private adminService = inject(AdminService);
    private updateService = inject(UpdateService);
    private uploadService = inject(UploadService);
    private zoneService = inject(ZoneService);
    private cdr = inject(ChangeDetectorRef);

    constructor() { }

    ngOnInit(): void {
        console.log("OtherTaskComponent initialized");
        this.setCurrentUserRoleAndDetailsById();
        this.loadInitialDropdownData();
        this.loadOtherTasks(this.currentPage);
    }

    ngAfterViewInit(): void {
        // Initialize Bootstrap modals if needed
    }

    ngOnDestroy(): void {
        this.destroy$.next();
        this.destroy$.complete();
        console.log("OtherTaskComponent destroyed");
    }

    private setCurrentUserRoleAndDetailsById(): void {
        try {
            const userString = localStorage.getItem('user');
            if (!userString || userString.trim() === "" || ['null', 'undefined', '""', '" "'].includes(userString.trim().toLowerCase())) {
                this.currentUserRole = ''; this.loggedInAdminId = null; this.loggedInPlantIds = [];
                this.toast?.showErrorToast("User session invalid."); return;
            }
            const currentUser = JSON.parse(userString);
            this.loggedInAdminId = currentUser?.id ?? null;
            this.loggedInPlantIds = (Array.isArray(currentUser?.plantIds) && currentUser.plantIds.length > 0)
                ? currentUser.plantIds.map((id: any) => Number(id)).filter((id: number) => !isNaN(id)) : [];
            const roleId = currentUser?.adminsRoleId;
            if (roleId === 1 || roleId === 6) { this.currentUserRole = this.componentRoles.SUPER_ADMIN; }
            else if (roleId === 2) {
                this.currentUserRole = this.componentRoles.PLANT_ADMIN;
                if (this.loggedInPlantIds.length === 0) { this.toast?.showErrorToast("Plant Admin has no assigned plants."); }
            } else { this.currentUserRole = ''; this.toast?.showErrorToast("Invalid user role."); }
            console.log(`Role: ${this.currentUserRole}, Plants: [${this.loggedInPlantIds.join(', ')}]`);
        } catch (error) {
            console.error("Error parsing user data:", error);
            this.currentUserRole = ''; this.loggedInAdminId = null; this.loggedInPlantIds = [];
            this.toast?.showErrorToast("Error reading user session.");
        }
    }

    getInitialNewTaskData(): NewTaskData {
        return { assignToId: null, plantId: null, zoneId: null, desc: null, enabled: true, imageFile: null };
    }

    async loadInitialDropdownData(): Promise<void> {
        await this.getPlants();
    }

    async getPlants(): Promise<void> {
        const plantParams = { sort: 'name,ASC', filter: ['enabled||eq||true'], limit: 1000 };
        const axiosConfig = createAxiosConfig(plantParams);
        let allEnabledPlants: Plant[] = [];
        try {
            const response = await this.plantService.getPlants(axiosConfig);
            allEnabledPlants = response?.data ?? response ?? [];
        } catch (error) {
            console.error("Error fetching plants:", error);
            this.availablePlants = []; this.toast?.showErrorToast('Failed to load plant list.'); return;
        }

        if (this.currentUserRole === this.componentRoles.PLANT_ADMIN && this.loggedInPlantIds.length > 0) {
            this.availablePlants = allEnabledPlants.filter(plant => this.loggedInPlantIds.includes(plant.id));
        } else if (this.currentUserRole === this.componentRoles.SUPER_ADMIN) {
            this.availablePlants = allEnabledPlants;
        } else { this.availablePlants = []; }
        console.log(`Loaded ${this.availablePlants.length} plants for dropdown based on role.`);
    }

    async loadAssignees(plantId: number | null): Promise<void> {
        this.availableAssignees = [];
        if (!plantId) return;

        const filters = ['enabled||eq||true', 'status||eq||1', `plant.id||$eq||${plantId}`];
        const data = { sort: 'firstName,ASC', filter: filters, limit: 1000 };
        const param = createAxiosConfig(data);
        console.log("Loading assignees for plant:", plantId, "with params:", param);
        try {
            const response = await this.adminService.getAdmin(param);
            this.availableAssignees = response?.data ?? response ?? [];
            console.log(`Loaded ${this.availableAssignees.length} assignees for plant ${plantId}.`);
        } catch (error) {
            console.error("Error fetching assignees for plant:", plantId, error);
            this.availableAssignees = [];
            this.toast?.showErrorToast('Failed to load assignees for the selected plant.');
        }
    }

    async loadZones(plantId: number | null = null): Promise<void> {
        const targetPlantId = plantId ?? this.newTaskData.plantId;
        this.availableZones = [];
        if (!targetPlantId) return;

        this.zonesLoading = true;
        const zoneFilters = ['enabled||eq||true', `plantId||eq||${targetPlantId}`];
        const data = { sort: 'zoneName,ASC', filter: zoneFilters, limit: 1000 };
        const param = createAxiosConfig(data);
        try {
            const response = await this.zoneService.getZone(param);
            this.availableZones = response?.data ?? response ?? [];
        } catch (error) {
            console.error(`Error fetching zones for plant ${targetPlantId}:`, error);
            this.availableZones = [];
            this.toast?.showErrorToast("Failed to load zones.");
        } finally {
            this.zonesLoading = false;
            this.cdr.detectChanges();
        }
    }

    async onPlantChange(event: Event): Promise<void> {
        const selectElement = event.target as HTMLSelectElement;
        const plantId = selectElement.value ? Number(selectElement.value) : null;
        this.newTaskData.zoneId = null;
        this.newTaskData.assignToId = null;
        this.availableZones = [];
        this.availableAssignees = [];
        await this.loadZones(plantId);
        await this.loadAssignees(plantId);
    }

    openFilterModal(): void {
        this.isFilterModalOpen = true;
        // If a plant is already selected in filters, load its assignees
        if (this.filters.plantId) {
            this.loadAssignees(this.filters.plantId);
        }
    }

    closeFilterModal(): void {
        this.isFilterModalOpen = false;
    }

    async onFilterPlantChange(event: Event): Promise<void> {
        const selectElement = event.target as HTMLSelectElement;
        const plantId = selectElement.value ? Number(selectElement.value) : null;

        // Reset assignee selection when plant changes
        this.filters.assigneeId = null;

        // Load assignees for the selected plant
        if (plantId) {
            await this.loadAssignees(plantId);
        } else {
            // Clear assignees list when "All Plants" is selected
            this.availableAssignees = [];
        }
    }

    async openCreateModal(): Promise<void> {
        if (this.currentUserRole === this.componentRoles.PLANT_ADMIN && this.loggedInPlantIds.length === 0) {
            this.toast?.showErrorToast("Cannot create task: You are not assigned to any plants.");
            return;
        }

        this.newTaskData = this.getInitialNewTaskData();
        this.availableZones = [];
        this.availableAssignees = [];
        this.imagePreviewUrl = null;
        const fileInput = document.getElementById('createImage') as HTMLInputElement;
        if (fileInput) fileInput.value = '';

        if (this.currentUserRole === this.componentRoles.PLANT_ADMIN && this.availablePlants.length === 1) {
            this.newTaskData.plantId = this.availablePlants[0].id;
            await this.loadZones(this.newTaskData.plantId);
            await this.loadAssignees(this.newTaskData.plantId);
            console.log(`Pre-selecting plant ${this.newTaskData.plantId} for Plant Admin.`);
        }

        setTimeout(() => this.createTaskForm?.resetForm(this.newTaskData), 0);
        this.isCreateModalOpen = true;
        this.createLoading = false;
    }

    closeCreateModal(): void {
        this.isCreateModalOpen = false;
        this.newTaskData.imageFile = null;
        this.imagePreviewUrl = null;
    }

    onFileSelect(event: Event): void {
        const element = event.currentTarget as HTMLInputElement;
        const fileList: FileList | null = element.files;
        if (fileList && fileList.length > 0) {
            const file = fileList[0];
            const allowedTypes = ['image/png', 'image/jpeg', 'image/gif'];
            const maxSize = 5 * 1024 * 1024;
            if (!allowedTypes.includes(file.type)) { this.toast?.showErrorToast('Invalid file type.'); this.clearSelectedFile(element); return; }
            if (file.size > maxSize) { this.toast?.showErrorToast(`File size exceeds ${maxSize / 1024 / 1024}MB limit.`); this.clearSelectedFile(element); return; }
            this.newTaskData.imageFile = file;
            this.generatePreview(file);
        } else {
            this.clearSelectedFile(element);
        }
    }

    clearSelectedFile(inputElement?: HTMLInputElement | null): void {
        this.newTaskData.imageFile = null;
        this.imagePreviewUrl = null;
        const fileInput = inputElement ?? document.getElementById('createImage') as HTMLInputElement;
        if (fileInput) fileInput.value = '';
    }

    generatePreview(file: File): void {
        const reader = new FileReader();
        reader.onload = () => { this.imagePreviewUrl = reader.result; this.cdr.detectChanges(); };
        reader.onerror = (error) => { console.error('Error reading file:', error); this.imagePreviewUrl = null; this.toast?.showErrorToast("Could not generate image preview."); };
        reader.readAsDataURL(file);
    }

    // --- Helper to trim input fields ---
    trimInputField(formData: any, fieldName: string): void {
        if (formData && formData[fieldName] && typeof formData[fieldName] === 'string') {
            formData[fieldName] = formData[fieldName].trim();
        }
    }

    async submitCreateForm(): Promise<void> {
        this.createTaskForm?.form.markAllAsTouched();
        if (this.createTaskForm?.invalid) {
            this.toast?.showErrorToast("Please fill all required fields.");
            return;
        }

        // Trim string values before submission
        if (this.newTaskData.desc) {
            this.trimInputField(this.newTaskData, 'desc');
        }

        const selectedPlantId = this.newTaskData.plantId;
        if (!selectedPlantId) {
            this.toast?.showErrorToast("Please select a Plant.");
            return;
        }

        // Role/Plant Validation (Ensures Plant Admin selects one of their own plants)
        if (this.currentUserRole === this.componentRoles.PLANT_ADMIN && !this.loggedInPlantIds.includes(selectedPlantId)) {
            this.toast?.showErrorToast("You cannot create tasks for this plant.");
            return;
        }

        // Check if an assignee is selected (as it's required by the form)
        const selectedAssigneeId = this.newTaskData.assignToId;
        if (!selectedAssigneeId) {
            this.toast?.showErrorToast("Please select an Assignee.");
            const assigneeControl = this.createTaskForm?.controls['assignToId'];
            if (assigneeControl) { assigneeControl.markAsTouched(); }
            return;
        }

        // --- Assignee Validation Removed ---
        // The check: const isValidAssignee = this.availableAssignees.some(a => a.id === selectedAssigneeId);
        // is removed because loadAssignees(selectedPlantId) should only populate
        // availableAssignees with valid users for that plant. We trust that process.
        // We still keep the check above to ensure *an* assignee was selected.
        // --- End Assignee Validation Removal ---


        this.createLoading = true;
        let uploadedImageUrl: string | null = null;

        // Image Upload Logic...
        if (this.newTaskData.imageFile) {
            const fileFormData = new FormData();
            fileFormData.append('file', this.newTaskData.imageFile, this.newTaskData.imageFile.name);
            try {
                const uploadResponse = await this.uploadService.upload(fileFormData);
                if (uploadResponse && typeof uploadResponse === 'string' && uploadResponse.trim() !== '') {
                    uploadedImageUrl = uploadResponse;
                } else { throw new Error(`Invalid upload response: ${uploadResponse}`); }
            } catch (uploadError) {
                console.error('Error uploading image:', uploadError);
                this.toast?.showErrorToast('Failed to upload image. Task creation aborted.');
                this.createLoading = false; return;
            }
        }

        const currentUserId = this.loggedInAdminId;
        if (!currentUserId) { this.toast?.showErrorToast("Authentication error."); this.createLoading = false; return; }

        // Prepare Payload
        const taskPayload = {
            id: 0, adminId: currentUserId, plantId: Number(this.newTaskData.plantId), zoneId: Number(this.newTaskData.zoneId),
            areaId: null, qrId: null, type: 1, desc: this.newTaskData.desc || "", status: null, isQr: false, deadline: "", completeRemark: "",
            image: uploadedImageUrl ? [uploadedImageUrl] : [],
            enabled: this.newTaskData.enabled,
            assignToId: Number(this.newTaskData.assignToId), // Assignee ID is required
            createdBy: currentUserId
        };
        console.log("Submitting task creation payload:", JSON.stringify(taskPayload, null, 2));

        // Submit Task
        try {
            await this.otherTaskService.createOtherTask(taskPayload);
            this.toast?.showSuccessToast("Task created successfully!");
            this.closeCreateModal();
            this.currentPage = 1;
            await this.loadOtherTasks(this.currentPage);
        } catch (taskCreateError: any) {
            console.error("Error creating task:", taskCreateError);
            this.toast?.showErrorToast(taskCreateError?.response?.data?.message || "Failed to create task.");
        } finally {
            this.createLoading = false;
        }
    }

    async loadOtherTasks(page: number): Promise<void> {
        if (this.listLoading) return;
        this.listLoading = true;
        this.otherTaskList = [];
        this.cdr.detectChanges();

        const filterParams: string[] = [];

        // --- Apply Role-Based Plant Filtering ---
        if (this.currentUserRole === this.componentRoles.PLANT_ADMIN) {
            if (this.loggedInPlantIds.length > 0) {
                if (this.filters.plantId !== null && this.filters.plantId !== undefined) {
                    // Plant Admin selected a specific plant from their assigned list
                    // Assuming UI prevents selecting unassigned plants, directly filter by the selected plant ID
                    filterParams.push(`plantId||eq||${this.filters.plantId}`);
                    console.log("Plant Admin: Filtering tasks by selected plant ID:", this.filters.plantId);
                } else {
                    // Plant Admin selected "All My Plants" (or default) - filter by all their assigned plants
                    filterParams.push(`plantId||$in||${this.loggedInPlantIds.join(',')}`);
                    console.log("Plant Admin: Filtering tasks by assigned plant IDs:", this.loggedInPlantIds);
                }
            } else {
                // Plant Admin has no plants assigned - apply a filter that returns no data
                console.warn("Plant Admin has no assigned plants. Applying impossible filter.");
                filterParams.push(`plantId||eq||-1`);
            }
        } else if (this.currentUserRole === this.componentRoles.SUPER_ADMIN) {
            // Super Admin: Only filter if they explicitly selected a plant
            if (this.filters.plantId !== null && this.filters.plantId !== undefined) {
                filterParams.push(`plantId||eq||${this.filters.plantId}`);
                console.log("Super Admin: Filtering tasks by selected plant ID:", this.filters.plantId);
            } else {
                 console.log("Super Admin: No specific plant selected, showing all.");
                 // No plant filter added
            }
        } else {
            // No role or unknown role - potentially restrict or show all? Showing all for now.
            console.warn("Unknown user role, not applying specific plant filters.");
            // No plant filter added by default
        }
        // --- End Role-Based Plant Filtering ---

        if (this.filters.enabled !== null && this.filters.enabled !== undefined) { filterParams.push(`enabled||eq||${this.filters.enabled}`); }
        if (this.filters.assigneeId) { filterParams.push(`assignToId||eq||${this.filters.assigneeId}`); }

        const dataRequest = {
            page: page, limit: this.itemsPerPage,
            sort: `${this.filters.sortField || 'id'},${this.filters.sortDirection || 'DESC'}`,
            filter: filterParams,
            join: ['assignTo', 'plant', 'zone']
        };

        try {
            const param = createAxiosConfig(dataRequest);
            console.log("Other Task Filter Params:", filterParams);
            console.log("Loading tasks with params:", param);
            const response = await this.otherTaskService.getOtherTasks(param);
            this.otherTaskList = response?.data?.map((task: any) => ({
                ...task, imageUrl: task.imageUrl || task.imagePath || task.pictureUrl || task.image?.[0] || null
            })) ?? [];
            this.totalItems = response?.total ?? 0;
        } catch (error: any) {
            console.error("Error fetching other tasks:", error);
            this.otherTaskList = []; this.totalItems = 0;
            this.toast?.showErrorToast(error?.response?.data?.message || "Failed to load tasks.");
        } finally {
            this.listLoading = false;
            this.cdr.detectChanges();
        }
    }

    onPageChange(page: number): void {
        if (this.currentPage === page || this.listLoading) return;
        this.currentPage = page;
        this.loadOtherTasks(this.currentPage);
    }

    applyFilters(): void {
        // Check if form is valid before applying filters
        if (this.filterForm && this.filterForm.invalid) {
            this.toast?.showErrorToast("Please correct the validation errors before applying filters.");
            return;
        }

        this.currentPage = 1;
        this.loadOtherTasks(this.currentPage);
        this.closeFilterModal();
    }

    resetFilters(): void {
        this.filters = {
            assigneeId: null, plantId: null, enabled: null,
            sortDirection: 'DESC', sortField: 'id'
        };
        this.currentPage = 1;
        this.loadOtherTasks(this.currentPage);
    }

    getSortClass(key: string): string {
        if (this.filters.sortField === key) {
            return this.filters.sortDirection === 'ASC' ? 'sorting_asc' : 'sorting_desc';
        }
        return 'sorting';
    }

    sortBy(field: string): void {
        if (this.listLoading) return;
        if (this.filters.sortField === field) {
            this.filters.sortDirection = this.filters.sortDirection === 'ASC' ? 'DESC' : 'ASC';
        } else {
            this.filters.sortField = field;
            this.filters.sortDirection = 'DESC';
        }
        this.currentPage = 1;
        this.loadOtherTasks(this.currentPage);
    }

    async onSwitchToggle(isEnabled: boolean, id: number): Promise<void> {
        const task = this.otherTaskList.find(t => t.id === id);
        if (this.currentUserRole === this.componentRoles.PLANT_ADMIN && task?.plantId && !this.loggedInPlantIds.includes(task.plantId)) {
            this.toast?.showErrorToast("Permission denied to change status for this task.");
            setTimeout(() => { const taskToRevert = this.otherTaskList.find(t => t.id === id); if (taskToRevert) taskToRevert.enabled = !isEnabled; this.cdr.detectChanges(); }, 0);
            return;
        }

        console.log(`Toggling status for task ${id} to ${isEnabled}`);
        const data = { tableName: 'other-task', id: id, data: { enabled: isEnabled }, createdBy: this.loggedInAdminId };
        const index = this.otherTaskList.findIndex(task => task.id === id);
        const previousState = index > -1 ? this.otherTaskList[index].enabled : null;
        if (index > -1) this.otherTaskList[index].enabled = isEnabled;

        try {
            await this.updateService.update(data);
            this.toast?.showSuccessToast(`Task ${id} status updated.`);
        } catch (error: any) {
            console.error(`Error updating task ${id} status:`, error);
            this.toast?.showErrorToast(error?.response?.data?.message || `Failed to update status.`);
            if (index > -1 && previousState !== null) { this.otherTaskList[index].enabled = previousState; this.cdr.detectChanges(); }
        }
    }

    openEditPage(item: OtherTask): void {
        if (this.currentUserRole === this.componentRoles.PLANT_ADMIN && item.plantId && !this.loggedInPlantIds.includes(item.plantId)) {
            this.toast?.showErrorToast("Permission denied to edit this task.");
            return;
        }

        // When implementing the edit functionality, make sure to trim string fields
        // Example: if (item.desc) { this.trimInputField(item, 'desc'); }

        console.log("Navigate to edit page for task:", item.id);
        alert(`Edit action triggered for Task ID: ${item.id}. Edit modal/page needs implementation.`);
    }

    openImagePreview(imageUrl: string | null): void {
        if (!imageUrl) return;
        this.previewImageUrl = imageUrl;
        const modalElement = document.getElementById('imagePreviewModal');
        if (!modalElement) { console.error("Preview modal element not found."); return; }
        try {
            const modal = bootstrap.Modal.getOrCreateInstance(modalElement);
            modal.show();
        } catch (e) { console.error("Bootstrap modal error:", e); }
    }

    // --- ADD THIS METHOD ---
    getCurrentListData(): OtherTask[] | undefined {
        // In this component, the primary list is always otherTaskList
        return this.otherTaskList;
    }
    // --- END ADDED METHOD ---

    async fetchAllFilteredTasks(): Promise<OtherTask[] | null> {
        const filterParams: string[] = [];

        if (this.currentUserRole === this.componentRoles.PLANT_ADMIN) {
            if (this.loggedInPlantIds.length > 0) {
                // Always use eq for selected plant, only use $in when showing all plants
                if (this.filters.plantId) {
                    filterParams.push(`plantId||eq||${this.filters.plantId}`);
                } else {
                    filterParams.push(`plantId||$in||${this.loggedInPlantIds.join(',')}`);
                }
            } else {
                filterParams.push(`plantId||eq||-1`);
            }
        } else if (this.currentUserRole === this.componentRoles.SUPER_ADMIN) {
            if (this.filters.plantId) {
                filterParams.push(`plantId||eq||${this.filters.plantId}`);
            }
        }

        if (this.filters.enabled !== null && this.filters.enabled !== undefined) {
            filterParams.push(`enabled||eq||${this.filters.enabled}`);
        }
        if (this.filters.assigneeId) {
            filterParams.push(`assignToId||eq||${this.filters.assigneeId}`);
        }

        const dataRequest = {
            limit: 10000,
            sort: `${this.filters.sortField || 'id'},${this.filters.sortDirection || 'DESC'}`,
            filter: filterParams,
            join: ['assignTo', 'plant', 'zone']
        };

        this.listLoading = true;
        try {
            const param = createAxiosConfig(dataRequest);
            console.log('API Request Params (Task Download - All Data):', JSON.stringify(param, null, 2));
            const response = await this.otherTaskService.getOtherTasks(param);

            const tasks = response?.data?.map((task: any) => ({
                ...task,
                imageUrl: task.imageUrl || task.imagePath || task.pictureUrl || task.image?.[0] || null
            })) ?? [];
            return tasks;

        } catch (error: any) {
            console.error("Error fetching all tasks for download:", error);
            this.toast?.showErrorToast(error?.response?.data?.message || "Failed to retrieve full data for download.");
            return null;
        } finally {
            this.listLoading = false;
        }
    }

    async downloadExcel(type: 'current' | 'all') {
        if (this.isDownloadingExcel) return;

        this.isDownloadingExcel = true;
        this.downloadType = type;
        this.toast?.showSuccessToast(`Preparing download for ${type === 'current' ? 'current page' : 'all filtered'} tasks...`);

        let dataToExport: OtherTask[] | null = null;

        try {
            if (type === 'all') {
                dataToExport = await this.fetchAllFilteredTasks();
            } else {
                dataToExport = this.getCurrentListData() ?? null;
                if (dataToExport === undefined) { dataToExport = null; }
            }

            if (dataToExport === null) { this.toast?.showErrorToast("Failed to retrieve data for download."); return; }
            if (!dataToExport || dataToExport.length === 0) { this.toast?.showErrorToast(`No tasks available to download.`); return; }

            console.log(`Fetched ${dataToExport.length} tasks for Excel export (${type}).`);

            const dataForExcel = dataToExport.map(task => ({
                'Task ID': task.id,
                'Status': task.enabled ? 'Active' : 'Inactive',
                'Assignee Name': task.assignTo ? `${task.assignTo.firstName} ${task.assignTo.lastName}`.trim() : 'N/A',
                'Assignee ID': task.assignToId ?? '',
                'Plant Name': task.plant?.name ?? 'N/A',
                'Plant ID': task.plantId ?? '',
                'Zone Name': task.zone?.zoneName ?? 'N/A',
                'Zone ID': task.zoneId ?? '',
                'Zone Area': task.zone?.zoneArea ?? 'N/A',
                'Description': task.desc ?? '',
                'Image URL': task.imageUrl ?? '',
                'Created Date': task.createdTimestamp ? new Date(task.createdTimestamp).toLocaleDateString() : '',
                'Created Time': task.createdTimestamp ? new Date(task.createdTimestamp).toLocaleTimeString() : '',
                'Last Updated': task.updatedTimestamp ? new Date(task.updatedTimestamp).toLocaleString() : '',
            }));

            const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(dataForExcel);
            const wb: XLSX.WorkBook = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(wb, ws, 'OtherTasks');

            const dateStr = new Date().toISOString().slice(0, 10);
            const typeStr = type === 'current' ? `Page${this.currentPage}` : 'All';
            const fileName = `OtherTasks_${typeStr}_${dateStr}.xlsx`;

            XLSX.writeFile(wb, fileName);
            this.toast?.showSuccessToast(`Excel file download started (${type}).`);

        } catch (error) {
            console.error(`Error generating Excel file (${type}):`, error);
            this.toast?.showErrorToast(`An error occurred while generating the Excel file (${type}).`);
        } finally {
            this.isDownloadingExcel = false;
            this.downloadType = null;
        }
    }
}