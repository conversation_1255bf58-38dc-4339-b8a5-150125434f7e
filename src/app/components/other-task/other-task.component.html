<app-toast-message></app-toast-message>
<div class="app-container">
    <div class="card custom-card">
        <div class="card-header">
            <div class="row align-items-center">
                <div class="col">
                    <h6 class="mb-0">Other Task List</h6>
                </div>
                <div class="col text-end d-flex align-items-center justify-content-end">
                    <button class="btn-sm adani-btn me-2" (click)="openCreateModal()" title="Create New Task">
                        <i class="bi bi-plus-circle me-1"></i> Create Task
                    </button>
                    <!-- *** REPLACE existing simple Download button with this Dropdown *** -->
                    <div ngbDropdown class="d-inline-block me-2">
                        <button type="button" class="btn btn-sm adani-btn dropdown-toggle" id="downloadTaskExcelDropdown" ngbDropdownToggle
                            [disabled]="isDownloadingExcel || listLoading">
                            <span *ngIf="!isDownloadingExcel">
                                <i class="bi bi-file-earmark-excel me-1"></i> Download Excel
                            </span>
                            <span *ngIf="isDownloadingExcel">
                                <span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
                                Downloading {{ downloadType === 'current' ? 'Page' : (downloadType === 'all' ? 'All' : '') }}...
                            </span>
                        </button>
                        <ul ngbDropdownMenu aria-labelledby="downloadTaskExcelDropdown">
                            <li>
                                <button ngbDropdownItem (click)="downloadExcel('current')" [disabled]="isDownloadingExcel || listLoading || (otherTaskList?.length ?? 0) === 0">
                                    <i class="bi bi-download me-1"></i> Download Current Page ({{ otherTaskList?.length ?? 0 }})
                                </button>
                            </li>
                            <li>
                                <button ngbDropdownItem (click)="downloadExcel('all')" [disabled]="isDownloadingExcel || listLoading || totalItems === 0">
                                    <i class="bi bi-cloud-download me-1"></i> Download All Filtered ({{ totalItems }})
                                </button>
                            </li>
                        </ul>
                    </div>
                    <img src="../../../assets/svg/filter.svg" class="filter-button ms-3" (click)="openFilterModal()" alt="Filter" style="width: 35px; cursor: pointer;" title="Filter Tasks"/>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered custom-table table-hover">
                    <thead class="table-header">
                        <tr>
                            <th scope="col" class="text-center">Id</th>
                            <th scope="col" class="text-center">Status</th>
                            <th scope="col" class="text-center">Image</th>
                            <th scope="col" class="text-center">Assign To</th>
                            <th scope="col" class="text-center">Plant</th>
                            <th scope="col" class="text-center">Zone</th>
                            <th scope="col" class="text-center">Area</th>
                            <th scope="col" class="text-center">Description</th>
                            <th scope="col" class="text-center">Created Date</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- Loading State -->
                        <tr *ngIf="listLoading">
                            <td colspan="9" class="text-center p-4">
                                <span class="spinner-border spinner-border-sm text-primary" role="status" aria-hidden="true"></span>
                                <span class="ms-2">Loading tasks...</span>
                            </td>
                        </tr>
                        <!-- No Data State -->
                        <tr *ngIf="!listLoading && (!otherTaskList || otherTaskList.length === 0)">
                             <td colspan="9" class="text-center p-4 text-muted">
                                <i class="bi bi-info-circle me-2"></i>No tasks found matching the criteria.
                            </td>
                        </tr>
                        <!-- Data Rows -->
                        <tr *ngFor="let item of otherTaskList">
                            <td class="text-center align-middle">{{ item.id }}</td>
                            <td class="text-center align-middle">
                                <app-switch [(checked)]="item.enabled" (checkedChange)="onSwitchToggle($event, item.id)" onLabel="Active" offLabel="Inactive"></app-switch>
                            </td>
                            <td class="text-center align-middle">
                                <!-- Image in table with click preview -->
                                <img *ngIf="item.imageUrl" [src]="item.imageUrl" alt="Task Image" class="img-thumbnail" style="max-width: 60px; max-height: 40px; cursor: pointer;" (click)="openImagePreview(item.imageUrl)">
                                <span *ngIf="!item.imageUrl" class="text-muted small fst-italic">No Image</span>
                            </td>
                            <td class="text-center align-middle">
                                {{ item.assignTo?.firstName }} {{ item.assignTo?.lastName || 'N/A' }}
                            </td>
                            <td class="text-center align-middle">
                                {{ item.plant?.name || 'N/A' }}
                            </td>
                            <td class="text-center align-middle">
                                {{ item.zone?.zoneName || 'N/A' }}
                            </td>
                            <td class="text-center align-middle">
                                {{ item.zone?.zoneArea || 'N/A' }}
                            </td>
                            <td class="text-center align-middle" style="max-width: 250px; overflow-wrap: break-word;">
                                {{ item.desc || 'N/A' }}
                            </td>
                            <td class="text-center align-middle">
                                <span class="text-danger" style="font-size: 0.9em;">{{ item.createdTimestamp | date: 'dd MMM yyyy' }}</span><br>
                                <small class="text-muted">({{ item.createdTimestamp | date: 'shortTime' }})</small>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <div class="card-footer text-muted text-center bg-light py-2">
            <app-pagination [currentPage]="currentPage" [totalItems]="totalItems" [itemsPerPage]="itemsPerPage"
                (pageChange)="onPageChange($event)"></app-pagination>
        </div>
    </div>
</div>

<!-- ======================= -->
<!-- Offcanvas Components    -->
<!-- ======================= -->

<!-- Filter Offcanvas -->
<app-offcanvas [title]="'Filter Other Tasks'" *ngIf="isFilterModalOpen" (onClickCross)="closeFilterModal()">
   <!-- ... existing filter form content ... -->
    <div class="filter-container p-3">
        <form #filterForm="ngForm" (ngSubmit)="applyFilters()">
            <div class="row g-3">
                <div class="col-12">
                    <label class="form-label" for="filterPlant">Plant</label>
                    <select id="filterPlant" class="form-select form-select-sm" [(ngModel)]="filters.plantId" name="plantId" (change)="onFilterPlantChange($event)">
                        <option [ngValue]="null">All Plants</option>
                        <option *ngFor="let plant of availablePlants" [value]="plant.id">{{ plant.name }}</option>
                    </select>
                </div>
                <div class="col-12">
                    <label class="form-label" for="filterAssignee">Assignee</label>
                    <select id="filterAssignee" class="form-select form-select-sm" [(ngModel)]="filters.assigneeId" name="assigneeId" [disabled]="!filters.plantId">
                        <option [ngValue]="null">{{ filters.plantId ? 'All Assignees' : 'Select a plant first' }}</option>
                        <option *ngFor="let assignee of availableAssignees" [value]="assignee.id">{{ assignee.firstName }} {{assignee.lastName}}</option>
                    </select>
                </div>
                 <div class="col-12">
                    <label class="form-label" for="filterEnabled">Status</label>
                    <select id="filterEnabled" class="form-select form-select-sm" [(ngModel)]="filters.enabled" name="enabled"> <option [ngValue]="null">Any Status</option> <option value="true">Active</option> <option value="false">Inactive</option> </select>
                </div>
                <div class="col-12">
                    <label class="form-label" for="filterSortField">Sort By</label>
                    <div class="input-group input-group-sm">
                        <select id="filterSortField" class="form-select" [(ngModel)]="filters.sortField" name="sortField"> <option value="id">ID</option> <option value="assignTo.firstName">Assignee</option> <option value="plant.name">Plant</option> <option value="zone.zoneName">Zone</option> <option value="enabled">Status</option> <option value="createdTimestamp">Created</option> </select>
                        <select id="filterSortDir" class="form-select" style="max-width: 120px;" [(ngModel)]="filters.sortDirection" name="sortDirection"> <option value="DESC">Desc</option> <option value="ASC">Asc</option> </select>
                    </div>
                </div>
                <div class="col-12 mt-4 d-flex justify-content-end gap-2">
                     <button type="button" class="btn btn-sm btn-secondary" (click)="resetFilters()"> <i class="bi bi-arrow-clockwise me-1"></i> Reset </button>
                     <button type="submit" class="btn btn-sm adani-btn"> <i class="bi bi-funnel-fill me-1"></i> Apply Filters </button>
                </div>
            </div>
        </form>
    </div>
</app-offcanvas>


<!-- Create Task Offcanvas -->
<app-offcanvas [title]="'Create New Other Task'" *ngIf="isCreateModalOpen" (onClickCross)="closeCreateModal()">
    <div class="create-task-container p-3">
        <form #createTaskForm="ngForm" (ngSubmit)="submitCreateForm()">
            <div class="row g-3">

                <!-- Plant Selection -->
                <div class="col-12">
                    <label class="form-label" for="createPlant">Plant <span class="text-danger">*</span></label>
                    <select id="createPlant" class="form-select form-select-sm" name="plantId" [(ngModel)]="newTaskData.plantId" required (change)="onPlantChange($event)"> <option [ngValue]="null" disabled>-- Select Plant --</option> <option *ngFor="let plant of availablePlants" [value]="plant.id">{{ plant.name }}</option> </select>
                     <div *ngIf="createTaskForm.controls['plantId']?.invalid && (createTaskForm.controls['plantId']?.dirty || createTaskForm.controls['plantId']?.touched)" class="text-danger small mt-1"> Plant is required. </div>
                </div>

                <!-- Assignee Selection -->
                <div class="col-12">
                    <label class="form-label" for="createAssignee">Assign To <span class="text-danger">*</span></label>
                    <select id="createAssignee" class="form-select form-select-sm" name="assignToId" [(ngModel)]="newTaskData.assignToId" required> <option [ngValue]="null" disabled>-- Select Assignee --</option> <option *ngFor="let assignee of availableAssignees" [value]="assignee.id"> {{ assignee.firstName }} {{ assignee.lastName }} </option> </select>
                    <div *ngIf="createTaskForm.controls['assignToId']?.invalid && (createTaskForm.controls['assignToId']?.dirty || createTaskForm.controls['assignToId']?.touched)" class="text-danger small mt-1"> Assignee is required. </div>
                </div>

                 <!-- Zone Selection -->
                 <div class="col-12">
                    <label class="form-label" for="createZone">Zone <span class="text-danger">*</span></label>
                    <select id="createZone" class="form-select form-select-sm" name="zoneId" [(ngModel)]="newTaskData.zoneId" required [disabled]="!newTaskData.plantId || zonesLoading"> <option [ngValue]="null" disabled>-- Select Zone --</option> <option *ngIf="zonesLoading" disabled>Loading zones...</option> <option *ngFor="let zone of availableZones" [value]="zone.id"> {{ zone.zoneName }} ({{ zone.zoneArea }}) </option> </select>
                    <div *ngIf="createTaskForm.controls['zoneId']?.invalid && (createTaskForm.controls['zoneId']?.dirty || createTaskForm.controls['zoneId']?.touched)" class="text-danger small mt-1"> Zone is required. </div>
                 </div>

                <!-- Description -->
                <div class="col-12">
                    <label class="form-label" for="createDescription">Description <span class="text-danger">*</span></label>
                    <textarea id="createDescription" class="form-control form-control-sm" name="desc" rows="4"
                        [(ngModel)]="newTaskData.desc" required placeholder="Enter task description..."
                        maxlength="200" #descInput="ngModel" pattern="^[a-zA-Z0-9\s.,!?-]*$"
                        (blur)="trimInputField(newTaskData, 'desc')"></textarea>
                    <div class="d-flex justify-content-between mt-1">
                        <div *ngIf="createTaskForm.controls['desc']?.invalid && (createTaskForm.controls['desc']?.dirty || createTaskForm.controls['desc']?.touched)" class="text-danger small">
                            <span *ngIf="createTaskForm.controls['desc']?.errors?.['required']">Description is required.</span>
                            <span *ngIf="descInput.errors?.['pattern']">Description should contain only alphanumeric characters and basic punctuation.</span>
                        </div>
                        <small class="text-muted ms-auto">{{ newTaskData.desc?.length || 0 }}/200 characters</small>
                    </div>
                </div>

                <!-- Image Upload -->
                <div class="col-12">
                    <label for="createImage" class="form-label">Upload Image (Optional)</label>
                    <input class="form-control form-control-sm" type="file" id="createImage" name="imageFile" (change)="onFileSelect($event)" accept="image/png, image/jpeg, image/gif">

                    <!-- Display Selected File Info -->
                    <div *ngIf="newTaskData.imageFile" class="mt-1 small text-muted d-flex align-items-center">
                       <i class="bi bi-paperclip me-1"></i>
                       <span>{{ newTaskData.imageFile.name }} ({{ (newTaskData.imageFile.size / 1024).toFixed(1) }} KB)</span>
                       <button type="button" class="btn btn-sm btn-outline-danger p-0 px-1 ms-2" (click)="clearSelectedFile()" title="Clear Selection">
                           <i class="bi bi-x-lg" style="font-size: 0.8em;"></i> <!-- Smaller icon -->
                       </button>
                    </div>

                     <!-- *** ADDED: Image Preview *** -->
                     <div *ngIf="imagePreviewUrl" class="mt-2 text-center border rounded p-1" style="max-width: 150px;">
                         <img [src]="imagePreviewUrl" alt="Image Preview" class="img-fluid" style="max-height: 100px;">
                         <small class="d-block text-muted mt-1">Preview</small>
                     </div>
                     <!-- *** END: Image Preview *** -->
                </div>
                <!-- END Image Upload -->

                 <!-- Enabled Status -->
                <div class="col-12">
                     <label class="form-label d-block">Initial Status</label>
                     <app-switch [(checked)]="newTaskData.enabled" name="enabled" onLabel="Active" offLabel="Inactive"></app-switch>
                </div>

                <!-- Action Buttons -->
                <div class="col-12 mt-4 d-flex justify-content-end gap-2 border-top pt-3">
                    <button type="button" class="btn btn-sm btn-secondary" (click)="closeCreateModal()"> Cancel </button>
                    <button type="submit" class="btn btn-sm adani-btn" [disabled]="createTaskForm.invalid || createLoading"> <span *ngIf="!createLoading"><i class="bi bi-plus-circle-fill me-1"></i> Create Task</span> <span *ngIf="createLoading"> <span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span> Creating... </span> </button>
                </div>

            </div>
        </form>
    </div>
</app-offcanvas>

<!-- Image Preview Modal (for table images) -->
<div class="modal fade" id="imagePreviewModal" tabindex="-1" aria-labelledby="imagePreviewModalLabel" aria-hidden="true">
  <!-- ... existing modal content ... -->
   <div class="modal-dialog modal-dialog-centered modal-lg"> <div class="modal-content"> <div class="modal-header"> <h5 class="modal-title" id="imagePreviewModalLabel">Image Preview</h5> <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button> </div> <div class="modal-body text-center"> <img [src]="previewImageUrl" class="img-fluid" alt="Task Image Preview" style="max-height: 70vh;"> </div> </div> </div>
</div>