import { CommonModule } from '@angular/common';
import { Component, OnInit, ViewChild, inject, ChangeDetectorRef } from '@angular/core'; // Added ChangeDetectorRef
import { FormsModule, NgForm } from '@angular/forms';
import { PlantManagementService } from '../../services/plant-management/plant-management.service';
import { createAxiosConfig } from '../../core/utilities/axios-param-config';
import { PaginationComponent } from "../../shared/pagination/pagination.component";
import { OffcanvasComponent } from "../../shared/offcanvas/offcanvas.component";
import { PlantTypeService } from '../../services/master-management/plant-type/plant-type.service';
import { AdminService } from '../../services/admin/admin.service';
import { SwitchComponent } from "../../shared/switch/switch.component";
import { UpdateService } from '../../services/update/update.service';
import { ClusterService } from '../../services/master-management/cluster/cluster.service';
import { OpcoService } from '../../services/master-management/opco/opco.service';
import { ToastMessageComponent } from '../../shared/toast-message/toast-message.component';
import { NgbDropdownModule } from '@ng-bootstrap/ng-bootstrap';
import * as XLSX from 'xlsx';

// --- Define ROLES constant ---
const ROLES = {
    SUPER_ADMIN: 'super_admin',
    PLANT_ADMIN: 'plant_admin',
};

// --- Interfaces ---
interface PlantFilter {
    name?: string | null;
    plantTypeId?: number | null;
    plantAdminId?: number | null; // Filter by assigned admin (may need adjustment based on backend)
    enabled?: string | null;
    sortDirection?: 'ASC' | 'DESC';
    sortField?: string;
    // Added for role-based filtering:
    id?: number[] | null; // To filter by specific plant IDs for Plant Admin
}
interface SimpleMaster { id: number; title: string; }
interface AdminUser { id: number; firstName: string; lastName: string; }
interface Plant {
    id: number; name: string; emergencyContactNumber: string | null; enabled: boolean;
    plantType?: SimpleMaster | null; cluster?: SimpleMaster | null; opco?: SimpleMaster | null;
    plantAdmin?: AdminUser | null; // Admin assigned to this plant
    plantAdminId?: number | null; // Added for easier reference
    createdAt?: string | Date; updatedTimestamp?: string | Date;
}
interface EditPlantData {
    id: number; name: string; emergencyContactNumber: string | null; enabled: boolean;
    plantTypeId: number | null; clusterId: number | null; opcoId: number | null; plantAdminId: number | null;
}
interface NewPlantData {
    name: string; emergencyContactNumber: string | null; enabled: boolean;
    plantTypeId: number | null; clusterId: number | null; opcoId: number | null; plantAdminId: number | null;
}

@Component({
    selector: 'app-plant-management',
    standalone: true,
    imports: [
    CommonModule,
    FormsModule,
    PaginationComponent,
    OffcanvasComponent,
    SwitchComponent,
    NgbDropdownModule,
    ToastMessageComponent
],
    templateUrl: './plant-management.component.html',
    styleUrls: ['./plant-management.component.scss']
})
export class PlantManagementComponent implements OnInit {
    // --- Expose ROLES constant ---
    public componentRoles = ROLES;

    // --- ViewChild References ---
    @ViewChild('editForm') editForm?: NgForm;
    @ViewChild('createForm') createForm?: NgForm;
    @ViewChild(ToastMessageComponent) toast!: ToastMessageComponent;

    // --- Component State ---
    plantList: Plant[] = [];
    currentPage = 1;
    itemsPerPage = 10;
    totalItems = 0;
    isFilterModalOpen = false;
    isEditModalOpen = false;
    isCreateModalOpen = false;
    listLoading = false;
    editLoading = false;
    createLoading = false;
    isDownloadingExcel = false;
    downloadType: 'current' | 'all' | null = null;
    exportingQRPlantId: number | null = null; // Track which plant's QR code is being exported

    // --- Role-Based Access Control ---
    currentUserRole: string = '';
    loggedInAdminId: number | null = null;
    loggedInPlantIds: number[] = []; // Store all assigned plant IDs for Plant Admin

    // --- Filter State ---
    filters: PlantFilter = {
        name: null, plantTypeId: null, plantAdminId: null, id: null, // Added 'id' for role filtering
        enabled: 'true', sortDirection: 'ASC', sortField: 'name'
    };

    // --- Dropdown Data ---
    availablePlantTypes: SimpleMaster[] = [];
    availablePlantAdmins: AdminUser[] = []; // Admins with Plant Admin role
    availableClusters: SimpleMaster[] = [];
    availableOpcos: SimpleMaster[] = [];

    // --- Edit/Create State ---
    selectedPlant: EditPlantData | null = null;
    newPlantData: NewPlantData = this.getInitialNewPlantData();

    // --- Inject Services ---
    private plantService = inject(PlantManagementService);
    private plantTypeService = inject(PlantTypeService);
    private adminService = inject(AdminService);
    private updateService = inject(UpdateService);
    private clusterService = inject(ClusterService);
    private opcoService = inject(OpcoService);
    // Removed readonly from injected services as it's not standard practice with inject()

    constructor(private cdr: ChangeDetectorRef) { }

    ngOnInit() {
        this.setCurrentUserRoleAndDetailsById(); // Determine role and plants first
        // Load dropdowns
        this.loadPlantTypes();
        this.loadPlantAdmins(); // Load admins for dropdowns (might need role filtering later)
        this.loadClusters();
        this.loadOpcos();
        // Load initial plant list (will respect role filter)
        this.loadPlants(this.currentPage);
    }

    private setCurrentUserRoleAndDetailsById(): void {
        try {
            const userString = localStorage.getItem('user');
            if (!userString || userString.trim() === "" || ['null', 'undefined', '""', '" "'].includes(userString.trim().toLowerCase())) {
                console.error("User data not found or is invalid in localStorage.");
                this.currentUserRole = '';
                this.loggedInAdminId = null;
                this.loggedInPlantIds = [];
                this.toast?.showErrorToast("User session invalid. Please log in again.");
                return;
            }

            const currentUser = JSON.parse(userString);
            console.log('Current User Parsed for Role Check (Plant Mgmt):', currentUser);

            this.loggedInAdminId = currentUser?.id ?? null;

            this.loggedInPlantIds = (Array.isArray(currentUser?.plantIds) && currentUser.plantIds.length > 0)
                                      ? currentUser.plantIds.map((id: any) => Number(id)).filter((id: number) => !isNaN(id))
                                      : [];

            const roleId = currentUser?.adminsRoleId;

            if (roleId === 1 || roleId === 6) {
                this.currentUserRole = this.componentRoles.SUPER_ADMIN;
            } else if (roleId === 2) {
                this.currentUserRole = this.componentRoles.PLANT_ADMIN;
                if (this.loggedInPlantIds.length === 0) {
                    console.error(`Plant Admin (Role ID ${roleId}, User ID ${this.loggedInAdminId}) has no plants assigned!`);
                    this.toast?.showErrorToast("User configuration error: Plant Admin has no assigned plants.");
                } else {
                     // **Important: Pre-apply plant filter for Plant Admins**
                     // This ensures they only see their plants by default.
                     // The filter modal will allow them to select *within* their list.
                     this.filters.id = this.loggedInPlantIds;
                }
            } else {
                console.error(`Invalid or missing adminsRoleId (${roleId}) for User ID ${this.loggedInAdminId}.`);
                this.currentUserRole = '';
                this.toast?.showErrorToast("User configuration error: Invalid role.");
            }

            console.log(`User Role Determined: ${this.currentUserRole || 'None'}, Plant IDs: [${this.loggedInPlantIds.join(', ')}]`);

        } catch (error) {
            console.error("Error parsing user data from localStorage:", error);
            this.currentUserRole = '';
            this.loggedInAdminId = null;
            this.loggedInPlantIds = [];
            this.toast?.showErrorToast("Error reading user session. Please log in again.");
        }
    }


    getInitialNewPlantData(): NewPlantData {
        return {
            name: '', emergencyContactNumber: null, enabled: true,
            plantTypeId: null, clusterId: null, opcoId: null, plantAdminId: null
        };
    }

    async loadPlantTypes(): Promise<void> {
        const data = { sort: 'title,ASC', filter: ['enabled||eq||true'], limit: 1000 };
        const params = createAxiosConfig(data);
        try {
            const response = await this.plantTypeService.getPlantType(params);
            this.availablePlantTypes = response ?? [];
        } catch (error) {
            console.error("Error fetching plant types:", error); this.availablePlantTypes = [];
            this.toast?.showErrorToast('Failed to load plant types.');
        }
    }

    async loadClusters(): Promise<void> {
        const data = { sort: 'title,ASC', filter: ['enabled||eq||true'], limit: 1000 };
        const params = createAxiosConfig(data);
        try {
            const response = await this.clusterService.getCluster(params);
            this.availableClusters = response ?? [];
        } catch (error) {
            console.error("Error fetching clusters:", error); this.availableClusters = [];
             this.toast?.showErrorToast('Failed to load clusters.');
        }
    }

    async loadOpcos(): Promise<void> {
        const data = { sort: 'title,ASC', filter: ['enabled||eq||true'], limit: 1000 };
        const params = createAxiosConfig(data);
        try {
            const response = await this.opcoService.getOpco(params);
            this.availableOpcos = response ?? [];
        } catch (error) {
            console.error("Error fetching OPCOs:", error); this.availableOpcos = [];
             this.toast?.showErrorToast('Failed to load OPCOs.');
        }
    }

    async loadPlantAdmins(): Promise<void> {
        // Fetch only users with the Plant Admin role (e.g., adminsRoleId === 2) and are active
        const data = { sort: 'firstName,ASC', filter: [ 'enabled||eq||true', 'status||eq||1', 'adminsRoleId||eq||2' ], limit: 10000 };
        const param = createAxiosConfig(data);
        try {
            const response = await this.adminService.getAdmin(param);
            this.availablePlantAdmins = response?.data ?? response ?? []; // Adjust based on actual response structure
        } catch (error) {
            console.error("Error fetching plant admins:", error); this.availablePlantAdmins = [];
             this.toast?.showErrorToast('Failed to load plant admins.');
        }
    }

    openFilterModal(): void { this.isFilterModalOpen = true; }
    closeFilterModal(): void { this.isFilterModalOpen = false; }

    openEditModal(plant: Plant): void {
        // Check if Plant Admin is trying to edit a plant they don't have access to
        if(this.currentUserRole === this.componentRoles.PLANT_ADMIN && !this.loggedInPlantIds.includes(plant.id)) {
             this.toast?.showErrorToast("You do not have permission to edit this plant.");
             return;
        }

        this.selectedPlant = {
            id: plant.id, name: plant.name, emergencyContactNumber: plant.emergencyContactNumber,
            enabled: plant.enabled, plantTypeId: plant.plantType?.id ? Number(plant.plantType.id) : null,
            clusterId: plant.cluster?.id ? Number(plant.cluster.id) : null,
            opcoId: plant.opco?.id ? Number(plant.opco.id) : null,
            plantAdminId: (plant.plantAdmin?.id ?? plant.plantAdminId) ? Number(plant.plantAdmin?.id ?? plant.plantAdminId) : null // Use plantAdminId field if present
        };
        this.isEditModalOpen = true; this.editLoading = false;
    }
    closeEditModal(): void { this.isEditModalOpen = false; this.selectedPlant = null; }

    async submitEditForm(): Promise<void> {
        if (!this.editForm?.valid || !this.selectedPlant) {
             if (this.editForm) { Object.values(this.editForm.controls).forEach(control => control.markAsTouched()); }
             this.toast?.showErrorToast('Please fill all required fields.');
             return;
        }

        // Validate name contains only alphabets
        if (this.selectedPlant.name && !this.isValidName(this.selectedPlant.name)) {
            this.toast?.showErrorToast("Plant name should contain only alphabets.");
            return;
        }

        // Validate emergency contact number if provided
        if (this.selectedPlant.emergencyContactNumber && !this.isValidContactNumber(this.selectedPlant.emergencyContactNumber)) {
            this.toast?.showErrorToast("Emergency contact must be a 10-digit number.");
            return;
        }

        // Trim string values
        if (this.selectedPlant.name) {
            this.selectedPlant.name = this.selectedPlant.name.trim();
        }

        // Add authorization check again before submitting
        if(this.currentUserRole === this.componentRoles.PLANT_ADMIN && !this.loggedInPlantIds.includes(this.selectedPlant.id)) {
             this.toast?.showErrorToast("You do not have permission to save changes for this plant.");
             this.editLoading = false; // Ensure loading stops
             return;
        }


        this.editLoading = true;
        const updateData: Partial<EditPlantData> & { id?: number } = { // Ensure ID is optional if not needed in data part
            name: this.selectedPlant.name,
            emergencyContactNumber: this.selectedPlant.emergencyContactNumber,
            enabled: this.selectedPlant.enabled,
            plantTypeId: this.selectedPlant.plantTypeId,
            clusterId: this.selectedPlant.clusterId,
            opcoId: this.selectedPlant.opcoId,
            plantAdminId: this.selectedPlant.plantAdminId
        };
        // Remove ID from data if updateService doesn't expect it there
        // delete updateData.id;

        const updatePayload = {
             tableName: 'plant',
             id: this.selectedPlant.id,
             data: updateData,
             createdBy: this.loggedInAdminId // Add createdBy if updateService needs it
        };

        try {
            await this.update(updatePayload);
            this.toast?.showSuccessToast('Plant updated successfully!');
            this.closeEditModal();
            this.loadPlants(this.currentPage);
        } catch (error) {
            console.error("Error submitting plant update:", error);
             this.toast?.showErrorToast('Failed to update plant.');
        } finally { this.editLoading = false; }
    }

    async exportQRCode(plant: Plant) {
        // Check if Plant Admin is trying to export QR for a plant they don't have access to
        if (this.currentUserRole === this.componentRoles.PLANT_ADMIN && !this.loggedInPlantIds.includes(plant.id)) {
            this.toast?.showErrorToast("You do not have permission to export QR for this plant.");
            return;
        }

        // Set loading state for this specific plant
        this.exportingQRPlantId = plant.id;

        try {
            const response = await this.plantService.exportPlantQr({ plantId: plant.id });

            // If response is a string, treat it as the URL
            let url: string | undefined = undefined;
            if (typeof response === 'string') {
                url = response;
            } else if (response && typeof response.url === 'string') {
                url = response.url;
            }

            if (url) {
                // Sanitize the URL to remove any trailing '}' or unwanted characters
                let cleanUrl = url.replace(/[}]+$/, '');
                window.open(cleanUrl, '_blank');
            } else if (response && response.message) {
                this.toast?.showErrorToast(response.message);
            } else {
                this.toast?.showErrorToast('Could not generate QR Code export URL.');
            }
        } catch (error) {
            console.error("Error exporting QR code:", error);
            this.toast?.showErrorToast('Failed to export QR Code.');
        } finally {
            // Clear loading state
            this.exportingQRPlantId = null;
        }
    }

    openCreateModal(): void {
        // Super Admins should be the only ones allowed to create plants typically
        if (this.currentUserRole !== this.componentRoles.SUPER_ADMIN) {
            this.toast?.showErrorToast("You do not have permission to add new plants.");
            return;
        }
        this.newPlantData = this.getInitialNewPlantData();
        setTimeout(() => { this.createForm?.resetForm(this.getInitialNewPlantData()); }, 0);
        this.isCreateModalOpen = true; this.createLoading = false;
    }
    closeCreateModal(): void { this.isCreateModalOpen = false; }

    async submitCreateForm(): Promise<void> {
         // Add role check before submitting
         if (this.currentUserRole !== this.componentRoles.SUPER_ADMIN) {
            this.toast?.showErrorToast("You do not have permission to add new plants.");
            return;
        }
        if (this.createForm?.invalid) {
            Object.values(this.createForm.controls).forEach(control => control.markAsTouched());
             this.toast?.showErrorToast('Please fill all required fields.');
            return;
        }

        // Validate name contains only alphabets
        if (this.newPlantData.name && !this.isValidName(this.newPlantData.name)) {
            this.toast?.showErrorToast("Plant name should contain only alphabets.");
            return;
        }

        // Validate emergency contact number if provided
        if (this.newPlantData.emergencyContactNumber && !this.isValidContactNumber(this.newPlantData.emergencyContactNumber)) {
            this.toast?.showErrorToast("Emergency contact must be a 10-digit number.");
            return;
        }

        // Trim string values
        if (this.newPlantData.name) {
            this.newPlantData.name = this.newPlantData.name.trim();
        }
        this.createLoading = true;
        const payload: Partial<NewPlantData> = { ...this.newPlantData };
        try {
            await this.plantService.createPlant(payload);
            this.toast?.showSuccessToast('Plant added successfully!');
            this.closeCreateModal(); this.currentPage = 1; this.loadPlants(this.currentPage);
        } catch (error: any) {
            console.error("Error creating plant:", error);
             this.toast?.showErrorToast(error?.response?.data?.message || 'Failed to add plant.');
        } finally { this.createLoading = false; }
    }

    async loadPlants(page: number) {
        this.listLoading = true;
        const filterParams: string[] = [];

        // --- Apply Role-Based Filtering FIRST ---
        if (this.currentUserRole === this.componentRoles.PLANT_ADMIN) {
            if (this.loggedInPlantIds.length > 0) {
                 // If a specific plant ID is selected in the filter *by the Plant Admin*, use it.
                 // This allows them to filter *within* their assigned list.
                 if (this.filters.id && this.filters.id.length === 1 && this.loggedInPlantIds.includes(this.filters.id[0])) {
                      filterParams.push(`id||eq||${this.filters.id[0]}`);
                      console.log(`Plant Admin filtering by selected plant: ${this.filters.id[0]}`);
                 } else {
                      // Otherwise, filter by all their assigned plants
                      filterParams.push(`id||$in||${this.loggedInPlantIds.join(',')}`);
                      console.log(`Plant Admin filtering by all assigned plants: [${this.loggedInPlantIds.join(',')}]`);
                      // Reset the visual filter if it was invalid or set to multiple
                      if (this.filters.id && this.filters.id.length !== 1 || (this.filters.id && !this.loggedInPlantIds.includes(this.filters.id[0]))) {
                           this.filters.id = null;
                      }
                 }
            } else {
                // Plant Admin with no assigned plants - show nothing
                console.warn("Plant Admin has no plants assigned, showing empty list.");
                filterParams.push(`id||eq||-1`); // Condition that will likely match nothing
            }
        }
        // Super Admin doesn't need automatic plant filtering here, they use the filter modal if needed.

        // Apply other filters from the modal (excluding 'id' which is handled by role logic)
        if (this.filters.enabled !== null && this.filters.enabled !== '') filterParams.push(`enabled||eq||${this.filters.enabled}`);
        if (this.filters.name) filterParams.push(`name||$contL||${this.filters.name}`);
        if (this.filters.plantTypeId) filterParams.push(`plantTypeId||eq||${this.filters.plantTypeId}`);
        // The filter by plantAdminId might need backend adjustment if it requires a join
        if (this.filters.plantAdminId) filterParams.push(`plantAdminId||eq||${this.filters.plantAdminId}`);
        // --- End Filter Application ---


        const data = {
            page: page, limit: this.itemsPerPage,
            sort: `${this.filters.sortField || 'name'},${this.filters.sortDirection || 'ASC'}`,
            filter: filterParams,
            join: ['plantType', 'cluster', 'opco', 'plantAdmin'] // Ensure joins match display needs
        };
        console.log("API Request Params (loadPlants):", JSON.stringify(data, null, 2)); // Log final params

        try {
            const param = createAxiosConfig(data);
            const response = await this.plantService.getPlants(param);
             // Add plantAdminId directly to the plant object if it's not already there from the join
             this.plantList = (response?.data ?? []).map((plant: Plant) => ({
                 ...plant,
                 plantAdminId: plant.plantAdmin?.id ?? plant.plantAdminId // Ensure plantAdminId is available
             }));
            this.totalItems = response?.total ?? 0;
        } catch (error) {
            console.error("Error fetching plants:", error);
            this.plantList = []; this.totalItems = 0;
             this.toast?.showErrorToast('Failed to load plants.');
        } finally { this.listLoading = false; }
    }

    onPageChange(page: number) {
        if (this.currentPage === page || this.listLoading) return;
        this.currentPage = page; this.loadPlants(this.currentPage);
    }

    @ViewChild('filterForm') filterForm!: NgForm;

    applyFilters() {
        // Check if form is valid before applying filters
        if (this.filterForm && this.filterForm.invalid) {
            this.toast?.showErrorToast("Please correct the validation errors before applying filters.");
            return;
        }

        // Validate name contains only alphabets if provided
        if (this.filters.name && !this.isValidName(this.filters.name)) {
            this.toast?.showErrorToast("Plant name should contain only alphabets.");
            return;
        }

        // Trim string values to prevent whitespace-only searches
        if (this.filters.name) {
            this.filters.name = this.filters.name.trim();
        }

        // **Important for Plant Admin**: When they apply filters,
        // we need to ensure the `filters.id` (used for the base plant list)
        // doesn't conflict with specific filters they might have chosen.
        // If they chose a specific plantAdminId, plantTypeId, or name,
        // the base `id||$in` filter should still apply to limit the search *within* their plants.
        // The backend needs to handle combining these filters correctly (e.g., AND condition).
        this.currentPage = 1;
        this.loadPlants(this.currentPage);
        this.closeFilterModal();
    }

    // --- Validation Helper Methods ---
    private isValidName(name: string): boolean {
        const nameRegex = /^[a-zA-Z\s]*$/;
        return nameRegex.test(name);
    }

    // --- Helper to trim input fields ---
    trimInputField(formData: any, fieldName: string): void {
        if (formData && formData[fieldName] && typeof formData[fieldName] === 'string') {
            formData[fieldName] = formData[fieldName].trim();
        }
    }

    private isValidContactNumber(contactNumber: string): boolean {
        const contactRegex = /^[0-9]{10}$/;
        return contactRegex.test(contactNumber);
    }

    resetFilters() {
        this.filters = {
            name: null, plantTypeId: null, plantAdminId: null,
            id: null, // Reset the ID filter too
            enabled: 'true', sortDirection: 'ASC', sortField: 'name'
        };
        // Re-apply the base plant filter for Plant Admins after reset
        if (this.currentUserRole === this.componentRoles.PLANT_ADMIN && this.loggedInPlantIds.length > 0) {
             this.filters.id = this.loggedInPlantIds;
        }
        this.currentPage = 1;
        this.loadPlants(this.currentPage);
    }

    async onSwitchToggle(isEnabled: boolean, plant: Plant): Promise<void> {
         // Add role check
         if(this.currentUserRole === this.componentRoles.PLANT_ADMIN && !this.loggedInPlantIds.includes(plant.id)) {
              this.toast?.showErrorToast("You do not have permission to change the status of this plant.");
              // Revert UI immediately without waiting for API call failure
              const index = this.plantList.findIndex(p => p.id === plant.id);
              if (index !== -1) this.plantList[index] = { ...this.plantList[index], enabled: !isEnabled };
              this.cdr.detectChanges(); // Update UI
              return;
         }

        const originalState = plant.enabled; plant.enabled = isEnabled; // Optimistic update
        const updatePayload = {
             tableName: 'plant',
             id: plant.id,
             data: { enabled: isEnabled },
             createdBy: this.loggedInAdminId // Add createdBy if needed
        };
        try {
            await this.update(updatePayload);
            this.toast?.showSuccessToast('Plant status updated.');
        } catch (error) {
            console.error(`Error updating enabled status for plant ${plant.id}:`, error);
            this.toast?.showErrorToast('Failed to update plant status.');
            // Revert UI on error
            const index = this.plantList.findIndex(p => p.id === plant.id);
            if (index !== -1) this.plantList[index] = { ...this.plantList[index], enabled: originalState };
        }
    }

    async update(data: { tableName: string, id: number, data: any, createdBy?: number | null }): Promise<void> {
        try { await this.updateService.update(data); }
        catch (error) { console.error("Update service call failed:", error); throw error; }
    }

    getSortClass(key: string): string {
        return this.filters.sortField === key ? `sort-${this.filters.sortDirection?.toLowerCase()}` : '';
    }

    sortBy(field: string) {
        if (this.listLoading) return;
        if (this.filters.sortField === field) { this.filters.sortDirection = this.filters.sortDirection === 'ASC' ? 'DESC' : 'ASC'; }
        else { this.filters.sortField = field; this.filters.sortDirection = 'ASC'; }
        this.currentPage = 1; this.loadPlants(this.currentPage);
    }

    getCurrentListData(): Plant[] {
        return this.plantList;
    }

    async fetchAllFilteredPlants(): Promise<Plant[] | null> {
        this.listLoading = true; // Show loading during fetch all
        const filterParams: string[] = [];

        // --- Apply Role-Based Filtering FIRST ---
        if (this.currentUserRole === this.componentRoles.PLANT_ADMIN) {
            if (this.loggedInPlantIds.length > 0) {
                 if (this.filters.id && this.filters.id.length === 1 && this.loggedInPlantIds.includes(this.filters.id[0])) {
                      filterParams.push(`id||eq||${this.filters.id[0]}`);
                 } else {
                      filterParams.push(`id||$in||${this.loggedInPlantIds.join(',')}`);
                       if (this.filters.id && this.filters.id.length !== 1 || (this.filters.id && !this.loggedInPlantIds.includes(this.filters.id[0]))) {
                           this.filters.id = null; // Correct base filter state if inconsistent
                       }
                 }
            } else {
                filterParams.push(`id||eq||-1`); // Block data
            }
        }

        // Apply other filters
        if (this.filters.enabled !== null && this.filters.enabled !== '') filterParams.push(`enabled||eq||${this.filters.enabled}`);
        if (this.filters.name) filterParams.push(`name||$contL||${this.filters.name}`);
        if (this.filters.plantTypeId) filterParams.push(`plantTypeId||eq||${this.filters.plantTypeId}`);
        if (this.filters.plantAdminId) filterParams.push(`plantAdminId||eq||${this.filters.plantAdminId}`);

        const data = {
            limit: 10000,
            sort: `${this.filters.sortField || 'name'},${this.filters.sortDirection || 'ASC'}`,
            filter: filterParams,
            join: ['plantType', 'cluster', 'opco', 'plantAdmin']
        };

        try {
            const param = createAxiosConfig(data);
            console.log('API Request Params (Plant Download - All Data):', JSON.stringify(param, null, 2));
            const response = await this.plantService.getPlants(param);
            // Map to include plantAdminId consistently
            const plants = (response).map((plant: Plant) => ({
                ...plant,
                plantAdminId: plant.plantAdmin?.id ?? plant.plantAdminId
            }));
            return plants;
        } catch (error: any) {
            console.error("Error fetching all plants for download:", error);
            const errorMsg = error?.response?.data?.message || "Failed to retrieve full data for download.";
            this.toast?.showErrorToast(errorMsg);
            return null;
        } finally {
            this.listLoading = false; // Hide loading after fetch all
        }
    }

    async downloadExcel(type: 'current' | 'all') {
        if (this.isDownloadingExcel) return;

        this.isDownloadingExcel = true;
        this.downloadType = type;
        this.toast?.showSuccessToast(`Preparing download for ${type === 'current' ? 'current page' : 'all filtered'} plants...`);

        let dataToExport: Plant[] | null = null;

        try {
            if (type === 'all') {
                dataToExport = await this.fetchAllFilteredPlants();
            } else {
                dataToExport = this.getCurrentListData();
            }

            if (dataToExport === null) { return; }
            if (!dataToExport || dataToExport.length === 0) {
                this.toast?.showErrorToast(`No plants available to download.`);
                return;
            }

            console.log(`Fetched ${dataToExport.length} plants for Excel export (${type}).`);

            const dataForExcel = dataToExport.map(plant => ({
                'Plant ID': plant.id,
                'Name': plant.name,
                'Status': plant.enabled ? 'Active' : 'Inactive',
                'Emergency Contact': plant.emergencyContactNumber ?? '',
                'Plant Type': plant.plantType?.title ?? '',
                'Cluster': plant.cluster?.title ?? '',
                'OPCO': plant.opco?.title ?? '',
                'Plant Admin Name': `${plant.plantAdmin?.firstName ?? ''} ${plant.plantAdmin?.lastName ?? ''}`.trim(),
                'Plant Admin ID': plant.plantAdmin?.id ?? plant.plantAdminId ?? '', // Use field if available
                'Created At': plant.createdAt ? new Date(plant.createdAt).toLocaleString() : '',
                'Updated At': plant.updatedTimestamp ? new Date(plant.updatedTimestamp).toLocaleString() : '',
            }));

            const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(dataForExcel);
            const wb: XLSX.WorkBook = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(wb, ws, 'Plants');

            const dateStr = new Date().toISOString().slice(0, 10);
            const typeStr = type === 'current' ? `Page${this.currentPage}` : 'All';
            const fileName = `Plants_${typeStr}_${dateStr}.xlsx`;

            XLSX.writeFile(wb, fileName);
            this.toast?.showSuccessToast(`Excel file download started (${type}).`);

        } catch (error) {
            console.error(`Error generating Excel file (${type}):`, error);
            this.toast?.showErrorToast(`An error occurred while generating the Excel file (${type}).`);
        } finally {
            this.isDownloadingExcel = false;
            this.downloadType = null;
        }
    }

}