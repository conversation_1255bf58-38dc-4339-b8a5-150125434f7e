.card-body{
    height: 70vh;
    overflow: auto;
}

.table-container {
    width: 100%;
    overflow-x: auto;
}

.table-responsive {
    overflow-x: auto;
    white-space: nowrap;
    max-width: 100%;
}

table {
    min-width: 900px; /* Adjusted for the new three-column structure */
    border-collapse: collapse;
}

.table-header th {
    font-size: 12px;
    background-color: #0B74B0 !important;
    color: white !important;
    text-align: center;
}

/* Column widths */
.col-actions {
    width: 20%;
}

.col-status {
    width: 15%;
}

.col-details {
    width: 65%;
    max-width: 200px; /* Further reduced max-width */
}

/* Filter button */
.filter-button {
    width: 35px;
    cursor: pointer;
}

td {
    font-size: 12px;
}

i.edit {
    font-size: 12px;
}

.actions {
    text-align: center !important;
    vertical-align: middle !important;
}

/* Button text color */
.adani-btn {
    color: white !important;
}

/* Ensure Export QR button text is white */
.export-qr-btn,
.export-qr-btn:hover,
.export-qr-btn:active,
.export-qr-btn:focus {
    color: white !important;
}

.white-text,
.white-text i,
.white-text span {
    color: white !important;
}

.edit-btn,
.edit-btn:hover,
.edit-btn:active,
.edit-btn:focus {
    color: white !important;
}

.status-cell {
    text-align: center !important;
    vertical-align: middle !important;
}

.details-cell {
    max-width: 200px;
    vertical-align: middle !important;
    word-wrap: break-word;
    overflow-wrap: break-word;
    white-space: normal;
    padding: 8px; /* Add consistent padding */
}

/* Styling for plant details */
.details-container {
    display: table;
    width: 100%;
    text-align: left;
    padding: 5px;
    border-spacing: 0 8px;
    table-layout: fixed; /* Ensure consistent column widths */
}

.label-value {
    margin-bottom: 8px;
    line-height: 1.5;
    display: flex;
    align-items: baseline;
    padding: 2px 0;
    border-bottom: 1px dashed #eee;
    padding-bottom: 8px;
    justify-content: flex-start; /* Ensure content is left-aligned */
}

/* Space between label and value is maintained with padding on value-text */

.label-value:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.label-value strong {
    font-weight: 500;
    color: #777;
    margin-right: 8px; /* Increased spacing between label and value */
    min-width: 140px; /* Increased width to ensure consistent alignment */
    width: 140px; /* Fixed width to ensure all labels align properly */
    font-size: 12px;
    display: inline-block;
    vertical-align: top;
}

.value-text {
    font-weight: 600;
    color: #222;
    font-size: 12px;
    display: inline-block;
    word-break: break-word;
    overflow-wrap: break-word;
    flex: 1;
    letter-spacing: normal;
    line-height: 1.5;
    padding-left: 0; /* Removed padding since we have fixed width labels */
    text-align: left; /* Ensure all values are left-aligned */
}

/* Style for bold text in table cells to match value-text */
td b,
td strong,
td span:not(.info-label) {
    font-weight: 600;
    color: #222;
    font-size: 12px;
    letter-spacing: normal;
    line-height: 1.5;
}

/* Add bottom border to table rows */
.table-bordered tbody tr {
    border-bottom: 1px dashed #eee;
}

/* Styling for modal tables */
.modal-body table {
    width: 100%;
    border-collapse: collapse;
}

.modal-body table th {
    font-weight: 600;
    background-color: #f5f7fa;
    color: #333;
    text-align: center;
    padding: 8px;
}

.modal-body table td {
    font-weight: 500;
    padding: 8px;
    border-bottom: 1px dashed #eee;
}