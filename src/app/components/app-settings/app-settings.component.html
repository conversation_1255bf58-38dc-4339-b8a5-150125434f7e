<app-toast-message></app-toast-message> <!-- Add this line -->
<div class="container mt-4">
  <h2>App Settings</h2>
  <hr>

  <div *ngIf="loading" class="text-center my-5">
    <div class="spinner-border text-primary" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
  </div>

  <form *ngIf="!loading && settingsForm && !isPlantAdmin()" [formGroup]="settingsForm" (ngSubmit)="submitForm()">

    <!-- Settings Grid: All fields except About Us Link Content, Terms & Conditions Content, Privacy Policy Content -->
    <div class="settings-grid mb-4">
      <!-- App Name -->
      <div class="settings-grid-item" *ngIf="isGroupAdmin()">
        <label for="appName" class="form-label">App Name *</label>
        <input type="text" class="form-control" id="appName" formControlName="appName" placeholder="App Name"
               [class.is-invalid]="isInvalid('appName')">
        <div *ngIf="isInvalid('appName')" class="invalid-feedback">
          {{ getErrorMessage('appName') }}
        </div>
      </div>
      <!-- Version -->
      <div class="settings-grid-item" *ngIf="isGroupAdmin()">
        <label for="version" class="form-label">Version *</label>
        <input type="text" class="form-control" id="version" formControlName="version" placeholder="Version"
               [class.is-invalid]="isInvalid('version')">
        <div *ngIf="isInvalid('version')" class="invalid-feedback">
          {{ getErrorMessage('version') }}
        </div>
      </div>
      <!-- Share App Message -->
      <div class="settings-grid-item" *ngIf="isGroupAdmin()">
        <label for="shareAppMessage" class="form-label">Share App Message *</label>
        <textarea class="form-control" id="shareAppMessage" formControlName="shareAppMessage" rows="3" placeholder="Share app message"
                  [class.is-invalid]="isInvalid('shareAppMessage')"></textarea>
        <div *ngIf="isInvalid('shareAppMessage')" class="invalid-feedback">
          {{ getErrorMessage('shareAppMessage') }}
        </div>
      </div>
      <!-- iOS Version -->
      <div class="settings-grid-item" *ngIf="isGroupAdmin()">
        <label for="iosVersion" class="form-label">iOS Version</label>
        <input type="text" class="form-control" id="iosVersion" formControlName="iosVersion" placeholder="iOS Version"
               [class.is-invalid]="isInvalid('iosVersion')">
        <div *ngIf="isInvalid('iosVersion')" class="invalid-feedback">
          {{ getErrorMessage('iosVersion') }}
        </div>
      </div>
      <!-- Valid Versions -->
      <div class="settings-grid-item settings-grid-item-span2" *ngIf="isGroupAdmin()">
        <label class="form-label">Valid Versions</label>
        <div class="tag-container">
          <span *ngFor="let versionControl of validVersions.controls; let i = index" class="badge bg-primary me-1 p-2">
            {{ versionControl.value }}
            <button type="button" class="btn-close btn-close-white ms-1" aria-label="Remove Version" (click)="handleVersionClose(i)"></button>
          </span>
          <input #versionInput
                 *ngIf="versionInputVisible"
                 type="text"
                 class="form-control form-control-sm d-inline-block input-new-tag"
                 [(ngModel)]="inputValue"
                 [ngModelOptions]="{standalone: true}"
                 (blur)="handleVersionInputConfirm()"
                 (keydown.enter)="handleVersionInputConfirm(); $event.preventDefault()"
                 placeholder="New Version">
          <button *ngIf="!versionInputVisible"
                  type="button"
                  class="btn btn-sm btn-outline-primary button-new-tag"
                  (click)="showVersionInputMethod()">
            + Version
          </button>
        </div>
      </div>
      <!-- Play Store Link -->
      <div class="settings-grid-item" *ngIf="isGroupAdmin()">
        <label for="playstoreLink" class="form-label">Play Store Link *</label>
        <input type="url" class="form-control" id="playstoreLink" formControlName="playstoreLink" placeholder="Play Store Link"
               [class.is-invalid]="isInvalid('playstoreLink')">
        <div *ngIf="isInvalid('playstoreLink')" class="invalid-feedback">
          {{ getErrorMessage('playstoreLink') }}
        </div>
      </div>
      <!-- App Store Link -->
      <div class="settings-grid-item" *ngIf="isGroupAdmin()">
        <label for="appstoreLink" class="form-label">App Store Link *</label>
        <input type="url" class="form-control" id="appstoreLink" formControlName="appstoreLink" placeholder="App Store Link"
               [class.is-invalid]="isInvalid('appstoreLink')">
        <div *ngIf="isInvalid('appstoreLink')" class="invalid-feedback">
          {{ getErrorMessage('appstoreLink') }}
        </div>
      </div>
      <!-- Contact Mail -->
      <div class="settings-grid-item" *ngIf="isSuperAdmin()">
        <label for="contactUsEmail" class="form-label">Contact Mail *</label>
        <input type="email" class="form-control" id="contactUsEmail" formControlName="contactUsEmail" placeholder="Email"
               [class.is-invalid]="isInvalid('contactUsEmail')">
        <div *ngIf="isInvalid('contactUsEmail')" class="invalid-feedback">
           {{ getErrorMessage('contactUsEmail') }}
        </div>
      </div>
      <!-- Contact Number -->
      <div class="settings-grid-item" *ngIf="isSuperAdmin()">
        <label for="contactNumber" class="form-label">Contact Number *</label>
        <input type="text" class="form-control" id="contactNumber" formControlName="contactNumber" placeholder="Contact number"
               [class.is-invalid]="isInvalid('contactNumber')">
         <div *ngIf="isInvalid('contactNumber')" class="invalid-feedback">
           {{ getErrorMessage('contactNumber') }}
        </div>
      </div>
      <!-- Whatsapp Number -->
      <div class="settings-grid-item" *ngIf="isSuperAdmin()">
        <label for="whatappNumber" class="form-label">Whatsapp Number *</label>
        <input type="text" class="form-control" id="whatappNumber" formControlName="whatappNumber" placeholder="Whatsapp number"
               [class.is-invalid]="isInvalid('whatappNumber')">
        <div *ngIf="isInvalid('whatappNumber')" class="invalid-feedback">
           {{ getErrorMessage('whatappNumber') }}
        </div>
      </div>
      <!-- Meter -->
      <div class="settings-grid-item" *ngIf="isSuperAdmin()">
        <label for="meter" class="form-label">Meter *</label>
        <input type="number" class="form-control" id="meter" formControlName="meter" placeholder="Meter" min="1"
               [class.is-invalid]="isInvalid('meter')">
        <div *ngIf="isInvalid('meter')" class="invalid-feedback">
          {{ getErrorMessage('meter') }}
        </div>
      </div>
      <!-- Crisis Emails -->
      <div class="settings-grid-item settings-grid-item-span2" *ngIf="isSuperAdmin()">
        <label class="form-label">Crisis Emails</label>
        <div class="tag-container">
          <span *ngFor="let emailControl of crisisEmails.controls; let i = index" class="badge bg-danger me-1 p-2">
            {{ emailControl.value }}
            <button type="button" class="btn-close btn-close-white ms-1" aria-label="Remove Crisis Email" (click)="handleCrisisEmailClose(i)"></button>
          </span>
          <input #crisisEmailInput
                 *ngIf="crisisEmailInputVisible"
                 type="email"
                 class="form-control form-control-sm d-inline-block input-new-tag"
                 [(ngModel)]="inputValue"
                 [ngModelOptions]="{standalone: true}"
                 (blur)="handleCrisisEmailInputConfirm()"
                 (keydown.enter)="handleCrisisEmailInputConfirm(); $event.preventDefault()"
                 placeholder="New Email">
          <button *ngIf="!crisisEmailInputVisible"
                  type="button"
                  class="btn btn-sm btn-outline-danger button-new-tag"
                  (click)="showCrisisEmailInputMethod()">
            + Crisis Email
          </button>
        </div>
      </div>
      <!-- Crisis CC Emails -->
      <div class="settings-grid-item settings-grid-item-span2" *ngIf="isSuperAdmin()">
        <label class="form-label">Crisis CC Emails</label>
        <div class="tag-container">
          <span *ngFor="let ccEmailControl of crisisCCEmails.controls; let i = index" class="badge bg-warning text-dark me-1 p-2">
            {{ ccEmailControl.value }}
            <button type="button" class="btn-close ms-1" aria-label="Remove Crisis CC Email" (click)="handleCrisisCCEmailClose(i)"></button>
          </span>
          <input #crisisCCEmailInput
                 *ngIf="crisisCCEmailInputVisible"
                 type="email"
                 class="form-control form-control-sm d-inline-block input-new-tag"
                 [(ngModel)]="inputValue"
                 [ngModelOptions]="{standalone: true}"
                 (blur)="handleCrisisCCEmailInputConfirm()"
                 (keydown.enter)="handleCrisisCCEmailInputConfirm(); $event.preventDefault()"
                 placeholder="New Email">
          <button *ngIf="!crisisCCEmailInputVisible"
                  type="button"
                  class="btn btn-sm btn-outline-warning text-dark button-new-tag"
                  (click)="showCrisisCCEmailInputMethod()">
            + Crisis CC Email
          </button>
        </div>
      </div>
      <!-- SOS Radius -->
      <div class="settings-grid-item">
        <label for="sosRadius" class="form-label">SOS Radius *</label>
        <input type="number" class="form-control" id="sosRadius" formControlName="sosRadius" placeholder="SOS Radius"
               [class.is-invalid]="isInvalid('sosRadius')">
        <div *ngIf="isInvalid('sosRadius')" class="invalid-feedback">
          {{ getErrorMessage('sosRadius') }}
        </div>
      </div>
      <!-- Referral Amount -->
      <div class="settings-grid-item">
        <label for="referralAmount" class="form-label">Referral Amount *</label>
        <input type="number" class="form-control" id="referralAmount" formControlName="referralAmount" placeholder="Referral Amount"
               [class.is-invalid]="isInvalid('referralAmount')">
        <div *ngIf="isInvalid('referralAmount')" class="invalid-feedback">
          {{ getErrorMessage('referralAmount') }}
        </div>
      </div>
      <!-- Reward Amount -->
      <div class="settings-grid-item">
        <label for="rewardAmount" class="form-label">Reward Amount *</label>
        <input type="number" class="form-control" id="rewardAmount" formControlName="rewardAmount" placeholder="Reward Amount"
               [class.is-invalid]="isInvalid('rewardAmount')">
        <div *ngIf="isInvalid('rewardAmount')" class="invalid-feedback">
          {{ getErrorMessage('rewardAmount') }}
        </div>
      </div>
      <!-- Observation Reward Amount -->
      <div class="settings-grid-item">
        <label for="observationRewardAmount" class="form-label">Observation Reward Amount *</label>
        <input type="number" class="form-control" id="observationRewardAmount" formControlName="observationRewardAmount" placeholder="Observation Reward Amount"
               [class.is-invalid]="isInvalid('observationRewardAmount')">
        <div *ngIf="isInvalid('observationRewardAmount')" class="invalid-feedback">
          {{ getErrorMessage('observationRewardAmount') }}
        </div>
      </div>
      <!-- Safe Act Amount -->
      <div class="settings-grid-item">
        <label for="safeActAmount" class="form-label">Safe Act Amount *</label>
        <input type="number" class="form-control" id="safeActAmount" formControlName="safeActAmount" placeholder="Safe Act Amount"
               [class.is-invalid]="isInvalid('safeActAmount')">
        <div *ngIf="isInvalid('safeActAmount')" class="invalid-feedback">
          {{ getErrorMessage('safeActAmount') }}
        </div>
      </div>
      <!-- Unsafe Act Amount -->
      <div class="settings-grid-item">
        <label for="unSafeActAmount" class="form-label">Unsafe Act Amount *</label>
        <input type="number" class="form-control" id="unSafeActAmount" formControlName="unSafeActAmount" placeholder="Unsafe Act Amount"
               [class.is-invalid]="isInvalid('unSafeActAmount')">
        <div *ngIf="isInvalid('unSafeActAmount')" class="invalid-feedback">
          {{ getErrorMessage('unSafeActAmount') }}
        </div>
      </div>
      <!-- Unsafe Act Add With Fixit Amount -->
      <div class="settings-grid-item">
        <label for="unSafeActAddWithFixitAmount" class="form-label">Unsafe Act Add With Fixit Amount *</label>
        <input type="number" class="form-control" id="unSafeActAddWithFixitAmount" formControlName="unSafeActAddWithFixitAmount" placeholder="Unsafe Act Add With Fixit Amount"
               [class.is-invalid]="isInvalid('unSafeActAddWithFixitAmount')">
        <div *ngIf="isInvalid('unSafeActAddWithFixitAmount')" class="invalid-feedback">
          {{ getErrorMessage('unSafeActAddWithFixitAmount') }}
        </div>
      </div>
      <!-- Unsafe Act FiFi Edit Amount -->
      <div class="settings-grid-item">
        <label for="unSafeActfiFiEditAmount" class="form-label">Unsafe Act FiFi Edit Amount *</label>
        <input type="number" class="form-control" id="unSafeActfiFiEditAmount" formControlName="unSafeActfiFiEditAmount" placeholder="Unsafe Act FiFi Edit Amount"
               [class.is-invalid]="isInvalid('unSafeActfiFiEditAmount')">
        <div *ngIf="isInvalid('unSafeActfiFiEditAmount')" class="invalid-feedback">
          {{ getErrorMessage('unSafeActfiFiEditAmount') }}
        </div>
      </div>
      <!-- Unsafe Act Fixit Amount -->
      <div class="settings-grid-item">
        <label for="unSafeActFixitAmount" class="form-label">Unsafe Act Fixit Amount *</label>
        <input type="number" class="form-control" id="unSafeActFixitAmount" formControlName="unSafeActFixitAmount" placeholder="Unsafe Act Fixit Amount"
               [class.is-invalid]="isInvalid('unSafeActFixitAmount')">
        <div *ngIf="isInvalid('unSafeActFixitAmount')" class="invalid-feedback">
          {{ getErrorMessage('unSafeActFixitAmount') }}
        </div>
      </div>
      <!-- In Event -->
      <div class="settings-grid-item">
        <label for="inEvent" class="form-label">In Event</label>
        <div class="form-check form-switch d-flex align-items-center">
          <input class="form-check-input" type="checkbox" id="inEvent" formControlName="inEvent">
        </div>
      </div>
      <!-- Is Reward -->
      <div class="settings-grid-item">
        <label for="isReward" class="form-label">Is Reward</label>
        <div class="form-check form-switch d-flex align-items-center">
          <input class="form-check-input" type="checkbox" id="isReward" formControlName="isReward">
        </div>
      </div>
    </div>
    <!-- End Settings Grid -->

    <!-- Rich Text Editors -->
    <div class="row mb-3" *ngIf="isSuperAdmin()">
      <label for="aboutUsLink" class="col-sm-3 col-form-label">About Us Link Content *</label>
      <div class="col-sm-9">
         <quill-editor id="aboutUsLink" formControlName="aboutUsLink" [modules]="quillConfig" placeholder="Enter content here..."
                       [ngClass]="{'is-invalid': isInvalid('aboutUsLink')}" class="fixed-editor-height"></quill-editor>
         <div *ngIf="isInvalid('aboutUsLink')" class="invalid-feedback d-block"> <!-- d-block needed for quill -->
           {{ getErrorMessage('aboutUsLink') }}
        </div>
      </div>
    </div>

    <div class="row mb-3" *ngIf="isSuperAdmin()">
      <label for="termsAndConditionsLink" class="col-sm-3 col-form-label">Terms & Conditions Content *</label>
      <div class="col-sm-9">
         <quill-editor id="termsAndConditionsLink" formControlName="termsAndConditionsLink" [modules]="quillConfig" placeholder="Enter content here..."
                       [ngClass]="{'is-invalid': isInvalid('termsAndConditionsLink')}" class="fixed-editor-height"></quill-editor>
         <div *ngIf="isInvalid('termsAndConditionsLink')" class="invalid-feedback d-block">
           {{ getErrorMessage('termsAndConditionsLink') }}
        </div>
      </div>
    </div>

     <div class="row mb-3" *ngIf="isSuperAdmin()">
      <label for="privacyPolicyLink" class="col-sm-3 col-form-label">Privacy Policy Content *</label>
      <div class="col-sm-9">
         <quill-editor id="privacyPolicyLink" formControlName="privacyPolicyLink" [modules]="quillConfig" placeholder="Enter content here..."
                       [ngClass]="{'is-invalid': isInvalid('privacyPolicyLink')}" class="fixed-editor-height"></quill-editor>
         <div *ngIf="isInvalid('privacyPolicyLink')" class="invalid-feedback d-block">
           {{ getErrorMessage('privacyPolicyLink') }}
        </div>
      </div>
    </div>


    <!-- Action Buttons -->
    <div class="row">
      <div class="col-sm-9 offset-sm-3">
        <button type="submit" class="btn btn-primary me-2" [disabled]="loading || settingsForm.invalid">
          <span *ngIf="loading" class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
          Save
        </button>
        <button type="button" class="btn btn-secondary" (click)="resetForm()" [disabled]="loading">
          Reset
        </button>
      </div>
    </div>

  </form>
</div>

<!-- Settings Grid CSS -->
<style>
.settings-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1.5rem;
}
.settings-grid-item {
  display: flex;
  flex-direction: column;
  min-width: 0;
}
.settings-grid-item-span2 {
  grid-column: span 2;
}
@media (max-width: 1200px) {
  .settings-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
@media (max-width: 768px) {
  .settings-grid {
    grid-template-columns: 1fr;
  }
}
</style>