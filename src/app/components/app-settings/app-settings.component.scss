.fixed-editor-height {
  height: 250px;
  overflow: auto;
}
// Make tag input fields smaller and align buttons
.tag-container {
    .input-new-tag {
      width: 150px; // Adjust as needed
      vertical-align: middle; // Align better with badges/buttons
    }
  
    .button-new-tag {
      vertical-align: middle; // Align better with badges/inputs
      margin-left: 5px;
    }
  
    .badge {
      vertical-align: middle; // Align badges nicely
      .btn-close {
        font-size: 0.7em; // Smaller close button
        padding: 0.1em 0.25em; // Adjust padding
        vertical-align: middle;
        line-height: 1; // Ensure proper alignment
        position: relative; // Fine-tune position if needed
        top: -1px;
      }
    }
  }
  
  // Add border to quill editor when invalid, like other form controls
  quill-editor.is-invalid .ql-container {
    border: 1px solid #dc3545; // Bootstrap's danger color
  }
  
  quill-editor.is-invalid .ql-toolbar {
      border-top-left-radius: var(--bs-border-radius);
      border-top-right-radius: var(--bs-border-radius);
      border: 1px solid #dc3545; // Bootstrap's danger color
      border-bottom: 0; // Bottom border is on the container
  }
  
  // Ensure invalid feedback shows for Quill
  .invalid-feedback.d-block {
    display: block !important; // Override potential quill styles hiding it
  }