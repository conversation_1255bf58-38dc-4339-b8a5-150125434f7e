import { Component, OnInit, ElementRef, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup, Validators, FormArray, ReactiveFormsModule, FormsModule } from '@angular/forms';
import { AppSettingsService } from '../../services/app-settings/app-settings.service'; // Adjust path if needed
import { AppSettingsResponse } from '../../model/app-settings.model';
import { QuillModule, QuillModules } from 'ngx-quill';
import { CommonModule } from '@angular/common';
import { UpdateService } from '../../services/update/update.service';
import { ToastMessageComponent } from '../../shared/toast-message/toast-message.component'; // Ensure ToastMessageComponent is imported
import { AuthService } from '../../services/auth.service'; // Import AuthService

@Component({
  selector: 'app-app-settings',
  standalone: true, // Make sure it's standalone if using Angular 14+ without modules
  imports: [
    ReactiveFormsModule,
    CommonModule,
    QuillModule,
    FormsModule,
    ToastMessageComponent // Add ToastMessageComponent here if not already present globally
  ],
  templateUrl: './app-settings.component.html',
  styleUrls: ['./app-settings.component.scss']
})
export class AppSettingsComponent implements OnInit {

  settingsForm!: FormGroup;
  loading = false;
  initialSettings: any = {};
  adminsRoleId: number | null = null; // Property to store the user's adminsRoleId

  // Tag input state variables
  versionInputVisible = false;
  crisisEmailInputVisible = false;
  crisisCCEmailInputVisible = false;
  inputValue = '';

  // ViewChild references
  @ViewChild('versionInput') versionInput!: ElementRef<HTMLInputElement>;
  @ViewChild('crisisEmailInput') crisisEmailInput!: ElementRef<HTMLInputElement>;
  @ViewChild('crisisCCEmailInput') crisisCCEmailInput!: ElementRef<HTMLInputElement>;
  @ViewChild(ToastMessageComponent) toast!: ToastMessageComponent; // Ensure ViewChild for toast

  // Quill config
  quillConfig: QuillModules = {
    toolbar: [
        ['bold', 'italic', 'underline', 'strike'],        // toggled buttons
        ['blockquote', 'code-block'],
        [{ 'header': 1 }, { 'header': 2 }],               // custom button values
        [{ 'list': 'ordered'}, { 'list': 'bullet' }],
        [{ 'script': 'sub'}, { 'script': 'super' }],      // superscript/subscript
        [{ 'indent': '-1'}, { 'indent': '+1' }],          // outdent/indent
        [{ 'direction': 'rtl' }],                         // text direction
        [{ 'size': ['small', false, 'large', 'huge'] }],  // custom dropdown
        [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
        [{ 'color': [] }, { 'background': [] }],          // dropdown with defaults from theme
        [{ 'font': [] }],
        [{ 'align': [] }],
        ['clean'],                                         // remove formatting button
        ['link', 'image', 'video']                         // link and image, video
      ]
  };


  constructor(
    private fb: FormBuilder,
    private appSettingsService: AppSettingsService, // Inject the service
    private updateService: UpdateService, // Assuming the same service is used for updating
    private authService: AuthService // Inject AuthService
  ) { }

  ngOnInit(): void {
    this.adminsRoleId = this.authService.getAdminsRoleId(); // Get user's adminsRoleId on init
    this.initializeForm();
    this.getSettings();
  }

  initializeForm(): void {
    this.settingsForm = this.fb.group({
      id: [null],
      shareAppMessage: ['', this.isGroupAdmin() ? Validators.required : []], // Conditionally required for group admin
      contactUsEmail: ['', this.isSuperAdmin() ? [Validators.required, Validators.email] : []], // Conditionally required for super admin
      contactNumber: ['', this.isSuperAdmin() ? Validators.required : []], // Conditionally required for super admin
      whatappNumber: ['', this.isSuperAdmin() ? Validators.required : []], // Conditionally required for super admin
      meter: [null, this.isSuperAdmin() ? [Validators.required, Validators.min(1)] : []], // Conditionally required for super admin
      iosVersion: [''], // Optional
      playstoreLink: ['', this.isGroupAdmin() ? Validators.required : []], // New field, conditionally required for group admin
      appstoreLink: ['', this.isGroupAdmin() ? Validators.required : []], // New field, conditionally required for group admin
      version: [''], // New field for Global Admin
      appName: [''], // New field for Global Admin
  
      // --- Ensure these have no array-level validators ---
      validVersions: this.fb.array([]),        // No validators here - Optional, but elements will be required for group admin
      crisisEmails: this.fb.array([]),         // No validators here - Optional, but elements will be required for super admin
      crisisCCEmails: this.fb.array([]),       // No validators here - Optional, but elements will be required for super admin
      // ----------------------------------------------------
  
      aboutUsLink: ['', this.isSuperAdmin() ? Validators.required : []], // Conditionally required for super admin
      termsAndConditionsLink: ['', this.isSuperAdmin() ? Validators.required : []], // Conditionally required for super admin
      privacyPolicyLink: ['', this.isSuperAdmin() ? Validators.required : []], // Conditionally required for super admin
      sosRadius: [0],
      referralAmount: [0],
      rewardAmount: [0],
      observationRewardAmount: [0],
      safeActAmount: [0],
      unSafeActAmount: [0],
      unSafeActAddWithFixitAmount: [0],
      unSafeActfiFiEditAmount: [0],
      unSafeActFixitAmount: [0],
      inEvent: [false],
      isReward: [false],
      priority: [null],
    });
  }

  async getSettings() {
    this.loading = true;
    try {
      // Assuming getSettings returns the object with all fields including 'id'
      const response: AppSettingsResponse[] = await this.appSettingsService.getSettings({}); // Or specific ID if needed
      if (response && response.length > 0) {
        this.initialSettings = { ...response[0] }; // Store original data (deep copy if nested objects exist)

        // Create FormControls for FormArrays before patching, applying conditional validators
        this.setFormArray('validVersions', response[0].validVersions || [], this.isGroupAdmin() ? [Validators.required] : []);
        this.setFormArray('crisisEmails', response[0].crisisEmails || [], this.isSuperAdmin() ? [Validators.required, Validators.email] : []); // Add email validation to array controls
        this.setFormArray('crisisCCEmails', response[0].crisisCCEmails || [], this.isSuperAdmin() ? [Validators.required, Validators.email] : []); // Add email validation

        // Patch the rest of the form values (including the ID)
        this.settingsForm.patchValue(response[0]);

      } else {
         console.warn("No settings received from the service.");
         // Handle case where no settings are found (e.g., initialize with defaults)
      }
    } catch (error) {
      console.error('Error fetching settings:', error);
      alert('Failed to load settings. Please try again.'); // User feedback
    } finally {
      this.loading = false;
    }
  }

  // Helper to populate FormArray from data with optional validators
  setFormArray(arrayName: string, items: string[], validators: any[] = []): void { // Changed default to empty array
      const formArray = this.settingsForm.get(arrayName) as FormArray;
      formArray.clear(); // Remove existing controls
      items.forEach(item => formArray.push(this.fb.control(item, validators)));
  }

  // --- FormArray Getters ---
  get validVersions(): FormArray { return this.settingsForm.get('validVersions') as FormArray; }
  get crisisEmails(): FormArray { return this.settingsForm.get('crisisEmails') as FormArray; }
  get crisisCCEmails(): FormArray { return this.settingsForm.get('crisisCCEmails') as FormArray; }

  // --- Tag Input Logic (Version) ---
  showVersionInputMethod(): void {
    this.versionInputVisible = true;
    this.inputValue = ''; // Clear previous input
    setTimeout(() => this.versionInput?.nativeElement?.focus(), 0);
  }
  handleVersionInputConfirm(): void {
    const value = this.inputValue.trim();
    if (value) {
      // Optional: Check for duplicates
      if (!this.validVersions.controls.some(control => control.value === value)) {
         this.validVersions.push(this.fb.control(value, this.isGroupAdmin() ? Validators.required : [])); // Conditionally required
      } else {
          console.warn(`Version "${value}" already exists.`);
          // Optionally show a user message
      }
    }
    this.inputValue = '';
    this.versionInputVisible = false;
  }
  handleVersionClose(index: number): void { this.validVersions.removeAt(index); }

  // --- Tag Input Logic (Crisis Email) ---
  showCrisisEmailInputMethod(): void {
    this.crisisEmailInputVisible = true;
    this.inputValue = '';
    setTimeout(() => this.crisisEmailInput?.nativeElement?.focus(), 0);
  }
  handleCrisisEmailInputConfirm(): void {
    const value = this.inputValue.trim();
    const emailPattern = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    if (value && emailPattern.test(value)) {
      if (!this.crisisEmails.controls.some(control => control.value === value)) {
         this.crisisEmails.push(this.fb.control(value, this.isSuperAdmin() ? [Validators.required, Validators.email] : [Validators.email])); // Conditionally required
      } else {
         console.warn(`Crisis email "${value}" already exists.`);
      }
    } else if (value) {
      console.warn('Invalid email format entered for Crisis Email:', value);
      alert(`"${value}" is not a valid email format.`); // User feedback
    }
    this.inputValue = ''; // Clear input regardless of success
    // Keep input visible if the format was invalid, allowing correction
    this.crisisEmailInputVisible = !(value && emailPattern.test(value));
    if (!this.crisisEmailInputVisible) {
        setTimeout(() => this.crisisEmailInput?.nativeElement?.focus(), 0); // Re-focus if staying visible
    }
  }
  handleCrisisEmailClose(index: number): void { this.crisisEmails.removeAt(index); }

   // --- Tag Input Logic (Crisis CC Email) ---
   showCrisisCCEmailInputMethod(): void {
     this.crisisCCEmailInputVisible = true;
     this.inputValue = '';
     setTimeout(() => this.crisisCCEmailInput?.nativeElement?.focus(), 0);
   }
   handleCrisisCCEmailInputConfirm(): void {
     const value = this.inputValue.trim();
     const emailPattern = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
     if (value && emailPattern.test(value)) {
        if (!this.crisisCCEmails.controls.some(control => control.value === value)) {
            this.crisisCCEmails.push(this.fb.control(value, this.isSuperAdmin() ? [Validators.required, Validators.email] : [Validators.email])); // Conditionally required
        } else {
           console.warn(`Crisis CC email "${value}" already exists.`);
        }
     } else if (value) {
       console.warn('Invalid email format entered for Crisis CC Email:', value);
       alert(`"${value}" is not a valid email format.`); // User feedback
     }
     this.inputValue = '';
     // Keep input visible if the format was invalid, allowing correction
     this.crisisCCEmailInputVisible = !(value && emailPattern.test(value));
     if (!this.crisisCCEmailInputVisible) {
        setTimeout(() => this.crisisCCEmailInput?.nativeElement?.focus(), 0); // Re-focus if staying visible
     }
   }
   handleCrisisCCEmailClose(index: number): void { this.crisisCCEmails.removeAt(index); }

  // --- Form Submission ---
  async submitForm() {
    this.settingsForm.markAllAsTouched(); // Mark fields to show validation errors

    // Check specifically for FormArray validation errors (e.g., invalid email within the array)
    if (this.crisisEmails.invalid) {
        console.error('Invalid Crisis Emails found:', this.crisisEmails.errors, this.crisisEmails.controls.filter(c => c.invalid).map(c => c.value));
        alert('One or more Crisis Emails are invalid. Please correct them.');
        return; // Stop submission
    }
     if (this.crisisCCEmails.invalid) {
        console.error('Invalid Crisis CC Emails found:', this.crisisCCEmails.errors, this.crisisCCEmails.controls.filter(c => c.invalid).map(c => c.value));
        alert('One or more Crisis CC Emails are invalid. Please correct them.');
        return; // Stop submission
    }

    if (this.settingsForm.invalid) {
      console.error('Form is invalid. Errors:', this.settingsForm.errors);
       // Log specific invalid controls for easier debugging
       Object.keys(this.settingsForm.controls).forEach(key => {
         const controlErrors = this.settingsForm.get(key)?.errors;
         if (controlErrors) {
           console.error(`Control '${key}' has errors: `, controlErrors);
         }
       });
      alert('Please correct the errors in the form before saving.');
      return; // Stop submission if the form is invalid
    }

    this.loading = true;
    const formValue = this.settingsForm.value; // Get all values from the form group

    // Create a copy of formValue to modify for the 'data' part of the payload
    const dataForPayload = { ...formValue };

    // Remove fields that are not part of the standard update payload
    delete dataForPayload.id;
    delete dataForPayload.iosVersion;
    delete dataForPayload.validVersions;
    delete dataForPayload.playstoreLink;
    delete dataForPayload.appstoreLink;
    delete dataForPayload.version; // Remove new field
    delete dataForPayload.appName; // Remove new field

    const updatePayload = {
      tableName: "app-setting",        // Fixed value as per requirement
      id: formValue.id,             // Get the ID from the form (loaded via getSettings)
      data: dataForPayload        // Use the modified data object
    };

    console.log('Submitting Payload:', JSON.stringify(updatePayload, null, 2)); // Log the structured payload

    try {
      // Use the specific update method from your service (assuming it's updateSettings here)
      // Pass the structured updatePayload
      const response = await this.updateService.update(updatePayload); // <--- Pass the constructed payload

      console.log('Update successful:', response);
      this.toast.showSuccessToast('Settings saved successfully!'); // Changed to custom toast

      // Optionally, refresh the data or reset the form's state
      this.initialSettings = { ...formValue }; // Update initial settings to current saved state for reset logic
      this.settingsForm.markAsPristine(); // Reset dirty state
      // this.getSettings(); // Or reload from server if necessary

    } catch (error) {
      console.error('Error updating settings:', error);
      // Provide more specific error feedback if possible
      alert(`Error saving settings: ${error instanceof Error ? error.message : 'Unknown error'}. Please check console for details.`);
    } finally {
      this.loading = false;
    }
  }

  // --- Reset Form ---
  resetForm() {
      // Reset to the last fetched/saved state stored in initialSettings
      if (this.initialSettings && this.initialSettings.id) {
          // Need to handle arrays correctly when resetting
          this.setFormArray('validVersions', this.initialSettings.validVersions || [], this.isGroupAdmin() ? [Validators.required] : []);
          this.setFormArray('crisisEmails', this.initialSettings.crisisEmails || [], this.isSuperAdmin() ? [Validators.required, Validators.email] : []);
          this.setFormArray('crisisCCEmails', this.initialSettings.crisisCCEmails || [], this.isSuperAdmin() ? [Validators.required, Validators.email] : []);
          // Patch the rest of the form
          this.settingsForm.patchValue(this.initialSettings);
          this.settingsForm.markAsPristine(); // Mark as not dirty
          this.settingsForm.markAsUntouched(); // Mark as untouched
      } else {
          // If no initial settings, re-initialize and potentially fetch again
          this.initializeForm();
          this.getSettings();
      }
      // Reset tag input visibility states
      this.versionInputVisible = false;
      this.crisisEmailInputVisible = false;
      this.crisisCCEmailInputVisible = false;
      this.inputValue = '';
  }


  // --- Validation Helpers ---
  isInvalid(controlName: string): boolean {
    const control = this.settingsForm.get(controlName);
    return !!control && control.invalid && (control.dirty || control.touched);
  }

  getErrorMessage(controlName: string): string {
    const control = this.settingsForm.get(controlName);
    if (!control) { return ''; }

    if (control.errors?.['required']) { return 'This field is required.'; }
    if (control.errors?.['email']) { return 'Please enter a valid email address.'; }
    if (control.errors?.['min']) { return `Minimum value is ${control.errors['min'].min}.`; }
    if (control.errors?.['pattern']) { return 'Please enter a valid URL (e.g., https://example.com).'; }
    if (control.errors?.['minlength']) { return 'At least one entry is required.'; } // For FormArrays if minLength validator used

    // Add more specific messages as needed
    return 'Invalid input.';
  }
  // Role-based access checks
  isPlantAdmin(): boolean {
    return this.adminsRoleId === 2; // Assuming 2 is the ID for plant admin
  }

  isGroupAdmin(): boolean {
    // adminsRoleId 6 is now confirmed as Group Admin
    return this.adminsRoleId === 6;
  }

  isSuperAdmin(): boolean {
    // 1 is the ID for super admin. Role 6 (Group/Global Admin) should NOT see these fields.
    return this.adminsRoleId === 1;
  }
}