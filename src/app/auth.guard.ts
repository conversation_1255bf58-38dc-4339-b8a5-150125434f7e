import { CanActivateFn, RouterStateSnapshot, ActivatedRouteSnapshot } from '@angular/router';
import { inject } from '@angular/core';
import { Router } from '@angular/router';

export const authGuard: CanActivateFn = (
  route: ActivatedRouteSnapshot,
  state: RouterStateSnapshot
) => {
  const router = inject(Router);
  const token = localStorage.getItem('token');

  if (!token) {
    router.navigate(['/']); // Redirect to login page
    return false; // Prevent navigation to the requested route
  }

  try {
    const userString = localStorage.getItem('user');
    if (!userString || userString.trim() === "" || userString.trim().toLowerCase() === 'null') {
      console.error("User data not found or is invalid in localStorage for authGuard.");
      router.navigate(['/home/<USER>']); // Redirect to a safe default
      return false;
    }
    const currentUser = JSON.parse(userString);
    const userRoleId = currentUser?.adminsRoleId;

    const SUPER_ADMIN_ROLE_ID = 1;
    const GLOBAL_ADMIN_ROLE_ID = 6;

    // Define routes that require specific roles
    const restrictedRoutes: { [key: string]: number[] } = {
      '/home/<USER>/unfilled-report': [SUPER_ADMIN_ROLE_ID, GLOBAL_ADMIN_ROLE_ID],
    };

    // Check if the current route is restricted
    const requiredRoles = restrictedRoutes[state.url];

    if (requiredRoles) {
      // If the route is restricted, check if the user has one of the required roles
      if (!requiredRoles.includes(userRoleId)) {
        console.warn(`Access Denied: User with role ID ${userRoleId} attempted to access restricted route ${state.url}`);
        router.navigate(['/home/<USER>']); // Redirect to dashboard or an access denied page
        return false;
      }
    }

  } catch (error) {
    console.error("Error parsing user data in authGuard:", error);
    router.navigate(['/']); // Redirect to login in case of data parsing error
    return false;
  }

  // Token exists and role check passed (or route is not restricted), allow access
  return true;
};
