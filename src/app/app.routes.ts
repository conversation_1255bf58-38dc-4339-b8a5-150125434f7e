import { Routes } from '@angular/router';
import { LoginComponent } from './components/login/login.component';
import { HomeComponent } from './components/home/<USER>';
import { QrcodeManagementComponent } from './components/qrcode-management/qrcode-management.component';
import { TourManagementComponent } from './components/tour-management/tour-management.component';
import { IncidentManagementComponent } from './components/incident-management/incident-management.component';
import { PlantManagementComponent } from './components/plant-management/plant-management.component';
import { ManageObservationComponent } from './components/manage-observation/manage-observation.component';
import { OtherTaskComponent } from './components/other-task/other-task.component';
import { CrisisManagementComponent } from './components/crisis-management/crisis-management.component';
import { LeaderboardComponent } from './components/leaderboard/leaderboard.component';
import { AppSettingsComponent } from './components/app-settings/app-settings.component';
import { NotificationManagementComponent } from './components/notification-management/notification-management.component';
import { AddDjpComponent } from './components/add-djp/add-djp.component';
import { FeatureManagementComponent } from './components/master-management/feature-management/feature-management.component';

// Dashboard Components
import { DashboardComponent } from './components/dashboard/dashboard.component';

// Safety Training Components
import { SafetyTrainingDashboardComponent } from './components/safety-training/safety-training-dashboard/safety-training-dashboard.component';
import { ManageSafetyTrainingComponent } from './components/safety-training/manage-safety-training/manage-safety-training.component';

// Digisafe Components
import { ManageDigisafeComponent } from './components/digisafe/manage-digisafe/manage-digisafe.component';
import { GmrDashboardComponent } from './components/digisafe/gmr-dashboard/gmr-dashboard.component';
import { UnfilledReportComponent } from './components/digisafe/unfilled-report/unfilled-report.component';
import { MisDashboardComponent } from './components/digisafe/mis-dashboard/mis-dashboard.component';

// Report Management Components
import { UserwiseReportComponent } from './components/report-management/bog-tour/userwise-report/userwise-report.component';
import { UserwiseReportComponent as ZoneUserwiseReportComponent } from './components/report-management/bog-zone/userwise-report/userwise-report.component';
import { PlantwiseReportComponent } from './components/report-management/bog-tour/plantwise-report/plantwise-report.component';
import { PlantwiseReportComponent as ZonePlantwiseReportComponent } from './components/report-management/bog-zone/plantwise-report/plantwise-report.component';
import { PlantwiseReportComponent as RAGPlantwiseReportComponent } from './components/report-management/bog-rag/plantwise-report/plantwise-report.component';
import { QrCodeReportComponent } from './components/report-management/bog-rag/qr-code-report/qr-code-report.component';
import { PlantwiseReportComponent as ObservationPlantwiseReportComponent } from './components/report-management/bog-observation/plantwise-report/plantwise-report.component';

// Admin Management Components
import { ActiveUserComponent } from './components/admin-management/active-user/active-user.component';
import { InactiveUserComponent } from './components/admin-management/inactive-user/inactive-user.component';
import { TransferRequestComponent } from './components/admin-management/transfer-request/transfer-request.component';
import { DeleteUserComponent } from './components/admin-management/delete-user/delete-user.component';
import { RolesComponent } from './components/admin-management/roles/roles.component';

// Master Management Components
import { DepartmentComponent } from './components/master-management/department/department.component';
import { DesignationComponent } from './components/master-management/designation/designation.component';
import { ClusterComponent } from './components/master-management/cluster/cluster.component';
import { PlantTypeComponent } from './components/master-management/plant-type/plant-type.component';
import { SegmentComponent } from './components/master-management/segment/segment.component';
import { QrTypeComponent } from './components/master-management/qr-type/qr-type.component';
import { LocationTypeComponent } from './components/master-management/location-type/location-type.component';
import { LocationComponent } from './components/master-management/location/location.component';
import { OpcoComponent } from './components/master-management/opco/opco.component';
import { RelatestoComponent } from './components/master-management/relatesto/relatesto.component';
import { AreaComponent } from './components/master-management/area/area.component';
import { EquipmentComponent } from './components/master-management/equipment/equipment.component';
import { BodyPartComponent } from './components/master-management/body-part/body-part.component';
import { RootCauseComponent } from './components/master-management/root-cause/root-cause.component';
import { InspectionToolComponent } from './components/master-management/inspection-tool/inspection-tool.component';
import { RecommendedTypeComponent } from './components/master-management/recommended-type/recommended-type.component';
import { IncidentMasterComponent } from './components/master-management/incident-master/incident-master.component';
import { BusinessUnitComponent } from './components/master-management/business-unit/business-unit.component';
import { FeatureMasterComponent } from './components/master-management/feature-master/feature-master.component';

import { authGuard } from './auth.guard';
import { checkLoginGuard } from './check-login.guard';
import { FeatureGuard } from './guards/feature.guard';

export const routes: Routes = [
    { path: "", component: LoginComponent, canActivate: [checkLoginGuard] },
    {
        path: 'home', component: HomeComponent,
        canActivateChild: [authGuard],
        children: [
            {
                path: 'dashboard', component: DashboardComponent
            },
            {
                path: 'safetytraining/dashboard', component: SafetyTrainingDashboardComponent
            },
            {
                path: 'safetytraining/manage', component: ManageSafetyTrainingComponent
            },
            {
                path: 'qrcode', component: QrcodeManagementComponent, canActivate: [FeatureGuard]
            },
            {
                path: 'tour', component: TourManagementComponent, canActivate: [FeatureGuard]
            },
            {
                path: 'incident', component: IncidentManagementComponent, canActivate: [FeatureGuard]
            },
            {
                path: 'plant', component: PlantManagementComponent, canActivate: [FeatureGuard]
            },
            {
                path: 'feature-management', component: FeatureManagementComponent, canActivate: [FeatureGuard]
            },
            {
                path: 'observation', component: ManageObservationComponent, canActivate: [FeatureGuard]
            },
            {
                path: 'digisafe/manage', component: ManageDigisafeComponent, canActivate: [FeatureGuard]
            },
            {
                path: 'digisafe/mis', component: MisDashboardComponent, canActivate: [FeatureGuard]
            },
            {
                path: 'digisafe/gmr', component: GmrDashboardComponent, canActivate: [FeatureGuard]
            },
            {
                path: 'digisafe/unfilled-report', component: UnfilledReportComponent, canActivate: [FeatureGuard]
            },
            {
                path: 'othertask', component: OtherTaskComponent, canActivate: [FeatureGuard]
            },
            {
                path: 'crisismanagement', component: CrisisManagementComponent, canActivate: [FeatureGuard]
            },
            {
                path: 'leaderboard', component: LeaderboardComponent, canActivate: [FeatureGuard]
            },
            {
                path: 'reportmanagement/tour/userwise', component: UserwiseReportComponent
            },
            {
                path: 'reportmanagement/tour/plantwise', component: PlantwiseReportComponent
            },
            {
                path: 'reportmanagement/zone/userwise', component: ZoneUserwiseReportComponent
            },
            {
                path: 'reportmanagement/zone/plantwise', component: ZonePlantwiseReportComponent
            },
            {
                path: 'reportmanagement/rag/plantwise', component: RAGPlantwiseReportComponent
            },
            {
                path: 'reportmanagement/rag/qrcode', component: QrCodeReportComponent
            },
            {
                path: 'reportmanagement/observation/plantwise', component: ObservationPlantwiseReportComponent
            },
            {
                path: 'adminmanagement/activeusers', component: ActiveUserComponent
            },
            {
                path: 'adminmanagement/inactiveusers', component: InactiveUserComponent
            },
            {
                path: 'adminmanagement/transferrequest', component: TransferRequestComponent
            },
            {
                path: 'adminmanagement/deletedusers', component: DeleteUserComponent
            },
            {
                path: 'adminmanagement/roles', component: RolesComponent
            },
            {
                path: 'appsettings', component: AppSettingsComponent
            },
            {
                path: 'masterManagement/department', component: DepartmentComponent
            },
            {
                path: 'masterManagement/business-unit', component: BusinessUnitComponent
            },
            {
                path: 'masterManagement/designation', component: DesignationComponent
            },
            {
                path: 'masterManagement/cluster', component: ClusterComponent
            },
            {
                path: 'masterManagement/plantType', component: PlantTypeComponent
            },
            {
                path: 'masterManagement/segment', component: SegmentComponent
            },
            {
                path: 'masterManagement/qrtype', component: QrTypeComponent
            },
            {
                path: 'masterManagement/locationtype', component: LocationTypeComponent
            },
            {
                path: 'masterManagement/location', component: LocationComponent
            },
            {
                path: 'masterManagement/opco', component: OpcoComponent
            },
            {
                path: 'masterManagement/relatesto', component: RelatestoComponent
            },
            {
                path: 'masterManagement/featuremaster', component: FeatureMasterComponent
            },
            {
                path: 'masterManagement/area', component: AreaComponent
            },
            {
                path: 'masterManagement/equipments', component: EquipmentComponent
            },
            {
                path: 'masterManagement/incidentmaster', component: IncidentMasterComponent
            },
            {
                path: 'masterManagement/bodypart', component: BodyPartComponent
            },
            {
                path: 'masterManagement/rootcause', component: RootCauseComponent
            },
            {
                path: 'masterManagement/inspectiontool', component: InspectionToolComponent
            },
            {
                path: 'masterManagement/recommendedtype', component: RecommendedTypeComponent
            },
            {
                path: 'notificationmanagement', component: NotificationManagementComponent
            },
            {
                path: 'addDjp', component: AddDjpComponent, canActivate: [FeatureGuard]
            },
        ]
    }
];
