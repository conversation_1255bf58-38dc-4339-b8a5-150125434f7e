export interface Feature {
    id: number;
    enabled: boolean;
    featureMasterId: number;
    businessUnitId: number;
privilege: boolean;
    adminRoute: string | null;
    appRoute: string | null;
    priority: number | null;
    createdBy: string | null;
    createdTimestamp: string;
    updatedBy: string | null;
    updatedTimestamp: string;
    featureMaster: {
        id: number;
        enabled: boolean;
        title: string;
        priority: number | null;
        createdBy: string | null;
        createdTimestamp: string;
        updatedBy: string | null;
        updatedTimestamp: string;
    };
    businessUnit: {
        id: number;
        enabled: boolean;
        isDeleted: boolean;
        title: string;
        icon: string | null;
        priority: number | null;
        createdBy: string | null;
        createdTimestamp: string;
        updatedBy: string | null;
        updatedTimestamp: string;
    };
}