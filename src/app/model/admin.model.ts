export interface AdminModel {
  businessUnitId?: number;
    id: number;
    enabled: boolean;
    isDeleted: boolean;
    firstName: string;
    lastName: string;
    gender: string;
    email: string;
    contactNumber: string;
    dob: string; // ISO date string
    priority: number;
    adminsRoleId: number;
    dialCode: string;
    countryCode: string;
    steps: number;
    profilePicture: string | null; // URL as a string
    status: number;
    opCoId: number | null;
    plantIds: number[];
    plant: any[];
    designationId: number;
    departmentId: number;
    otpCount: number;
    otpTime: string | null; // ISO date string or null
    verifyCount: number;
    verifyTime: string | null; // ISO date string or null
    otpBlock: boolean;
    OSVersion: string;
    pin: string;
    createdBy: number | null;
    createdTimestamp: string; // ISO date string
    accessToken: string | null;
    updatedBy: number;
    wbiRoleId: number;
    wbiDepartmentId: number;
    updatedTimestamp: string; // ISO date string
  }
