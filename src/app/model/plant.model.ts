export interface PlantModel {
    id: number;
    enabled: boolean;
    isDeleted: boolean;
    name: string;
    opcoId: number;
    clusterId: number;
    plantTypeId: number;
    plantAdminId: number;
    emergencyContactNumber: string;
    priority: number | null;
    createdBy: number | null;
    createdTimestamp: string;
    updatedBy: number | null;
    updatedTimestamp: string;
    cluster: Cluster;
    plantType: PlantType;
    opco: OpCo;
    plantAdmin: PlantAdmin;
  }
  
  export interface Cluster {
    id: number;
    enabled: boolean;
    isDeleted: boolean;
    title: string;
    priority: number | null;
    createdBy: number | null;
    createdTimestamp: string;
    updatedBy: number | null;
    updatedTimestamp: string;
  }
  
  export interface PlantType {
    id: number;
    enabled: boolean;
    isDeleted: boolean;
    title: string;
    priority: number | null;
    createdBy: number | null;
    createdTimestamp: string;
    updatedBy: number | null;
    updatedTimestamp: string;
  }
  
  export interface OpCo {
    id: number;
    enabled: boolean;
    isDeleted: boolean;
    title: string;
    priority: number | null;
    createdBy: number | null;
    createdTimestamp: string;
    updatedBy: number | null;
    updatedTimestamp: string;
  }
  
  export interface PlantAdmin {
    id: number;
    enabled: boolean;
    isDeleted: boolean;
    firstName: string;
    lastName: string;
    gender: string;
    email: string;
    contactNumber: string;
    dob: string;
    priority: number;
    adminsRoleId: number;
    dialCode: string;
    countryCode: string;
    steps: number;
    profilePicture: string;
    status: number;
    plantIds: number[];
    designationId: number;
    departmentId: number;
    createdBy: number | null;
    createdTimestamp: string;
    updatedBy: number | null;
    updatedTimestamp: string;
  }
  