class NavigationItem{
    title!: string;
    route!: string;
    icon!: string;
    expanded!: boolean;
    subitems!: NavigationItem[];
    allowedRole!: string[];

    constructor(title: string, 
        route: string, 
        icon: string, 
        subitems: NavigationItem[] = [], 
        expanded: boolean = false,
        allowedRole: string[] = []
    ) {
        this.title = title;
        this.route = route;
        this.icon = icon;
        this.subitems = subitems;
        this.expanded = expanded;
        this.allowedRole = allowedRole;
    }
}
export default NavigationItem;