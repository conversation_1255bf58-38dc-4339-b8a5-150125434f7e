export function convertDate(inputDate: string): string {
    const date = new Date(inputDate);
    if (isNaN(date.getTime())) {
      return "Invalid date"; // Handle invalid input
    }
  
    // Get the last day of the month
    const lastDayOfMonth = new Date(date.getFullYear(), date.getMonth() + 1, 0).getDate();
  
    // Create a new date with the last day of the month and the specified time
    const newDate = new Date(date.getFullYear(), date.getMonth(), lastDayOfMonth, 18, 30, 0, 0);
  
    // Convert to ISO string with UTC timezone (Z)
    return newDate.toISOString();
  }