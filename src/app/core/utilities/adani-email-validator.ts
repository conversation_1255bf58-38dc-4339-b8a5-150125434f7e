import { AbstractControl, ValidationErrors, ValidatorFn } from '@angular/forms';

export function adaniDomainValidator(): ValidatorFn {
  return (control: AbstractControl): ValidationErrors | null => {
    const value = control.value;
    if (!value) {
      return null; // If empty, leave validation to `Validators.required`
    }

    // Regex to validate the email
    const emailRegex = /^[a-zA-Z0-9._%+-]+@adani\.com$/;

    // Ensure the local part contains at least one alphabet
    const [localPart] = value.split('@'); // Extract the part before `@`
    const localPartRegex = /^(?=.*[a-zA-Z])[a-zA-Z0-9._%+-]+$/; // Must include at least one alphabet

    // Validate both the domain and the local part
    if (!emailRegex.test(value) || !localPartRegex.test(localPart)) {
      return { adaniDomain: true }; // Return an error object if validation fails
    }

    return null; // Validation passes
  };
}
