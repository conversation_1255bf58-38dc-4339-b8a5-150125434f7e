<nav *ngIf="totalPages > 1 || totalItems > 0" class="pagination-container d-flex align-items-center justify-content-between">
    <ul class="pagination modern-pagination mb-0">
      <li class="page-item" [class.disabled]="currentPage === 1">
        <button class="page-link" (click)="changePage(1)">
          <i class="bi bi-chevron-double-left"></i>
        </button>
      </li>
      <li class="page-item" [class.disabled]="currentPage === 1">
        <button class="page-link" (click)="changePage(currentPage - 1)">
          <i class="bi bi-chevron-left"></i>
        </button>
      </li>
      <li class="page-item" *ngFor="let page of pages" [class.active]="page === currentPage">
        <button class="page-link" (click)="changePage(page)">{{ page }}</button>
      </li>
      <li class="page-item" [class.disabled]="currentPage === totalPages">
        <button class="page-link" (click)="changePage(currentPage + 1)">
          <i class="bi bi-chevron-right"></i>
        </button>
      </li>
      <li class="page-item" [class.disabled]="currentPage === totalPages">
        <button class="page-link" (click)="changePage(totalPages)">
          <i class="bi bi-chevron-double-right"></i>
        </button>
      </li>
    </ul>
    <div class="pagination-info ms-3">
      Showing {{ (currentPage - 1) * itemsPerPage + 1 }} to
      {{ currentPage * itemsPerPage > totalItems ? totalItems : currentPage * itemsPerPage }}
      of {{ totalItems }} entries
    </div>
  </nav>
