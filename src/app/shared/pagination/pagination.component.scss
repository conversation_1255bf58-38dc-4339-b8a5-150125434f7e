pagination-container {
    display: flex;
    justify-content: center;
    margin-top: 20px;
  }

  .modern-pagination {
    display: flex;
    list-style: none;
    padding: 0;
    margin: 0;
  }

  .page-item {
    margin: 0 2px;
  }

  .page-link {
    background-color: #f0f0f0;
    border: 1px solid #ddd;
    color: #333;
    padding: 8px 12px;
    text-decoration: none;
    cursor: pointer;
    border-radius: 4px;
    font-size: 12px;
    transition: background-color 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 36px;
    height: 36px;
  }

  .page-link:hover,
  .page-item.active .page-link {
    background: linear-gradient(to right, rgb(11, 116, 176), rgb(117, 71, 156), rgb(189, 54, 129));
    color: white;
    border-color: #007bff;
  }

  .page-item.disabled .page-link {
    background-color: #eee;
    color: #aaa;
    cursor: not-allowed;
    border-color: #ddd;
  }

  .bi {
    font-size: 1rem;
  }
  .pagination-info{
    font-size: 12px;
  }