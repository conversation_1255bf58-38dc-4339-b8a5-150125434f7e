import { trigger, state, style, transition, animate } from '@angular/animations';
import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, EventEmitter, Input, Output, SimpleChanges } from '@angular/core';

@Component({
  selector: 'app-tab',
  imports: [CommonModule],
  templateUrl: './tab.component.html',
  styleUrl: './tab.component.scss',
})
export class TabComponent {
  @Input() tabs: { title: string }[] = [];
  @Input() selectedTabIndex: number = 0;
  @Output() tabSelected = new EventEmitter<number>();

  selectTab(index: number) {
    this.tabSelected.emit(index);
  }
}
