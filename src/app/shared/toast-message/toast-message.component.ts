import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';

@Component({
  selector: 'app-toast-message',
  templateUrl: './toast-message.component.html',
  standalone: true,
  imports:[CommonModule],
  styleUrls: ['./toast-message.component.scss']
})
export class ToastMessageComponent {
  showToast: boolean = false; // Internal control for showing the toast
  toastType: 'success' | 'error' = 'success'; // Track toast type
  toastMsg: string = ''; // Toast message
  private toastTimeout: any = 2000; // Store timeout reference

  // Display success toast
  showSuccessToast(msg: string): void {
    this.toastMsg = msg;
    this.toastType = 'success';
    this.triggerToast();
  }

  // Display error toast
  showErrorToast(msg: string,toastTimeout?:any): void {
    this.toastMsg = msg;
    this.toastType = 'error';
    this.triggerToast(toastTimeout);
  }

  private triggerToast(toastTimeout?:any): void {
    this.resetToast(); // Clear existing timeout
    this.showToast = true; // Show the toast
    if(toastTimeout){
      this.toastTimeout = setTimeout(() => {
        this.resetToast();
      }, toastTimeout);
    }
    else{
      this.toastTimeout = setTimeout(() => {
        this.resetToast();
      }, 2000);
    }
 // Hide toast after 2 seconds
  }

  // Reset toast state
  resetToast(): void {
    if (this.toastTimeout) {
      clearTimeout(this.toastTimeout);
    }
    this.showToast = false;
  }
}
