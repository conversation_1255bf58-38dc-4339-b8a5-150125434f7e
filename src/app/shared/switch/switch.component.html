<label class="switch">
  <!-- Use (change) event for better compatibility with letting default behavior happen -->
  <input
    type="checkbox"
    [checked]="checked"
    (click)="onSwitchClick($event)"
    [disabled]="disabled">
  <span class="slider"></span>
</label>
<span class="label-text">{{ checked ? onLabel : offLabel }}</span>

<!-- Confirmation Modal -->
<!-- Add #confirmationModal template reference variable -->
<!-- Use dynamic [id] binding -->
<div #confirmationModal class="modal fade" [id]="modalId" tabindex="-1" [attr.aria-labelledby]="modalId + '-label'" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header gradient-header">
        <!-- Update aria-labelledby to match the dynamic ID -->
        <h5 class="modal-title" [id]="modalId + '-label'">Confirmation</h5>
        <!-- Use data-bs-dismiss for standard Bootstrap closing -->
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <!-- Updated confirmation text -->
        <p>Are you sure you want to <strong>{{ actionText }}</strong> this?</p>
      </div>
      <div class="modal-footer">
        <div class="d-flex gap-3 justify-content-center w-100">
          <button type="button" class="button-submit button-left" (click)="confirmToggle()">
            <span class="ps-5">Yes</span>
            <img alt="" src="../../../assets/svg/right-arrow.svg" class="left-arrow" />
          </button>
          <!-- Use (click) for cancel button as well -->
          <button type="button" class="button-back button-left" (click)="cancelToggle()">
            <span class="ps-5">No</span>
            <img alt="" src="../../../assets/svg/Edit-User.svg" class="left-arrow" />
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
