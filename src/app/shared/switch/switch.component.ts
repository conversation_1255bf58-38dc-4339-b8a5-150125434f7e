import { Component, EventEmitter, Input, Output, OnInit, OnDestroy, AfterViewInit, ElementRef, ViewChild } from '@angular/core';

// Declare bootstrap if you're using it globally via <script> tag
declare var bootstrap: any;

@Component({
  selector: 'app-switch',
  templateUrl: './switch.component.html',
  styleUrls: ['./switch.component.scss'] // Corrected property name
})
export class SwitchComponent implements OnInit, OnDestroy, AfterViewInit {
  @Input() checked: boolean = false;
  @Output() checkedChange = new EventEmitter<boolean>();
  @Input() disabled: boolean = false;
  @Input() onLabel: string = 'ON';
  @Input() offLabel: string = 'OFF';
  @Input() requireConfirmation: boolean = false; // New Input: Default is false (no confirmation)
  @Input() confirmationOnText: string = 'enable'; // Customizable text for turning ON
  @Input() confirmationOffText: string = 'disable'; // Customizable text for turning OFF

  // Use ViewChild to get a reference to the modal element within this component's template
  @ViewChild('confirmationModal') modalElementRef!: ElementRef;
  modalInstance: any; // Holds the Bootstrap Modal instance

  private intendedState: boolean = false; // Store intended toggle state only when confirmation is needed
  actionText: string = ''; // Text for modal confirmation

  // Unique ID for the modal to avoid conflicts if multiple switches are on the page
  modalId: string = 'switchConfirmModal-' + Math.random().toString(36).substring(2, 9);

  constructor() {}

  ngOnInit(): void {
    // Initialize action text based on potential initial confirmation
    this.actionText = this.checked ? this.confirmationOffText : this.confirmationOnText;
  }

  ngAfterViewInit(): void {
    // Initialize the Bootstrap modal instance after the view is ready
    // Only create the instance if confirmation might be required
    if (this.requireConfirmation && this.modalElementRef?.nativeElement) {
      this.modalInstance = new bootstrap.Modal(this.modalElementRef.nativeElement);
    }
  }

  ngOnDestroy(): void {
    // Clean up the modal instance when the component is destroyed
    this.modalInstance?.dispose();
  }

  // Handle the click event on the switch container/input
  onSwitchClick(event: Event) {
    if (this.disabled) {
      event.preventDefault(); // Prevent change if disabled
      return;
    }

    const target = event.target as HTMLInputElement;
    const newState = target.checked; // The state the checkbox *wants* to change to

    if (this.requireConfirmation) {
      // If confirmation is needed:
      event.preventDefault(); // *** Crucial: Stop the checkbox from changing its state immediately ***
      this.intendedState = !this.checked; // Store the state we *intend* to switch to
      this.actionText = this.intendedState ? this.confirmationOnText : this.confirmationOffText; // Set modal text based on intended action
      this.openModal();
    } else {
      // If no confirmation is needed:
      // Let the checkbox change state naturally (no preventDefault)
      // Emit the new state immediately
      // Note: [(checked)] binding handles updating the component's `checked` property
      this.checkedChange.emit(newState);
    }
  }

  // Show the confirmation modal
  private openModal() {
    if (!this.modalInstance && this.modalElementRef?.nativeElement) {
        // Initialize modal instance lazily if not done in ngAfterViewInit
        // (e.g., if requireConfirmation was initially false but changed later)
         this.modalInstance = new bootstrap.Modal(this.modalElementRef.nativeElement);
    }
    this.modalInstance?.show();
  }

  // Confirm toggle after approval
  confirmToggle() {
    this.checked = this.intendedState; // Apply the intended state
    this.checkedChange.emit(this.checked); // Emit the change
    this.modalInstance?.hide(); // Close modal
  }

  // Close modal without changing state (cancel)
  cancelToggle() {
    // No state change, just hide the modal
    this.modalInstance?.hide();
  }
}