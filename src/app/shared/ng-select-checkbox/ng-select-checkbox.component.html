<ng-select
  class="form-control p-0"
  [items]="items"
  [bindLabel]="bindLabel"
  [bindValue]="bindValue"
  [multiple]="true"
  [closeOnSelect]="false"
  [searchable]="false"
  [clearable]="false"
  [(ngModel)]="selectedItems"
  (change)="onChange()"
  [virtualScroll]="true"
  [dropdownPosition]="'bottom'"
  [placeholder]="placeholder">

  <!-- Header -->
  <ng-template ng-header-tmp>
    <div class="form-check px-3 py-2 border-bottom">
      <input
        type="checkbox"
        class="form-check-input"
        [(ngModel)]="selectAll"
        (change)="toggleSelectAll()" />
      <label class="form-check-label ms-2">Select All</label>
    </div>
  </ng-template>

  <!-- Item Checkbox -->
  <ng-template ng-option-tmp let-item="item">
    <div class="form-check px-3">
      <input
        type="checkbox"
        class="form-check-input"
        [checked]="isItemSelected(item)"
        (change)="toggleItem(item)" />
      <label class="form-check-label ms-2">
        {{ item[bindLabel] }}
      </label>
    </div>
  </ng-template>

  <!-- Custom Badge Display -->
  <ng-template ng-label-tmp>
    <div *ngIf="selectedItems.length <= displayLimit">
      <span *ngFor="let item of selectedItems" class="badge bg-primary me-1">
        {{ item[bindLabel] }}
      </span>
    </div>
    <div *ngIf="selectedItems.length > displayLimit">
      <span *ngFor="let item of selectedItems.slice(0, displayLimit)" class="badge bg-primary me-1">
        {{ item[bindLabel] }}
      </span>
      <span class="badge bg-secondary">+{{ selectedItems.length - displayLimit }} more</span>
    </div>
  </ng-template>
</ng-select>
