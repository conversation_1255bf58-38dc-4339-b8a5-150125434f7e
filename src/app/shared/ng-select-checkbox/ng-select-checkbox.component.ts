import { CommonModule } from '@angular/common';
import { ChangeDetectorRef, Component, EventEmitter, forwardRef, Input, NgModule, Output } from '@angular/core';
import { ControlValueAccessor, FormsModule, NG_VALUE_ACCESSOR, ReactiveFormsModule } from '@angular/forms';
import { NgSelectModule } from '@ng-select/ng-select';

@Component({
  selector: 'app-ng-select-checkbox',
  imports: [
    NgSelectModule,
    FormsModule,
    ReactiveFormsModule,
    CommonModule
  ],
  templateUrl: './ng-select-checkbox.component.html',
  styleUrl: './ng-select-checkbox.component.scss'
})
export class NgSelectCheckboxComponent {
  @Input() items: any[] = [];
  @Input() bindLabel: string = 'name';
  @Input() bindValue: string = 'id';
  @Input() placeholder: string = 'Select items';
  @Input() displayLimit: number = 4;


  @Output() selectionChange = new EventEmitter<any[]>();

  selectedItems: any[] = [];
  selectAll = false;

  toggleSelectAll() {
    this.selectAll = !this.selectAll;
    this.selectedItems = this.selectAll ? [...this.items] : [];
    this.selectionChange.emit(this.selectedItems);
  }

  onChange() {
    this.selectAll = this.selectedItems.length === this.items.length;
    this.selectionChange.emit(this.selectedItems);
  }

  isItemSelected(item: any): boolean {
    return this.selectedItems.some(i => i[this.bindValue] === item[this.bindValue]);
  }

  toggleItem(item: any) {
    const index = this.selectedItems.findIndex(i => i[this.bindValue] === item[this.bindValue]);
    if (index >= 0) {
      this.selectedItems.splice(index, 1);
    } else {
      this.selectedItems.push(item);
    }
    this.selectAll = this.selectedItems.length === this.items.length;
    this.selectionChange.emit(this.selectedItems);
  }

}
