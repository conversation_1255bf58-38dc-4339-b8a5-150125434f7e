/* Force solid background and dropdown style */
.ng-dropdown-panel {
    background-color: #fff !important;  /* Override transparency */
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
    z-index: 1050 !important; /* Bootstrap modal/z-index fix */
  }
  
  /* Optional: to make options more like Bootstrap dropdown items */
  .ng-option {
    padding: 0.5rem 1rem;
    cursor: pointer;
  }
  
  .ng-option:hover {
    background-color: #f8f9fa;
  }
  
  .ng-select.form-control {
    background-color: #fff; /* Ensure consistent input bg */
  }
  