// .top-bar{
//     height: 74px;
//     padding-top: 12px;
//     padding-left: 40px;
// }
// .switch-container {
//     display: flex;
//     align-items: center;
//     justify-content: center;
//     gap: 10px;
//   }
// .switch {
//     position: relative;
//     display: inline-block;
//     width: 81px;
//     height: 42px;
//   }

//   .switch-input {
//     opacity: 0;
//     width: 0;
//     height: 0;
//   }

//   .switch-label {
//     position: absolute;
//     cursor: pointer;
//     top: 0;
//     left: 0;
//     right: 0;
//     bottom: 0;
//     background-color: #ccc;
//     transition: background-color 0.3s;
//     border-radius: 34px;
//   }

//   .switch-label:before {
//     content: "";
//     position: absolute;
//     height: 32px;
//     width: 32px;
//     border-radius: 50%;
//     left: 4px;
//     bottom: 4px;
//     background: linear-gradient(141.04deg, #0B74B0 16.88%, #75479C 52.26%, #BD3861 82.19%);
//     transition: transform 0.3s, background 0.3s;
//   }


//   .switch-input:checked + .switch-label:before {
//     transform: translateX(40px);
//   }

img {
  height: 100%;
  width: 100%;
}

.header {
  height: 52px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0px 2px 2px 0px rgba(0, 0, 0, 0.1019607843);
  padding: 0px 35px;
  background-color: #FFF;
}

.header .text-logo {
  height: 29px;
}

.header .group-logo {
  height: 48px;
}

.header .acc-logo {
  height: 30px;
}

.avatar-profile {
  display: flex;
  align-items: center;
}

.avatar {
  // height: 29px;
  margin-right: 16px;
  cursor: pointer;
}

.bell-icon {
  font-size: 20px;
}

.dropdown-menu {
  font-family: 'adani';
  border-radius: 0;
  font-size: 12px;
  font-weight: 500;
  box-shadow: inset 0px 0px 1px 0px;
}

.calender-label {
  font-size: 13px;
  font-weight: 700;
}

.profile-dropdown-img {
  height: 20px;
  width: 20px;
  margin-right: 15px;
}

.gradient-header {
  background: linear-gradient(90deg, #0B74B0 0%, #75479C 54.17%, #BD3861 100%);
  color: white;
  /* Optional: Make text white for better contrast */
  padding: 1rem;
  /* Optional: Adjust padding for aesthetics */
  border-top-left-radius: 0.3rem;
  /* Match modal border radius */
  border-top-right-radius: 0.3rem;
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: center;
}

.spinner-container {
  background: white;
  padding: 2rem;
  border-radius: 8px;
  text-align: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.profile-avatar {
  width: 100px;
  height: 100px;
}

.btn-section {
  >button {
    width: 140px;
  }
}

.button-back {
  border: 1px solid;
  border-radius: 30px;
  height: 39px;
  color: #bd3861 !important;
  font-size: 12px;
  font-weight: 600;
  margin: 0 10px;
  width: 156px;
  background: #fff;
}

.burgerIcon {
  width: 30px;
  margin-right: 10px;
}

.ambujaLogo {
  width: 75px;
}

.adaniGroupIcon {
  width: 100px;
  margin-left: 90px;
}

.weCareIcon {
  width: 120px;
}

.avatar {
  margin-right: 5px;
  height: 36px;
  width: 36px;
}

.downArrow {
  width: 15px;
  margin-right: 30px;
}

.accLogo {
  margin-left: 20px;
}

.modal-header {
  background-image: linear-gradient(90deg, #0b74b0, #75479c, #bd3681) !important;
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header .btn-close,
.register-modal-close {
  width: 1.5rem !important;
  height: 1.5rem !important;
  padding: 0.25rem !important;
  margin: 0 !important;
  position: absolute;
  top: 1rem;
  right: 1rem;
  z-index: 10;
  background: transparent !important;
  box-shadow: none !important;
  opacity: 1 !important;
}

.modelStyle {
  width: 80%;
}