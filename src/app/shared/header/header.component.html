<div class="header">
  <!-- Special layout for businessUnitId === 2 -->
  <ng-container *ngIf="admin?.businessUnitId === 2; else defaultHeader">
    <!-- Left: Hamburger Menu -->
    <div class="text-logo">
      <a href="javascript:void(0)">
        <img src="../../../assets/img/menu-burger.png" alt="" class="burgerIcon" (click)="sideMenuOpenClose()"
          matTooltip="Open/Close side menu" />
      </a>
    </div>

    <!-- Center: AESL and We Care Logos -->
    <div class="group-logo" style="flex-grow: 1; display: flex; justify-content: center; align-items: center; gap: 15px;">
      <img src="../../../assets/img/adani_AESL 1.png" alt="Adani AESL" style="height: 40px; width: auto; max-width: 150px; object-fit: contain;" />
      <img src="../../../assets/img/we-care-logo.png" alt="We Care" style="height: 40px; width: auto; max-width: 120px; object-fit: contain;" />
    </div>

    <!-- Right: Avatar Profile -->
    <div class="avatar-profile" style="display: flex; align-items: center; gap: 15px;">
      <!-- Business Unit Dropdown for businessUnitId === 1 -->
      <div class="business-unit-dropdown" *ngIf="showBusinessUnitDropdown">
        <select class="form-select form-select-sm" 
                [(ngModel)]="selectedBusinessUnitId"
                (change)="onBusinessUnitChange($event)"
                style="min-width: 150px; height: 35px;">
          <option [value]="null">Select Business Unit</option>
          <option *ngFor="let unit of businessUnits" [value]="unit.id">
            {{unit.title}}
          </option>
        </select>
      </div>

      <div class="avatar dropdown">
        <a id="dropdownMenuButton" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
          <div class="d-flex align-items-center">
            <img [src]="admin?.profilePicture ? admin?.profilePicture : '../../../assets/svg/Avatar.svg'" alt=""
              class="avatar" />
            <img src="../../../assets/svg/down-vector.svg" alt="" class="downArrow" />
          </div>
        </a>
        <div class="dropdown-menu" aria-labelledby="dropdownMenuButton">
          <a class="dropdown-item d-flex align-items-center" href="javascript:void(0);" (click)="openUserProfileDialog()">
            <div class="profile-dropdown-img"> <img
                [src]="admin?.profilePicture ? admin?.profilePicture : '../../../assets/svg/Avatar.svg'"
                alt="Admin Avatar" /> </div>Hi
            {{admin?.firstName}}{{admin?.lastName}}!
          </a>
          <a class="dropdown-item d-flex align-items-center" href="javascript:void(0);" (click)="logout()">
            <div class="profile-dropdown-img"> <img src="../../../assets/svg/Logout.svg" alt="" /> </div>
            Logout
          </a>
        </div>
      </div>
      <!-- ACC logo is intentionally omitted for businessUnitId === 2 as per original request -->
    </div>
  </ng-container>
  <!-- Default header for other business units -->
  <ng-template #defaultHeader>
    <div class="text-logo">
      <a href="javascript:void(0)">
        <img src="../../../assets/img/menu-burger.png" alt="" class="burgerIcon" (click)="sideMenuOpenClose()"
          matTooltip="Open/Close side menu" />
        <img src="../../../assets/img/ambuja-logo.png" alt="" class="ambujaLogo" />
      </a>
    </div>

    <div class="group-logo">
      <img src="../../../assets/img/adani-group-icon.png" alt="" class="adaniGroupIcon" />
      <img src="../../../assets/img/we-care-logo.png" alt="" class="weCareIcon" />
    </div>

    <div class="avatar-profile" style="display: flex; align-items: center; gap: 15px;">
      <!-- Business Unit Dropdown for businessUnitId === 1 -->
      <div class="business-unit-dropdown" *ngIf="showBusinessUnitDropdown">
        <select class="form-select form-select-sm" 
                [(ngModel)]="selectedBusinessUnitId"
                (change)="onBusinessUnitChange($event)"
                style="min-width: 150px; height: 35px;">
          <option [value]="null">Select Business Unit</option>
          <option *ngFor="let unit of businessUnits" [value]="unit.id">
            {{unit.title}}
          </option>
        </select>
      </div>

      <div class="avatar dropdown">

        <a id="dropdownMenuButton" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
          <div class="d-flex align-items-center">
            <img [src]="admin?.profilePicture ? admin?.profilePicture : '../../../assets/svg/Avatar.svg'" alt=""
              class="avatar" />
            <img src="../../../assets/svg/down-vector.svg" alt="" class="downArrow" />
          </div>
        </a>
        <div class="dropdown-menu" aria-labelledby="dropdownMenuButton">
          <a class="dropdown-item d-flex align-items-center" href="javascript:void(0);" (click)="openUserProfileDialog()">
            <div class="profile-dropdown-img"> <img
                [src]="admin?.profilePicture ? admin?.profilePicture : '../../../assets/svg/Avatar.svg'"
                alt="Admin Avatar" /> </div>Hi
            {{admin?.firstName}}{{admin?.lastName}}!
          </a>
          <a class="dropdown-item d-flex align-items-center" href="javascript:void(0);" (click)="logout()">
            <div class="profile-dropdown-img"> <img src="../../../assets/svg/Logout.svg" alt="" /> </div>
            Logout
          </a>
        </div>
      </div>

      <div class="acc-logo">
        <img src="../../../assets/svg/ACC-Logo.svg" alt="" class="accLogo" />
      </div>
    </div>
  </ng-template>
</div>
<app-offcanvas [title]="'Edit Profile'" [width]="'350px'" *ngIf="isEditUserModalOpen"
  (closeModal)="closeEditUserModal()" (onClickCross)="closeEditModal()">
  <div>
    <form [formGroup]="editUserForm" (ngSubmit)="onEditSubmit()">
      <div class="input-text-group text-center">
        <img [src]="profileImageUrl || '../../../assets/svg/Avatar.svg'" alt="Profile Picture"
          class="profile-avatar mb-3" width="100" height="100" style="border-radius: 50%; object-fit: cover;" />
        <input type="file" #fileInput class="form-control form-control-lg" id="profile" (change)="onFileChange($event)"
          accept="image/*" />
      </div>

      <div class="input-text-group">
        <label for="firstName">First Name</label>
        <input type="text" class="form-control" formControlName="firstName" placeholder="Edit First Name"
          oninput="event.target.value=event.target.value.trimStart('')" />
        <span id="txt_firstName_error" class="text-danger"
          *ngIf="(editUserForm.get('firstName')?.touched || submitted) && editUserForm.get('firstName')?.errors?.['required']">
          First Name is required.
        </span>
      </div>
      <div class="input-text-group">
        <label for="lastName">Last Name</label>
        <input type="text" class="form-control" formControlName="lastName" placeholder="Edit Last Name"
          oninput="event.target.value=event.target.value.trimStart('')" />
        <span id="txt_lastName_error" class="text-danger"
          *ngIf="(editUserForm.get('lastName')?.touched || submitted) && editUserForm.get('lastName')?.errors?.['required']">
          Last Name is required.
        </span>
      </div>
      <div class="input-text-group">
        <label for="gender">Gender</label>
        <select class="form-control" formControlName="gender" id="gender">
          <option value="">Select Gender</option>
          <option value=1>Male</option>
          <option value=0>Female</option>
          <option value=2>Other</option>
        </select>
        <span id="txt_gender_error" class="text-danger"
          *ngIf="(editUserForm.get('gender')?.touched || submitted) && editUserForm.get('gender')?.errors?.['required']">
          Please select a gender.
        </span>
      </div>
      <div class="input-text-group">
        <label for="email">Email</label>
        <input type="text" class="form-control" formControlName="email" placeholder="Edit Email"
          oninput="event.target.value=event.target.value.trimStart('')" readonly />
        <div id="txt_email_error" class="text-danger validation" *ngIf="
            editUserForm.get('email')?.value &&
            editUserForm.hasError('validEmail')
          ">
          Invalid Email ID.
        </div>
        <span id="txt_email_error" class="text-danger"
          *ngIf="(editUserForm.get('email')?.touched || submitted) && editUserForm.get('email')?.errors?.['required']">
          Email is required.
        </span>
      </div>
      <div class="w-100 d-flex mt-4 btn-section">
        <button class="button-submit" type="submit">Submit</button>
        <button type="button" (click)="resetEditForm()" class="button-back">
          Reset
        </button>
      </div>
    </form>
  </div>
</app-offcanvas>

<!-- End Transfer User Modal -->
<!-- Delete Confirmation Modal -->
<div class="modal fade" #deleteConfirmationModalElement id="deleteConfirmationModal" tabindex="-1"
  aria-labelledby="deleteConfirmationModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content modelStyle">
      <div class="modal-header bg-danger text-white"> <!-- Danger header -->
        <h5 class="modal-title" id="deleteConfirmationModalLabel"> Confirmation
          <!-- <i class="bi bi-exclamation-triangle-fill me-2"></i> Confirmation -->
        </h5>
        <button type="button" class="btn-close btn-close-white" aria-label="Close"
          (click)="closeDeleteConfirmation()"></button>
      </div>
      <div class="modal-body">
        <p>Are you sure you want to logout ?</p>
      </div>
      <div class="modal-footer justify-content-center"> <!-- Center buttons -->
        <button type="button" class="btn btn-secondary" (click)="closeDeleteConfirmation()">
          No<!-- <i class="bi bi-x-lg me-1"></i> Cancel -->
        </button>
        <button type="button" class="btn btn-danger" (click)="confirmDelete()"> Yes
          <!-- <i class="bi bi-trash-fill me-1"></i> Yes -->
        </button>
      </div>
    </div>
  </div>
</div>
<!-- End Delete Confirmation Modal -->
