/* === NO CHANGES NEEDED IN CSS === */
/* Keep your existing styles from the previous overlay example */

.carousel-container {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    padding: 20px;
  }

  .image-container {
    text-align: center;
    margin: 0 15px;
    /* Optional: Add min-width/height if needed for the 'No images' state */
    min-width: 100px;
    min-height: 100px;
    display: flex; /* Helps center placeholder text */
    align-items: center;
    justify-content: center;
    border: 1px solid #eee; /* Optional border for placeholder */
    border-radius: 8px;
  }

  .no-images-placeholder {
    color: #888;
    font-size: 0.9em;
  }

  .carousel-image {
    width: 130px;
    height: 130px;
    object-fit: cover;
    border-radius: 8px;
    cursor: pointer;
    transition: transform 0.2s ease-in-out;
    display: block;
  }

  .carousel-image:hover {
    transform: scale(1.1);
  }

  .nav-button {
    background-color: black;
    color: white;
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    font-size: 18px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    transition: background-color 0.2s ease;
  }

  .nav-button:hover:not(:disabled) { /* Only apply hover if not disabled */
    background-color: #555;
  }

  .nav-button:disabled {
    background-color: #ccc;
    cursor: not-allowed;
    opacity: 0.6;
  }

  /* Overlay Styles */
  .image-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.85);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1050;
    cursor: pointer;
    padding: 20px;
    box-sizing: border-box;
    animation: fadeIn 0.3s ease-out;
  }

  .overlay-image {
    display: block;
    max-width: 90%;
    max-height: 90vh;
    object-fit: contain;
    cursor: default;
    border-radius: 5px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
  }

  .overlay-close-button {
    position: absolute;
    top: 15px;
    right: 20px;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    font-size: 24px;
    line-height: 40px;
    text-align: center;
    cursor: pointer;
    transition: background-color 0.2s ease;
    padding: 0;
    z-index: 1051;
  }

  .overlay-close-button:hover {
    background: rgba(255, 255, 255, 0.4);
  }

  /* Overlay Navigation Buttons */
  .overlay-nav-button {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: none;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    font-size: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background-color 0.2s ease;
    z-index: 1051;
  }

  .overlay-nav-button:hover {
    background: rgba(255, 255, 255, 0.4);
  }

  .overlay-nav-button:disabled {
    opacity: 0.3;
    cursor: not-allowed;
  }

  .overlay-prev {
    left: 20px;
  }

  .overlay-next {
    right: 20px;
  }

  /* Image Counter */
  .overlay-counter {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.5);
    color: white;
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 14px;
    z-index: 1051;
  }

  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }