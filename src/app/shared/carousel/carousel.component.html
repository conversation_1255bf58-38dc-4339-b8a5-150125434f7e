<!-- Keep ALL original class names -->
<div class="carousel-container">
    <button
      type="button"
      class="nav-button prev"
      (click)="prevImage()"
      [disabled]="!images || images.length <= 1"
      >‹</button>

    <div class="image-container">
      <!-- Check if images array exists and has items -->
      <ng-container *ngIf="images && images.length > 0; else noImages">
        <img
          alt="observation thumbnail"
          [src]="getCurrentImageUrl()"
          class="carousel-image"
          (click)="showOverlay(getCurrentImageUrl()!)"
          (keydown.enter)="showOverlay(getCurrentImageUrl()!)"
          tabindex="0"
        />
      </ng-container>
      <!-- Template to show if no images are available -->
      <ng-template #noImages>
        <span class="no-images-placeholder">No images</span> <!-- Optional placeholder -->
      </ng-template>
    </div>

    <button
      type="button"
      class="nav-button next"
      (click)="nextImage()"
      [disabled]="!images || images.length <= 1"
      >›</button>
  </div>

  <!-- Enhanced Image Overlay with Navigation -->
  <div class="image-overlay" *ngIf="isOverlayVisible" (click)="hideOverlay()" (keydown.escape)="hideOverlay()">
    <img
        *ngIf="overlayImageUrl"
        [src]="overlayImageUrl"
        alt="Enlarged observation"
        class="overlay-image"
        (click)="$event.stopPropagation()"
        (keydown)="$event.stopPropagation()"
     />
     <button
        type="button"
        class="overlay-close-button"
        (click)="hideOverlay()"
        aria-label="Close image viewer">×</button>

     <!-- Navigation buttons for overlay -->
     <button
        type="button"
        class="overlay-nav-button overlay-prev"
        (click)="prevImageInOverlay($event)"
        [disabled]="!images || images.length <= 1"
        aria-label="Previous image">
        <span>‹</span>
     </button>
     <button
        type="button"
        class="overlay-nav-button overlay-next"
        (click)="nextImageInOverlay($event)"
        [disabled]="!images || images.length <= 1"
        aria-label="Next image">
        <span>›</span>
     </button>

     <!-- Image counter -->
     <div class="overlay-counter" *ngIf="images && images.length > 1" aria-live="polite">
        {{ currentIndex + 1 }} / {{ images.length }}
     </div>
  </div>