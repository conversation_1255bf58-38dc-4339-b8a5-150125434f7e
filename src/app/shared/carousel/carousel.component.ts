import { CommonModule } from '@angular/common';
import { Component, Input, HostListener, Renderer2, OnInit } from '@angular/core'; // Added Input and OnInit

@Component({
  selector: 'app-carousel', // KEEP YOUR ORIGINAL SELECTOR
  imports:[CommonModule],
  standalone: true,
  templateUrl: './carousel.component.html',
  styleUrls: ['./carousel.component.scss'] // Or .scss
})
export class Carousel implements OnInit { // Implement OnInit for potential initial checks

  // --- Make images an Input property ---
  @Input() images: string[] = []; // Initialize with empty array

  // --- Existing properties ---
  currentIndex = 0;
  isOverlayVisible = false;
  overlayImageUrl: string | null = null;

  constructor(private renderer: Renderer2) {}

  // Optional: Handle potential initial state if needed
  ngOnInit(): void {
    // Reset index if images are initially provided but empty, or handle other setup
    if (!this.images || this.images.length === 0) {
      this.currentIndex = 0; // Or -1 if you prefer to indicate no valid index
    } else {
      this.currentIndex = 0; // Ensure it starts at 0 if images exist
    }
  }


  nextImage(): void {
    // Add guard clause for safety, although disabled attribute should handle UI
    if (!this.images || this.images.length <= 1) {
      return;
    }
    this.currentIndex = (this.currentIndex + 1) % this.images.length;
  }

  prevImage(): void {
    // Add guard clause for safety
    if (!this.images || this.images.length <= 1) {
      return;
    }
    this.currentIndex = (this.currentIndex - 1 + this.images.length) % this.images.length;
  }

  showOverlay(imageUrl: string): void {
    this.overlayImageUrl = imageUrl;
    this.isOverlayVisible = true;
    // Prevent body scroll
    this.renderer.setStyle(document.body, 'overflow', 'hidden');
  }

  hideOverlay(): void {
    this.isOverlayVisible = false;
    this.overlayImageUrl = null;
    // Restore body scroll
    this.renderer.setStyle(document.body, 'overflow', 'auto');
  }

  // Navigation methods for overlay
  nextImageInOverlay(event: Event): void {
    event.stopPropagation(); // Prevent overlay from closing
    this.nextImage();
    this.updateOverlayImage();
  }

  prevImageInOverlay(event: Event): void {
    event.stopPropagation(); // Prevent overlay from closing
    this.prevImage();
    this.updateOverlayImage();
  }

  // Update the overlay image when navigating
  private updateOverlayImage(): void {
    const currentImage = this.getCurrentImageUrl();
    if (currentImage) {
      this.overlayImageUrl = currentImage;
    }
  }

  @HostListener('document:keydown.escape', ['$event'])
  onEscapeHandler(event: KeyboardEvent) {
    if (this.isOverlayVisible) {
      this.hideOverlay();
    }
  }

  @HostListener('document:keydown.arrowleft', ['$event'])
  onLeftArrowHandler(event: KeyboardEvent) {
    if (this.isOverlayVisible && this.images && this.images.length > 1) {
      this.prevImageInOverlay(event);
    }
  }

  @HostListener('document:keydown.arrowright', ['$event'])
  onRightArrowHandler(event: KeyboardEvent) {
    if (this.isOverlayVisible && this.images && this.images.length > 1) {
      this.nextImageInOverlay(event);
    }
  }

  // Helper to safely get the current image URL for the template
  getCurrentImageUrl(): string | null {
    if (this.images && this.images.length > 0 && this.currentIndex >= 0 && this.currentIndex < this.images.length) {
      return this.images[this.currentIndex];
    }
    return null; // Return null if no image is available/valid
  }
}