import { CommonModule } from '@angular/common';
import { Component, Input, OnChanges, SimpleChanges } from '@angular/core';

@Component({
  selector: 'app-video-player',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="video-player-container">
      <video 
        *ngIf="videoUrl" 
        controls 
        class="video-player"
        [src]="videoUrl"
        (error)="onVideoError($event)">
        Your browser does not support the video tag.
      </video>
      <div *ngIf="!videoUrl" class="no-video-message">
        No video selected
      </div>
      <div *ngIf="errorMessage" class="error-message alert alert-danger mt-2">
        {{ errorMessage }}
      </div>
    </div>
  `,
  styles: [`
    .video-player-container {
      width: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
    }
    
    .video-player {
      max-width: 100%;
      max-height: 70vh;
      border-radius: 4px;
    }
    
    .no-video-message {
      padding: 2rem;
      color: #6c757d;
      text-align: center;
    }
    
    .error-message {
      width: 100%;
      text-align: center;
      font-size: 0.9rem;
    }
  `]
})
export class VideoPlayer implements OnChanges {
  @Input() videoUrl: string | null = null;
  errorMessage: string | null = null;
  
  ngOnChanges(changes: SimpleChanges): void {
    if (changes['videoUrl']) {
      this.errorMessage = null;
    }
  }
  
  onVideoError(event: Event): void {
    console.error('Video error:', event);
    this.errorMessage = 'Error loading video. The format may not be supported or the file may be unavailable.';
  }
}
