import { Component, EventEmitter, Input, Output } from '@angular/core';

@Component({
  selector: 'app-offcanvas',
  imports: [],
  templateUrl: './offcanvas.component.html',
  styleUrl: './offcanvas.component.scss'
})
export class OffcanvasComponent {
  @Output() closeModal = new EventEmitter<void>();
  @Output() onClickCross = new EventEmitter<void>();

  @Input() title: string = '';
  @Input() width: string = '350px'
  ngOnInit() {
    document.body.classList.add('custom-modal-open'); //For body section overlay hide
  }

  ngOnDestroy() {
    document.body.classList.remove('custom-modal-open');
  }

  close() {
    this.closeModal.emit();
  }

  crossClick() {
    this.onClickCross.emit();
  }
}
