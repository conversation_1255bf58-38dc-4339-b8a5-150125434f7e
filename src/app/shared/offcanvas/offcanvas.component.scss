.custom-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.30);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    animation: fadeIn 0.3s forwards;
}

@keyframes fadeIn {
    to {
        opacity: 1;
    }
}

.custom-modal-content {
    background: white;
    // padding: 20px;
    border-radius: 5px;
    max-width: 1100px;
    // width: 50%;
    z-index: 1000;
    position: fixed;
    right: 0;
    top: 0;
    height: 100%;
    transform: translateX(100%);
    animation: slideIn 0.5s forwards;
    overflow-x: hidden;
}

@keyframes slideIn {
    to {
      transform: translateX(0);
    }
  }

.custom-modal-header {
    position: sticky;
    top: 0;
    z-index: 10;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 53px;
    background: linear-gradient(90deg, #0b74b0 0%, #75479c 52.08%, #bd3861 100%);
    padding: 0 20px;
    color: #fff;
}

.close-button {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #fff;
}

.custom-modal-body {
    height: 100%;
    margin: 20px 10px;
}

.custom-modal-footer {
    text-align: right;
    margin: 0 10px;
}