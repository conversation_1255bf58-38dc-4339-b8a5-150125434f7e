import { Injectable } from '@angular/core';
import { ApiService } from '../api.service';
import { ENDPOINTS } from '../../core/endpoints';

@Injectable({
  providedIn: 'root'
})
export class DashboardService {

  constructor(readonly apiService: ApiService) { }

  async getDashboard(params?: any) {
    return await this.apiService.getData(ENDPOINTS.DASHBOARD.ADMIN_DASHBOARD, params);
  }

  async getClusterWiseGraph(params: any) {
    return await this.apiService.postData(ENDPOINTS.DASHBOARD.CLUSTER_WISE_GRAPH, params);
  }

  async getClusterWiseStatusGraph(params: any) {
    return await this.apiService.postData(ENDPOINTS.DASHBOARD.CLUSTER_WISE_STATUS, params);
  }

  async getTourObservationCount(params: any) {
    return await this.apiService.getData(ENDPOINTS.DASHBOARD.TOUR_OBSERVATION_COUNT, params);
  }

  async getPlantTourCount(params: any) {
    return await this.apiService.postData(ENDPOINTS.DASHBOARD.PLANT_TOUR_COUNT, params);
  }
}
