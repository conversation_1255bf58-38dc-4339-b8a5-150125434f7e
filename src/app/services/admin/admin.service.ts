import { Injectable } from '@angular/core';
import { ApiService } from '../api.service';
import { ENDPOINTS } from '../../core/endpoints';

@Injectable({
  providedIn: 'root'
})
export class AdminService {
  getAdminsByIds(otherMemberIdsToFetch: number[]) {
      throw new Error('Method not implemented.');
  }

  constructor(readonly apiService: ApiService) { }

  async getAdmin(param:any){
    return await this.apiService.getData(ENDPOINTS.ADMINS.GET_ADMINS,param)
  }

  async getDeletedUserLog(param: any){
    return await this.apiService.getData(ENDPOINTS.ADMINS.GET_DELETED_USER,param)
  }

  async getPlantTransferRequest(param: any){
    return await this.apiService.getData(ENDPOINTS.ADMINS.PLANT_TRANSFER_REQUEST,param)
  }

  async getAdminRoles(param: any){
    return await this.apiService.getData(ENDPOINTS.ADMINS.ADMIN_ROLES,param)
  }

  async checkContactNumber(data: any) {
    return await this.apiService.postData(ENDPOINTS.ADMINS.CHECK_CONTACT_NUMBER, data);
  }

  async getDeviceDetails(param: any){
    return await this.apiService.getData(ENDPOINTS.DEVICES.GET_DEVICES,param)
  }

  async deletAdmin(data: any){
    return await this.apiService.postData(ENDPOINTS.ADMINS.ADMIN_DELETE,data)
  }

  async reactivateUser(data: any){
    return await this.apiService.postData(ENDPOINTS.ADMINS.DELETE,data)
  }

  async updatePantTransferRequest(data:any){
    return await this.apiService.postData(ENDPOINTS.ADMINS.PLANT_TRANSFER_UPDATE,data)
  }
  async checkEmailAndRole(data: any) {
    return await this.apiService.postData(ENDPOINTS.ADMINS.CHECK_ADMIN_EMAIL, data);
  }
}
