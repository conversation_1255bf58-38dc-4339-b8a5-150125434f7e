import { Injectable } from '@angular/core';
import axios, { AxiosInstance, AxiosRequestConfig } from 'axios';
import { Router } from '@angular/router';
import { environment } from '../enviornments/enviornments';
import { createAxiosConfig } from '../core/utilities/axios-param-config';
import { ENDPOINTS } from '../core/endpoints';

@Injectable({
  providedIn: 'root'
})
export class ApiService {
  private axiosInstance: AxiosInstance;

  constructor(private router: Router) {
    this.axiosInstance = axios.create({
      baseURL: environment.apiUrl, // Set your base API URL here
      headers: {
        'Access-Control-Allow-Origin': '*',
      },
    });

    // Request Interceptor
    this.axiosInstance.interceptors.request.use(config => {
      const authToken = localStorage.getItem('token');
      // Don't set content-type for FormData - axios will set it automatically

      if (config.data instanceof FormData) {
        config.headers['Content-Type'] = 'multipart/form-data';
      } else {
        config.headers['Content-Type'] = 'application/json';
      }

      if (authToken) {
        config.headers.Authorization = `Bearer ${authToken}`;
      }
      return config;
    });

    // Response Interceptor
    this.axiosInstance.interceptors.response.use(
      response => response,
      error => {
        console.log(error.response?.status); // Log the status code if available
        const status = error.response ? error.response.status : null; // Correct key for the status code
        if (status === 401) {
          // Handle unauthorized errors
          localStorage.clear();
          this.router.navigate(['']);
        } else if (status === 404) {
          // Handle not found errors
          console.log('Resource not found');
        } else {
          // Handle other errors
          console.error('An error occurred:', error.message);
        }


        return Promise.reject(error); // Forward the error for further handling
      }
    );


  }

  private getBusinessUnitIdFromLocalStorage(): number {
    let businessUnitId: number = 0; // Default to 0

    try {
      // First check if there's a selected business unit ID in localStorage
      const selectedBusinessUnitId = localStorage.getItem('selectedBusinessUnitId');

      if (selectedBusinessUnitId) {
        const selectedId = parseInt(selectedBusinessUnitId, 10);
        if (!isNaN(selectedId)) {
          console.log('API Service: Using selected business unit ID:', selectedId);
          return selectedId;
        }
      }

      // If no selected business unit, fall back to user's business unit
      const userString = localStorage.getItem('user');
      if (userString) {
        const user = JSON.parse(userString);
        if (user && typeof user.businessUnitId === 'number') {
          businessUnitId = user.businessUnitId;
        }
      }
    } catch (error) {
      console.error('Error parsing user from local storage:', error);
    }

    return businessUnitId;
  }

  /**
   * Adds businessUnitId filter to params if not already present
   * Uses selected business unit ID from localStorage if available, otherwise falls back to user's business unit ID
   * @param params Original parameters object
   * @returns Updated parameters with businessUnitId filter
   */
  private addBusinessUnitIdFilter(params: any): any {
    const businessUnitId = this.getBusinessUnitIdFromLocalStorage();
    const businessUnitFilter = `businessUnitId||eq||${businessUnitId}`;

    // If no params, create a simple config with businessUnitId filter
    if (!params) {
      return createAxiosConfig({
        filter: [businessUnitFilter]
      });
    }

    // Case 1: Params is already an AxiosRequestConfig with params property
    if (params.params) {
      const configCopy = { ...params };

      // Initialize filter array if it doesn't exist
      if (!configCopy.params.filter) {
        configCopy.params.filter = [];
      }

      // Add businessUnitId filter if not present
      const hasFilter = configCopy.params.filter.some((filter: string) =>
        filter.startsWith('businessUnitId||'));

      if (!hasFilter) {
        configCopy.params.filter.push(businessUnitFilter);
      }

      return configCopy;
    }

    // Case 2: Params is a plain object, convert to AxiosRequestConfig
    const paramsObj = { ...params };

    // Initialize filter array if it doesn't exist
    if (!paramsObj.filter) {
      paramsObj.filter = [businessUnitFilter];
    }
    // Add businessUnitId filter if not present
    else if (Array.isArray(paramsObj.filter) &&
      !paramsObj.filter.some((filter: string) => filter.startsWith('businessUnitId||'))) {
      paramsObj.filter.push(businessUnitFilter);
    }

    // Create proper AxiosRequestConfig using the utility
    return createAxiosConfig(paramsObj);
  }

  // GET request
  async getData(endpoint: string, params?: any) {
    let finalParams = params;
    if (endpoint !== ENDPOINTS.ADMINS.ADMIN_ROLES &&
        endpoint !== ENDPOINTS.BUSINESS_UNIT.GET_BUSINESS_UNIT &&
        endpoint !== ENDPOINTS.DASHBOARD.ADMIN_DASHBOARD &&
        endpoint !== ENDPOINTS.DASHBOARD.TOUR_OBSERVATION_COUNT &&
        endpoint !== ENDPOINTS.SAFETY_TRAINING.PARTICIPANT_TYPE_COUNT &&
        endpoint !== ENDPOINTS.SAFETY_TRAINING.TRAINING_MONTH_WISE_MAN_HOUR_COUNT &&
        endpoint !== ENDPOINTS.SAFETY_TRAINING.MALE_FEMALE_COUNT &&
        endpoint !== ENDPOINTS.SAFETY_TRAINING.TRAINING_MONTH_WISE_COUNT &&
        endpoint !== ENDPOINTS.SAFETY_TRAINING.TRAINING_TYPE_COUNT &&
        endpoint !== ENDPOINTS.SAFETY_TRAINING.AVERAGE_PARTICIPANT_PER_TRAINING &&
        endpoint !== ENDPOINTS.DIGISAFE.GMR_COUNT &&
        endpoint !== ENDPOINTS.DIGISAFE.MIS_COUNT &&
        endpoint !== ENDPOINTS.DIGISAFE.UNFILLED_REPORT &&
        endpoint !== ENDPOINTS.SAFETY_TRAINING.TRAINING_DATA_MONTH_WISE &&
        endpoint !== ENDPOINTS.SETTINGS.GET_GLOBAL_ADMIN_SETTINGS &&
        endpoint !== ENDPOINTS.FEATURE_MASTER.GET_FEATURE_MASTER &&
        endpoint !== ENDPOINTS.ADD_DJP.FEEDBACK_DJP) { // Exclude for global admin settings
      // Add businessUnitId=0 to all GET requests if not already present
      finalParams = this.addBusinessUnitIdFilter(params);
    }

    const response = await this.axiosInstance.get(endpoint, finalParams);
    return response.data;
  }

  // POST request
  async postData(endpoint: string, data: any) {
    let finalData: any;

    // Handle upload endpoint separately to preserve FormData structure
    if (endpoint === ENDPOINTS.UPLOAD.UPLOAD_FILE || endpoint === ENDPOINTS.ADD_DJP.BULK_UPLOAD_EXCEL) {
      // For upload endpoint, use the data directly without modification
      finalData = data;
    } else {
      // For other endpoints, create a copy and add businessUnitId if needed
      finalData = { ...data };

      // Add businessUnitId to the data payload for POST requests
      if (typeof finalData === 'object' && finalData !== null && !(finalData instanceof FormData)) {
        const businessUnitId = this.getBusinessUnitIdFromLocalStorage();

        // Check if there's a selected business unit ID that should override the existing one
        const selectedBusinessUnitId = localStorage.getItem('selectedBusinessUnitId');

        if (selectedBusinessUnitId) {
          // Override existing businessUnitId with selected one
          finalData.businessUnitId = businessUnitId;
          console.log(`Overrode businessUnitId with selected value ${businessUnitId} for endpoint: ${endpoint}`, finalData);
        } else {
          // Only add businessUnitId if it doesn't already exist in the data
          // This prevents overwriting businessUnitId in registration forms where user selects it
          if (finalData.businessUnitId === undefined || finalData.businessUnitId === null) {
            finalData.businessUnitId = businessUnitId;
            console.log(`Added businessUnitId ${businessUnitId} to POST data for endpoint: ${endpoint}`, finalData);
          } else {
            console.log(`BusinessUnitId already exists in POST data for endpoint: ${endpoint}`, finalData);
          }
        }
      }
    }

    const response = await this.axiosInstance.post(endpoint, finalData);
    return response.data;
  }

  // PUT request
  async putData(endpoint: string, data: any) {
    let finalData = { ...data };

    // Add/override businessUnitId for PUT requests
    if (typeof finalData === 'object' && finalData !== null && !(finalData instanceof FormData)) {
      const businessUnitId = this.getBusinessUnitIdFromLocalStorage();
      const selectedBusinessUnitId = localStorage.getItem('selectedBusinessUnitId');

      if (selectedBusinessUnitId) {
        finalData.businessUnitId = businessUnitId;
        console.log(`Overrode businessUnitId with selected value ${businessUnitId} for PUT endpoint: ${endpoint}`, finalData);
      } else if (finalData.businessUnitId === undefined || finalData.businessUnitId === null) {
        finalData.businessUnitId = businessUnitId;
        console.log(`Added businessUnitId ${businessUnitId} to PUT data for endpoint: ${endpoint}`, finalData);
      }
    }

    const response = await this.axiosInstance.put(endpoint, finalData);
    return response.data;
  }

  // PATCH request
  async patchData(id: any, endpoint: string, data: any) {
    let finalData = { ...data };

    // Add/override businessUnitId for PATCH requests
    if (typeof finalData === 'object' && finalData !== null && !(finalData instanceof FormData)) {
      const businessUnitId = this.getBusinessUnitIdFromLocalStorage();
      const selectedBusinessUnitId = localStorage.getItem('selectedBusinessUnitId');

      if (selectedBusinessUnitId) {
        finalData.businessUnitId = businessUnitId;
        console.log(`Overrode businessUnitId with selected value ${businessUnitId} for PATCH endpoint: ${endpoint}`, finalData);
      } else if (finalData.businessUnitId === undefined || finalData.businessUnitId === null) {
        finalData.businessUnitId = businessUnitId;
        console.log(`Added businessUnitId ${businessUnitId} to PATCH data for endpoint: ${endpoint}`, finalData);
      }
    }

    const response = await this.axiosInstance.patch(`${endpoint}/${id}`, finalData);
    return response.data;
  }

  // DELETE request
  async deleteData(id: any, endpoint: string, data: any) {
    let finalData = { ...data };

    // Add/override businessUnitId for DELETE requests
    if (typeof finalData === 'object' && finalData !== null && !(finalData instanceof FormData)) {
      const businessUnitId = this.getBusinessUnitIdFromLocalStorage();
      const selectedBusinessUnitId = localStorage.getItem('selectedBusinessUnitId');

      if (selectedBusinessUnitId) {
        finalData.businessUnitId = businessUnitId;
        console.log(`Overrode businessUnitId with selected value ${businessUnitId} for DELETE endpoint: ${endpoint}`, finalData);
      } else if (finalData.businessUnitId === undefined || finalData.businessUnitId === null) {
        finalData.businessUnitId = businessUnitId;
        console.log(`Added businessUnitId ${businessUnitId} to DELETE data for endpoint: ${endpoint}`, finalData);
      }
    }

    const response = await this.axiosInstance.patch(`${endpoint}/${id}`, finalData);
    return response.data;
  }
}
