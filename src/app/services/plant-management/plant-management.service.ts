import { Injectable } from '@angular/core';
import { ApiService } from '../api.service';
import { ENDPOINTS } from '../../core/endpoints';

@Injectable({
  providedIn: 'root'
})
export class PlantManagementService {

  constructor(readonly apiService: ApiService) { }

  /**
   * Trims all string values in an object (including nested objects)
   * @param data Object containing string fields to trim
   * @returns Object with trimmed string values
   */
  private trimStringFields(data: any): any {
    if (!data || typeof data !== 'object') {
      return data;
    }

    // Handle arrays
    if (Array.isArray(data)) {
      return data.map(item => this.trimStringFields(item));
    }

    // Handle objects
    const trimmedData = { ...data };
    Object.keys(trimmedData).forEach(key => {
      const value = trimmedData[key];
      if (typeof value === 'string') {
        trimmedData[key] = value.trim();
      } else if (value && typeof value === 'object') {
        trimmedData[key] = this.trimStringFields(value);
      }
    });

    return trimmedData;
  }

  async getPlants(params: any) {
    // Trim string values in filter parameters if they exist
    if (params?.params?.filter) {
      const filters = params.params.filter;
      params.params.filter = filters.map((filter: string) => {
        // Only trim the value part of the filter (after the last ||)
        const parts = filter.split('||');
        if (parts.length >= 3 && typeof parts[2] === 'string') {
          parts[2] = parts[2].trim();
          return parts.join('||');
        }
        return filter;
      });
    }

    const response = await this.apiService.getData(ENDPOINTS.PLANTS.GET_PLANTS, params);
    return response;
  }

  async getPlantAdmin(data: any) {
    const trimmedData = this.trimStringFields(data);
    const response = await this.apiService.postData(ENDPOINTS.ADMINS.GET_PLANT_ADMIN, trimmedData);
    return response;
  }

  async createPlant(data: any) {
    const trimmedData = this.trimStringFields(data);
    const response = await this.apiService.postData(ENDPOINTS.PLANTS.GET_PLANTS, trimmedData);
    return response;
  }

  async exportPlantQr(data: any) {
    const trimmedData = this.trimStringFields(data);
    const response = await this.apiService.postData(ENDPOINTS.PLANTS.QR_CODE_PLANT, trimmedData);
    return response;
  }

  async requestPlantTransfer(data: any) {
    const trimmedData = this.trimStringFields(data);
    const response = await this.apiService.postData(ENDPOINTS.PLANTS.REQUEST_PLANT_TRANSFER, trimmedData);
    return response;
  }
}
