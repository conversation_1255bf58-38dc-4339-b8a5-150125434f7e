import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { AuthService } from '../auth.service';
import { AdminService } from '../admin/admin.service';
import { environment } from '../../enviornments/enviornments';

@Injectable({
  providedIn: 'root'
})
export class SamlService {

  constructor(
    private authService: AuthService,
    private adminService: AdminService,
    private router: Router
  ) { }

  /**
   * Initiates SAML SSO login by redirecting to the SAML provider
   */
  initiateSSOLogin(): void {
    // Remove trailing slash from apiUrl if present, then add the endpoint
    const apiUrl = environment.apiUrl.endsWith('/') ? environment.apiUrl.slice(0, -1) : environment.apiUrl;
    const baseUrl = `${apiUrl}/saml/login`;
    console.log('Initiating SAML SSO to:', baseUrl);
    window.location.href = baseUrl;
  }

  /**
   * Verifies SAML token received from callback
   * @param token SAML token from callback
   * @returns Promise with response data
   */
  async verifySamlToken(token: string): Promise<any> {
    try {
      const data = { token };
      const response = await this.authService.verifySamlToken(data);
      
      console.log('SAML Token Verification Response:', response);
      
      // Handle different response codes
      switch (response.responseCode) {
        case 200:
          // Success - token stored in authService.verifySamlToken
          return { success: true, message: 'Login successful', data: response };
        case 400:
          return { success: false, message: 'Invalid User', code: 400 };
        case 404:
          return { success: false, message: 'User not found', code: 404 };
        case 500:
          return { success: false, message: 'Access Denied', code: 500 };
        default:
          return { success: false, message: 'Access Denied', code: response.responseCode };
      }
    } catch (error) {
      console.error('SAML Token Verification Error:', error);
      return { success: false, message: 'Token verification failed', error };
    }
  }

  /**
   * Checks if email exists and has proper role for SAML login
   * @param email Email to check
   * @returns Promise<boolean> indicating if email is valid for SAML
   */
  async checkEmailForSamlAccess(email: string): Promise<boolean> {
    try {
      const data = { email: email.toLowerCase().trim() };
      const response = await this.adminService.checkEmailAndRole(data);
      
      console.log('Email check response:', response);
      
      return response?.responseCode === 200;
    } catch (error) {
      console.error('Error checking email for SAML access:', error);
      return false;
    }
  }

  /**
   * Parses URL parameters to extract SAML token
   * @param url URL to parse (optional, uses current URL if not provided)
   * @returns token string or null
   */
  extractTokenFromUrl(url?: string): string | null {
    const urlToCheck = url || window.location.href;
    
    try {
      // Check for redirect parameter first
      const urlParams = new URLSearchParams(window.location.search);
      const redirect = urlParams.get('redirect');
      
      if (redirect) {
        // Parse the redirect URL to get the token
        const redirectParams = new URLSearchParams(redirect.split('?')[1]);
        return redirectParams.get('token');
      }
      
      // Fallback: check for direct token parameter
      return urlParams.get('token');
    } catch (error) {
      console.error('Error extracting token from URL:', error);
      return null;
    }
  }

  /**
   * Handles the complete SAML callback flow
   * @param token SAML token
   * @param redirectPath Path to redirect after successful login
   * @returns Promise with result
   */
  async handleSamlCallback(token: string, redirectPath: string = '/home'): Promise<any> {
    const verificationResult = await this.verifySamlToken(token);
    
    if (verificationResult.success) {
      // Redirect to the specified path
      this.router.navigate([redirectPath]);
      return verificationResult;
    }
    
    return verificationResult;
  }

  /**
   * Complete SAML login flow with email validation
   * @param email Email to validate
   * @returns Promise indicating success/failure
   */
  async performSamlLogin(email: string): Promise<{ success: boolean; message: string }> {
    try {
      // First check if email is valid for SAML access
      const hasAccess = await this.checkEmailForSamlAccess(email);
      
      if (!hasAccess) {
        return {
          success: false,
          message: 'Unauthorized access: You don\'t have permission to access this resource. Please log in with the correct credentials.'
        };
      }
      
      // Add a small delay to prevent rapid redirects and potential loops
      setTimeout(() => {
        this.initiateSSOLogin();
      }, 500);
      
      return {
        success: true,
        message: 'Redirecting to SAML login...'
      };
    } catch (error) {
      console.error('SAML login error:', error);
      return {
        success: false,
        message: 'Error during SAML login process'
      };
    }
  }
}