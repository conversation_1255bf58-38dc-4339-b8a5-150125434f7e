import { Injectable } from '@angular/core';
import { ApiService } from '../api.service';
import { ENDPOINTS } from '../../core/endpoints';

@Injectable({
  providedIn: 'root'
})
export class ZoneService {

  constructor(readonly apiService: ApiService) { }

  async getZone(param: any){
    return await this.apiService.getData(ENDPOINTS.ZONE.GET_ZONE, param);
  }

  async createZone(newZoneData: any) {
    return await this.apiService.postData(ENDPOINTS.ZONE.GET_ZONE, newZoneData);
  }
}
