import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class ManageSideMenuService {
  private readonly sidebarStateSource = new BehaviorSubject<boolean>(false);
  headerClicked$ = this.sidebarStateSource.asObservable();

  // Store sidebar state in localStorage to persist between page refreshes
  private readonly STORAGE_KEY = 'sidebar_collapsed';

  constructor() {
    // Initialize from localStorage if available
    const savedState = localStorage.getItem(this.STORAGE_KEY);
    if (savedState !== null) {
      this.sidebarStateSource.next(savedState === 'true');
    }
  }

  triggerHeaderClick() {
    // Get current state and toggle it
    const currentState = this.sidebarStateSource.value;
    const newState = !currentState;

    // Update the state
    this.sidebarStateSource.next(newState);

    // Log for debugging
    console.log(`Sidebar state changed: ${currentState} -> ${newState}`);

    // Save state to localStorage
    localStorage.setItem(this.STORAGE_KEY, String(newState));
  }

  // Explicitly expand the sidebar
  expandSidebar() {
    console.log('Expanding sidebar');
    this.sidebarStateSource.next(false);
    localStorage.setItem(this.STORAGE_KEY, 'false');
  }

  // Explicitly collapse the sidebar
  collapseSidebar() {
    console.log('Collapsing sidebar');
    this.sidebarStateSource.next(true);
    localStorage.setItem(this.STORAGE_KEY, 'true');
  }

  // Get current sidebar state
  get isCollapsed(): boolean {
    return this.sidebarStateSource.value;
  }
}
