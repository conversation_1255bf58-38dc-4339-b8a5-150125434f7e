import { Injectable } from '@angular/core';
import { ENDPOINTS } from '../../core/endpoints';
import { AppSettingsResponse } from '../../model/app-settings.model';
import { ApiService } from '../api.service';
import { AuthService } from '../auth.service'; // Import AuthService

@Injectable({
  providedIn: 'root'
})
export class AppSettingsService {

  constructor(readonly apiService: ApiService, private authService: AuthService) { }

  async getSettings(params: any): Promise<AppSettingsResponse[]> {
    const adminsRoleId = this.authService.getAdminsRoleId();

    if (adminsRoleId === 6) { // Group Admin
      const response = await this.apiService.getData(ENDPOINTS.SETTINGS.GET_GLOBAL_ADMIN_SETTINGS, params);
      // The endpoint returns a single object within an array, so extract the first element
      return response as AppSettingsResponse[]; // Response is already an array
    } else { // Other admins (including Super Admin)
      const response = await this.apiService.getData(ENDPOINTS.SETTINGS.GET_SETTINGS, params);
      return response as AppSettingsResponse[];
    }
  }
}
