import { Injectable } from '@angular/core';
import { ApiService } from '../api.service';
import { ENDPOINTS } from '../../core/endpoints';

@Injectable({
  providedIn: 'root'
})
export class TourManagementService {

  constructor(readonly apiService: ApiService) { }

  async getTours(param: any) {
    return await this.apiService.getData(ENDPOINTS.TOUR.GET_TOUR,param);
  }

  async getTourScanPoint(param: any) {
    return await this.apiService.getData(ENDPOINTS.TOUR.TOUR_SCAN_POINT,param);
  }
}
