
import { Injectable } from '@angular/core';
import { ApiService } from '../api.service';
import { Observable } from 'rxjs';
import { BusinessUnit } from '../../model/business-unit.model';

@Injectable({
  providedIn: 'root'
})
export class BusinessUnitService {

  constructor(private apiService: ApiService) { }

  async getBusinessUnits(params?: any): Promise<any> {
    return this.apiService.getData('/business-unit', params);
  }

  async createBusinessUnit(data: any): Promise<BusinessUnit> {
    return this.apiService.postData('/business-unit', data);
  }
}
