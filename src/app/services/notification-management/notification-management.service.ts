import { Injectable } from '@angular/core';
import { ApiService } from '../api.service';
import { ENDPOINTS } from '../../core/endpoints';

@Injectable({
  providedIn: 'root'
})
export class NotificationManagementService {

  constructor(readonly apiService: ApiService) { }

  async getNotifications(param: any) {
    return await this.apiService.getData(ENDPOINTS.NOTIFICATION.GET_NOTIFICATION, param);
  }

  async createNotification(data: any) {
    return await this.apiService.postData(ENDPOINTS.NOTIFICATION.GET_NOTIFICATION, data);
  }

  async updateNotification(id: number, data: any) {
    return await this.apiService.putData(`${ENDPOINTS.NOTIFICATION.GET_NOTIFICATION}/${id}`, data);
  }

  async createNotificationCustom(data: any) {
    // Use string literal for the endpoint to avoid TypeScript errors
    return await this.apiService.postData('/Notifications/send-notification-plant-wise', data);
  }

  async getUsersByPlantIds(data: { plantIds: number[] }) {
    // Use string literal for the endpoint to match the old project
    return await this.apiService.postData('/Notifications/get-user', data);
  }
}
