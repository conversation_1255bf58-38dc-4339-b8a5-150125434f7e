import { Injectable } from '@angular/core';
import { ApiService } from '../api.service';
import { ENDPOINTS } from '../../core/endpoints';

@Injectable({
  providedIn: 'root'
})
export class OtherTaskService {

  constructor(readonly apiService: ApiService) { }

  async getOtherTasks(params: any) {
    return await this.apiService.getData(ENDPOINTS.OTHER_TASK.GET_OTHER_TASK, params);
  }

  async createOtherTask(data: any) {
    return await this.apiService.postData(ENDPOINTS.OTHER_TASK.GET_OTHER_TASK, data);
  }
}
