import { Injectable } from '@angular/core';
import { ApiService } from '../api.service';
import { ENDPOINTS } from '../../core/endpoints';

@Injectable({
  providedIn: 'root'
})
export class QrCodeService {

  constructor(readonly apiService: ApiService) { }
  
  async getQrCode(params: any){
    const response = await this.apiService.getData(ENDPOINTS.QRCODE.GET_QRCODE,params)
    return response
  }
}
