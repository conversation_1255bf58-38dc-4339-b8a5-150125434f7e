import { Injectable } from '@angular/core';
import { ApiService } from '../../api.service';
import { ENDPOINTS } from '../../../core/endpoints';

@Injectable({
  providedIn: 'root'
})
export class RootCauseService {

  constructor(readonly apiService: ApiService) { }

  async getRootCause(params: any) {
    return await this.apiService.getData(ENDPOINTS.ROOT_CAUSE.ROOT_CAUSE_MASTER, params);
  }

  createRootCause(newRootCauseData: any) {
    return this.apiService.postData(ENDPOINTS.ROOT_CAUSE.ROOT_CAUSE_MASTER, newRootCauseData);
  }
}
