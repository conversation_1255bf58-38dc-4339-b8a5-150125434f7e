import { Injectable } from '@angular/core';
import { ApiService } from '../../api.service';
import { ENDPOINTS } from '../../../core/endpoints';

@Injectable({
  providedIn: 'root'
})
export class OpcoService {

  constructor(readonly apiService: ApiService) { }

  async getOpco(params: any) {
    return await this.apiService.getData(ENDPOINTS.OPCO.GET_OPCO, params);
  }

  async createOpco(newOpcoData: any) {
    return await this.apiService.postData(ENDPOINTS.OPCO.GET_OPCO, newOpcoData);
  }
  async getFacility(params: any) {
    return await this.apiService.getData(ENDPOINTS.OPCO.GET_FACILITY, params);
  }
}
