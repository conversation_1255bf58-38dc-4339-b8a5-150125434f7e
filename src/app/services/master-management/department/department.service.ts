import { Injectable } from '@angular/core';
import { ApiService } from '../../api.service';
import { ENDPOINTS } from '../../../core/endpoints';

@Injectable({
  providedIn: 'root'
})
export class DepartmentService {

  constructor(readonly apiService: ApiService) { }

  async getDepartments(params: any) {
    const response = await this.apiService.getData(ENDPOINTS.DEPARTMENTS.GET_DEPARTMENTS, params)
    return response;
  }

  async createDepartment(newDepartmentData: any) {
    return await this.apiService.postData(ENDPOINTS.DEPARTMENTS.GET_DEPARTMENTS, newDepartmentData);
  }
}
