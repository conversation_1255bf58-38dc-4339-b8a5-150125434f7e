import { Injectable } from '@angular/core';
import { ApiService } from '../../api.service';
import { ENDPOINTS } from '../../../core/endpoints';

@Injectable({
  providedIn: 'root'
})
export class DesignationService {

  constructor(readonly apiService: ApiService) { }

  async getDesignation(params: any) {
    const response = await this.apiService.getData(ENDPOINTS.DESIGNATIONS.GET_DESIGNATION, params)
    return response;
  }

  async createDesignation(newDesignationData: any) {
    return await this.apiService.postData(ENDPOINTS.DESIGNATIONS.GET_DESIGNATION, newDesignationData);
  }
}
