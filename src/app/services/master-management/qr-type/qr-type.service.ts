import { Injectable } from '@angular/core';
import { ApiService } from '../../api.service';
import { ENDPOINTS } from '../../../core/endpoints';

@Injectable({
  providedIn: 'root'
})
export class QrTypeService {

  constructor(readonly apiService: ApiService) { }

  async getQrType(params: any) {
    return await this.apiService.getData(ENDPOINTS.QRCODE.QR_TYPE, params);
  }

  async createQrType(newQrTypeData: any) {
    return await this.apiService.postData(ENDPOINTS.QRCODE.QR_TYPE, newQrTypeData);
  }
}
