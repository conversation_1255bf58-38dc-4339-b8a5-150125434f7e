import { Injectable } from '@angular/core';
import { ApiService } from '../../api.service';
import { ENDPOINTS } from '../../../core/endpoints';

@Injectable({
  providedIn: 'root'
})
export class PlantTypeService {

  constructor(readonly apiService: ApiService) { }

  async getPlantType(params: any) {
    return await this.apiService.getData(ENDPOINTS.PLANTS.PLANT_TYPE, params);
  }

  async createPlantType(newPlantTypeData: any) {
    return await this.apiService.postData(ENDPOINTS.PLANTS.PLANT_TYPE, newPlantTypeData);
  }
}
