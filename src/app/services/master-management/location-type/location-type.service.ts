import { Injectable } from '@angular/core';
import { ApiService } from '../../api.service';
import { ENDPOINTS } from '../../../core/endpoints';

@Injectable({
  providedIn: 'root'
})
export class LocationTypeService {

  constructor(readonly apiService: ApiService) { }

  async getLocationType(params: any) {
    return await this.apiService.getData(ENDPOINTS.LOCATION.LOCATION_TYPE, params);
  }

  async createLocationType(newLocationTypeData: any) {
    return await this.apiService.postData(ENDPOINTS.LOCATION.LOCATION_TYPE, newLocationTypeData);
  }
}
