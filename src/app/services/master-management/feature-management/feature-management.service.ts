import { Injectable } from '@angular/core';
import { ApiService } from '../../api.service';
import { ENDPOINTS } from '../../../core/endpoints';
import { Feature } from '../../../model/feature.model';

@Injectable({
  providedIn: 'root'
})
export class FeatureManagementService {

  constructor(private apiService: ApiService) { }

  async getFeatures(params: any): Promise<{ data: Feature[], total: number }> {
    return this.apiService.getData(ENDPOINTS.FEATURE.GET_FEATURES, params);
  }

  async updateFeature(payload: any): Promise<any> {
    return this.apiService.putData(ENDPOINTS.FEATURE.GET_FEATURES, payload);
  }

  async createFeature(payload: any): Promise<any> {
    return this.apiService.postData(ENDPOINTS.FEATURE.CREATE_FEATURE, payload);
  }
}