import { Injectable } from '@angular/core';
import { ApiService } from '../../api.service';
import { ENDPOINTS } from '../../../core/endpoints';

@Injectable({
  providedIn: 'root'
})
export class EquipmentService {

  constructor(readonly apiService: ApiService) { }

  async getEquipment(params: any) {
    return await this.apiService.getData(ENDPOINTS.EQUIPMENT.GET_EQUIPMENT_MASTER, params);
  }

  async createEquipment(newEquipmentData: any) {
    return await this.apiService.postData(ENDPOINTS.EQUIPMENT.GET_EQUIPMENT_MASTER, newEquipmentData);
}
}
