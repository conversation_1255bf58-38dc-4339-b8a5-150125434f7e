import { Injectable } from '@angular/core';
import { ApiService } from '../../api.service';
import { ENDPOINTS } from '../../../core/endpoints';

@Injectable({
  providedIn: 'root'
})
export class InspectionToolService {

  constructor(readonly apiService: ApiService) { }

  async getInspectionTool(params: any) {
    return await this.apiService.getData(ENDPOINTS.INSPECTION_TOOL.INSPECTION_TOOL_MASTER, params);
  }

  async createInspectionTool(newInspectionToolData: any) {
    return await this.apiService.postData(ENDPOINTS.INSPECTION_TOOL.INSPECTION_TOOL_MASTER, newInspectionToolData);
  }
}
