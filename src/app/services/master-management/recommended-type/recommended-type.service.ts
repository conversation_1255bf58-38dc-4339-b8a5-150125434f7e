import { Injectable } from '@angular/core';
import { ApiService } from '../../api.service';
import { ENDPOINTS } from '../../../core/endpoints';

@Injectable({
  providedIn: 'root'
})
export class RecommendedTypeService {

  constructor(readonly apiService: ApiService) { }

  async getRecommendedType(params: any) {
    return await this.apiService.getData(ENDPOINTS.RECOMMENDED_TYPE.RECOMMENDED_TYPE_MASTER, params);
  }

  createRecommendedType(newRecommendedTypeData: any) {
    return this.apiService.postData(ENDPOINTS.RECOMMENDED_TYPE.RECOMMENDED_TYPE_MASTER, newRecommendedTypeData);
  }
}
