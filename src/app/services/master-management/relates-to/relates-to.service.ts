import { Injectable } from '@angular/core';
import { ApiService } from '../../api.service';
import { ENDPOINTS } from '../../../core/endpoints';

@Injectable({
  providedIn: 'root'
})
export class RelatesToService {

  constructor(readonly apiService: ApiService) { }

  async getRelatesTo(params: any) {
    return await this.apiService.getData(ENDPOINTS.RELATESTO.GET_RELATESTO, params);
  }

  createRelatesTo(newRelatesToData: any) {
    return this.apiService.postData(ENDPOINTS.RELATESTO.GET_RELATESTO, newRelatesToData);
  }
}
