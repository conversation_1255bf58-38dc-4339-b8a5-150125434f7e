import { Injectable } from '@angular/core';
import { ApiService } from '../../api.service';
import { ENDPOINTS } from '../../../core/endpoints';

@Injectable({
  providedIn: 'root'
})
export class IncidentMasterService {

  constructor(readonly apiService: ApiService) { }

  async getIncidentMaster(params: any) {
    return await this.apiService.getData(ENDPOINTS.INCIDENT.INCIDENT_MASTER, params);
  }

  async createIncidentMaster(newIncidentData: any) {
    return await this.apiService.postData(ENDPOINTS.INCIDENT.INCIDENT_MASTER, newIncidentData);
}
}
