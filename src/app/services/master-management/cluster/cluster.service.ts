import { Injectable } from '@angular/core';
import { ApiService } from '../../api.service';
import { ENDPOINTS } from '../../../core/endpoints';

@Injectable({
  providedIn: 'root'
})
export class ClusterService {

  constructor(readonly apiService: ApiService) { }

  async getCluster(params: any) {
    return await this.apiService.getData(ENDPOINTS.CLUSTER.GET_CLUSTER, params);
  }

  async createCluster(newClusterData: any) {
    return await this.apiService.postData(ENDPOINTS.CLUSTER.GET_CLUSTER, newClusterData);

  }
}
