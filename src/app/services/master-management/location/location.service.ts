import { Injectable } from '@angular/core';
import { ApiService } from '../../api.service';
import { ENDPOINTS } from '../../../core/endpoints';

@Injectable({
  providedIn: 'root'
})
export class LocationService {

  constructor(readonly apiService: ApiService) { }

  async getLocation(params: any) {
    return await this.apiService.getData(ENDPOINTS.LOCATION.LOCATION, params);
  }

  async createLocation(newLocationData: any) {
    return await this.apiService.postData(ENDPOINTS.LOCATION.LOCATION, newLocationData);
  }
}
