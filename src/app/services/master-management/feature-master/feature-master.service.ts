import { Injectable } from '@angular/core';
import { ApiService } from '../../api.service';
import { ENDPOINTS } from '../../../core/endpoints';

@Injectable({
  providedIn: 'root'
})
export class FeatureMasterService {

  constructor(readonly apiService: ApiService) { }

  async getFeatureMaster(params: any) {
    return await this.apiService.getData(ENDPOINTS.FEATURE_MASTER.GET_FEATURE_MASTER, params);
  }

  async createFeatureMaster(newFeatureMasterData: any) {
    return await this.apiService.postData(ENDPOINTS.FEATURE_MASTER.GET_FEATURE_MASTER, newFeatureMasterData);
  }
}