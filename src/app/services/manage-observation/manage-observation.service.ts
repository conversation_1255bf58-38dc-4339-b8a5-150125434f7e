import { Injectable } from '@angular/core';
import { ApiService } from '../api.service';
import { ENDPOINTS } from '../../core/endpoints';

@Injectable({
  providedIn: 'root'
})
export class ManageObservationService {

  constructor(readonly apiService: ApiService) { }

  async getObservations(params: any) {
    return await this.apiService.getData(ENDPOINTS.OBSERVATION.GET_OBSERVATION, params);
  }

  async approveReward(data: any) {
    return await this.apiService.postData(ENDPOINTS.OBSERVATION.APPROVE_REWARD, data);
  }
}
