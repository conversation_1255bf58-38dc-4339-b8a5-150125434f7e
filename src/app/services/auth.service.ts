import { Injectable } from '@angular/core';
import { ApiService } from './api.service';
import { ENDPOINTS } from '../core/endpoints';
import { environment } from '../enviornments/enviornments';

@Injectable({
  providedIn: 'root'
})
export class AuthService {

  constructor(private apiService : ApiService) {}

  sendOtp(credentials : {email : string}){
    return this.apiService.postData(ENDPOINTS.AUTH.SEND_OTP,credentials)
  }

  async verifyOtp(otp : {email: string , otp: string}){
    const response = await this.apiService.postData(ENDPOINTS.AUTH.VERIFY_OTP,otp)
    if(response['responseCode'] == 200){
      localStorage.setItem('token', response['accessToken']);
      localStorage.setItem('user',JSON.stringify(response['user']))
    }
    return response;
  }

  signup(userData: any){
    return this.apiService.postData(ENDPOINTS.AUTH.SIGNUP,userData)
  }

  logout(){
    localStorage.removeItem('token');
    localStorage.removeItem('user'); // Clear user data on logout
  }

  getAdminsRoleId(): number | null {
    const user = localStorage.getItem('user');
    if (user) {
      const userData = JSON.parse(user);
      return userData.adminsRoleId || null;
    }
    return null;
  }

  getBusinessUnitId(): number | null {
    // First check if there's a selected business unit ID in localStorage
    const selectedBusinessUnitId = localStorage.getItem('selectedBusinessUnitId');
    if (selectedBusinessUnitId) {
      const selectedId = parseInt(selectedBusinessUnitId, 10);
      if (!isNaN(selectedId)) {
        console.log('AuthService: Using selected business unit ID:', selectedId);
        return selectedId;
      }
    }

    // If no selected business unit, fall back to user's business unit
    const user = localStorage.getItem('user');
    if (user) {
      const userData = JSON.parse(user);
      // Ensure businessUnitId is returned as a number
      const userBusinessUnitId = typeof userData.businessUnitId === 'number' ? userData.businessUnitId : null;
      console.log('AuthService: Using user business unit ID:', userBusinessUnitId);
      return userBusinessUnitId;
    }
    return null;
  }

  // SAML Login functionality
  initiateSSO(): void {
    // Remove trailing slash from apiUrl if present, then add the endpoint
    const apiUrl = environment.apiUrl.endsWith('/') ? environment.apiUrl.slice(0, -1) : environment.apiUrl;
    const baseUrl = `${apiUrl}/saml/login`;
    console.log('Initiating SAML SSO to:', baseUrl);
    window.location.href = baseUrl;
  }

  async verifySamlToken(data: { token: string }) {
    try {
      const response = await this.apiService.postData(ENDPOINTS.AUTH.SAML_VERIFY, data);
      if (response['responseCode'] === 200) {
        localStorage.setItem('token', response['accessToken']);
        localStorage.setItem('user', JSON.stringify(response['user']));
      }
      return response;
    } catch (error) {
      console.error('SAML token verification failed:', error);
      throw error;
    }
  }

  // Check if user exists and has proper role for SAML login
  async checkEmailAndRole(data: { email: string }) {
    return await this.apiService.postData(ENDPOINTS.ADMINS.CHECK_ADMIN_EMAIL, data);
  }
}
