import { CanActivateFn } from '@angular/router';
import { inject } from '@angular/core';
import { Router } from '@angular/router';

export const checkLoginGuard: CanActivateFn = (route, state) => {
  const router = inject(Router);
  const token = localStorage.getItem('token');

  if (token) {
    router.navigate(['/home/<USER>']); // Redirect to dashboard if logged in
    return false; // Prevent activation of the login route
  }

  return true; // Allow activation of the login route
};
