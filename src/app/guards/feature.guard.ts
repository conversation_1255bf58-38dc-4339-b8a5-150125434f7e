import { Injectable } from '@angular/core';
import { CanActivate, ActivatedRouteSnapshot, RouterStateSnapshot, Router } from '@angular/router';
import { FeatureManagementService } from '../services/master-management/feature-management/feature-management.service';
import { createAxiosConfig } from '../core/utilities/axios-param-config';

@Injectable({
  providedIn: 'root'
})
export class FeatureGuard implements CanActivate {
  private featureRouteStatusMap: { [route: string]: boolean } = {};
  private featuresLoaded = false;

  constructor(
    private featureManagementService: FeatureManagementService,
    private router: Router
  ) {}

  async canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Promise<boolean> {
    try {
      // Check if user is global admin (adminRoleId = 6)
      const userString = localStorage.getItem('user');
      if (userString) {
        const currentUser = JSON.parse(userString);
        const adminRoleId = currentUser?.adminsRoleId;

        // Global admins (adminRoleId = 6) bypass feature hiding
        if (adminRoleId === 6) {
          console.log('FeatureGuard: Global admin detected, bypassing feature check');
          return true;
        }
      }

      // Load features if not already loaded
      if (!this.featuresLoaded) {
        await this.loadUserFeatures();
      }

      const requestedRoute = state.url;
      console.log('FeatureGuard: Checking access for route:', requestedRoute);

      // Check if route exists in feature map
      if (this.featureRouteStatusMap.hasOwnProperty(requestedRoute)) {
        const isEnabled = this.featureRouteStatusMap[requestedRoute];
        if (!isEnabled) {
          console.log('FeatureGuard: Route disabled, redirecting to dashboard');
          this.router.navigate(['/home/<USER>']);
          return false;
        }
      }
      // If route doesn't exist in feature map, allow access (default enabled)

      console.log('FeatureGuard: Route access granted');
      return true;
    } catch (error) {
      console.error('FeatureGuard: Error checking route access:', error);
      // On error, allow access to prevent blocking the application
      return true;
    }
  }

  private async loadUserFeatures(): Promise<void> {
    try {
      const userString = localStorage.getItem('user');
      if (!userString) {
        console.error("FeatureGuard: User data not found in localStorage.");
        return;
      }
      
      const currentUser = JSON.parse(userString);
      
      // Get businessUnitId from user object - prioritize businessUnitId over plantIds
      let businessUnitId = currentUser?.businessUnitId;
      
      // If businessUnitId is not available, fall back to first plantId
      if (!businessUnitId && currentUser?.plantIds && currentUser.plantIds.length > 0) {
        businessUnitId = currentUser.plantIds[0];
      }

      if (businessUnitId) {
        console.log("FeatureGuard: Fetching features for businessUnitId:", businessUnitId);
        const requestData = {
          page: 1,
          limit: 1000,
          filter: [`businessUnitId||$eq||${businessUnitId}`]
        };
        const params = createAxiosConfig(requestData);
        const response = await this.featureManagementService.getFeatures(params);
        const features = response?.data ?? [];

        // Populate the featureRouteStatusMap
        features.forEach(feature => {
          if (feature.adminRoute) {
            this.featureRouteStatusMap[feature.adminRoute] = feature.enabled;
          }
        });
        
        this.featuresLoaded = true;
        console.log('FeatureGuard: Feature Route Status Map loaded:', this.featureRouteStatusMap);
      } else {
        console.warn("FeatureGuard: Business Unit ID not found for the user.");
      }
    } catch (error) {
      console.error("FeatureGuard: Error fetching user features:", error);
    }
  }

  // Method to refresh features (can be called when features are updated)
  public refreshFeatures(): void {
    this.featuresLoaded = false;
    this.featureRouteStatusMap = {};
  }
}
