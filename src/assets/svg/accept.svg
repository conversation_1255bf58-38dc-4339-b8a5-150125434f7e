<svg width="90" height="90" viewBox="0 0 90 90" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_892_608)">
<circle cx="53" cy="45" r="31" fill="white"/>
<circle cx="53" cy="45" r="30.5" stroke="#BD3861"/>
</g>
<path d="M40.8125 45L49.25 53.4375" stroke="#BD3861" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M49.25 53.4375L65.1875 36.5625" stroke="#BD3861" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/>
<defs>
<filter id="filter0_d_892_608" x="0" y="0" width="90" height="90" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-8"/>
<feGaussianBlur stdDeviation="7"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.4375 0 0 0 0 0 0 0 0 0 0.134868 0 0 0 0.4 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_892_608"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_892_608" result="shape"/>
</filter>
</defs>
</svg>
