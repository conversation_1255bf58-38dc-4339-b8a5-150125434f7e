.rating {
    display: flex;
    flex-direction: column;
    align-items: center;
    color: #b7b7b7;
    // background: #fff;
    // border-radius: 8px;
    box-shadow: 0 6px 33px rgba(19, 18, 18, 0.09);
    .list {
        padding: 0;
        margin: 0 0px 0 0;
        .star {
            display: inline-block;
            font-size: 16px;
            transition: all .2s ease-in-out; 
            cursor: pointer;
            &:hover {
                ~ .star:not(.active) {
                    color: inherit;
                }
            }
            &:first-child {
                margin-left: 0;
            }
            &.active {
                color: #ffe100;
            }
        }
    }
    .info {
        margin-top: 15px;
        font-size: 40px;
        text-align: center;
        display: table;
        .divider {
            margin: 0 5px;
            font-size: 30px;
        }
        .score-max {
            font-size: 30px;
            vertical-align: sub;
        }
    }
}