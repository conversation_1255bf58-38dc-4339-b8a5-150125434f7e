.toggle {
    position: relative;
    display: block;
    width: 90px;
    // height: 34px;
    /* padding: 3px;
    margin: auto; */
    height: 30px;
    border-radius: 50px;
    cursor: pointer;
    background: #DAFFEA;

}
 
.toggle-input {
    position: absolute;
    top: 0;
    left: 0;
    opacity: 0;
}
 
.toggle-label {
    position: relative;
    display: block;
    height: inherit;
    font-size: 12px;
    // background: white;
    border-radius: inherit;
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.12), inset 0 0 3px rgba(0, 0, 0, 0.15);
}
 
.toggle-label:before,
.toggle-label:after {
    position: absolute;
    top: 50%;
    color: #017835;
    margin-top: -.5em;
    line-height: 1;
}
 
 
.toggle-label:before {
    content: attr(data-off);
    right: 11px;
    color: #017835;
    font-weight: 600;
    text-shadow: 0 1px rgba(255, 255, 255, 0.5);
}
 
.toggle-label:after {
    content: attr(data-on);
    left: 11px;
    color: #686868;
    // color: gray;
    font-weight: 600;
    text-shadow: 0 1px rgba(0, 0, 0, 0.2);
    opacity: 0;
}
 
.toggle-input:checked~.toggle-label {
    background: #E0E0E0;
}
 
 
.toggle-input:checked~.toggle-label:before {
    opacity: 0;
}
 
.toggle-input:checked~.toggle-label:after {
    opacity: 1;
}
 
.toggle-handle {
    position: absolute;
    top: 1px;
    // left: 2px;
    width: 30px;
    height: 30px;
    // background: linear-gradient(to bottom, #FFFFFF 40%, #f0f0f0);
    border-radius: 50%;
    background-color: #017835;
    color: #6EBB8F;
}
 
.toggle-handle:before {
    position: absolute;
    top: 50%;
    left: 50%;
    margin: -6px 0 0 -6px;
    width: 16px;
    height: 16px;
    background-color: #fff
}
 
.toggle-input:checked~.toggle-handle {
    left: 60px;
    box-shadow: -1px 1px 5px rgba(0, 0, 0, 0.2);
    background-color: #605D5D;
    // border: solid 1px #0b74b0;
}
 
/* Transition*/
.toggle-label,
.toggle-handle {
    transition: All 0.3s ease;
    -webkit-transition: All 0.3s ease;
    -moz-transition: All 0.3s ease;
    -o-transition: All 0.3s ease;
}



