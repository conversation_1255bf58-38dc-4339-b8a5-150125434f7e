.mat-mdc-header-row {
    height: 40px !important;
    background: #0B74B0 !important;
    color: White !important;
}

.mat-mdc-row {
    height: 40px !important;
}

thead th {
    font-family: 'adani' !important;
    font-size: 11px;
    color: white;
    font-weight: 600 !important;
}
tbody td {
    font-size: 11px;
    font-weight: 600 !important;
    font-family: 'adani';
}

.table-title {
    font-weight: 600 !important;
    font-family: 'adani';
}

/* common */
.export, .column-selection, .filter {
    height: 30px;
    width: 30px;
    border: 1px solid;
    margin-left: 10px;
    background: #ffffff;
    border: 1px solid #CED4DA;
    border-radius: 2px;
    img {
        padding: 7px;
    }
}

.export:hover, .column-selection:hover, .filter :hover{
    background: rgb(126, 123, 123);
}

/* Search */
::ng-deep.mat-mdc-form-field-flex, .mat-mdc-form-field-flex {
    height: 30px !important;
}

::ng-deep.mat-mdc-form-field-infix {
    margin-top: -14px !important;
}

::ng-deep.mat-mdc-form-field-subscript-wrapper {
    height: 0px !important;
}

::ng-deep.mat-mdc-text-field-wrapper {
    background: #ffffff;
    border: 1px solid #CED4DA;
}