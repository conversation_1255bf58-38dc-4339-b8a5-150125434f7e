// @import './variables.scss'; // Already imported in style-resources-loader
// @import './mixins.scss'; // Already imported in style-resources-loader
@import "./transition.scss";
@import "./svgicon.scss";
@import "./digisafe.scss";

/* Global scss */

body {
  height: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  font-family: -apple-system, BlinkMacSystemFont, Segoe UI, Helvetica Neue,
    Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif;
}

html {
  height: 100%;
}

#app {
  height: 100%;
}

*,
*:before,
*:after {
  box-sizing: border-box;
}

a,
a:focus,
a:hover {
  color: inherit;
  outline: none;
  text-decoration: none !important;
}

div:focus {
  outline: none;
}

.clearfix {
  @include clearfix;
}

label {
  font-weight: 400;
}

.app-container {
  position: relative;
  padding: 20px;
}

.filter-button-sm {
  margin: 20px;
  display: flex;
  justify-content: end;
}
.pagination-container{
  overflow-x: scroll;
}

.components-container {
  margin: 30px 50px;
  position: relative;
}

// Refine element ui upload
.upload-container {
  .el-upload {
    width: 100%;

    .el-upload-dragger {
      width: 100%;
      height: 200px;
    }
  }
}

.el-upload-dragger {
  width: 220px;
  height: 150px;
}

aside {
  background: #eef1f6;
  color: #2c3e50;
  padding: 8px 24px;
  margin-bottom: 20px;
  border-radius: 2px;
  display: block;
  line-height: 32px;
  font-size: 16px;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  a {
    color: #337ab7;
    cursor: pointer;

    &:hover {
      color: rgb(32, 160, 255);
    }
  }
}

.fixed-width {
  .el-button--mini {
    padding: 7px 10px;
    width: 60px;
  }
}

.filter-container {
  padding-bottom: 10px;

  .filter-item {
    display: inline-block;
    vertical-align: middle;
    margin-bottom: 10px;
    margin-right: 5px;
  }
}

.link-type,
.link-type:focus {
  color: #337ab7;
  cursor: pointer;

  &:hover {
    color: rgb(32, 160, 255);
  }
}

.status-col {
  .cell {
    padding: 0 10px;
    text-align: center;
  }
}

.text-center {
  text-align: center;
}

.navbar {
  height: 65px !important;
}
.el-breadcrumb {
  margin-right: auto !important;
}
.sub-navbar {
  height: 50px;
  line-height: 50px;
  position: relative;
  width: 100%;
  text-align: right;
  padding-right: 20px;
  transition: 600ms ease position;
  background: linear-gradient(
    90deg,
    rgba(32, 182, 249, 1) 0%,
    rgba(32, 182, 249, 1) 0%,
    rgba(33, 120, 241, 1) 100%,
    rgba(33, 120, 241, 1) 100%
  );

  .subtitle {
    font-size: 20px;
    color: #fff;
  }

  &.draft {
    background: #d0d0d0;
  }

  &.deleted {
    background: #d0d0d0;
  }
}

.no-padding {
  padding: 0px !important;
}

.margin-horizontal {
  margin: 0 10px;
}
.el-menu {
  background-color: linear-gradient(#0b74b0, #75479c, #bd3681) !important;
  background: transparent !important;
  // background-color: #4666a4 !important;
}
.el-menu-item {
  position: relative;
}

.sidebar-container {
  background-image: linear-gradient(#0b74b0, #75479c, #bd3681) !important;
}

// button{
//   background-image: linear-gradient( to right ,#0B74B0,#BD3681) !important;
//   color: white !important;
// }
.theme-button {
  background-image: linear-gradient(
    to right,
    #0b74b0,
    #75479c,
    #bd3681
  ) !important;
  color: white !important;
  font-weight: 600;
  border-radius: 7px;
  i {
    font-weight: 600;
  }
}
.theme-add-button {
  background-color: #0b74b0;
}
.theme-edit-button {
  background-color: rgba(11, 116, 176, 0.8);
  color: white !important;
  font-weight: 600;
  border-radius: 7px;
}
.theme-delete-button {
  background-color: #bd3681;
  color: white !important;
  font-weight: 600;
  border-radius: 7px;
}
.el-icon-download,
.el-icon-plus {
  font-weight: 600;
}

.el-menu-item {
  // background: transparent !important;
  // background-image: linear-gradient(#4666a4, #964576) !important;
  // background-color: #4666a4 !important;
}
.el-menu-item:hover {
  background: #fff !important;
  background-color: #fff !important;
}
.el-menu-item .is-active {
  color: #555555 !important;
  background-color: #ffffff !important;
}
.el-submenu__title {
  font-weight: 600 !important;
  background: transparent !important;
}
.el-submenu__title:hover {
  background: #fff !important;
  color: #555555 !important;
  .unicon {
    fill: #555555 !important;
  }
}

.tags-view-container .tags-view-wrapper .tags-view-item.active {
  background-image: linear-gradient(
    to right,
    #0b74b0,
    #75479c,
    #bd3681
  ) !important;
}
.el-pagination.is-background .el-pager li:not(.disabled).active {
  background-image: linear-gradient(
    to right,
    #0b74b0,
    #75479c,
    #bd3681
  ) !important;
}

.el-tabs__item.is-active {
  color: #964576;
}
.el-tabs__active-bar {
  background: #964576 !important;
}
// .el-tabs__item{
//   padding: 0px !important;
// }
// .el-tabs__item:active{
//   background-color: #964576 !important;
// }
// .el-tabs__item:hover{
//   background-color: #964576 !important;
// }

// enable dotƒ
.enable-dot {
  .el-badge__content {
    cursor: pointer;
  }
}

.sc-enabled-button-style {
  margin: 3px;
  vertical-align: -webkit-baseline-middle;
}

// .form-block-container{
//   margin: 24px;
//   background-color: #EEEEEE;
// }
.form-block {
  background-color: #fcfcfc;
  border: 1px solid #ebebeb;
  border-radius: 3px;
  transition: 0.2s;
  width: -webkit-fill-available;
  margin: 24px;
  &:hover {
    box-shadow: 0 0 8px 0 rgba(232, 237, 250, 0.6),
      0 2px 4px 0 rgba(232, 237, 250, 0.5);
  }

  .source {
    padding: 2em 2em 0 2em;

    .el-form {
      width: 760px;
    }
  }
}

// custom css
.el-switch.is-checked .el-switch__core {
  border-color: #11ca92;
  background-color: #11ca92;
}

.el-switch__core {
  border: 1px solid #e94779;
  background: #e94779;
}

.el-avatar {
  background-color: #fff;
}

.el-date-editor .el-range-separator {
  width: 8%;
}

.el-row {
  border-bottom: 1px solid rgba(212, 212, 212, 0.6);
  padding-bottom: 3px;
}

.el-scrollbar__thumb {
  position: relative;
  display: block;
  width: 0;
  height: 0;
  cursor: pointer;
  border-radius: inherit;
  background-color: rgba(87, 87, 87, 0.9) !important;
  // -webkit-transition: .3s background-color;
  // transition: .3s background-color
}

.el-select-dropdown__wrap {
  max-height: 300px;
}
