/* Variables */
//new Themes
$theme1-primary:#0B74B0;
$theme1-main-heading:#161616;
$theme1-sub-heading:#161616;
$theme1-main-background:#fafafa;
$theme1-sub-background:#ffffff;
$theme1-table-data:#555555;
$theme1-shadow:#f0f0f0;

// Sidebar
$sideBarWidth: 250px;
$subMenuBg: #1f2d3d;
$subMenuHover: #22272D;
$subMenuActiveText: #1D3C42;
$menuBg:#1D3C42;
$menuText: #fff;
$menuActiveText:#1D3C42; // Also see settings.sidebarTextTheme

// Login page
$lightGray: #e0e0e0;
$darkGray: #505050;
$loginBg: #808080;
$loginCursorColor: #e0e0e0;

$textAreaBottom: #bfcbd9;

// The :export directive is the magic sauce for webpack
// https://mattferderer.com/use-sass-variables-in-typescript-and-javascript
:export {
  menuBg: $menuBg;
  menuText: $menuText;
  menuActiveText: $menuActiveText;
}
