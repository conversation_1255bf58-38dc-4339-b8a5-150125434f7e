/* You can add global styles to this file, and also import other style files */
/* You can add global styles to this file, and also import other style files */
@import "../node_modules/@angular/material/prebuilt-themes/deeppurple-amber.css";
@import "@ng-select/ng-select/themes/default.theme.css";

html,
body {
  height: 100%;
}

body {
  margin: 0;
  background-color: #f9f9f9 !important;
}

* {
  font-family: "adani", sans-serif !important;
}

/* You can add global styles to this file, and also import other style files */
@font-face {
  font-family: "adani";
  src: url("/assets/fonts/adani_bold-webfont.woff2") format("woff2"),
    url("/assets/fonts/adani_bold-webfont.woff") format("woff");
  font-family: "adani";
  src: url("/assets/fonts/adani_extralight-webfont.woff2") format("woff2"),
    url("/assets/fonts/adani_extralight-webfont.woff") format("woff");
  font-family: "adani";
  src: url("/assets/fonts/adani_light-webfont.woff2") format("woff2"),
    url("/assets/fonts/adani_light-webfont.woff") format("woff");
  font-family: "adani";
  src: url("/assets/fonts/adani_medium-webfont.woff2") format("woff2"),
    url("/assets/fonts/adani_medium-webfont.woff") format("woff");
  font-family: "adani";
  src: url("/assets/fonts/adani_regular-webfont.woff2") format("woff2"),
    url("/assets/fonts/adani_regular-webfont.woff") format("woff");
  // font-family: 'adani';
  // src: url('/assets/fonts/adani_semibold-webfont.woff2') format('woff2'),
  //   url('/assets/fonts/adani_semibold-webfont.woff') format('woff');

  font-weight: normal;
  font-style: normal;
}

// @import "~bootstrap/dist/css/bootstrap.css";
@import "./assets/scss/custom.scss";
@import "./assets/scss/table.scss";
@import "./assets/scss/toggle.scss";
@import "./assets/scss/variables";
@import './assets/scss/element-variables.scss';

body {
  margin: 0;
  padding: 0;
  font-family: "adani";
  // line-height: 0px !important;
}

body h1,
h2,
h3,
h4,
h5,
h6,
div,
textarea,
input,
select option,
ul li,
ul li a,
span {
  font-family: "adani" !important;
}

mat-select {
  font-family: "adani" !important;
}

th{
  background-color:#0B74B0 !important;
  color: white !important;
}

li,
p {
  font-family: "adani";
}

body label {
  font-family: "adani";
}

body .bd-container {
  // height: 100%;
  height: 100vh !important;
  background: #f9f9f9;
}

mat-checkbox.mat-checkbox-checked {
  box-shadow: none !important;
  /* Remove box shadow */
}

img {
  height: 100%;
  width: 100%;
}

.error-message {
  color: red;
  font-weight: 400;
  font-size: 14px;
}

.fixed-footer {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  margin: 0;
}

.modal-backdrop.show {
  opacity: 0.1 !important;
}

.modal-header {
  padding: 10px 16px !important;
  border-radius: 0px !important;
  margin-top: 0px !important;

  >.modal-title {
    font-size: 21px;
  }
}

input,
select,
option,
textarea {
  font-size: 12px !important;
  font-weight: 600 !important;
}

.page-header {
  font-size: 22px;
  font-weight: 600;
}

.section-header {
  font-size: 17px;
  font-weight: 600;
  margin-bottom: 0;
}

.label-header {
  font-size: 13px;
  font-weight: 600;
  margin-bottom: 0;
  color: #555555;
}

.input-text {
  border: 1px solid #ced4da;
  height: 37px;
  border-radius: 6px;
  width: 100%;
  padding: 9px;
}

.input-text:focus-visible {
  border: 0.5px solid #000;
}

.input-text-group {
  .w-260 {
    width: 260px;
  }

  label {
    z-index: 1000;
    font-family: Adani;
    font-size: 13px;
    font-weight: 600;
    letter-spacing: 0em;
    text-align: left;
    color: #000 !important;
    /* position: relative; */
    /* top: 10px; */
    /* left: 11px; */
    background: transparent;
  }

  >input {
    border-radius: 12px;
  }

  >select {
    border-radius: 12px;
  }
}

strong {
  font-size: 14px;
}
p {
  color: #313131;
}

.label-value {
  display: flex;
  gap: 10px; /* Adjust spacing */
}
.label-value strong {
  min-width: 150px; /* Ensures consistent label width */
}

.dropdown-list {
  border: 1px solid #ced4da;
  height: 37px;
  border-radius: 6px;
  width: 100%;
  padding: 9px;
}

.space-between {
  display: flex;
  justify-content: space-between;
}

.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.dropdown-list select {
  border: none;
  width: 100%;
  outline: none;
}

.dropdown-list-autocomplete {
  font-size: 12px;
  font-weight: 600;
  // margin-bottom: 9px !important;
  // border-radius: 12px !important;
  // height: 24px !important;
}

.text-textarea {
  width: 100%;
  border: 1px solid #ced4da;
  border-radius: 6px;
  padding: 9px;
}

.button-submit {
  background: linear-gradient(90deg, #0b74b0 0%, #75479c 52.08%, #bd3861 100%);
  border: 0;
  border-radius: 30px;
  height: 39px;
  color: #fff;
  font-size: 12px;
  font-weight: 600;
  width: 156px;
}

.button-back {
  border: 1px solid;
  border-radius: 30px;
  height: 39px;
  color: #bd3861 !important;
  font-size: 12px;
  font-weight: 600;
  width: 183px;
  margin: 0 20px;
  width: 156px;
  background: #fff;
}

.button-browser {
  padding: 6px 6px !important;
  font-size: 12px !important;
  font-weight: 600;
  background: #fcfdfd !important;
  border-top-right-radius: 5px !important;
  border-bottom-right-radius: 5px !important;
  border: solid 1px #d5d5d5 !important;
}

.bt-section {
  display: flex;
  justify-content: flex-end;
  // position: absolute;
  // right: 26px;
  // width: 330px;
}

.field-line {
  border-style: solid;
  border-color: #b5b5b5;
  border-width: 1px 0 0 0;
  width: 39px;
  height: 0px;
  transform-origin: 0 0;
  transform: rotate(90deg) scale(1, 1);
  margin-left: 15px;
}

.label-mandatory-field,
.asterisk {
  color: #ff0000 !important;
  padding-left: 3px;
}

.text-danger {
  font-family: "adani";
  font-size: 12px;
  font-weight: 600;
}

.button-right {
  display: flex;
  // justify-content: space-between;
  align-items: center;
  margin-left: 20px;

  span {
    margin-left: 0px;
  }

  .right-arrow {
    width: 60px;
    height: 61px;
    // padding-left: 5px;
    margin-left: -23px;
  }
}

.button-left {
  display: flex;
  justify-content: space-between;
  align-items: center;

  span {
    margin-right: 40px;
  }

  .left-arrow {
    width: 60px;
    height: 61px;
    // padding-left: 5px;
    // margin-left: -22px;
  }
}

button[disabled] {
  opacity: 0.5;
  cursor: no-drop !important;
}

.page-heading {
  font-size: 21px;
  font-weight: 700;
}

.fixed-bootom {
  position: fixed;
  bottom: 0;
  width: 100%;
}

.submit {
  background: linear-gradient(90deg, #0b74b0 0%, #75479c 52.08%, #bd3861 100%);
  border-radius: 30px !important;
  width: 120px;
  color: white !important;
}

.feature-card {
  background-color: #fff;
  padding-top: 20px;
  padding-bottom: 20px;
  border-radius: 16px;
  box-shadow: 0px 0px 24px 0px rgba(146, 171, 186, 0.20);
  border: none;
  // border-radius: 16px;
  // background: #fff;
  // box-shadow: 0px 0px 24px 0px rgba(146, 171, 186, 0.20);
}

/* Rectangle 22873 */

::ng-deep mat-form-field,
::ng-deep mat-label,
::ng-deep mat-select,
::ng-deep mat-option,
::ng-deep mat-input-element,
::ng-deep mat-form-field {
  font-family: "adani", sans-serif !important;
  font-size: 12px !important;
  font-weight: 600 !important;
}

.adani-btn {
  background-image: linear-gradient(90deg, #0b74b0, #75479c, #bd3681) !important;
  color: white;
  /* Ensures text is visible */
  border: none;
  padding: 10px 15px;
  font-size: 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease-in-out;
}

.modal-header{
  background-image: linear-gradient(90deg, #0b74b0, #75479c, #bd3681) !important;
  color: white;
}

::ng-deep .matTooltip {
  // background: linear-gradient(90deg, #0b74b0 0%, #75479c 52.08%, #bd3861 100%) !important;
  background: #75479c;
  color: #fff !important;
  /* White text */
  font-size: 14px;
  /* Font size */
  padding: 8px 12px;
  /* Padding */
  border-radius: 4px;
  /* Rounded corners */
  box-shadow: 0px 2px 6px rgba(0, 0, 0, 0.3);
  /* Subtle shadow */
}
/* styles.scss or styles.css */

/* Style ng-select to mimic Bootstrap 5 form-control */
.ng-select-bs5 {
  &.ng-select {
    // Match form-control sizing and border
    .ng-select-container {
      min-height: var(--bs-form-control-height, 38px); // Use BS variable or default
      border: var(--bs-border-width) solid var(--bs-border-color);
      border-radius: var(--bs-border-radius);
      align-items: center; // Vertically align content

      // Use form-control padding variables
      padding: var(--bs-form-control-padding-y) var(--bs-form-control-padding-x);

      // Use form-control font variables
      font-family: var(--bs-font-sans-serif); // Ensure font matches
      font-size: var(--bs-form-control-font-size);
      font-weight: var(--bs-form-control-font-weight);
      line-height: var(--bs-form-control-line-height);
      color: var(--bs-form-control-color);
      background-color: var(--bs-form-control-bg);

      // Add transition for focus effect
      transition: border-color .15s ease-in-out, box-shadow .15s ease-in-out;
    }

    // Placeholder styling
    .ng-select-container .ng-placeholder {
      font-size: 12px;
      color: var(--bs-form-placeholder-color);
    }

    // Multi-select value chip styling (like badges)
    .ng-select-container .ng-value-container .ng-value {
      background-color: var(--bs-secondary-bg); // Use a subtle BS background
      color: var(--bs-emphasis-color);         // Use appropriate text color
      border-radius: var(--bs-badge-border-radius);
      padding: var(--bs-badge-padding-y) var(--bs-badge-padding-x);
      font-size: 12px;
      font-weight: var(--bs-badge-font-weight);
      margin-right: 4px; // Spacing between tags
      margin-bottom: 4px; // Help with wrapping
      align-items: center;
      display: inline-flex; // Needed for alignment with remove icon

      // Remove icon within the tag
      .ng-value-icon {
        font-size: 1.1em; // Adjust size as needed
        line-height: 1;
        margin-left: 0.35em;
        padding: 0;
        border: none;
        background: none;
        color: inherit; // Inherit color from the tag

        &:hover {
          color: var(--bs-emphasis-color); // Keep color or change slightly on hover
          background-color: rgba(0,0,0,0.1); // Slight background darken
        }
      }
    }

    // Arrow styling
    .ng-arrow-wrapper .ng-arrow {
      border-color: var(--bs-secondary) transparent transparent; // Use a BS color
      border-style: solid;
      border-width: 5px 5px 0;
    }

    // Clear button styling
    .ng-clear-wrapper {
       color: var(--bs-secondary); // Use a BS color
       &:hover {
         color: var(--bs-dark); // Darken on hover
       }
    }
  }

  // --- Focus State ---
  &.ng-select-focused {
    .ng-select-container {
      border-color: var(--bs-primary-border-subtle); // Use BS focus border color variable
      outline: 0;
      // Use BS focus box-shadow variable
      box-shadow: 0 0 0 var(--bs-focus-ring-width) var(--bs-focus-ring-color);
    }
  }

  // --- Disabled State ---
  &.ng-select-disabled {
    .ng-select-container {
      color: var(--bs-form-disabled-color);
      background-color: var(--bs-form-disabled-bg);
      border-color: var(--bs-form-disabled-border-color);
    }
    // Disable multi-select value interactions
     .ng-select-container .ng-value-container .ng-value {
       background-color: var(--bs-secondary-bg); // Keep bg or change?
       color: var(--bs-form-disabled-color);
       .ng-value-icon {
         display: none; // Hide remove icon when disabled
       }
     }
  }


  // --- Dropdown Panel Styling (like .dropdown-menu) ---
  .ng-dropdown-panel {
    background-color: rgb(44, 33, 33);
    color: white;
    padding: 10px;
    border: var(--bs-dropdown-border-width) solid var(--bs-dropdown-border-color);
    border-radius: var(--bs-dropdown-border-radius);
    box-shadow: var(--bs-dropdown-box-shadow);
    margin-top: 2px; // Standard BS dropdown offset

    .ng-dropdown-header {
       padding: var(--bs-dropdown-header-padding-y) var(--bs-dropdown-header-padding-x);
       color: var(--bs-dropdown-header-color);
       border-bottom: var(--bs-dropdown-divider-border-width) solid var(--bs-dropdown-divider-bg); // Mimic divider
    }

    .ng-dropdown-footer {
       padding: var(--bs-dropdown-header-padding-y) var(--bs-dropdown-header-padding-x); // Use header padding
       border-top: var(--bs-dropdown-divider-border-width) solid var(--bs-dropdown-divider-bg); // Mimic divider
    }

    // --- Dropdown Option Styling (like .dropdown-item) ---
    .ng-option {
      padding: var(--bs-dropdown-item-padding-y) var(--bs-dropdown-item-padding-x);
      color: var(--bs-dropdown-link-color);
      line-height: var(--bs-form-control-line-height); // Match input line height
      min-height: var(--bs-form-control-height); // Match input height for consistency
      display: flex; // Allow vertical centering if needed
      align-items: center;

      // Hover/Active (Marked) State
      &.ng-option-marked {
        color: var(--bs-dropdown-link-hover-color);
        background-color: var(--bs-dropdown-link-hover-bg);
      }

      // Selected State
      &.ng-option-selected, &.ng-option-selected.ng-option-marked {
        color: var(--bs-dropdown-link-active-color);
        background-color: var(--bs-dropdown-link-active-bg);
      }

      // Disabled Option State
      &.ng-option-disabled {
        color: var(--bs-dropdown-link-disabled-color);
        background-color: transparent;
      }
    }
  }

   // --- Validation States (requires adding .is-invalid or .is-valid to the ng-select host) ---
   &.is-invalid {
    .ng-select-container {
      border-color: var(--bs-form-invalid-border-color);
    }
    // Optional: Add focus state for invalid
    &.ng-select-focused .ng-select-container {
      box-shadow: 0 0 0 var(--bs-focus-ring-width) var(--bs-form-invalid-focus-box-shadow);
    }
     // Optional: Add BS validation icon
     // Requires more complex positioning based on ng-select structure
  }

  &.is-valid {
    .ng-select-container {
      border-color: var(--bs-form-valid-border-color);
    }
     // Optional: Add focus state for valid
    &.ng-select-focused .ng-select-container {
      box-shadow: 0 0 0 var(--bs-focus-ring-width) var(--bs-form-valid-focus-box-shadow);
    }
    // Optional: Add BS validation icon
  }
}
.ng-select-container .ng-value-container { /* Target the container of selected items */
  max-height: 50px; /* Adjust this value as needed */
  overflow-y: auto;    /* Add vertical scrollbar when content overflows */
}

/* Optional: Style the scrollbar (for Webkit browsers like Chrome and Safari) */
.ng-select-container .ng-value-container::-webkit-scrollbar {
  width: 8px;
}

.ng-select-container .ng-value-container::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.ng-select-container .ng-value-container::-webkit-scrollbar-thumb {
  background: #888;
}

.ng-select-container .ng-value-container::-webkit-scrollbar-thumb:hover {
  background: #555;
}