<app-toast-message></app-toast-message>
<app-tab [tabs]="tabs" [selectedTabIndex]="selectedTabIndex" (tabSelected)="onTabSelected($event)">
</app-tab>

<!-- Pending Tab (Index 0) -->
<div *ngIf="selectedTabIndex === 0" class="card custom-card">
    <div class="card-header">
        <div class="row align-items-center">
            <div class="col d-flex align-items-center">
                <h6>Pending Transfer Request List</h6> <!-- Removed class= -->
            </div>
            <div class="col text-end d-flex align-items-center justify-content-end">
                <!-- Download Dropdown -->
                <div ngbDropdown class="d-inline-block me-2">
                    <button type="button" class="btn btn-sm adani-btn dropdown-toggle"
                        [id]="'downloadTransferExcelDropdown' + selectedTabIndex" ngbDropdownToggle
                        [disabled]="isDownloadingExcel || isLoadingData">
                        <span *ngIf="!isDownloadingExcel">
                            <i class="bi bi-file-earmark-excel me-1"></i> Download Excel
                        </span>
                        <span *ngIf="isDownloadingExcel">
                            <span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
                            Downloading {{ downloadType === 'current' ? 'Page' : (downloadType === 'all' ? 'All' : '')
                            }}...
                        </span>
                    </button>
                    <ul ngbDropdownMenu [attr.aria-labelledby]="'downloadTransferExcelDropdown' + selectedTabIndex">
                        <li>
                            <button ngbDropdownItem (click)="downloadExcel('current')"
                                [disabled]="isDownloadingExcel || isLoadingData || (getCurrentListData()?.length ?? 0) === 0">
                                <i class="bi bi-download me-1"></i> Download Current Page ({{
                                getCurrentListData()?.length ?? 0 }})
                            </button>
                        </li>
                        <li>
                            <button ngbDropdownItem (click)="downloadExcel('all')"
                                [disabled]="isDownloadingExcel || isLoadingData || totalItems === 0">
                                <i class="bi bi-cloud-download me-1"></i> Download All Filtered ({{ totalItems }})
                            </button>
                        </li>
                    </ul>
                </div>
                <!-- Filter Button -->
                <button type="button" class="btn p-0 ms-3" (click)="openFilterModal()" title="Filter">
                    <img src="../../../assets/svg/filter.svg" class="filter-button" alt="Filter" />
                </button>
            </div>
        </div>
    </div>
    <div class="card-body">
        <div class="table-responsive my-table">
            <table class="table table-bordered custom-table">
                <thead class="table-header">
                    <tr>
                        <th scope="col" class="col-actions">Actions</th>
                        <th scope="col" class="col-user-details">User Detail</th>
                        <th scope="col" class="col-plant-info">Transfer to Plant</th>
                        <th scope="col" class="col-requested-by">Transfer Requested By</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- Loading State -->
                    <tr *ngIf="isLoadingData">
                        <td colspan="4" class="text-center p-4"> <!-- Corrected Colspan -->
                            <span class="spinner-border spinner-border-sm me-2"></span>
                            Loading pending requests...
                        </td>
                    </tr>
                    <!-- Empty State -->
                    <tr *ngIf="!isLoadingData && pendingTransferList.length === 0"> <!-- Use correct list -->
                        <td colspan="4" class="text-center p-4 text-muted"> <!-- Corrected Colspan -->
                            No pending transfer requests found matching the criteria.
                        </td>
                    </tr>
                    <!-- Data Rows -->
                    <tr *ngFor="let item of pendingTransferList"> <!-- Use correct list -->
                        <!-- Actions Column -->
                        <td class="text-center align-middle">
                            <div class="text-center">
                                <div class="mb-2">
                                    <!-- Use safe navigation -->
                                    <b>ID:</b> {{ item.admin?.id ?? item.adminId }}
                                </div>
                                <div class="mb-3">
                                    <!-- Use safe navigation -->
                                    <img [src]="item.admin?.profilePicture || '../../../assets/svg/Avatar.svg'"
                                        class="img-thumbnail rounded-circle" alt="Avatar" width="60"/>
                                </div>
                                <div>
                                    <!-- Add RBAC check for buttons -->
                                    <div class="d-flex justify-content-center gap-2" *ngIf="currentUserRole === componentRoles.SUPER_ADMIN || (currentUserRole === componentRoles.PLANT_ADMIN && loggedInPlantIds.includes(item.plantId))">
                                        <button type="button" class="btn btn-success btn-sm"
                                        (click)="openConfirmationModal(item, 'accept')" title="Accept Request">
                                            <i class="bi bi-check-lg"></i>
                                        </button>
                                        <button type="button" class="btn btn-danger btn-sm"
                                        (click)="openConfirmationModal(item, 'reject')" title="Reject Request">
                                            <i class="bi bi-x-lg"></i>
                                        </button>
                                    </div>
                                    <!-- Optional: Show a message if Plant Admin cannot act -->
                                    <div *ngIf="currentUserRole === componentRoles.PLANT_ADMIN && !loggedInPlantIds.includes(item.plantId)" class="text-muted small mt-2">
                                        (Requires action by admin of target plant)
                                    </div>
                                </div>
                            </div>
                        </td>

                        <!-- User Detail Column -->
                        <td class="align-middle">
                            <div class="details-container">
                                <p class="label-value"><strong>Name:</strong> <span class="value-text">{{ item.admin?.firstName }} {{ item.admin?.lastName }}</span></p>
                                <p class="label-value"><strong>DOB:</strong> <span class="value-text">{{ item.admin?.dob ? (item.admin.dob | date: 'dd-MMM-yyyy') : 'N/A' }}</span></p>
                                <p class="label-value">
                                    <strong>Gender:</strong>
                                    <span class="value-text" [ngSwitch]="+(item.admin?.gender ?? -1)">
                                        <span *ngSwitchCase="0">Female</span>
                                        <span *ngSwitchCase="1">Male</span>
                                        <span *ngSwitchCase="2">Other</span>
                                        <span *ngSwitchDefault>{{ item.admin?.gender || 'N/A' }}</span>
                                    </span>
                                </p>
                                <p class="label-value"><strong>Role:</strong> <span class="value-text">{{ item.admin?.adminsRole?.name || 'N/A' }}</span></p>
                                <p class="label-value">
                                    <strong>Email:</strong>
                                    <span class="value-text">
                                        <a *ngIf="item.admin?.email" [href]="'mailto:' + item.admin.email" class="text-primary">{{ item.admin.email }}</a>
                                        <span *ngIf="!item.admin?.email">N/A</span>
                                    </span>
                                </p>
                                <p class="label-value"><strong>Contact:</strong> <span class="value-text">{{ item.admin?.contactNumber || 'N/A' }}</span></p>
                            </div>
                        </td>

                        <!-- Transfer to Plant Column -->
                        <td class="text-center align-middle">
                            <!-- Use plant badge with color coding -->
                            <div class="plant-badge" [ngClass]="{'bg-primary': item.plantId % 6 === 0, 'bg-secondary': item.plantId % 6 === 1, 'bg-success': item.plantId % 6 === 2, 'bg-info': item.plantId % 6 === 3, 'bg-warning': item.plantId % 6 === 4, 'bg-danger': item.plantId % 6 === 5}">
                                {{ item.plant?.name || 'N/A' }}
                            </div>
                        </td>

                        <!-- Transfer Requested By Column -->
                        <td class="align-middle">
                            <div class="details-container">
                                <p class="label-value"><strong>Name:</strong> <span class="value-text">{{ item.transferedByAdmin?.firstName }} {{ item.transferedByAdmin?.lastName }}</span></p>
                                <p class="label-value">
                                    <strong>Email:</strong>
                                    <span class="value-text">
                                        <a *ngIf="item.transferedByAdmin?.email" [href]="'mailto:' + item.transferedByAdmin.email" class="text-primary">{{ item.transferedByAdmin.email }}</a>
                                        <span *ngIf="!item.transferedByAdmin?.email">N/A</span>
                                    </span>
                                </p>
                                <p class="label-value"><strong>Requested:</strong> <span class="value-text">{{ item.createdAt ? (item.createdAt | date:'dd-MMM-yyyy HH:mm') : 'N/A' }}</span></p>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
    <div class="card-footer text-muted text-center">
        <app-pagination [currentPage]="currentPage" [totalItems]="totalItems" [itemsPerPage]="itemsPerPage"
            (pageChange)="onPageChange($event)"></app-pagination>
    </div>
</div>

<!-- Transferred Tab (Index 1) -->
<div *ngIf="selectedTabIndex === 1" class="card custom-card">
    <div class="card-header">
        <div class="row align-items-center">
            <div class="col d-flex align-items-center">
                <h6>Transferred Request List</h6> <!-- Updated title -->
            </div>
            <div class="col text-end d-flex align-items-center justify-content-end">
                <!-- Download Dropdown -->
                <div ngbDropdown class="d-inline-block me-2">
                    <button type="button" class="btn btn-sm adani-btn dropdown-toggle"
                        [id]="'downloadTransferExcelDropdown' + selectedTabIndex" ngbDropdownToggle
                        [disabled]="isDownloadingExcel || isLoadingData">
                        <span *ngIf="!isDownloadingExcel">
                            <i class="bi bi-file-earmark-excel me-1"></i> Download Excel
                        </span>
                        <span *ngIf="isDownloadingExcel">
                            <span class="spinner-border spinner-border-sm me-1"></span>
                            Downloading {{ downloadType === 'current' ? 'Page' : (downloadType === 'all' ? 'All' : '')
                            }}...
                        </span>
                    </button>
                    <ul ngbDropdownMenu [attr.aria-labelledby]="'downloadTransferExcelDropdown' + selectedTabIndex">
                        <li>
                            <button ngbDropdownItem (click)="downloadExcel('current')"
                                [disabled]="isDownloadingExcel || isLoadingData || (getCurrentListData()?.length ?? 0) === 0">
                                <i class="bi bi-download me-1"></i> Download Current Page ({{
                                getCurrentListData()?.length ?? 0 }})
                            </button>
                        </li>
                        <li>
                            <button ngbDropdownItem (click)="downloadExcel('all')"
                                [disabled]="isDownloadingExcel || isLoadingData || totalItems === 0">
                                <i class="bi bi-cloud-download me-1"></i> Download All Filtered ({{ totalItems }})
                            </button>
                        </li>
                    </ul>
                </div>
                 <!-- Filter Button -->
                 <button type="button" class="btn p-0 ms-3" (click)="openFilterModal()" title="Filter">
                     <img src="../../../assets/svg/filter.svg" class="filter-button" alt="Filter" />
                 </button>
            </div>
        </div>
    </div>
    <div class="card-body">
        <div class="table-responsive my-table">
            <table class="table table-bordered custom-table">
                <thead class="table-header">
                    <tr>
                        <th scope="col" class="col-avatar">User Info</th> <!-- Combined ID/Avatar -->
                        <th scope="col" class="col-user-details">User Detail</th>
                        <th scope="col" class="col-plant-info">Transferred to Plant</th> <!-- Renamed -->
                        <th scope="col" class="col-requested-by">Requested By</th> <!-- Renamed -->
                        <th scope="col" class="col-requested-by">Approved By</th>
                    </tr>
                </thead>
                <tbody>
                     <!-- Loading State -->
                     <tr *ngIf="isLoadingData">
                        <td colspan="5" class="text-center p-4"> <!-- Corrected Colspan -->
                            <span class="spinner-border spinner-border-sm me-2"></span>
                            Loading transferred requests...
                        </td>
                    </tr>
                    <!-- Empty State -->
                    <tr *ngIf="!isLoadingData && transferredTransferList.length === 0"> <!-- Use correct list -->
                        <td colspan="5" class="text-center p-4 text-muted"> <!-- Corrected Colspan -->
                            No transferred requests found matching the criteria.
                        </td>
                    </tr>
                     <!-- Data Rows -->
                    <tr *ngFor="let item of transferredTransferList"> <!-- Use correct list -->
                        <!-- User Info Column -->
                        <td class="text-center align-middle">
                            <div class="text-center">
                                <div class="mb-2">
                                    <b>ID:</b> {{ item.admin?.id ?? item.adminId }}
                                </div>
                                <div>
                                    <img [src]="item.admin?.profilePicture || '../../../assets/svg/Avatar.svg'"
                                        class="img-thumbnail rounded-circle" alt="Avatar" width="60"/>
                                </div>
                            </div>
                        </td>

                        <!-- User Detail Column -->
                        <td class="align-middle">
                            <div class="details-container">
                                <p class="label-value"><strong>Name:</strong> <span class="value-text">{{ item.admin?.firstName }} {{ item.admin?.lastName }}</span></p>
                                <p class="label-value"><strong>DOB:</strong> <span class="value-text">{{ item.admin?.dob ? (item.admin.dob | date: 'dd-MMM-yyyy') : 'N/A' }}</span></p>
                                <p class="label-value">
                                    <strong>Gender:</strong>
                                    <span class="value-text" [ngSwitch]="+(item.admin?.gender ?? -1)">
                                        <span *ngSwitchCase="0">Female</span>
                                        <span *ngSwitchCase="1">Male</span>
                                        <span *ngSwitchCase="2">Other</span>
                                        <span *ngSwitchDefault>{{ item.admin?.gender || 'N/A' }}</span>
                                    </span>
                                </p>
                                <p class="label-value"><strong>Role:</strong> <span class="value-text">{{ item.admin?.adminsRole?.name || 'N/A' }}</span></p>
                                <p class="label-value">
                                    <strong>Email:</strong>
                                    <span class="value-text">
                                        <a *ngIf="item.admin?.email" [href]="'mailto:' + item.admin.email" class="text-primary">{{ item.admin.email }}</a>
                                        <span *ngIf="!item.admin?.email">N/A</span>
                                    </span>
                                </p>
                                <p class="label-value"><strong>Contact:</strong> <span class="value-text">{{ item.admin?.contactNumber || 'N/A' }}</span></p>
                            </div>
                        </td>

                        <!-- Transferred to Plant Column -->
                        <td class="text-center align-middle">
                            <!-- Use plant badge with color coding -->
                            <div class="plant-badge" [ngClass]="{'bg-primary': item.plantId % 6 === 0, 'bg-secondary': item.plantId % 6 === 1, 'bg-success': item.plantId % 6 === 2, 'bg-info': item.plantId % 6 === 3, 'bg-warning': item.plantId % 6 === 4, 'bg-danger': item.plantId % 6 === 5}">
                                {{ item.plant?.name || 'N/A' }}
                            </div>
                        </td>

                        <!-- Requested By Column -->
                        <td class="align-middle">
                            <div class="details-container">
                                <p class="label-value"><strong>Name:</strong> <span class="value-text">{{ item.transferedByAdmin?.firstName }} {{ item.transferedByAdmin?.lastName }}</span></p>
                                <p class="label-value">
                                    <strong>Email:</strong>
                                    <span class="value-text">
                                        <a *ngIf="item.transferedByAdmin?.email" [href]="'mailto:' + item.transferedByAdmin.email" class="text-primary">{{ item.transferedByAdmin.email }}</a>
                                        <span *ngIf="!item.transferedByAdmin?.email">N/A</span>
                                    </span>
                                </p>
                                <p class="label-value"><strong>Requested:</strong> <span class="value-text">{{ item.createdAt ? (item.createdAt | date:'dd-MMM-yyyy HH:mm') : 'N/A' }}</span></p>
                            </div>
                        </td>

                        <!-- Approved By Column -->
                        <td class="align-middle">
                            <div class="details-container">
                                <p class="label-value"><strong>Name:</strong> <span class="value-text">{{ item.approvedByAdmin?.firstName }} {{ item.approvedByAdmin?.lastName }}</span></p>
                                <p class="label-value">
                                    <strong>Email:</strong>
                                    <span class="value-text">
                                        <a *ngIf="item.approvedByAdmin?.email" [href]="'mailto:' + item.approvedByAdmin.email" class="text-primary">{{ item.approvedByAdmin.email }}</a>
                                        <span *ngIf="!item.approvedByAdmin?.email">N/A</span>
                                    </span>
                                </p>
                                <p class="label-value"><strong>Approved:</strong> <span class="value-text">{{ item.updatedAt ? (item.updatedAt | date:'dd-MMM-yyyy HH:mm') : 'N/A' }}</span></p>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
    <div class="card-footer text-muted text-center">
        <app-pagination [currentPage]="currentPage" [totalItems]="totalItems" [itemsPerPage]="itemsPerPage"
            (pageChange)="onPageChange($event)"></app-pagination>
    </div>
</div>

<!-- Rejected Tab (Index 2) -->
<div *ngIf="selectedTabIndex === 2" class="card custom-card">
    <div class="card-header">
        <div class="row align-items-center">
            <div class="col d-flex align-items-center">
                <h6>Rejected Transfer Request List</h6>
            </div>
            <div class="col text-end d-flex align-items-center justify-content-end">
                <!-- Download Dropdown -->
                 <div ngbDropdown class="d-inline-block me-2">
                    <button type="button" class="btn btn-sm adani-btn dropdown-toggle"
                        [id]="'downloadTransferExcelDropdown' + selectedTabIndex" ngbDropdownToggle
                        [disabled]="isDownloadingExcel || isLoadingData">
                        <span *ngIf="!isDownloadingExcel">
                            <i class="bi bi-file-earmark-excel me-1"></i> Download Excel
                        </span>
                        <span *ngIf="isDownloadingExcel">
                            <span class="spinner-border spinner-border-sm me-1"></span>
                            Downloading {{ downloadType === 'current' ? 'Page' : (downloadType === 'all' ? 'All' : '')
                            }}...
                        </span>
                    </button>
                    <ul ngbDropdownMenu [attr.aria-labelledby]="'downloadTransferExcelDropdown' + selectedTabIndex">
                        <li>
                            <button ngbDropdownItem (click)="downloadExcel('current')"
                                [disabled]="isDownloadingExcel || isLoadingData || (getCurrentListData()?.length ?? 0) === 0">
                                <i class="bi bi-download me-1"></i> Download Current Page ({{
                                getCurrentListData()?.length ?? 0 }})
                            </button>
                        </li>
                        <li>
                            <button ngbDropdownItem (click)="downloadExcel('all')"
                                [disabled]="isDownloadingExcel || isLoadingData || totalItems === 0">
                                <i class="bi bi-cloud-download me-1"></i> Download All Filtered ({{ totalItems }})
                            </button>
                        </li>
                    </ul>
                </div>
                 <!-- Filter Button -->
                 <button type="button" class="btn p-0 ms-3" (click)="openFilterModal()" title="Filter">
                     <img src="../../../assets/svg/filter.svg" class="filter-button" alt="Filter" />
                 </button>
            </div>
        </div>
    </div>
    <div class="card-body">
        <div class="table-responsive my-table">
            <table class="table table-bordered custom-table">
                <thead class="table-header">
                    <tr>
                        <th scope="col" class="col-avatar">User Info</th> <!-- Combined ID/Avatar -->
                        <th scope="col" class="col-user-details">User Detail</th>
                        <th scope="col" class="col-plant-info">Target Plant</th> <!-- Renamed -->
                        <th scope="col" class="col-requested-by">Requested By</th> <!-- Renamed -->
                    </tr>
                </thead>
                <tbody>
                    <!-- Loading State -->
                    <tr *ngIf="isLoadingData">
                        <td colspan="5" class="text-center p-4"> <!-- Corrected Colspan -->
                            <span class="spinner-border spinner-border-sm me-2"></span>
                            Loading rejected requests...
                        </td>
                    </tr>
                    <!-- Empty State -->
                    <tr *ngIf="!isLoadingData && rejectedTransferList.length === 0"> <!-- Use correct list -->
                        <td colspan="5" class="text-center p-4 text-muted"> <!-- Corrected Colspan -->
                            No rejected transfer requests found matching the criteria.
                        </td>
                    </tr>
                    <!-- Data Rows -->
                    <tr *ngFor="let item of rejectedTransferList"> <!-- Use correct list -->
                         <!-- User Info Column -->
                         <td class="text-center align-middle">
                            <div class="text-center">
                                <div class="mb-2">
                                    <b>ID:</b> {{ item.admin?.id ?? item.adminId }}
                                </div>
                                <div>
                                    <img [src]="item.admin?.profilePicture || '../../../assets/svg/Avatar.svg'"
                                        class="img-thumbnail rounded-circle" alt="Avatar" width="60"/>
                                </div>
                            </div>
                        </td>

                        <!-- User Detail Column -->
                        <td class="align-middle">
                            <div class="details-container">
                                <p class="label-value"><strong>Name:</strong> <span class="value-text">{{ item.admin?.firstName }} {{ item.admin?.lastName }}</span></p>
                                <p class="label-value"><strong>DOB:</strong> <span class="value-text">{{ item.admin?.dob ? (item.admin.dob | date: 'dd-MMM-yyyy') : 'N/A' }}</span></p>
                                <p class="label-value">
                                    <strong>Gender:</strong>
                                    <span class="value-text" [ngSwitch]="+(item.admin?.gender ?? -1)">
                                        <span *ngSwitchCase="0">Female</span>
                                        <span *ngSwitchCase="1">Male</span>
                                        <span *ngSwitchCase="2">Other</span>
                                        <span *ngSwitchDefault>{{ item.admin?.gender || 'N/A' }}</span>
                                    </span>
                                </p>
                                <p class="label-value"><strong>Role:</strong> <span class="value-text">{{ item.admin?.adminsRole?.name || 'N/A' }}</span></p>
                                <p class="label-value">
                                    <strong>Email:</strong>
                                    <span class="value-text">
                                        <a *ngIf="item.admin?.email" [href]="'mailto:' + item.admin.email" class="text-primary">{{ item.admin.email }}</a>
                                        <span *ngIf="!item.admin?.email">N/A</span>
                                    </span>
                                </p>
                                <p class="label-value"><strong>Contact:</strong> <span class="value-text">{{ item.admin?.contactNumber || 'N/A' }}</span></p>
                            </div>
                        </td>

                        <!-- Target Plant Column -->
                        <td class="text-center align-middle">
                            <!-- Use plant badge with color coding -->
                            <div class="plant-badge" [ngClass]="{'bg-primary': item.plantId % 6 === 0, 'bg-secondary': item.plantId % 6 === 1, 'bg-success': item.plantId % 6 === 2, 'bg-info': item.plantId % 6 === 3, 'bg-warning': item.plantId % 6 === 4, 'bg-danger': item.plantId % 6 === 5}">
                                {{ item.plant?.name || 'N/A' }}
                            </div>
                        </td>

                        <!-- Requested By Column -->
                        <td class="align-middle">
                            <div class="details-container">
                                <p class="label-value"><strong>Name:</strong> <span class="value-text">{{ item.transferedByAdmin?.firstName }} {{ item.transferedByAdmin?.lastName }}</span></p>
                                <p class="label-value">
                                    <strong>Email:</strong>
                                    <span class="value-text">
                                        <a *ngIf="item.transferedByAdmin?.email" [href]="'mailto:' + item.transferedByAdmin.email" class="text-primary">{{ item.transferedByAdmin.email }}</a>
                                        <span *ngIf="!item.transferedByAdmin?.email">N/A</span>
                                    </span>
                                </p>
                                <p class="label-value"><strong>Requested:</strong> <span class="value-text">{{ item.createdAt ? (item.createdAt | date:'dd-MMM-yyyy HH:mm') : 'N/A' }}</span></p>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
    <div class="card-footer text-muted text-center">
        <app-pagination [currentPage]="currentPage" [totalItems]="totalItems" [itemsPerPage]="itemsPerPage"
            (pageChange)="onPageChange($event)"></app-pagination>
    </div>
</div>

<!-- Filter Offcanvas -->
<!-- Use isFilterOffcanvasOpen based on TS -->
<app-offcanvas [title]="'Filter Transfer Requests'" *ngIf="isFilterOffcanvasOpen" (onClickCross)="closeFilterModal()">
    <div class="container">
        <form #filterForm="ngForm">
            <div class="row g-3">

                <!-- Text Filters -->
                <div class="col-md-12">
                    <label for="filterFirstName" class="form-label">First Name</label>
                    <input type="text" id="filterFirstName" class="form-control" placeholder="Filter by User First Name"
                        [(ngModel)]="filterFirstName" name="filterFirstName" #firstName="ngModel"
                        pattern="^[a-zA-Z ]*$" maxlength="30">
                    <div *ngIf="firstName.invalid && (firstName.dirty || firstName.touched)" class="text-danger small mt-1">
                        <div *ngIf="firstName.errors?.['pattern']">First name should contain only alphabets</div>
                    </div>
                </div>
                <div class="col-md-12">
                    <label for="filterLastName" class="form-label">Last Name</label>
                    <input type="text" id="filterLastName" class="form-control" placeholder="Filter by User Last Name"
                        [(ngModel)]="filterLastName" name="filterLastName" #lastName="ngModel"
                        pattern="^[a-zA-Z ]*$" maxlength="30">
                    <div *ngIf="lastName.invalid && (lastName.dirty || lastName.touched)" class="text-danger small mt-1">
                        <div *ngIf="lastName.errors?.['pattern']">Last name should contain only alphabets</div>
                    </div>
                </div>
                <div class="col-md-12">
                    <label for="filterEmail" class="form-label">Email</label>
                    <input type="email" id="filterEmail" class="form-control" placeholder="Filter by User Email with &#64;adani.com domain"
                        [(ngModel)]="filterEmail" name="filterEmail" #email="ngModel"
                        pattern="[a-zA-Z0-9._%+-]+&#64;adani\.com$">
                    <div *ngIf="email.invalid && (email.dirty || email.touched)" class="text-danger small mt-1">
                        <div *ngIf="email.errors?.['pattern']">Email must use the &#64;adani.com domain</div>
                    </div>
                </div>
                <div class="col-md-12">
                    <label for="filterMobile" class="form-label">Mobile Number</label>
                    <input type="text" id="filterMobile" class="form-control" placeholder="Filter by User Mobile"
                        [(ngModel)]="filterMobile" name="filterMobile" #mobile="ngModel"
                        maxlength="10" pattern="^[0-9]*$">
                    <div *ngIf="mobile.invalid && (mobile.dirty || mobile.touched)" class="text-danger small mt-1">
                        <div *ngIf="mobile.errors?.['pattern']">Mobile number should contain only digits</div>
                    </div>
                </div>

                <!-- Select Filters -->
                <div class="col-md-12">
                    <label for="filterPlantId" class="form-label">Target Plant</label>
                    <select id="filterPlantId" class="form-select" [(ngModel)]="filterPlantId" name="filterPlantId" [disabled]="isLoadingFilters">
                        <option [ngValue]="null">Filter by Target Plant</option>
                        <option *ngFor="let plant of plantsList" [value]="plant.id">{{ plant.name }}</option>
                    </select>
                </div>

                <div class="col-md-12">
                    <label for="filterRoleId" class="form-label">Role</label>
                    <select id="filterRoleId" class="form-select" [(ngModel)]="filterRoleId" name="filterRoleId" [disabled]="isLoadingFilters">
                        <option [ngValue]="null">Filter by User Role</option>
                        <option *ngFor="let role of rolesList" [value]="role.id">{{ role.name }}</option>
                    </select>
                </div>

                <!-- Sort By -->
                <div class="col-12">
                    <label for="filterSortBy" class="form-label">Sort By</label>
                    <select id="filterSortBy" class="form-select" [(ngModel)]="filterSortBy" name="filterSortBy">
                        <option *ngFor="let sort of sortOptions" [value]="sort.value">{{ sort.label }}</option>
                    </select>
                </div>

                <!-- Buttons -->
                <div class="col-12 mt-4 d-grid gap-2"> <!-- Increased top margin -->
                    <button type="button" class="btn adani-btn w-100" (click)="applyFilter()"
                        [disabled]="isLoadingFilters || filterForm.invalid">
                        <i class="bi bi-search me-1"></i> Apply Filters
                    </button>
                    <button type="button" class="btn btn-secondary w-100" (click)="resetFilters()">
                        <i class="bi bi-arrow-clockwise me-1"></i> Reset Filters
                    </button>
                </div>
            </div>
        </form>
    </div>
</app-offcanvas>

<!-- Transfer Action Confirmation Modal -->
<div class="modal fade" #transferActionConfirmationModalElement id="transferActionConfirmationModal" tabindex="-1" aria-labelledby="transferActionConfirmationModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
      <div class="modal-content">
        <div class="modal-header" [ngClass]="confirmButtonClass"> <!-- Dynamic background -->
          <h5 class="modal-title text-white" id="transferActionConfirmationModalLabel"> <!-- White text -->
             <i [ngClass]="confirmIconClass" class="me-2"></i> <!-- Dynamic icon -->
             {{ modalTitle }} <!-- Dynamic Title -->
          </h5>
          <button type="button" class="btn-close btn-close-white" aria-label="Close" (click)="closeConfirmationModal()"></button>
        </div>
        <div class="modal-body">
          <p>{{ modalMessage }}</p> <!-- Dynamic Message -->
          <!-- Display User Info for context -->
          <div *ngIf="itemToConfirmAction" class="mt-2 p-2 border rounded bg-light small">
              <!-- Use safe navigation -->
              <div><strong>User:</strong> {{ itemToConfirmAction.admin?.firstName }} {{ itemToConfirmAction.admin?.lastName }} (ID: {{itemToConfirmAction.admin?.id ?? itemToConfirmAction.adminId}})</div>
              <div><strong>Transfer To:</strong> {{ itemToConfirmAction.plant?.name ?? 'N/A' }}</div>
              <div><strong>Requested By:</strong> {{ itemToConfirmAction.transferedByAdmin?.firstName }} {{ itemToConfirmAction.transferedByAdmin?.lastName ?? 'N/A' }}</div>
          </div>
        </div>
        <div class="modal-footer justify-content-center"> <!-- Centered Buttons -->
          <button type="button" class="btn btn-secondary" (click)="closeConfirmationModal()">
              <i class="bi bi-x-lg me-1"></i> Cancel
          </button>
          <button type="button" class="btn" [ngClass]="confirmButtonClass.replace('bg-', 'btn-')" (click)="confirmAction()"> <!-- Convert bg-* to btn-* -->
              <i [ngClass]="confirmIconClass" class="me-1"></i>
              {{ confirmButtonText }} <!-- Dynamic Text -->
          </button>
        </div>
      </div>
    </div>
  </div>
  <!-- End Transfer Action Confirmation Modal -->