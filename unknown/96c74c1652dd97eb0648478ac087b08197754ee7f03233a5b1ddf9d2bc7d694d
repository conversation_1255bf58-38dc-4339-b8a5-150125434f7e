name: Azure Static Web Apps CI/CD for dev-azure-2 # Added a descriptive name

trigger:
  branches:
    include:
      - azure-dev-pipelines # Target branch from your original file

jobs:
- job: build_and_deploy_job
  displayName: Build and Deploy Angular Job # Updated display name
  # condition: or(eq(variables['Build.Reason'], 'Manual'), or(eq(variables['Build.Reason'], 'PullRequest'), eq(variables['Build.Reason'], 'IndividualCI'))) # Optional: Uncomment if you want the same trigger conditions as the reference file
  pool:
    vmImage: ubuntu-latest
  variables:
  # Use the variable group specific to THIS application/environment
  - group: BOG-ANGULAR # Kept from your original file

  steps:
  - checkout: self
    submodules: true # Kept from your original file

  # Best Practice: Explicitly install the required Node.js version
  # Choose a version compatible with your Angular project (e.g., 16.x, 18.x, 20.x).
  # The reference file didn't specify, relying on the agent's default, which is less reliable.
  # Let's assume Angular 16+ and use Node 18.x. Adjust if needed.
  - task: NodeTool@0
    inputs:
      versionSpec: '18.x'
    displayName: "Install Node.js 18.x"

  - script: |
      node -v
      yarn -v
    displayName: "Check Node.js and Yarn Versions"

  # Optional: Install Angular CLI globally (like reference file)
  # Note: This is often not necessary if @angular/cli is a dev dependency in package.json,
  # as 'yarn build' will use the local version. Remove if not needed.
  # Match the version if your project requires a specific CLI version (e.g., 16.0.0 from reference)
  # - script: |
  #     npm install -g @angular/cli@<your-required-version> # e.g., @angular/cli@16.0.0
  #   displayName: "Install Angular CLI Globally (Optional)"

  # Configure Git to use HTTPS (Optional: Keep if needed for private dependencies)
  - script: |
      git config --global url."https://github.com/".insteadOf "**************:"
    displayName: "Configure Git to use HTTPS"

  # Install dependencies using yarn (matches reference file)
  - script: |
      yarn install --frozen-lockfile # Using --frozen-lockfile is recommended in CI for reproducibility
      # Alternatively, just 'yarn' like the reference file:
      # yarn
    displayName: "Install Dependencies with Yarn"

  # Build the Angular project using yarn (matches reference file)
  # Assumes 'yarn build' executes 'ng build' (likely with production flags) defined in your package.json scripts.
  - script: |
      yarn build
    displayName: "Build Angular Project"

  - task: AzureStaticWebApp@0
    inputs:
      # Use the token variable corresponding to the variable group above
      azure_static_web_apps_api_token: $(STATIC_WEB_APPS_ANGULAR) # Kept from your original file
      app_location: "/" # Root of your Angular project (where angular.json is)
      api_location: "" # Leave empty or remove if you don't have a backend API function folder
      # CRITICAL: Update output_location to match YOUR Angular project's build output path!
      # Check your angular.json -> projects -> <your-project-name> -> architect -> build -> options -> outputPath
      # The reference file used "dist/wbi/browser". Yours might be similar or just "dist/<your-app-name>". VERIFY THIS.
      output_location: "dist/bog-adani-angular/browser" # *** CHANGE <your-angular-app-name> *** (or remove '/browser' if not applicable)
    displayName: "Deploying Angular App to Azure Static Web Apps"