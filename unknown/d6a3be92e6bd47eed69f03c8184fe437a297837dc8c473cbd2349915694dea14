.logininformationarea {
    background: linear-gradient(89.27deg, rgba(30, 105, 178, 0.1) 0.6%, rgba(128, 66, 146, 0.1) 51.03%, rgba(175, 58, 110, 0.1) 99.39%);
    border-radius: 13px;
    padding: 17px 22px;
    height: 100%;
}

.logininformationarea > h4 {
    color: #0B74B0;
    font-size: 20px;
    font-weight: 600;
}

.logininformationarea > p {
    color: #000;
    font-size: 12px;
    font-weight: 400;
    line-height: 14px;
    margin-bottom: 8px !important;
}

//Profile
.tab-class {
    background: linear-gradient(90deg,#0B74B0 0%,#75479C 54.17%,#BD3861 100%) !important;
    padding-left: 20px !important;
}

.bg-color {
    background-color: #F9F9F9;
}

.profile-card {
    background-color: #ffffff;
    border-radius: 10px;
    padding: 20px;
}

.hint-text {
    padding-left: 5px;
    font-size: 10px;
    font-weight: 700;
    color: red;
}


.position-header {
    top: 0px;
    position: relative;
    width: 100%;
}

.position-footer {
    bottom: 0px;
    position: fixed;
    width: 100%;
}