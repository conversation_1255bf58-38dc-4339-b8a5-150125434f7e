<svg width="82" height="83" viewBox="0 0 82 83" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_422_2264)">
<circle cx="34.5" cy="41.5" r="27.5" transform="rotate(180 34.5 41.5)" fill="white"/>
<circle cx="34.5" cy="41.5" r="27.25" transform="rotate(180 34.5 41.5)" stroke="#0B74B0" stroke-width="0.5"/>
</g>
<path d="M44.4004 36.3365C45.1111 36.8351 45.208 37.6937 44.5943 38.3307L37.6166 45.7259C36.7444 46.6122 35.9691 47 35 47C34.0309 47 33.2556 46.6122 32.3834 45.7259L25.4057 38.3307C24.792 37.6937 24.8889 36.8351 25.5996 36.3365C26.3102 35.8103 27.3763 35.9211 27.9254 36.5304L34.5477 43.5932C34.6447 43.704 34.7739 43.8425 35 43.8425C35.2261 43.8425 35.3553 43.704 35.4523 43.5932L42.0746 36.5304C42.6237 35.9211 43.6898 35.8103 44.4004 36.3365Z" fill="#0B74B0"/>
<defs>
<filter id="filter0_d_422_2264" x="0.384554" y="0.764554" width="81.4709" height="81.4709" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="6.62"/>
<feGaussianBlur stdDeviation="6.61772"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.035625 0 0 0 0 0.21447 0 0 0 0 0.316667 0 0 0 0.4 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_422_2264"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_422_2264" result="shape"/>
</filter>
</defs>
</svg>
