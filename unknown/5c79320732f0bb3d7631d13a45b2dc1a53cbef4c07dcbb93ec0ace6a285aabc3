.tab-container {
    display: flex;
    justify-content: center; /* Centers the .tab-headers horizontally */
    align-items: center;     /* Centers the .tab-headers vertically, if needed */
    width: 100%;             /* Ensures the container spans full width */
    margin-bottom: 20px;
  }
  
  .tab-headers {
    display: flex;
    justify-content: center; /* Centers the individual .tab-header elements horizontally */
    border: 1px solid #0B74B0;
    border-radius: 25px;
    background-color: white;
    color: #0B74B0;
  }
  
  .tab-header {
    padding: 10px 20px;
    cursor: pointer;
    font-size: 12px;
    transition: transform 0.3s,
  }
  .tab-header.active {
    background: linear-gradient(90deg, #0B74B0 0%, #75479C 54.17%, #BD3861 100%);
    color: white;
    border-radius: 23px;
    font-weight: bold;
  }