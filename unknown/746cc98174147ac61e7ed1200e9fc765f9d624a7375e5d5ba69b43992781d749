import { Injectable } from '@angular/core';
import { ApiService } from '../api.service';
import { ENDPOINTS } from '../../core/endpoints';

@Injectable({
  providedIn: 'root'
})
export class UpdateService {

  constructor(readonly apiService: ApiService) { }

  async update(data:any){
    // Use direct string for endpoint to avoid TypeScript errors
    // and match the old Vue.js project's endpoint
    console.log('Update service called with data:', JSON.stringify(data, null, 2));
    return await this.apiService.postData('/update', data);
  }
}
