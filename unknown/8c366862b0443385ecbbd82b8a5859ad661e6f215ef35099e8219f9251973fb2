<app-toast-message></app-toast-message>
<div class="card custom-card">
    <div class="card-header">
        <div class="row align-items-center">
            <div class="col d-flex align-items-center">
                <h6 class=>Deleted User List</h6>
            </div>
            <div class="col text-end d-flex align-items-center justify-content-end">
                <!-- *** REPLACE the existing simple Download button with this Dropdown *** -->
                <div ngbDropdown class="d-inline-block me-2">
                    <button type="button" class="btn btn-sm adani-btn dropdown-toggle"
                        id="downloadDeletedUserExcelDropdown" ngbDropdownToggle
                        [disabled]="isDownloadingExcel || listLoading">
                        <span *ngIf="!isDownloadingExcel">
                            <i class="bi bi-file-earmark-excel me-1"></i> Download Excel
                        </span>
                        <span *ngIf="isDownloadingExcel">
                            <span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
                            Downloading {{ downloadType === 'current' ? 'Page' : (downloadType === 'all' ? 'All' : '')
                            }}...
                        </span>
                    </button>
                    <ul ngbDropdownMenu aria-labelledby="downloadDeletedUserExcelDropdown">
                        <li>
                            <button ngbDropdownItem (click)="downloadExcel('current')"
                                [disabled]="isDownloadingExcel || listLoading || (deletedUserList?.length ?? 0) === 0">
                                <i class="bi bi-download me-1"></i> Download Current Page ({{ deletedUserList?.length ??
                                0 }})
                            </button>
                        </li>
                        <li>
                            <button ngbDropdownItem (click)="downloadExcel('all')"
                                [disabled]="isDownloadingExcel || listLoading || totalItems === 0">
                                <i class="bi bi-cloud-download me-1"></i> Download All Filtered ({{ totalItems }})
                            </button>
                        </li>
                    </ul>
                </div>
                <img src="../../../assets/svg/filter.svg" class="filter-button ms-3" (click)="openFilterModal()" alt="Filter" />
            </div>
        </div>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered custom-table">
                <thead class="table-header">
                    <tr>
                        <th scope="col" class="col-actions">Actions</th>
                        <th scope="col" class="col-user-details">User Detail</th>
                        <th scope="col" class="col-plant-info">Plant Information</th>
                        <th scope="col" class="col-deleted-by">Deleted By</th>
                    </tr>
                </thead>
                <tbody>
                    <tr *ngIf="listLoading">
                        <td colspan="4" class="text-center p-4"> <!-- Colspan = 4 -->
                            <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                            Loading deleted user logs...
                        </td>
                    </tr>
                    <tr *ngIf="!listLoading && deletedUserList.length === 0">
                        <td colspan="4" class="text-center p-4 text-muted"> <!-- Colspan = 4 -->
                            No deleted user logs found matching the criteria.
                        </td>
                    </tr>
                    <tr *ngFor="let item of deletedUserList">
                        <!-- Actions Column -->
                        <td class="text-center align-middle">
                            <button *ngIf="item.status === 3" type="button" class="btn btn-primary btn-sm"
                            (click)="openReactivateConfirmation(item)">
                                Re-Activate
                            </button>
                            <button *ngIf="item.status === 1" type="button" class="btn btn-success btn-sm" disabled>
                                Activated
                            </button>
                        </td>

                        <!-- User Detail Column -->
                        <td>
                            <div class="details-container">
                                <p class="label-value"><strong>Name:</strong> <span class="value-text">{{ item.firstName }} {{ item.lastName }}</span></p>
                                <p class="label-value"><strong>DOB:</strong> <span class="value-text">{{ item.dob | date: 'dd-MMM-yyyy' }}</span></p>
                                <p class="label-value">
                                    <strong>Gender:</strong>
                                    <span class="value-text" [ngSwitch]="+(item.gender ?? -1)">
                                        <span *ngSwitchCase="0">Female</span>
                                        <span *ngSwitchCase="1">Male</span>
                                        <span *ngSwitchCase="2">Other</span>
                                        <span *ngSwitchDefault>{{ item.gender || 'N/A' }}</span>
                                    </span>
                                </p>
                                <p class="label-value"><strong>Role:</strong> <span class="value-text">{{ item.adminsRole?.name }}</span></p>
                                <p class="label-value">
                                    <strong>Email:</strong>
                                    <span class="value-text">
                                        <a [href]="'mailto:' + item.email" class="text-primary">{{ item.email }}</a>
                                    </span>
                                </p>
                                <p class="label-value"><strong>Contact:</strong> <span class="value-text">{{ item.contactNumber }}</span></p>
                            </div>
                        </td>

                        <!-- Plant Information Column -->
                        <td>
                            <div class="details-container">
                                <p class="label-value">
                                    <strong>Plants:</strong>
                                    <span class="value-text">
                                        <div class="plant-badges-container" *ngIf="item.plant && item.plant.length > 0">
                                            <span class="plant-badge bg-primary" *ngFor="let plant of item.plant | slice:0:5">{{ getPlantName(plant) }}</span>
                                            <a *ngIf="item.plant && item.plant.length > 5" (click)="showMorePlants(item)" class="text-success cursor-pointer">
                                                ...and {{ item.plant.length - 5 }} more
                                            </a>
                                        </div>
                                        <div *ngIf="!item.plant || item.plant.length === 0" class="text-muted">No plants assigned</div>
                                    </span>
                                </p>
                                <p class="label-value"><strong>Department:</strong> <span class="value-text">{{ item.department?.title || 'N/A' }}</span></p>
                                <p class="label-value"><strong>Designation:</strong> <span class="value-text">{{ item.designation?.title || 'N/A' }}</span></p>
                            </div>
                        </td>

                        <!-- Deleted By Column -->
                        <td>
                            <div class="details-container">
                                <p class="label-value"><strong>ID:</strong> <span class="value-text">{{ item.adminId }}</span></p>
                                <p class="label-value"><strong>Name:</strong> <span class="value-text">{{ item.admin.firstName }} {{ item.admin.lastName }}</span></p>
                                <p class="label-value"><strong>Role:</strong> <span class="value-text">Superadmin</span></p>
                                <p class="label-value">
                                    <strong>Email:</strong>
                                    <span class="value-text">
                                        <a [href]="'mailto:' + item.admin.email" class="text-primary">{{ item.admin.email }}</a>
                                    </span>
                                </p>
                                <p class="label-value"><strong>Contact:</strong> <span class="value-text">{{ item.admin.contactNumber }}</span></p>
                                <p class="label-value"><strong>Deleted On:</strong> <span class="value-text">{{ item.createdTimestamp | date: 'dd-MMM-yyyy HH:mm' }}</span></p>
                            </div>
                        </td>


                    </tr>
                </tbody>
            </table>
        </div>
    </div>
    <div class="card-footer text-muted text-center">
        <app-pagination [currentPage]="currentPage" [totalItems]="totalItems" [itemsPerPage]="itemsPerPage"
            (pageChange)="onPageChange($event)"></app-pagination>
    </div>
</div>
<app-offcanvas [title]="'Filter Deleted User Log'" *ngIf="isFilterOffcanvasOpen" (onClickCross)="closeFilterModal()">
    <div class="filter-container p-3">
        <form #filterForm="ngForm" (ngSubmit)="applyFilters()">
            <div class="row g-3">

                <div class="col-12">
                    <label class="form-label" for="filterFirstName">First Name</label>
                    <input type="text" id="filterFirstName" class="form-control" placeholder="User's First Name"
                        [(ngModel)]="filters.firstName" name="firstName" #firstName="ngModel"
                        pattern="^[a-zA-Z\s]*$" maxlength="30"
                        [ngClass]="{'is-invalid': firstName.invalid && (firstName.dirty || firstName.touched)}">
                    <div *ngIf="firstName.invalid && (firstName.dirty || firstName.touched)" class="invalid-feedback">
                        <div *ngIf="firstName.errors?.['pattern']">First name should contain only alphabets.</div>
                    </div>
                </div>
                <div class="col-12">
                    <label class="form-label" for="filterLastName">Last Name</label>
                    <input type="text" id="filterLastName" class="form-control" placeholder="User's Last Name"
                        [(ngModel)]="filters.lastName" name="lastName" #lastName="ngModel"
                        pattern="^[a-zA-Z\s]*$" maxlength="30"
                        [ngClass]="{'is-invalid': lastName.invalid && (lastName.dirty || lastName.touched)}">
                    <div *ngIf="lastName.invalid && (lastName.dirty || lastName.touched)" class="invalid-feedback">
                        <div *ngIf="lastName.errors?.['pattern']">Last name should contain only alphabets.</div>
                    </div>
                </div>
                <div class="col-12">
                    <label class="form-label" for="filterEmail">Email</label>
                    <input type="email" id="filterEmail" class="form-control" placeholder="User's Email with &#64;adani.com domain"
                        [(ngModel)]="filters.email" name="email" #email="ngModel"
                        pattern="[a-zA-Z0-9._%+-]+&#64;adani\.com$"
                        [ngClass]="{'is-invalid': email.invalid && (email.dirty || email.touched)}">
                    <div *ngIf="email.invalid && (email.dirty || email.touched)" class="invalid-feedback">
                        <div *ngIf="email.errors?.['pattern']">Email must use the &#64;adani.com domain.</div>
                    </div>
                </div>
                <div class="col-12">
                    <label class="form-label" for="filterMobile">Mobile Number</label>
                    <input type="text" id="filterMobile" class="form-control" placeholder="User's Mobile"
                        [(ngModel)]="filters.mobile" name="mobile" #mobile="ngModel"
                        pattern="^[0-9]*$" maxlength="10"
                        [ngClass]="{'is-invalid': mobile.invalid && (mobile.dirty || mobile.touched)}">
                    <div *ngIf="mobile.invalid && (mobile.dirty || mobile.touched)" class="invalid-feedback">
                        <div *ngIf="mobile.errors?.['pattern']">Mobile number should contain only digits.</div>
                    </div>
                </div>
                <div class="col-12">
                    <label class="form-label" for="filterPlant">Select Plant</label>
                    <select id="filterPlant" class="form-select" [(ngModel)]="filters.plantId" name="plantId">
                        <option [ngValue]="null">All Plants</option>
                        <option *ngFor="let plant of availablePlants" [value]="plant.id">{{ plant.name }}</option>
                    </select>
                </div>
                <div class="col-12">
                    <label class="form-label" for="filterDesignation">Filter by Designation</label>
                    <select id="filterDesignation" class="form-select" [(ngModel)]="filters.designationId"
                        name="designationId">
                        <option [ngValue]="null">All Designations</option>
                        <option *ngFor="let desg of availableDesignations" [value]="desg.id">{{ desg.title }}</option>
                    </select>
                </div>
                <div class="col-12">
                    <label class="form-label" for="filterDepartment">Filter by Department</label>
                    <select id="filterDepartment" class="form-select" [(ngModel)]="filters.departmentId"
                        name="departmentId">
                        <option [ngValue]="null">All Departments</option>
                        <option *ngFor="let dept of availableDepartments" [value]="dept.id">{{ dept.title }}</option>
                    </select>
                </div>
                <div class="col-12">
                    <label class="form-label" for="filterAdminRole">Search by Admin Role</label>
                    <select id="filterAdminRole" class="form-select" [(ngModel)]="filters.adminRoleId"
                        name="adminRoleId">
                        <option [ngValue]="null">All Roles</option>
                        <option *ngFor="let role of availableAdminRoles" [value]="role.id">{{ role.name }}</option>
                    </select>
                </div>
                <div class="col-12">
                    <label class="form-label" for="filterSortBy">Sort By</label>
                    <select id="filterSortBy" class="form-select" [(ngModel)]="filters.sortField" name="sortField">
                        <option [ngValue]="null">Default Sort (Log ID DESC)</option>
                        <option *ngFor="let field of availableSortFields" [value]="field.value">{{ field.label }}
                        </option>
                    </select>
                    <label class="form-label mt-2" for="filterSortDir">Sort Direction</label>
                    <select id="filterSortDir" class="form-select" [(ngModel)]="filters.sortDirection"
                        name="sortDirection">
                        <option value="DESC">Descending</option>
                        <option value="ASC">Ascending</option>
                    </select>
                </div>
                <div class="col-12 mt-4 d-grid gap-2">
                    <button type="submit" class="btn adani-btn" [disabled]="filterForm.invalid">
                        <i class="bi bi-search me-1"></i> Search
                    </button>
                    <button type="button" class="btn btn-secondary" (click)="resetFilters()">
                        <i class="bi bi-arrow-clockwise me-1"></i> Reset Filters
                    </button>
                </div>
            </div>
        </form>
    </div>
</app-offcanvas>
<!-- Re-Activate Confirmation Modal -->
<div class="modal fade" #reactivateConfirmationModalElement id="reactivateConfirmationModal" tabindex="-1" aria-labelledby="reactivateConfirmationModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
      <div class="modal-content">
        <div class="modal-header bg-primary text-white"> <!-- Primary header -->
          <h5 class="modal-title" id="reactivateConfirmationModalLabel">
            <i class="bi bi-arrow-clockwise me-2"></i> Confirm Re-Activation
          </h5>
          <button type="button" class="btn-close btn-close-white" aria-label="Close" (click)="closeReactivateConfirmation()"></button>
        </div>
        <div class="modal-body">
          <p>Are you sure you want to re-activate the user:</p>
          <p><strong>{{ itemToReactivate?.firstName }} {{ itemToReactivate?.lastName }} (ID: {{ itemToReactivate?.id }})</strong>?</p>
          <p class="text-muted small">This will likely set their status back to 'Active' or 'Pending'.</p>
        </div>
        <div class="modal-footer justify-content-center"> <!-- Center buttons -->
          <button type="button" class="btn btn-secondary" (click)="closeReactivateConfirmation()">
              <i class="bi bi-x-lg me-1"></i> Cancel
          </button>
          <button type="button" class="btn btn-primary" (click)="confirmReactivate()"> <!-- Primary button -->
              <i class="bi bi-check-circle-fill me-1"></i> Yes, Re-Activate
          </button>
        </div>
      </div>
    </div>
  </div>
  <!-- End Re-Activate Confirmation Modal -->