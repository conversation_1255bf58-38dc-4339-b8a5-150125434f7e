import { Injectable } from '@angular/core';
import { ApiService } from '../../api.service';
import { ENDPOINTS } from '../../../core/endpoints';

@Injectable({
  providedIn: 'root'
})
export class BodyPartService {

  constructor(readonly apiService: ApiService) { }

  async getBodyPart(params: any) {
    return await this.apiService.getData(ENDPOINTS.BODY_PART.BODY_PART_MASTER, params);
  }

  async createBodyPart(newBodyPartData: any) {
    return await this.apiService.postData(ENDPOINTS.BODY_PART.BODY_PART_MASTER, newBodyPartData);
  }

}
