import { Injectable } from '@angular/core';
import { ApiService } from '../../api.service';
import { ENDPOINTS } from '../../../core/endpoints';

@Injectable({
  providedIn: 'root'
})
export class SegmentService {

  constructor(readonly apiService: ApiService) { }

  async getSegment(params: any) {
    return await this.apiService.getData(ENDPOINTS.SEGNMENT.GET_SEGMENT, params);
  }

  async createSegment(newSegmentData: any) {
    return await this.apiService.postData(ENDPOINTS.SEGNMENT.GET_SEGMENT, newSegmentData);
  }
}
