<app-toast-message></app-toast-message>
<div class="card custom-card">
  <div class="card-header d-flex align-items-center justify-content-between">
    <div>
      <h6 class="mb-0">Plant wise report</h6>
    </div>
    <div class="d-flex align-items-center">
        <!-- *** REPLACE the existing simple Download button with this Dropdown *** -->
        <div ngbDropdown class="d-inline-block me-2">
          <button type="button" class="btn btn-sm adani-btn dropdown-toggle" id="downloadRAGExcelDropdown"
            ngbDropdownToggle [disabled]="isDownloadingExcel || isLoadingReport">
            <span *ngIf="!isDownloadingExcel">
              <i class="bi bi-file-earmark-excel me-1"></i> Download Excel
            </span>
            <span *ngIf="isDownloadingExcel">
              <span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
              Downloading {{ downloadType === 'current' ? 'Page' : (downloadType === 'all' ? 'All' : '') }}...
            </span>
          </button>
          <ul ngbDropdownMenu aria-labelledby="downloadRAGExcelDropdown">
            <li>
              <button ngbDropdownItem (click)="downloadExcel('current')"
                [disabled]="isDownloadingExcel || isLoadingReport || (list?.length ?? 0) === 0">
                <i class="bi bi-download me-1"></i> Download Current Page ({{ list?.length ?? 0 }})
              </button>
            </li>
            <li>
              <button ngbDropdownItem (click)="downloadExcel('all')"
                [disabled]="isDownloadingExcel || isLoadingReport || totalItems === 0">
                <i class="bi bi-cloud-download me-1"></i> Download All Filtered ({{ totalItems }})
              </button>
            </li>
          </ul>
        </div>
        <img src="../../../assets/svg/filter.svg" class="filter-button ms-3" (click)="openFilterModal()" alt=""
          style="width: 35px;" />
      </div>
    </div>
  <div class="card-body">
    <div class="table-responsive">
      <table class="table custom-table">
        <thead class="table-header">
          <tr>
            <th scope="col" rowspan="2">Plant</th>
            <th scope="col" colspan="3">QR Codes</th>
          </tr>
          <tr>
            <th scope="col">Red QR Count</th>
            <th scope="col">Yellow QR Count</th>
            <th scope="col">Green QR Count</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngIf="isLoadingReport">
            <td colspan="4" class="text-center p-4"> <!-- Colspan = 4 -->
              <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
              Loading plant wise RAG report...
            </td>
          </tr>
          <tr *ngIf="!isLoadingReport && list.length === 0">
            <td colspan="4" class="text-center p-4 text-muted"> <!-- Colspan = 4 -->
              No plant wise RAG report data found matching the criteria.
            </td>
          </tr>
          <tr *ngFor="let item of list">
            <td>{{ item.name }}</td>
            <td>{{ item.redZoneCount }}</td>
            <td>{{ item.yellowZoneCount }}</td>
            <td>{{ item.greenZoneCount }}</td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
  <div class="card-footer text-muted text-center">
    <app-pagination [currentPage]="currentPage" [totalItems]="totalItems" [itemsPerPage]="itemsPerPage"
      (pageChange)="onPageChange($event)"></app-pagination>
  </div>
</div>
<!-- Filter Offcanvas -->
<app-offcanvas [title]="'Filter PlantWise RAG Report'" *ngIf="isEditUserModalOpen" (onClickCross)="closeModal()">
  <div class="container">
    <div class="row g-2">
      <div class="col-12">
        <label for="plantSelectNg" class="form-label">Plant(s)</label>
        <ng-select  id="plantSelectNg" [items]="plantsList" bindLabel="name" bindValue="id"
          placeholder="Select Plant(s) or leave empty for All" [multiple]="true" [(ngModel)]="selectedPlantIds"
          [loading]="isLoadingPlants" [searchable]="true" [closeOnSelect]="false">
        </ng-select>
        <!-- Optional: Add message if plant list fails to load -->
        <div *ngIf="!isLoadingPlants && plantsList.length === 0" class="text-muted small mt-1">
          Could not load plants.
        </div>
      </div>
      <div class="col-12 mt-3"> <!-- Added margin-top -->
        <!-- Removed the empty label -->
        <button type="button" (click)="applyFilter()" class="btn adani-btn w-100" [disabled]="isLoadingPlants">
          <i class="bi bi-search"></i> Search
        </button>
      </div>
    </div>
  </div>
</app-offcanvas>
