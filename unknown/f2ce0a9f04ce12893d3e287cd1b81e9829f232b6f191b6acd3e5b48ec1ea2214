/**
 * SAML Test Helper Utilities
 * Utility functions to help test SAML functionality
 */

/**
 * Mock SAML token for testing purposes
 * DO NOT use in production
 */
export const generateMockSamlToken = (): string => {
  return 'mock-saml-token-' + Date.now();
};

/**
 * Simulate SAML callback URL for testing
 */
export const createMockSamlCallbackUrl = (token: string): string => {
  const baseUrl = window.location.origin + window.location.pathname;
  return `${baseUrl}?redirect=${encodeURIComponent(`callback?token=${token}`)}`;
};

/**
 * Test helper to validate email format for SAML
 */
export const isValidAdaniEmail = (email: string): boolean => {
  return email.endsWith('@adani.com') && email.includes('@');
};