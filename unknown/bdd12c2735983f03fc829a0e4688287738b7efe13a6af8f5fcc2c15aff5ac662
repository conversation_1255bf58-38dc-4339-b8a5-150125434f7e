import { Injectable } from '@angular/core';
import { ApiService } from '../api.service';
import { ENDPOINTS } from '../../core/endpoints';

@Injectable({
  providedIn: 'root'
})
export class CrisisManagementService {

  constructor(readonly apiService: ApiService) { }

  async getCrises(params: any) {
    return await this.apiService.getData(ENDPOINTS.CRISIS_REPORT.GET_CRISIS_REPORT, params);
  }

  async getSpokeDetail(params: any) {
    return await this.apiService.getData(ENDPOINTS.CRISIS_REPORT.SPOKE_DETAIL, params);
  }

  async createCrisis(data: any) {
    return await this.apiService.postData(ENDPOINTS.CRISIS_REPORT.CREATE, data);
  }

  async sendCrisisEmail(data: any) {
    return await this.apiService.postData(ENDPOINTS.CRISIS_REPORT.SEND_EMAIL, data);
  }

  // Helper function to convert UTC date to IST
  convertUTCtoIST(utcDate: string | number | Date): Date {
    const date = utcDate instanceof Date ? new Date(utcDate.getTime()) : new Date(utcDate);
    // IST is UTC+5:30
    date.setHours(date.getHours() + 5);
    date.setMinutes(date.getMinutes() + 30);
    return date;
  }

  // Helper function to format date as YYYY-MM-DD
  formatDate(dateString: string): string {
    const parts = dateString.split('-');
    if (parts.length === 3) {
      return `${parts[0]}-${parts[1]}-${parts[2]}`;
    }
    return dateString;
  }

  // Helper function to convert base64 to Blob
  base64ToBlob(base64: string, contentType: string): Blob {
    const byteCharacters = atob(base64.split(',')[1]);
    const byteArrays = [];

    for (let offset = 0; offset < byteCharacters.length; offset += 512) {
      const slice = byteCharacters.slice(offset, offset + 512);

      const byteNumbers = new Array(slice.length);
      for (let i = 0; i < slice.length; i++) {
        byteNumbers[i] = slice.charCodeAt(i);
      }

      const byteArray = new Uint8Array(byteNumbers);
      byteArrays.push(byteArray);
    }

    return new Blob(byteArrays, { type: contentType });
  }
}
