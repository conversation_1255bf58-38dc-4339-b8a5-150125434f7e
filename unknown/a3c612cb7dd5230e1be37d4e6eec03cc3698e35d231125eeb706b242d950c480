<mat-dialog-content class="dialog-container">
    <div class="header">
        <p class="dialog-title">Cluster Wise BOG Tour</p>
        <button mat-raised-button color="accent" class="cluster-btn">{{this.data.clusterTitle}}</button>
        <button class="close-button" (click)="crossClick()">×</button>
    </div>

    <div class="table-container">
        <table class="mat-elevation-z8 custom-table" mat-table [dataSource]="dataSource">
            <ng-container matColumnDef="no">
                <th mat-header-cell *matHeaderCellDef class="sticky-header"> Sr. No. </th>
                <td mat-cell *matCellDef="let element"> {{element.no}} </td>
            </ng-container>

            <ng-container matColumnDef="plantsName">
                <th mat-header-cell *matHeaderCellDef class="sticky-header"> Plants Name </th>
                <td mat-cell *matCellDef="let element"> {{element.plantsName}} </td>
            </ng-container>


            <ng-container matColumnDef="NoBogTours">
                <th mat-header-cell *matHeaderCellDef class="sticky-header"> Total number of Tours </th>
                <td mat-cell *matCellDef="let element"> {{element.NoBogTours}} </td>
            </ng-container>


            <tr mat-header-row *matHeaderRowDef="displayedColumns" class="sticky-header-row"></tr>

            <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
        </table>

        <div *ngIf="!isLoading && (!dataSource || dataSource?.data?.length === 0)" class="no-data-found">
            <p>No data found</p>
        </div>
    </div>

    <div class="loading-spinner-container" *ngIf="isLoading">
        <mat-spinner diameter="40"></mat-spinner>
    </div>
</mat-dialog-content>