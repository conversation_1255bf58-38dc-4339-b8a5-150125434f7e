<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 12.1.1 (20240910.0053)
 -->
<!-- Title: INFRA Pages: 1 -->
<svg width="7677pt" height="80pt"
 viewBox="0.00 0.00 7676.97 80.00" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 76)">
<title>INFRA</title>
<polygon fill="white" stroke="none" points="-4,4 -4,-76 7672.97,-76 7672.97,4 -4,4"/>
<!-- LoginComponent -->
<g id="node1" class="node">
<title>LoginComponent</title>
<path fill="#e78f81" stroke="black" stroke-width="2" d="M141.74,-72C141.74,-72 12,-72 12,-72 6,-72 0,-66 0,-60 0,-60 0,-12 0,-12 0,-6 6,0 12,0 12,0 141.74,0 141.74,0 147.74,0 153.74,-6 153.74,-12 153.74,-12 153.74,-60 153.74,-60 153.74,-66 147.74,-72 141.74,-72"/>
<text text-anchor="middle" x="76.87" y="-40.2" font-family="Arial" font-size="14.00">LoginComponent</text>
<text text-anchor="middle" x="76.87" y="-23.4" font-family="Arial" font-size="14.00">(Angular::Component)</text>
</g>
<!-- HomeComponent -->
<g id="node2" class="node">
<title>HomeComponent</title>
<path fill="#e78f81" stroke="black" stroke-width="2" d="M313.74,-72C313.74,-72 184,-72 184,-72 178,-72 172,-66 172,-60 172,-60 172,-12 172,-12 172,-6 178,0 184,0 184,0 313.74,0 313.74,0 319.74,0 325.74,-6 325.74,-12 325.74,-12 325.74,-60 325.74,-60 325.74,-66 319.74,-72 313.74,-72"/>
<text text-anchor="middle" x="248.87" y="-40.2" font-family="Arial" font-size="14.00">HomeComponent</text>
<text text-anchor="middle" x="248.87" y="-23.4" font-family="Arial" font-size="14.00">(Angular::Component)</text>
</g>
<!-- QrcodeManagementComponent -->
<g id="node3" class="node">
<title>QrcodeManagementComponent</title>
<path fill="#e6e6e6" stroke="black" stroke-width="2" d="M547.87,-72C547.87,-72 355.87,-72 355.87,-72 349.87,-72 343.87,-66 343.87,-60 343.87,-60 343.87,-12 343.87,-12 343.87,-6 349.87,0 355.87,0 355.87,0 547.87,0 547.87,0 553.87,0 559.87,-6 559.87,-12 559.87,-12 559.87,-60 559.87,-60 559.87,-66 553.87,-72 547.87,-72"/>
<text text-anchor="middle" x="451.87" y="-40.2" font-family="Arial" font-size="14.00">QrcodeManagementComponent</text>
<text text-anchor="middle" x="451.87" y="-23.4" font-family="Arial" font-size="14.00">(Angular::Component)</text>
</g>
<!-- TourManagementComponent -->
<g id="node4" class="node">
<title>TourManagementComponent</title>
<path fill="#e6e6e6" stroke="black" stroke-width="2" d="M764.31,-72C764.31,-72 589.43,-72 589.43,-72 583.43,-72 577.43,-66 577.43,-60 577.43,-60 577.43,-12 577.43,-12 577.43,-6 583.43,0 589.43,0 589.43,0 764.31,0 764.31,0 770.31,0 776.31,-6 776.31,-12 776.31,-12 776.31,-60 776.31,-60 776.31,-66 770.31,-72 764.31,-72"/>
<text text-anchor="middle" x="676.87" y="-40.2" font-family="Arial" font-size="14.00">TourManagementComponent</text>
<text text-anchor="middle" x="676.87" y="-23.4" font-family="Arial" font-size="14.00">(Angular::Component)</text>
</g>
<!-- IncidentManagementComponent -->
<g id="node5" class="node">
<title>IncidentManagementComponent</title>
<path fill="#e6e6e6" stroke="black" stroke-width="2" d="M1001.43,-72C1001.43,-72 806.3,-72 806.3,-72 800.3,-72 794.3,-66 794.3,-60 794.3,-60 794.3,-12 794.3,-12 794.3,-6 800.3,0 806.3,0 806.3,0 1001.43,0 1001.43,0 1007.43,0 1013.43,-6 1013.43,-12 1013.43,-12 1013.43,-60 1013.43,-60 1013.43,-66 1007.43,-72 1001.43,-72"/>
<text text-anchor="middle" x="903.87" y="-40.2" font-family="Arial" font-size="14.00">IncidentManagementComponent</text>
<text text-anchor="middle" x="903.87" y="-23.4" font-family="Arial" font-size="14.00">(Angular::Component)</text>
</g>
<!-- DashboardComponent -->
<g id="node6" class="node">
<title>DashboardComponent</title>
<path fill="#e6e6e6" stroke="black" stroke-width="2" d="M1176.3,-72C1176.3,-72 1043.43,-72 1043.43,-72 1037.43,-72 1031.43,-66 1031.43,-60 1031.43,-60 1031.43,-12 1031.43,-12 1031.43,-6 1037.43,0 1043.43,0 1043.43,0 1176.3,0 1176.3,0 1182.3,0 1188.3,-6 1188.3,-12 1188.3,-12 1188.3,-60 1188.3,-60 1188.3,-66 1182.3,-72 1176.3,-72"/>
<text text-anchor="middle" x="1109.87" y="-40.2" font-family="Arial" font-size="14.00">DashboardComponent</text>
<text text-anchor="middle" x="1109.87" y="-23.4" font-family="Arial" font-size="14.00">(Angular::Component)</text>
</g>
<!-- PlantManagementComponent -->
<g id="node7" class="node">
<title>PlantManagementComponent</title>
<path fill="#e6e6e6" stroke="black" stroke-width="2" d="M1395.87,-72C1395.87,-72 1217.87,-72 1217.87,-72 1211.87,-72 1205.87,-66 1205.87,-60 1205.87,-60 1205.87,-12 1205.87,-12 1205.87,-6 1211.87,0 1217.87,0 1217.87,0 1395.87,0 1395.87,0 1401.87,0 1407.87,-6 1407.87,-12 1407.87,-12 1407.87,-60 1407.87,-60 1407.87,-66 1401.87,-72 1395.87,-72"/>
<text text-anchor="middle" x="1306.87" y="-40.2" font-family="Arial" font-size="14.00">PlantManagementComponent</text>
<text text-anchor="middle" x="1306.87" y="-23.4" font-family="Arial" font-size="14.00">(Angular::Component)</text>
</g>
<!-- ManageObservationComponent -->
<g id="node8" class="node">
<title>ManageObservationComponent</title>
<path fill="#e6e6e6" stroke="black" stroke-width="2" d="M1628.1,-72C1628.1,-72 1437.64,-72 1437.64,-72 1431.64,-72 1425.64,-66 1425.64,-60 1425.64,-60 1425.64,-12 1425.64,-12 1425.64,-6 1431.64,0 1437.64,0 1437.64,0 1628.1,0 1628.1,0 1634.1,0 1640.1,-6 1640.1,-12 1640.1,-12 1640.1,-60 1640.1,-60 1640.1,-66 1634.1,-72 1628.1,-72"/>
<text text-anchor="middle" x="1532.87" y="-40.2" font-family="Arial" font-size="14.00">ManageObservationComponent</text>
<text text-anchor="middle" x="1532.87" y="-23.4" font-family="Arial" font-size="14.00">(Angular::Component)</text>
</g>
<!-- OtherTaskComponent -->
<g id="node9" class="node">
<title>OtherTaskComponent</title>
<path fill="#e6e6e6" stroke="black" stroke-width="2" d="M1799.74,-72C1799.74,-72 1670,-72 1670,-72 1664,-72 1658,-66 1658,-60 1658,-60 1658,-12 1658,-12 1658,-6 1664,0 1670,0 1670,0 1799.74,0 1799.74,0 1805.74,0 1811.74,-6 1811.74,-12 1811.74,-12 1811.74,-60 1811.74,-60 1811.74,-66 1805.74,-72 1799.74,-72"/>
<text text-anchor="middle" x="1734.87" y="-40.2" font-family="Arial" font-size="14.00">OtherTaskComponent</text>
<text text-anchor="middle" x="1734.87" y="-23.4" font-family="Arial" font-size="14.00">(Angular::Component)</text>
</g>
<!-- CrisisManagementComponent -->
<g id="node10" class="node">
<title>CrisisManagementComponent</title>
<path fill="#e6e6e6" stroke="black" stroke-width="2" d="M2022.41,-72C2022.41,-72 1841.32,-72 1841.32,-72 1835.32,-72 1829.32,-66 1829.32,-60 1829.32,-60 1829.32,-12 1829.32,-12 1829.32,-6 1835.32,0 1841.32,0 1841.32,0 2022.41,0 2022.41,0 2028.41,0 2034.41,-6 2034.41,-12 2034.41,-12 2034.41,-60 2034.41,-60 2034.41,-66 2028.41,-72 2022.41,-72"/>
<text text-anchor="middle" x="1931.87" y="-40.2" font-family="Arial" font-size="14.00">CrisisManagementComponent</text>
<text text-anchor="middle" x="1931.87" y="-23.4" font-family="Arial" font-size="14.00">(Angular::Component)</text>
</g>
<!-- LeaderboardComponent -->
<g id="node11" class="node">
<title>LeaderboardComponent</title>
<path fill="#e6e6e6" stroke="black" stroke-width="2" d="M2207.76,-72C2207.76,-72 2063.98,-72 2063.98,-72 2057.98,-72 2051.98,-66 2051.98,-60 2051.98,-60 2051.98,-12 2051.98,-12 2051.98,-6 2057.98,0 2063.98,0 2063.98,0 2207.76,0 2207.76,0 2213.76,0 2219.76,-6 2219.76,-12 2219.76,-12 2219.76,-60 2219.76,-60 2219.76,-66 2213.76,-72 2207.76,-72"/>
<text text-anchor="middle" x="2135.87" y="-40.2" font-family="Arial" font-size="14.00">LeaderboardComponent</text>
<text text-anchor="middle" x="2135.87" y="-23.4" font-family="Arial" font-size="14.00">(Angular::Component)</text>
</g>
<!-- AppSettingsComponent -->
<g id="node12" class="node">
<title>AppSettingsComponent</title>
<path fill="#e6e6e6" stroke="black" stroke-width="2" d="M2389.81,-72C2389.81,-72 2249.93,-72 2249.93,-72 2243.93,-72 2237.93,-66 2237.93,-60 2237.93,-60 2237.93,-12 2237.93,-12 2237.93,-6 2243.93,0 2249.93,0 2249.93,0 2389.81,0 2389.81,0 2395.81,0 2401.81,-6 2401.81,-12 2401.81,-12 2401.81,-60 2401.81,-60 2401.81,-66 2395.81,-72 2389.81,-72"/>
<text text-anchor="middle" x="2319.87" y="-40.2" font-family="Arial" font-size="14.00">AppSettingsComponent</text>
<text text-anchor="middle" x="2319.87" y="-23.4" font-family="Arial" font-size="14.00">(Angular::Component)</text>
</g>
<!-- NotificationManagementComponent -->
<g id="node13" class="node">
<title>NotificationManagementComponent</title>
<path fill="#e6e6e6" stroke="black" stroke-width="2" d="M2647.54,-72C2647.54,-72 2432.19,-72 2432.19,-72 2426.19,-72 2420.19,-66 2420.19,-60 2420.19,-60 2420.19,-12 2420.19,-12 2420.19,-6 2426.19,0 2432.19,0 2432.19,0 2647.54,0 2647.54,0 2653.54,0 2659.54,-6 2659.54,-12 2659.54,-12 2659.54,-60 2659.54,-60 2659.54,-66 2653.54,-72 2647.54,-72"/>
<text text-anchor="middle" x="2539.87" y="-40.2" font-family="Arial" font-size="14.00">NotificationManagementComponent</text>
<text text-anchor="middle" x="2539.87" y="-23.4" font-family="Arial" font-size="14.00">(Angular::Component)</text>
</g>
<!-- ManageDigisafeComponent -->
<g id="node14" class="node">
<title>ManageDigisafeComponent</title>
<path fill="#e6e6e6" stroke="black" stroke-width="2" d="M2854.64,-72C2854.64,-72 2689.09,-72 2689.09,-72 2683.09,-72 2677.09,-66 2677.09,-60 2677.09,-60 2677.09,-12 2677.09,-12 2677.09,-6 2683.09,0 2689.09,0 2689.09,0 2854.64,0 2854.64,0 2860.64,0 2866.64,-6 2866.64,-12 2866.64,-12 2866.64,-60 2866.64,-60 2866.64,-66 2860.64,-72 2854.64,-72"/>
<text text-anchor="middle" x="2771.87" y="-40.2" font-family="Arial" font-size="14.00">ManageDigisafeComponent</text>
<text text-anchor="middle" x="2771.87" y="-23.4" font-family="Arial" font-size="14.00">(Angular::Component)</text>
</g>
<!-- UserwiseReportComponent -->
<g id="node15" class="node">
<title>UserwiseReportComponent</title>
<path fill="#e6e6e6" stroke="black" stroke-width="2" d="M3060.85,-72C3060.85,-72 2896.89,-72 2896.89,-72 2890.89,-72 2884.89,-66 2884.89,-60 2884.89,-60 2884.89,-12 2884.89,-12 2884.89,-6 2890.89,0 2896.89,0 2896.89,0 3060.85,0 3060.85,0 3066.85,0 3072.85,-6 3072.85,-12 3072.85,-12 3072.85,-60 3072.85,-60 3072.85,-66 3066.85,-72 3060.85,-72"/>
<text text-anchor="middle" x="2978.87" y="-40.2" font-family="Arial" font-size="14.00">UserwiseReportComponent</text>
<text text-anchor="middle" x="2978.87" y="-23.4" font-family="Arial" font-size="14.00">(Angular::Component)</text>
</g>
<!-- PlantwiseReportComponent -->
<g id="node16" class="node">
<title>PlantwiseReportComponent</title>
<path fill="#e6e6e6" stroke="black" stroke-width="2" d="M3269.03,-72C3269.03,-72 3102.71,-72 3102.71,-72 3096.71,-72 3090.71,-66 3090.71,-60 3090.71,-60 3090.71,-12 3090.71,-12 3090.71,-6 3096.71,0 3102.71,0 3102.71,0 3269.03,0 3269.03,0 3275.03,0 3281.03,-6 3281.03,-12 3281.03,-12 3281.03,-60 3281.03,-60 3281.03,-66 3275.03,-72 3269.03,-72"/>
<text text-anchor="middle" x="3185.87" y="-40.2" font-family="Arial" font-size="14.00">PlantwiseReportComponent</text>
<text text-anchor="middle" x="3185.87" y="-23.4" font-family="Arial" font-size="14.00">(Angular::Component)</text>
</g>
<!-- QrCodeReportComponent -->
<g id="node17" class="node">
<title>QrCodeReportComponent</title>
<path fill="#e6e6e6" stroke="black" stroke-width="2" d="M3466.58,-72C3466.58,-72 3311.16,-72 3311.16,-72 3305.16,-72 3299.16,-66 3299.16,-60 3299.16,-60 3299.16,-12 3299.16,-12 3299.16,-6 3305.16,0 3311.16,0 3311.16,0 3466.58,0 3466.58,0 3472.58,0 3478.58,-6 3478.58,-12 3478.58,-12 3478.58,-60 3478.58,-60 3478.58,-66 3472.58,-72 3466.58,-72"/>
<text text-anchor="middle" x="3388.87" y="-40.2" font-family="Arial" font-size="14.00">QrCodeReportComponent</text>
<text text-anchor="middle" x="3388.87" y="-23.4" font-family="Arial" font-size="14.00">(Angular::Component)</text>
</g>
<!-- ActiveUserComponent -->
<g id="node18" class="node">
<title>ActiveUserComponent</title>
<path fill="#e6e6e6" stroke="black" stroke-width="2" d="M3640.9,-72C3640.9,-72 3508.84,-72 3508.84,-72 3502.84,-72 3496.84,-66 3496.84,-60 3496.84,-60 3496.84,-12 3496.84,-12 3496.84,-6 3502.84,0 3508.84,0 3508.84,0 3640.9,0 3640.9,0 3646.9,0 3652.9,-6 3652.9,-12 3652.9,-12 3652.9,-60 3652.9,-60 3652.9,-66 3646.9,-72 3640.9,-72"/>
<text text-anchor="middle" x="3574.87" y="-40.2" font-family="Arial" font-size="14.00">ActiveUserComponent</text>
<text text-anchor="middle" x="3574.87" y="-23.4" font-family="Arial" font-size="14.00">(Angular::Component)</text>
</g>
<!-- InactiveUserComponent -->
<g id="node19" class="node">
<title>InactiveUserComponent</title>
<path fill="#e6e6e6" stroke="black" stroke-width="2" d="M3824.96,-72C3824.96,-72 3682.78,-72 3682.78,-72 3676.78,-72 3670.78,-66 3670.78,-60 3670.78,-60 3670.78,-12 3670.78,-12 3670.78,-6 3676.78,0 3682.78,0 3682.78,0 3824.96,0 3824.96,0 3830.96,0 3836.96,-6 3836.96,-12 3836.96,-12 3836.96,-60 3836.96,-60 3836.96,-66 3830.96,-72 3824.96,-72"/>
<text text-anchor="middle" x="3753.87" y="-40.2" font-family="Arial" font-size="14.00">InactiveUserComponent</text>
<text text-anchor="middle" x="3753.87" y="-23.4" font-family="Arial" font-size="14.00">(Angular::Component)</text>
</g>
<!-- TransferRequestComponent -->
<g id="node20" class="node">
<title>TransferRequestComponent</title>
<path fill="#e6e6e6" stroke="black" stroke-width="2" d="M4035.19,-72C4035.19,-72 3866.54,-72 3866.54,-72 3860.54,-72 3854.54,-66 3854.54,-60 3854.54,-60 3854.54,-12 3854.54,-12 3854.54,-6 3860.54,0 3866.54,0 3866.54,0 4035.19,0 4035.19,0 4041.19,0 4047.19,-6 4047.19,-12 4047.19,-12 4047.19,-60 4047.19,-60 4047.19,-66 4041.19,-72 4035.19,-72"/>
<text text-anchor="middle" x="3950.87" y="-40.2" font-family="Arial" font-size="14.00">TransferRequestComponent</text>
<text text-anchor="middle" x="3950.87" y="-23.4" font-family="Arial" font-size="14.00">(Angular::Component)</text>
</g>
<!-- DeleteUserComponent -->
<g id="node21" class="node">
<title>DeleteUserComponent</title>
<path fill="#e6e6e6" stroke="black" stroke-width="2" d="M4212.07,-72C4212.07,-72 4077.67,-72 4077.67,-72 4071.67,-72 4065.67,-66 4065.67,-60 4065.67,-60 4065.67,-12 4065.67,-12 4065.67,-6 4071.67,0 4077.67,0 4077.67,0 4212.07,0 4212.07,0 4218.07,0 4224.07,-6 4224.07,-12 4224.07,-12 4224.07,-60 4224.07,-60 4224.07,-66 4218.07,-72 4212.07,-72"/>
<text text-anchor="middle" x="4144.87" y="-40.2" font-family="Arial" font-size="14.00">DeleteUserComponent</text>
<text text-anchor="middle" x="4144.87" y="-23.4" font-family="Arial" font-size="14.00">(Angular::Component)</text>
</g>
<!-- RolesComponent -->
<g id="node22" class="node">
<title>RolesComponent</title>
<path fill="#e6e6e6" stroke="black" stroke-width="2" d="M4383.74,-72C4383.74,-72 4254,-72 4254,-72 4248,-72 4242,-66 4242,-60 4242,-60 4242,-12 4242,-12 4242,-6 4248,0 4254,0 4254,0 4383.74,0 4383.74,0 4389.74,0 4395.74,-6 4395.74,-12 4395.74,-12 4395.74,-60 4395.74,-60 4395.74,-66 4389.74,-72 4383.74,-72"/>
<text text-anchor="middle" x="4318.87" y="-40.2" font-family="Arial" font-size="14.00">RolesComponent</text>
<text text-anchor="middle" x="4318.87" y="-23.4" font-family="Arial" font-size="14.00">(Angular::Component)</text>
</g>
<!-- DepartmentComponent -->
<g id="node23" class="node">
<title>DepartmentComponent</title>
<path fill="#e6e6e6" stroke="black" stroke-width="2" d="M4563.63,-72C4563.63,-72 4426.11,-72 4426.11,-72 4420.11,-72 4414.11,-66 4414.11,-60 4414.11,-60 4414.11,-12 4414.11,-12 4414.11,-6 4420.11,0 4426.11,0 4426.11,0 4563.63,0 4563.63,0 4569.63,0 4575.63,-6 4575.63,-12 4575.63,-12 4575.63,-60 4575.63,-60 4575.63,-66 4569.63,-72 4563.63,-72"/>
<text text-anchor="middle" x="4494.87" y="-40.2" font-family="Arial" font-size="14.00">DepartmentComponent</text>
<text text-anchor="middle" x="4494.87" y="-23.4" font-family="Arial" font-size="14.00">(Angular::Component)</text>
</g>
<!-- DesignationComponent -->
<g id="node24" class="node">
<title>DesignationComponent</title>
<path fill="#e6e6e6" stroke="black" stroke-width="2" d="M4744.03,-72C4744.03,-72 4605.71,-72 4605.71,-72 4599.71,-72 4593.71,-66 4593.71,-60 4593.71,-60 4593.71,-12 4593.71,-12 4593.71,-6 4599.71,0 4605.71,0 4605.71,0 4744.03,0 4744.03,0 4750.03,0 4756.03,-6 4756.03,-12 4756.03,-12 4756.03,-60 4756.03,-60 4756.03,-66 4750.03,-72 4744.03,-72"/>
<text text-anchor="middle" x="4674.87" y="-40.2" font-family="Arial" font-size="14.00">DesignationComponent</text>
<text text-anchor="middle" x="4674.87" y="-23.4" font-family="Arial" font-size="14.00">(Angular::Component)</text>
</g>
<!-- ClusterComponent -->
<g id="node25" class="node">
<title>ClusterComponent</title>
<path fill="#e6e6e6" stroke="black" stroke-width="2" d="M4915.74,-72C4915.74,-72 4786,-72 4786,-72 4780,-72 4774,-66 4774,-60 4774,-60 4774,-12 4774,-12 4774,-6 4780,0 4786,0 4786,0 4915.74,0 4915.74,0 4921.74,0 4927.74,-6 4927.74,-12 4927.74,-12 4927.74,-60 4927.74,-60 4927.74,-66 4921.74,-72 4915.74,-72"/>
<text text-anchor="middle" x="4850.87" y="-40.2" font-family="Arial" font-size="14.00">ClusterComponent</text>
<text text-anchor="middle" x="4850.87" y="-23.4" font-family="Arial" font-size="14.00">(Angular::Component)</text>
</g>
<!-- PlantTypeComponent -->
<g id="node26" class="node">
<title>PlantTypeComponent</title>
<path fill="#e6e6e6" stroke="black" stroke-width="2" d="M5087.74,-72C5087.74,-72 4958,-72 4958,-72 4952,-72 4946,-66 4946,-60 4946,-60 4946,-12 4946,-12 4946,-6 4952,0 4958,0 4958,0 5087.74,0 5087.74,0 5093.74,0 5099.74,-6 5099.74,-12 5099.74,-12 5099.74,-60 5099.74,-60 5099.74,-66 5093.74,-72 5087.74,-72"/>
<text text-anchor="middle" x="5022.87" y="-40.2" font-family="Arial" font-size="14.00">PlantTypeComponent</text>
<text text-anchor="middle" x="5022.87" y="-23.4" font-family="Arial" font-size="14.00">(Angular::Component)</text>
</g>
<!-- SegmentComponent -->
<g id="node27" class="node">
<title>SegmentComponent</title>
<path fill="#e6e6e6" stroke="black" stroke-width="2" d="M5259.74,-72C5259.74,-72 5130,-72 5130,-72 5124,-72 5118,-66 5118,-60 5118,-60 5118,-12 5118,-12 5118,-6 5124,0 5130,0 5130,0 5259.74,0 5259.74,0 5265.74,0 5271.74,-6 5271.74,-12 5271.74,-12 5271.74,-60 5271.74,-60 5271.74,-66 5265.74,-72 5259.74,-72"/>
<text text-anchor="middle" x="5194.87" y="-40.2" font-family="Arial" font-size="14.00">SegmentComponent</text>
<text text-anchor="middle" x="5194.87" y="-23.4" font-family="Arial" font-size="14.00">(Angular::Component)</text>
</g>
<!-- QrTypeComponent -->
<g id="node28" class="node">
<title>QrTypeComponent</title>
<path fill="#e6e6e6" stroke="black" stroke-width="2" d="M5431.74,-72C5431.74,-72 5302,-72 5302,-72 5296,-72 5290,-66 5290,-60 5290,-60 5290,-12 5290,-12 5290,-6 5296,0 5302,0 5302,0 5431.74,0 5431.74,0 5437.74,0 5443.74,-6 5443.74,-12 5443.74,-12 5443.74,-60 5443.74,-60 5443.74,-66 5437.74,-72 5431.74,-72"/>
<text text-anchor="middle" x="5366.87" y="-40.2" font-family="Arial" font-size="14.00">QrTypeComponent</text>
<text text-anchor="middle" x="5366.87" y="-23.4" font-family="Arial" font-size="14.00">(Angular::Component)</text>
</g>
<!-- LocationTypeComponent -->
<g id="node29" class="node">
<title>LocationTypeComponent</title>
<path fill="#e6e6e6" stroke="black" stroke-width="2" d="M5622.09,-72C5622.09,-72 5473.65,-72 5473.65,-72 5467.65,-72 5461.65,-66 5461.65,-60 5461.65,-60 5461.65,-12 5461.65,-12 5461.65,-6 5467.65,0 5473.65,0 5473.65,0 5622.09,0 5622.09,0 5628.09,0 5634.09,-6 5634.09,-12 5634.09,-12 5634.09,-60 5634.09,-60 5634.09,-66 5628.09,-72 5622.09,-72"/>
<text text-anchor="middle" x="5547.87" y="-40.2" font-family="Arial" font-size="14.00">LocationTypeComponent</text>
<text text-anchor="middle" x="5547.87" y="-23.4" font-family="Arial" font-size="14.00">(Angular::Component)</text>
</g>
<!-- LocationComponent -->
<g id="node30" class="node">
<title>LocationComponent</title>
<path fill="#e6e6e6" stroke="black" stroke-width="2" d="M5793.74,-72C5793.74,-72 5664,-72 5664,-72 5658,-72 5652,-66 5652,-60 5652,-60 5652,-12 5652,-12 5652,-6 5658,0 5664,0 5664,0 5793.74,0 5793.74,0 5799.74,0 5805.74,-6 5805.74,-12 5805.74,-12 5805.74,-60 5805.74,-60 5805.74,-66 5799.74,-72 5793.74,-72"/>
<text text-anchor="middle" x="5728.87" y="-40.2" font-family="Arial" font-size="14.00">LocationComponent</text>
<text text-anchor="middle" x="5728.87" y="-23.4" font-family="Arial" font-size="14.00">(Angular::Component)</text>
</g>
<!-- OpcoComponent -->
<g id="node31" class="node">
<title>OpcoComponent</title>
<path fill="#e6e6e6" stroke="black" stroke-width="2" d="M5965.74,-72C5965.74,-72 5836,-72 5836,-72 5830,-72 5824,-66 5824,-60 5824,-60 5824,-12 5824,-12 5824,-6 5830,0 5836,0 5836,0 5965.74,0 5965.74,0 5971.74,0 5977.74,-6 5977.74,-12 5977.74,-12 5977.74,-60 5977.74,-60 5977.74,-66 5971.74,-72 5965.74,-72"/>
<text text-anchor="middle" x="5900.87" y="-40.2" font-family="Arial" font-size="14.00">OpcoComponent</text>
<text text-anchor="middle" x="5900.87" y="-23.4" font-family="Arial" font-size="14.00">(Angular::Component)</text>
</g>
<!-- RelatestoComponent -->
<g id="node32" class="node">
<title>RelatestoComponent</title>
<path fill="#e6e6e6" stroke="black" stroke-width="2" d="M6137.74,-72C6137.74,-72 6008,-72 6008,-72 6002,-72 5996,-66 5996,-60 5996,-60 5996,-12 5996,-12 5996,-6 6002,0 6008,0 6008,0 6137.74,0 6137.74,0 6143.74,0 6149.74,-6 6149.74,-12 6149.74,-12 6149.74,-60 6149.74,-60 6149.74,-66 6143.74,-72 6137.74,-72"/>
<text text-anchor="middle" x="6072.87" y="-40.2" font-family="Arial" font-size="14.00">RelatestoComponent</text>
<text text-anchor="middle" x="6072.87" y="-23.4" font-family="Arial" font-size="14.00">(Angular::Component)</text>
</g>
<!-- AreaComponent -->
<g id="node33" class="node">
<title>AreaComponent</title>
<path fill="#e6e6e6" stroke="black" stroke-width="2" d="M6309.74,-72C6309.74,-72 6180,-72 6180,-72 6174,-72 6168,-66 6168,-60 6168,-60 6168,-12 6168,-12 6168,-6 6174,0 6180,0 6180,0 6309.74,0 6309.74,0 6315.74,0 6321.74,-6 6321.74,-12 6321.74,-12 6321.74,-60 6321.74,-60 6321.74,-66 6315.74,-72 6309.74,-72"/>
<text text-anchor="middle" x="6244.87" y="-40.2" font-family="Arial" font-size="14.00">AreaComponent</text>
<text text-anchor="middle" x="6244.87" y="-23.4" font-family="Arial" font-size="14.00">(Angular::Component)</text>
</g>
<!-- EquipmentComponent -->
<g id="node34" class="node">
<title>EquipmentComponent</title>
<path fill="#e6e6e6" stroke="black" stroke-width="2" d="M6483.52,-72C6483.52,-72 6352.21,-72 6352.21,-72 6346.21,-72 6340.21,-66 6340.21,-60 6340.21,-60 6340.21,-12 6340.21,-12 6340.21,-6 6346.21,0 6352.21,0 6352.21,0 6483.52,0 6483.52,0 6489.52,0 6495.52,-6 6495.52,-12 6495.52,-12 6495.52,-60 6495.52,-60 6495.52,-66 6489.52,-72 6483.52,-72"/>
<text text-anchor="middle" x="6417.87" y="-40.2" font-family="Arial" font-size="14.00">EquipmentComponent</text>
<text text-anchor="middle" x="6417.87" y="-23.4" font-family="Arial" font-size="14.00">(Angular::Component)</text>
</g>
<!-- BodyPartComponent -->
<g id="node35" class="node">
<title>BodyPartComponent</title>
<path fill="#e6e6e6" stroke="black" stroke-width="2" d="M6655.74,-72C6655.74,-72 6526,-72 6526,-72 6520,-72 6514,-66 6514,-60 6514,-60 6514,-12 6514,-12 6514,-6 6520,0 6526,0 6526,0 6655.74,0 6655.74,0 6661.74,0 6667.74,-6 6667.74,-12 6667.74,-12 6667.74,-60 6667.74,-60 6667.74,-66 6661.74,-72 6655.74,-72"/>
<text text-anchor="middle" x="6590.87" y="-40.2" font-family="Arial" font-size="14.00">BodyPartComponent</text>
<text text-anchor="middle" x="6590.87" y="-23.4" font-family="Arial" font-size="14.00">(Angular::Component)</text>
</g>
<!-- RootCauseComponent -->
<g id="node36" class="node">
<title>RootCauseComponent</title>
<path fill="#e6e6e6" stroke="black" stroke-width="2" d="M6832.08,-72C6832.08,-72 6697.66,-72 6697.66,-72 6691.66,-72 6685.66,-66 6685.66,-60 6685.66,-60 6685.66,-12 6685.66,-12 6685.66,-6 6691.66,0 6697.66,0 6697.66,0 6832.08,0 6832.08,0 6838.08,0 6844.08,-6 6844.08,-12 6844.08,-12 6844.08,-60 6844.08,-60 6844.08,-66 6838.08,-72 6832.08,-72"/>
<text text-anchor="middle" x="6764.87" y="-40.2" font-family="Arial" font-size="14.00">RootCauseComponent</text>
<text text-anchor="middle" x="6764.87" y="-23.4" font-family="Arial" font-size="14.00">(Angular::Component)</text>
</g>
<!-- InspectionToolComponent -->
<g id="node37" class="node">
<title>InspectionToolComponent</title>
<path fill="#e6e6e6" stroke="black" stroke-width="2" d="M7029.59,-72C7029.59,-72 6874.15,-72 6874.15,-72 6868.15,-72 6862.15,-66 6862.15,-60 6862.15,-60 6862.15,-12 6862.15,-12 6862.15,-6 6868.15,0 6874.15,0 6874.15,0 7029.59,0 7029.59,0 7035.59,0 7041.59,-6 7041.59,-12 7041.59,-12 7041.59,-60 7041.59,-60 7041.59,-66 7035.59,-72 7029.59,-72"/>
<text text-anchor="middle" x="6951.87" y="-40.2" font-family="Arial" font-size="14.00">InspectionToolComponent</text>
<text text-anchor="middle" x="6951.87" y="-23.4" font-family="Arial" font-size="14.00">(Angular::Component)</text>
</g>
<!-- RecommendedTypeComponent -->
<g id="node38" class="node">
<title>RecommendedTypeComponent</title>
<path fill="#e6e6e6" stroke="black" stroke-width="2" d="M7262.09,-72C7262.09,-72 7071.65,-72 7071.65,-72 7065.65,-72 7059.65,-66 7059.65,-60 7059.65,-60 7059.65,-12 7059.65,-12 7059.65,-6 7065.65,0 7071.65,0 7071.65,0 7262.09,0 7262.09,0 7268.09,0 7274.09,-6 7274.09,-12 7274.09,-12 7274.09,-60 7274.09,-60 7274.09,-66 7268.09,-72 7262.09,-72"/>
<text text-anchor="middle" x="7166.87" y="-40.2" font-family="Arial" font-size="14.00">RecommendedTypeComponent</text>
<text text-anchor="middle" x="7166.87" y="-23.4" font-family="Arial" font-size="14.00">(Angular::Component)</text>
</g>
<!-- MisDashboardComponent -->
<g id="node39" class="node">
<title>MisDashboardComponent</title>
<path fill="#e6e6e6" stroke="black" stroke-width="2" d="M7459.19,-72C7459.19,-72 7304.55,-72 7304.55,-72 7298.55,-72 7292.55,-66 7292.55,-60 7292.55,-60 7292.55,-12 7292.55,-12 7292.55,-6 7298.55,0 7304.55,0 7304.55,0 7459.19,0 7459.19,0 7465.19,0 7471.19,-6 7471.19,-12 7471.19,-12 7471.19,-60 7471.19,-60 7471.19,-66 7465.19,-72 7459.19,-72"/>
<text text-anchor="middle" x="7381.87" y="-40.2" font-family="Arial" font-size="14.00">MisDashboardComponent</text>
<text text-anchor="middle" x="7381.87" y="-23.4" font-family="Arial" font-size="14.00">(Angular::Component)</text>
</g>
<!-- IncidentMasterComponent -->
<g id="node40" class="node">
<title>IncidentMasterComponent</title>
<path fill="#e6e6e6" stroke="black" stroke-width="2" d="M7656.97,-72C7656.97,-72 7500.77,-72 7500.77,-72 7494.77,-72 7488.77,-66 7488.77,-60 7488.77,-60 7488.77,-12 7488.77,-12 7488.77,-6 7494.77,0 7500.77,0 7500.77,0 7656.97,0 7656.97,0 7662.97,0 7668.97,-6 7668.97,-12 7668.97,-12 7668.97,-60 7668.97,-60 7668.97,-66 7662.97,-72 7656.97,-72"/>
<text text-anchor="middle" x="7578.87" y="-40.2" font-family="Arial" font-size="14.00">IncidentMasterComponent</text>
<text text-anchor="middle" x="7578.87" y="-23.4" font-family="Arial" font-size="14.00">(Angular::Component)</text>
</g>
</g>
</svg>
