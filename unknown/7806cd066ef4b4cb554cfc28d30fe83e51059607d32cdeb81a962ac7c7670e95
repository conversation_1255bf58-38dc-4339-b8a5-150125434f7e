<app-toast-message></app-toast-message>
<div class="card custom-card">
    <div class="card-header">
      <div class="row align-items-center">
        <div class="col">
          <h6 class="mb-0">Plant wise report</h6>
        </div>
        <div class="col text-end d-flex align-items-center justify-content-end">
          <div ngbDropdown class="d-inline-block me-2">
            <button type="button" class="btn btn-sm adani-btn dropdown-toggle" id="downloadPlantReportExcelDropdown" ngbDropdownToggle
                [disabled]="isDownloadingExcel || listLoading">
                <span *ngIf="!isDownloadingExcel">
                    <i class="bi bi-file-earmark-excel me-1"></i> Download Excel
                </span>
                <span *ngIf="isDownloadingExcel">
                    <span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
                    Downloading {{ downloadType === 'current' ? 'Page' : (downloadType === 'all' ? 'All' : '') }}...
                </span>
            </button>
            <ul ngbDropdownMenu aria-labelledby="downloadPlantReportExcelDropdown">
                <li>
                    <button ngbDropdownItem (click)="downloadExcel('current')" [disabled]="isDownloadingExcel || listLoading || (list?.length ?? 0) === 0">
                        <i class="bi bi-download me-1"></i> Download Current Page ({{ list?.length ?? 0 }})
                    </button>
                </li>
                <li>
                    <button ngbDropdownItem (click)="downloadExcel('all')" [disabled]="isDownloadingExcel || listLoading || totalItems === 0">
                        <i class="bi bi-cloud-download me-1"></i> Download All Filtered ({{ totalItems }})
                    </button>
                </li>
            </ul>
        </div>
          <img src="../../../assets/svg/filter.svg" class="filter-button ms-3" (click)="openFilterModal()" alt="" style="width: 35px;" />
      </div>
      </div>
    </div>
    <div class="card-body">
      <div class="table-responsive">
        <table class="table custom-table">
          <thead class="table-header">
            <tr>
              <th scope="col" class="text-center">Plant</th>
              <th scope="col" class="text-center">Users In Plant</th>
              <th scope="col" class="text-center">Number of Tours</th>
              <th scope="col" class="text-center">Observations</th>
              <th scope="col" class="text-center">Open</th>
              <th scope="col" class="text-center">Closed</th>
              <th scope="col" class="text-center">Fixed It</th>
              <th scope="col" class="text-center">Safe Act</th>
              <th scope="col" class="text-center">UnSafe Act</th>
              <th scope="col" class="text-center">UnSafe Condition</th>
              <th scope="col" class="text-center">Total Tour Time</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngIf="listLoading">
              <!-- Use the getColspan() method -->
              <td colspan="11" class="text-center p-4">
                  <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                  Loading plantwise report...
              </td>
          </tr>
          <tr *ngIf="!listLoading && list.length === 0">
            <!-- Use the getColspan() method -->
            <td colspan="11" class="text-center p-4 text-muted">
               No plant wise tour report data found matching the criteria.
           </td>
       </tr>
            <tr *ngFor="let item of list">
              <td class="text-center">
                <div style="padding: 8px; border: 1px solid #ddd; border-radius: 4px; background-color: #fafafa;">
                  {{ item.name }}
                </div>
              </td>
              <td class="text-center" *ngIf="istotalUserInPlant">
                <div style="padding: 8px; border: 1px solid #ddd; border-radius: 4px; background-color: #fafafa;">
                  {{ item.totalUserInPlant }}
                </div>
              </td>
              <td class="text-center" *ngIf="istotalNumberOfTours">
                <div style="padding: 8px; border: 1px solid #ddd; border-radius: 4px; background-color: #fafafa;">
                  {{ item.totalNumberOfTours }}
                </div>
              </td>
              <td class="text-center" *ngIf="istotalObservation">
                <div style="padding: 8px; border: 1px solid #ddd; border-radius: 4px; background-color: #fafafa;">
                  {{ item.totalObservation }}
                </div>
              </td>
              <td class="text-center" *ngIf="istotalOpenObservation">
                <div style="padding: 8px; border: 1px solid #ddd; border-radius: 4px; background-color: #fafafa;">
                  {{ item.totalOpenObservation }}
                </div>
              </td>
              <td class="text-center" *ngIf="istotalClosedObservation">
                <div style="padding: 8px; border: 1px solid #ddd; border-radius: 4px; background-color: #fafafa;">
                  {{ item.totalClosedObservation }}
                </div>
              </td>
              <td class="text-center" *ngIf="istotalFixedItObservation">
                <div style="padding: 8px; border: 1px solid #ddd; border-radius: 4px; background-color: #fafafa;">
                  {{ item.totalFixedItObservation }}
                </div>
              </td>
              <td class="text-center" *ngIf="istotalSafeObservation">
                <div style="padding: 8px; border: 1px solid #ddd; border-radius: 4px; background-color: #fafafa;">
                  {{ item.totalSafeObservation }}
                </div>
              </td>
              <td class="text-center" *ngIf="istotalUnSafeObservation">
                <div style="padding: 8px; border: 1px solid #ddd; border-radius: 4px; background-color: #fafafa;">
                  {{ item.totalUnSafeObservation }}
                </div>
              </td>
              <td class="text-center" *ngIf="istotalUnSafeConditionObservation">
                <div style="padding: 8px; border: 1px solid #ddd; border-radius: 4px; background-color: #fafafa;">
                  {{ item.totalUnSafeConditionObservation }}
                </div>
              </td>
              <td class="text-center">
                <div style="padding: 8px; border: 1px solid #ddd; border-radius: 4px; background-color: #fafafa;">
                  <div style="margin-bottom: 6px; border-bottom: 1px solid #eee; padding-bottom: 6px;">
                    <b>Total Time: </b>
                    <span *ngIf="item.totalTourTime">
                        {{ ((+item.totalTourTime || 0) / 60).toFixed(2) + " Hours" }}
                    </span>
                    <span *ngIf="!item.totalTourTime">Total time not available</span>
                  </div>
                  <div>
                    <b>Average Time: </b>
                    <span *ngIf="item.avgTourTime">
                        {{ ((+item.avgTourTime || 0) / 60).toFixed(2) + " Hours" }}
                    </span>
                    <span *ngIf="!item.avgTourTime">Average time not available</span>
                  </div>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
    <div class="card-footer text-muted text-center">
      <app-pagination [currentPage]="currentPage" [totalItems]="totalItems" [itemsPerPage]="itemsPerPage"
      (pageChange)="onPageChange($event)"></app-pagination>
    </div>
  </div>
<!-- Filter Offcanvas -->
<app-offcanvas [title]="'Filter PlantWise Tour Report'" *ngIf="isEditUserModalOpen" (onClickCross)="closeModal()">
  <div class="filter-container p-3"> <!-- Added padding -->
      <form #filterForm="ngForm" (ngSubmit)="applyFilters()"> <!-- Add form -->
          <div class="row g-3"> <!-- Use g-3 -->

              <div class="col-12">
                  <label class="form-label" for="fromDatePlant">From Date <span class="text-danger">*</span></label>
                  <input type="date" id="fromDatePlant" class="form-control"
                         [(ngModel)]="filters.startDate" name="startDate"
                         required
                         #startDateModel="ngModel"
                         [class.is-invalid]="startDateModel.invalid && (startDateModel.dirty || startDateModel.touched || filterForm.submitted)" />
                  <!-- Validation Message -->
                  <div *ngIf="startDateModel.invalid && (startDateModel.dirty || startDateModel.touched || filterForm.submitted)"
                       class="invalid-feedback">
                      From Date is required.
                  </div>
              </div>

               <div class="col-12">
                   <label class="form-label" for="toDatePlant">To Date <span class="text-danger">*</span></label>
                   <input type="date" id="toDatePlant" class="form-control"
                          [(ngModel)]="filters.endDate" name="endDate"
                          required
                          #endDateModel="ngModel"
                          [class.is-invalid]="endDateModel.invalid && (endDateModel.dirty || endDateModel.touched || filterForm.submitted)" />
                   <!-- Validation Message -->
                   <div *ngIf="endDateModel.invalid && (endDateModel.dirty || endDateModel.touched || filterForm.submitted)"
                        class="invalid-feedback">
                       To Date is required.
                   </div>
                    <!-- Optional: Add validation to ensure To Date is not before From Date -->
                    <div *ngIf="endDateModel.valid && startDateModel.valid && (filters.endDate ?? '') < (filters.startDate ?? '') && (endDateModel.dirty || endDateModel.touched)"
                         class="text-danger small mt-1">
                         To Date cannot be before From Date.
                    </div>
               </div>

              <div class="col-12">
                <label class="form-label" for="filterPlant">Select Plant(s)</label>
                <ng-select
                    [items]="availablePlants"
                    bindLabel="name"
                    bindValue="id"
                    [multiple]="true"
                    placeholder="Select one or more plants"
                    [(ngModel)]="filters.plantIds"
                    name="plantIds"
                    [closeOnSelect]="false"
                    id="filterPlant"
                    (change)="updateSelectAllPlantsState()">
                    <ng-template ng-header-tmp>
                        <div class="form-check mb-1 ms-2">
                            <input class="form-check-input" type="checkbox"
                                   id="selectAllPlantsCheckbox"
                                   [checked]="isAllPlantsSelected"
                                   (change)="toggleSelectAllPlants($event)">
                            <label class="form-check-label small" for="selectAllPlantsCheckbox">
                                Select All / Deselect All
                            </label>
                        </div>
                    </ng-template>
                </ng-select>
                <!-- Optional: Add help text if needed -->
                <!-- <div id="plantHelp" class="form-text">You can select multiple plants.</div> -->
            </div>

              <!-- Add Sort controls here if needed -->
              <!-- <div class="col-12">
                  <label class="form-label" for="filterSortByPlantwise">Sort By</label>
                  <select id="filterSortByPlantwise" class="form-select" [(ngModel)]="filters.sortField" name="sortField">
                       <option [ngValue]="null">Default Sort</option>
                       <option *ngFor="let field of availableSortFields" [value]="field.value">{{ field.label }}</option>
                  </select>
                   <label class="form-label mt-2" for="filterSortDirPlantwise">Sort Direction</label>
                   <select id="filterSortDirPlantwise" class="form-select" [(ngModel)]="filters.sortDirection" name="sortDirection">
                      <option value="ASC">Ascending</option>
                      <option value="DESC">Descending</option>
                  </select>
              </div> -->

              <div class="col-12 mt-4 d-grid gap-2">
                   <button type="submit" class="btn adani-btn" [disabled]="!filterForm.valid || (filters.endDate && filters.startDate && (filters.endDate ?? '') < (filters.startDate ?? ''))">
                       <i class="bi bi-search me-1"></i> Search
                   </button>
                   <button type="button" class="btn btn-secondary" (click)="resetFilters()">
                       <i class="bi bi-arrow-clockwise me-1"></i> Reset Filters
                   </button>
               </div>
          </div>
      </form>
  </div>
</app-offcanvas>
