import { Injectable } from '@angular/core';
import { ApiService } from '../api.service';
import { ENDPOINTS } from '../../core/endpoints';

@Injectable({
  providedIn: 'root'
})
export class LeaderboardService {

  constructor(readonly apiService: ApiService) { }

  async getLeaderboard(data: any) {
    return await this.apiService.postData(ENDPOINTS.LEADERBOARD.GET_LEADERBOARD,data);
  }
  async getPlantLeaders(data: any) {
    return await this.apiService.postData(ENDPOINTS.LEADERBOARD.GET_PLANT_LEADERS, data);
  }
}
