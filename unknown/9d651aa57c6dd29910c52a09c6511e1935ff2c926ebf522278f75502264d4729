import { Injectable } from '@angular/core';
import { ApiService } from '../api.service';
import { ENDPOINTS } from '../../core/endpoints';

@Injectable({
  providedIn: 'root'
})
export class ReportManagementService {

  constructor(readonly apiService: ApiService) { }

  async tourUserWiseReport(data: any) {
    return await this.apiService.postData(ENDPOINTS.REPORT.TOUR_USERWISE, data);
  }

  async tourPlantWiseReport(data: any) {
    return await this.apiService.postData(ENDPOINTS.REPORT.TOUR_PLANTWISE, data);
  }

  async zoneUserWiseReport(data: any) {
    return await this.apiService.postData(ENDPOINTS.REPORT.ZONE_USERWISE, data);
  }

  async zonePlantWiseReport(data: any) {
    return await this.apiService.postData(ENDPOINTS.REPORT.ZONE_PLANTWISE, data);
  }

  async ragPlantWiseReport(data: any) {
    return await this.apiService.postData(ENDPOINTS.REPORT.RAG_PLANTWISE, data);
  }

  async ragQRCodeReport(data: any) {
    return await this.apiService.postData(ENDPOINTS.REPORT.RAG_QRWISE, data);
  }

  async observationReport(data: any){
    return await this.apiService.postData(ENDPOINTS.REPORT.BOG_OBSERVATION_PLANTWISE, data);
  }
}
