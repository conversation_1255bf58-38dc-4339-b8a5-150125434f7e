import * as XLSX from 'xlsx';
import { Injectable } from '@angular/core';
import { saveAs } from 'file-saver';

@Injectable({
  providedIn: 'root',
})
export class ExcelExportService {
  private data = [
    {
      email: '<EMAIL>',
      zone: 'Zone 1',
      title: 'title',
      jobDescription: 'jobDescription',
      Criticality: 1,
      fromDate: '1/23/2025 5:33',
      toDate: '1/23/2025 5:33',
      message: ''
    }
  ];
  constructor() { }

  exportToExcel(fileName: string): void {
    const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(this.data);

    // Enable text wrapping for all cells
    Object.keys(ws).forEach((cell) => {
      if (cell.startsWith('!')) return; // Skip meta cells
      ws[cell].s = { alignment: { wrapText: true, vertical: 'top' } };
    });

    // Define column widths
    const wscols = [
      { wch: 20 }, // PlantName
      { wch: 30 }, // ckname
      { wch: 25 }, // functionLocation
      { wch: 20 }, // equipmentNumber
      { wch: 20 }, // barcode
      { wch: 20 }, // name
      { wch: 20 }, // equipment
      { wch: 25 }, // checklistname
      { wch: 50 }, // Checkpointsname
      { wch: 20 }, // permissibleValue
      { wch: 25 }, // permissibleNumber
    ];
    ws['!cols'] = wscols;

    const wb: XLSX.WorkBook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');

    const excelBuffer: any = XLSX.write(wb, {
      bookType: 'xlsx',
      type: 'array',
    });
    this.saveAsExcelFile(excelBuffer, fileName);
  }

  private saveAsExcelFile(buffer: any, fileName: string): void {
    const data: Blob = new Blob([buffer], {
      type: 'application/octet-stream',
    });
    saveAs(data, `${fileName}.xlsx`);
  }
}
