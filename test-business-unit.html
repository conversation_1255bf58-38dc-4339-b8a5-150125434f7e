<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Business Unit Test</title>
</head>
<body>
    <h1>Business Unit LocalStorage Test</h1>
    
    <div>
        <h3>Current Values:</h3>
        <p>Selected Business Unit ID: <span id="selectedId">-</span></p>
        <p>User Business Unit ID: <span id="userId">-</span></p>
        <p>Final Business Unit ID (API will use): <span id="finalId">-</span></p>
    </div>
    
    <div>
        <h3>Actions:</h3>
        <button onclick="setSelectedBusinessUnit(2)">Set Selected Business Unit to 2</button>
        <button onclick="setSelectedBusinessUnit(3)">Set Selected Business Unit to 3</button>
        <button onclick="clearSelectedBusinessUnit()">Clear Selected Business Unit</button>
        <button onclick="setUserBusinessUnit(1)">Set User Business Unit to 1</button>
        <button onclick="updateDisplay()">Refresh Display</button>
    </div>

    <script>
        // Simulate the API service logic
        function getBusinessUnitIdFromLocalStorage() {
            let businessUnitId = 0; // Default to 0

            try {
                // First check if there's a selected business unit ID in localStorage
                const selectedBusinessUnitId = localStorage.getItem('selectedBusinessUnitId');
                console.log('Selected business unit ID from localStorage:', selectedBusinessUnitId);
                
                if (selectedBusinessUnitId) {
                    const selectedId = parseInt(selectedBusinessUnitId, 10);
                    if (!isNaN(selectedId)) {
                        console.log('Using selected business unit ID:', selectedId);
                        return selectedId;
                    }
                }

                // If no selected business unit, fall back to user's business unit
                const userString = localStorage.getItem('user');
                if (userString) {
                    const user = JSON.parse(userString);
                    if (user && typeof user.businessUnitId === 'number') {
                        businessUnitId = user.businessUnitId;
                        console.log('Using user business unit ID:', businessUnitId);
                    }
                }
            } catch (error) {
                console.error('Error parsing user from local storage:', error);
            }
            
            console.log('Final business unit ID being used:', businessUnitId);
            return businessUnitId;
        }

        function setSelectedBusinessUnit(id) {
            localStorage.setItem('selectedBusinessUnitId', id.toString());
            updateDisplay();
        }

        function clearSelectedBusinessUnit() {
            localStorage.removeItem('selectedBusinessUnitId');
            updateDisplay();
        }

        function setUserBusinessUnit(id) {
            const user = { businessUnitId: id, firstName: 'Test', lastName: 'User' };
            localStorage.setItem('user', JSON.stringify(user));
            updateDisplay();
        }

        function updateDisplay() {
            const selectedId = localStorage.getItem('selectedBusinessUnitId') || 'Not set';
            const userString = localStorage.getItem('user');
            let userId = 'Not set';
            
            if (userString) {
                try {
                    const user = JSON.parse(userString);
                    userId = user.businessUnitId || 'Not set';
                } catch (e) {
                    userId = 'Error parsing';
                }
            }
            
            const finalId = getBusinessUnitIdFromLocalStorage();
            
            document.getElementById('selectedId').textContent = selectedId;
            document.getElementById('userId').textContent = userId;
            document.getElementById('finalId').textContent = finalId;
        }

        // Initialize display
        updateDisplay();
    </script>
</body>
</html>