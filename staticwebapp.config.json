{"routes": [{"route": "/", "serve": "/index.html"}, {"route": "/home", "serve": "/index.html"}, {"route": "/home/<USER>", "serve": "/index.html"}, {"route": "/home/<USER>", "serve": "/index.html"}, {"route": "/home/<USER>", "serve": "/index.html"}, {"route": "/home/<USER>", "serve": "/index.html"}, {"route": "/home/<USER>", "serve": "/index.html"}, {"route": "/home/<USER>", "serve": "/index.html"}, {"route": "/home/<USER>/manage", "serve": "/index.html"}, {"route": "/home/<USER>/mis", "serve": "/index.html"}, {"route": "/home/<USER>", "serve": "/index.html"}, {"route": "/home/<USER>", "serve": "/index.html"}, {"route": "/home/<USER>", "serve": "/index.html"}, {"route": "/home/<USER>/tour/userwise", "serve": "/index.html"}, {"route": "/home/<USER>/tour/plantwise", "serve": "/index.html"}, {"route": "/home/<USER>/zone/userwise", "serve": "/index.html"}, {"route": "/home/<USER>/zone/plantwise", "serve": "/index.html"}, {"route": "/home/<USER>/rag/plantwise", "serve": "/index.html"}, {"route": "/home/<USER>/rag/qrcode", "serve": "/index.html"}, {"route": "/home/<USER>/observation/plantwise", "serve": "/index.html"}, {"route": "/home/<USER>/activeusers", "serve": "/index.html"}, {"route": "/home/<USER>/inactiveusers", "serve": "/index.html"}, {"route": "/home/<USER>/transferrequest", "serve": "/index.html"}, {"route": "/home/<USER>/deletedusers", "serve": "/index.html"}, {"route": "/home/<USER>/roles", "serve": "/index.html"}, {"route": "/home/<USER>", "serve": "/index.html"}, {"route": "/home/<USER>/department", "serve": "/index.html"}, {"route": "/home/<USER>/designation", "serve": "/index.html"}, {"route": "/home/<USER>/cluster", "serve": "/index.html"}, {"route": "/home/<USER>/plantType", "serve": "/index.html"}, {"route": "/home/<USER>/segment", "serve": "/index.html"}, {"route": "/home/<USER>/qrtype", "serve": "/index.html"}, {"route": "/home/<USER>/locationtype", "serve": "/index.html"}, {"route": "/home/<USER>/location", "serve": "/index.html"}, {"route": "/home/<USER>/opco", "serve": "/index.html"}, {"route": "/home/<USER>/relatesto", "serve": "/index.html"}, {"route": "/home/<USER>/area", "serve": "/index.html"}, {"route": "/home/<USER>/equipments", "serve": "/index.html"}, {"route": "/home/<USER>/incidentmaster", "serve": "/index.html"}, {"route": "/home/<USER>/bodypart", "serve": "/index.html"}, {"route": "/home/<USER>/rootcause", "serve": "/index.html"}, {"route": "/home/<USER>/inspectiontool", "serve": "/index.html"}, {"route": "/home/<USER>/recommendedtype", "serve": "/index.html"}, {"route": "/home/<USER>", "serve": "/index.html"}], "navigationFallback": {"rewrite": "/index.html"}, "globalHeaders": {"Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "X-XSS-Protection": "1; mode=block", "X-Frame-Options": "SAMEORIGIN", "X-Content-Type-Options": "nosniff", "Referrer-Policy": "strict-origin-when-cross-origin"}}