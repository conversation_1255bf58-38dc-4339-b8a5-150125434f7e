# Feature Hiding Implementation

## Overview
This implementation provides dynamic feature hiding functionality based on user's business unit and feature configuration from the `/feature` endpoint. The system controls both sidebar navigation visibility and route access based on feature flags.

## Key Requirements Implemented

1. **If adminRoute is not present** → Feature should be enabled by default
2. **If admin<PERSON>oute is present and disabled** → Feature should be hidden from sidebar and route blocked
3. **If adminRoute is present and enabled** → Feature should be visible and accessible
4. **Use root `enabled` field** → Not `featureMaster.enabled`
5. **Global Admin Exception** → Global admins (adminRoleId = 6) bypass all feature hiding rules

## Implementation Details

### 1. Updated Home Component (`src/app/components/home/<USER>

#### Key Changes:
- **Enhanced `fetchUserFeatures()` method**: Now properly gets `businessUnitId` from user object, with fallback to `plantIds[0]`
- **Improved `filterItemsRecursive()` method**: Implements the three rules for feature visibility
- **Updated `findFirstAccessibleRoute()` method**: Ensures first accessible route respects feature flags

#### Business Unit ID Resolution:
```typescript
// Priority: businessUnitId > plantIds[0]
let businessUnitId = currentUser?.businessUnitId;
if (!businessUnitId && currentUser?.plantIds && currentUser.plantIds.length > 0) {
  businessUnitId = currentUser.plantIds[0];
}
```

#### Feature Filtering Logic:
```typescript
// Global admins bypass feature hiding
if (this.currentUserRole === ROLES.GLOBAL_ADMIN) {
  return true;
}

// Rule 1: If route doesn't exist in feature map, default to enabled
// Rule 2: If route exists and disabled, hide it
// Rule 3: If route exists and enabled, show it
if (this.featureRouteStatusMap.hasOwnProperty(item.route)) {
  const isFeatureEnabled = this.featureRouteStatusMap[item.route];
  if (isFeatureEnabled === false) {
    return false; // Hide the item
  }
}
// Default to enabled if not in feature map
```

### 2. Route Guard (`src/app/guards/feature.guard.ts`)

#### Purpose:
- Prevents direct URL access to disabled features
- Redirects users to dashboard when accessing disabled routes
- Loads feature configuration on first use
- Bypasses feature hiding for global admins (adminRoleId = 6)

#### Key Features:
- Async feature loading with caching
- Graceful error handling (allows access on errors)
- Automatic redirect to dashboard for disabled routes
- Special handling for global admins

### 3. Updated Routes (`src/app/app.routes.ts`)

#### Applied FeatureGuard to:
- `/home/<USER>
- `/home/<USER>
- `/home/<USER>
- `/home/<USER>
- `/home/<USER>
- `/home/<USER>/*` - All Digisafe routes
- `/home/<USER>
- `/home/<USER>
- And other feature-controlled routes

### 4. Test Component (`src/app/test-feature-hiding.component.ts`)

#### Purpose:
- Visual testing of feature configuration
- Shows user's business unit ID and admin role
- Displays all features and their status
- Demonstrates the three rules in action
- Shows global admin bypass status

#### Access:
Navigate to `/home/<USER>

## API Integration

### Feature Endpoint Call:
```typescript
const requestData = {
  page: 1,
  limit: 1000,
  filter: [`businessUnitId||$eq||${businessUnitId}`]
};
```

### Expected Response Format:
```json
{
  "data": [
    {
      "id": 1,
      "enabled": true,  // <- This field is used (not featureMaster.enabled)
      "adminRoute": "/home/<USER>",
      "featureMaster": {
        "title": "QR Code Management"
      },
      "businessUnit": {
        "title": "Cement"
      }
    }
  ]
}
```

## User Data Structure

### Expected localStorage 'user' object:
```json
{
  "id": 123,
  "businessUnitId": 1,  // <- Primary field used
  "plantIds": [1, 2],   // <- Fallback if businessUnitId not available
  "adminsRoleId": 2,
  // ... other user fields
}
```

## Testing the Implementation

### 1. Test Feature Visibility:
1. Login with a user
2. Navigate to `/home/<USER>
3. Verify business unit ID is correctly identified
4. Check feature list shows correct enabled/disabled status
5. Verify global admin status is correctly identified

### 2. Test Sidebar Hiding:
1. Disable a feature in the feature management
2. Refresh the page or re-login
3. Verify the corresponding menu item is hidden from sidebar

### 3. Test Route Protection:
1. Disable a feature
2. Try to access the route directly via URL
3. Verify redirect to dashboard occurs

### 4. Test Global Admin Bypass:
1. Login with a global admin (adminRoleId = 6)
2. Disable a feature in the feature management
3. Verify the feature is still visible in the sidebar
4. Try to access the route directly via URL
5. Verify access is granted despite the feature being disabled

## Error Handling

### Graceful Degradation:
- If feature API fails → All features default to enabled
- If user data is invalid → Features default to enabled
- If businessUnitId not found → Warning logged, no feature filtering applied

### Logging:
- Comprehensive console logging for debugging
- Feature map status logged on load
- Route access decisions logged

## Performance Considerations

### Caching:
- Features loaded once per session in HomeComponent
- Route guard caches features after first load
- No repeated API calls during navigation

### Efficiency:
- Feature map stored as object for O(1) lookups
- Recursive filtering optimized for navigation tree structure

## Maintenance Notes

### Adding New Features:
1. Add route to `app.routes.ts` with `FeatureGuard`
2. Add navigation item to `home.component.ts`
3. Configure feature in backend with appropriate `adminRoute`

### Debugging:
- Check browser console for feature loading logs
- Use test component to verify feature configuration
- Verify user object structure in localStorage

## Security Notes

- Route guard provides additional security layer
- Feature flags are user-specific based on business unit
- Direct URL access is properly blocked for disabled features
- Graceful handling prevents application breaking on configuration errors
