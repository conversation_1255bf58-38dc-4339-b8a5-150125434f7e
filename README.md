# BOG Adani Angular Application

## Overview

This project is the frontend for the Adani "Boots on Ground" (BoG) application, built with Angular (~v19.2.0). It serves as a comprehensive digital platform designed to streamline and manage various operational and administrative activities across Adani facilities. The application aims to enhance efficiency, safety, and reporting by providing dedicated modules for key workflows.

The application features robust role-based access control, primarily supporting roles like Super Admin and Plant Admin, to ensure secure and appropriate access to different functionalities. It interacts seamlessly with a backend API, which handles data persistence, complex business logic, and integration with other systems.

## Core Modules and Features

The application is structured around several core modules, each addressing a specific operational or administrative need:

*   **Authentication:** Implements a secure login mechanism using an OTP (One-Time Password) for user verification. An `AuthGuard` is in place to protect authenticated routes.
*   **Dashboard:** Provides users with a centralized view of key operational metrics and performance indicators, including overall status, safety training compliance, and Digisafe MIS data, often presented with visualizations.
*   **Safety Training Management:** A dedicated module for managing safety training records. It includes a dashboard with charts and graphs for visual analysis and functionalities for adding, editing, and filtering training records.
*   **QR Code Management:** Facilitates the management of QR codes used within the application, likely for identifying assets, locations, or personnel during field activities like tours.
*   **Tour Management:** Supports the planning, execution, and tracking of field tours. This includes defining tour routes, managing scan points, and recording tour progress and observations.
*   **Incident Management:** Provides a structured process for recording, reporting, and managing incidents that occur within facilities. This module is crucial for tracking safety events and facilitating investigations.
*   **Plant Management:** Allows administrators to manage details related to different plants and facilities. It also handles the workflow for plant transfer requests for users or assets.
*   **Observation Management:** Enables users to record observations made during field activities. This module may include features for categorizing observations and potentially integrating with a reward system to encourage positive safety behaviors.
*   **Digisafe:** Manages Digisafe reports, which are likely related to safety audits or inspections. The module includes an approval workflow to ensure reports are reviewed and authorized, along with an associated MIS dashboard for tracking key metrics.
*   **Other Task Management:** A flexible module for handling miscellaneous operational tasks that may not fit into the more specific categories.
*   **Crisis Management:** Provides tools and workflows for reporting and managing crisis events, enabling quick communication and coordinated response.
*   **Leaderboard:** Displays user rankings based on predefined criteria, such as the number of tours completed or observations recorded, fostering engagement and friendly competition.
*   **Report Management:** Offers comprehensive reporting capabilities, allowing users to generate various reports filtered by User, Plant, and QR code. Reports cover areas like Tours, Zones, RAG (Red, Amber, Green) status, and Observations, providing valuable insights for decision-making.
*   **Admin Management:** A central hub for system administrators to manage application users (including their status - Active, Inactive, Deleted), define and assign roles, and handle user transfer requests between plants or departments.
*   **Master Data Management:** Provides a centralized interface for configuring and managing core data entities that are used across multiple modules. This includes essential data like Departments, Designations, Clusters, Plants, Locations, Equipment details, Incident types, and other foundational reference data.
*   **Notification Management:** Manages the system for sending and handling application notifications to keep users informed about important events, tasks, or updates.
*   **App Settings:** Allows administrators to configure various application-level settings to customize the application's behavior and appearance.

## Tech Stack

The application is built using modern web technologies and libraries:

*   **Framework:** Angular (~v19.2.0) - A powerful framework for building single-page applications, providing structure, component-based architecture, and efficient data binding.
*   **Language:** TypeScript (~v5.8.2) - A typed superset of JavaScript that enhances code quality, maintainability, and developer productivity through static typing and modern language features.
*   **Styling:** SCSS (Sass), Bootstrap 5, Bootstrap Icons, Font Awesome - Used for defining the application's visual styles, implementing a responsive design, and incorporating a wide range of icons.
*   **UI Components:** Angular Material, ng-bootstrap, ng-select - Libraries providing pre-built, accessible, and customizable UI components that adhere to Material Design principles and Bootstrap styling, accelerating frontend development.
*   **Charting:** ApexCharts (via `ng-apexcharts`) - A modern charting library used for creating interactive and visually appealing data visualizations within dashboards and reports.
*   **Rich Text Editor:** Quill (via `ngx-quill`) - Integrates a powerful and flexible rich text editor for areas in the application where formatted text input is required.
*   **HTTP Client:** Axios - A popular promise-based HTTP client used for making asynchronous requests to the backend API, simplifying data fetching and interaction.
*   **OTP Input:** ng-otp-input - A dedicated Angular component for creating and managing OTP input fields, ensuring a smooth user experience during authentication.
*   **Excel Handling:** xlsx - A library for reading and writing spreadsheet files, likely used for functionalities involving data import or export in formats like Excel.
*   **Build Tool:** Angular CLI - The official command-line interface for Angular projects, used for scaffolding, building, testing, and deploying Angular applications efficiently.
*   **Package Manager:** npm / yarn - Used for managing project dependencies, installing libraries, and running scripts.

## Project Structure

The project adheres to a standard and well-organized Angular CLI project structure, promoting modularity and maintainability:

```
.
├── dist/                  # Directory containing the build output of the application.
├── node_modules/          # Directory containing project dependencies installed via npm or yarn.
├── src/                   # Contains the application's source code.
│   ├── app/               # The main application source code.
│   │   ├── components/    # Contains feature-specific modules and components, organized by functional area (e.g., `safety-training`, `admin-management`).
│   │   ├── core/          # Houses core application services, guards (like `auth.guard.ts` for authentication protection), utilities, and centralized configurations (like `endpoints.ts` for API endpoint definitions).
│   │   ├── model/         # Defines TypeScript interfaces and classes for data structures used throughout the application, ensuring type safety.
│   │   ├── services/      # Contains Angular services responsible for specific functionalities, often interacting with the backend API (e.g., `api.service.ts`).
│   │   ├── shared/        # Includes reusable components (like Header, Footer, Pagination, Offcanvas), modules, and pipes that are used across different features of the application.
│   │   ├── app.component.* # Files related to the root application component.
│   │   ├── app.config.ts  # Application-level configuration file.
│   │   └── app.routes.ts  # Defines the main application routing and navigation structure.
│   ├── assets/            # Static assets used by the application, such as images, fonts, and global styles.
│   │   ├── fonts/         # Custom font files.
│   │   ├── img/           # Image assets.
│   │   ├── scss/          # SCSS files for global and component-specific styling.
│   │   └── svg/           # SVG icon assets.
│   ├── enviornments/      # Configuration files for different build environments (e.g., development, production), typically containing environment-specific variables like API base URLs.
│   ├── index.html         # The main HTML file that serves as the entry point for the Angular application.
│   ├── main.ts            # The main entry point for the application, responsible for bootstrapping the Angular module.
│   ├── styles.scss        # Global styles for the application.
│   └── ...                # Other source files.
├── angular.json           # Configuration file for the Angular CLI project.
├── package.json           # Project metadata and list of dependencies.
├── README.md              # This file, providing an overview and documentation.
├── tsconfig.*.json        # TypeScript configuration files for different contexts (application, spec, etc.).
└── yarn.lock              # A lock file generated by Yarn to ensure consistent dependency installations.
```

*   **`src/app/components/`**: Contains modules organized by feature (e.g., `safety-training`, `admin-management`).
*   **`src/app/core/`**: Holds core functionalities like API endpoint definitions (`endpoints.ts`) and potentially interceptors or guards (`auth.guard.ts`).
*   **`src/app/services/`**: Contains services responsible for specific functionalities, often interacting with the API via `ApiService`.
*   **`src/app/shared/`**: Includes reusable components (like Header, Footer, Pagination, Offcanvas) and potentially shared modules.
*   **`src/app/model/`**: Defines TypeScript interfaces for data structures used in the application.

## Prerequisites

To set up and run this project locally, you need to have the following installed:

*   Node.js (Check `.nvmrc` or project requirements for specific version, likely v18+)
*   npm or yarn (npm is included with Node.js)
*   Angular CLI (`npm install -g @angular/cli@^19`)

## Installation

Follow these steps to get the project running on your local machine:

1.  Clone the repository:
    ```bash
    git clone <repository_url>
    ```
2.  Navigate to the project directory:
    ```bash
    cd bog-adani-angular
    ```
3.  Install dependencies using your preferred package manager:
    ```bash
    npm install
    # or
    yarn install
    ```

## Development Server

To run a local development server:

Run `ng serve` or `npm start`. Navigate to `http://localhost:4200/`. The application will automatically reload if you change any of the source files.

## Build

To build the project for deployment:

Run `ng build` or `npm run build`. The build artifacts will be stored in the `dist/bog-adani-angular` directory. Use the `--configuration production` flag for a production build optimized for performance.

## API Configuration

The application interacts with a backend API for data operations.

*   API endpoints are defined centrally in `src/app/core/endpoints.ts`.
*   The base URL for the API is likely configured in the environment files (`src/app/enviornments/`) and utilized within the `src/app/services/api.service.ts`. Ensure the correct environment configuration is set up for development and production builds by modifying the respective environment files.

## Authentication

Authentication is handled via an OTP (One-Time Password) mechanism. This typically involves sending an OTP to the user's registered contact and verifying it for login. Endpoints related to authentication are likely defined within `ENDPOINTS.AUTH` in `src/app/core/endpoints.ts`. An `AuthGuard` (`src/app/auth.guard.ts`) is implemented to protect routes that require user authentication, preventing unauthorized access.

## Deployment

Configuration files for Azure Static Web Apps (`azure-static-web-apps-proud-beach-0ffdec200.yml`, `staticwebapp.config.json`) are present in the repository root, indicating that Azure Static Web Apps is the intended deployment target for this application. Refer to Azure Static Web Apps documentation for detailed deployment procedures and configuration.

## License

This project is proprietary to Adani. (Or specify the actual license if available, e.g., MIT, Apache 2.0).