digraph INFRA {
  node [ color = "black", fillcolor = "#E6E6E6", height =1, style = "filled,bold,rounded", fontname = "Arial" ];
  "LoginComponent" [ label = "LoginComponent
(Angular::Component)", shape =rectangle, fillcolor = "#E78F81" ];
  "HomeComponent" [ label = "HomeComponent
(Angular::Component)", shape =rectangle, fillcolor = "#E78F81" ];
  "QrcodeManagementComponent" [ label = "QrcodeManagementComponent
(Angular::Component)", shape =rectangle ];
  "TourManagementComponent" [ label = "TourManagementComponent
(Angular::Component)", shape =rectangle ];
  "IncidentManagementComponent" [ label = "IncidentManagementComponent
(Angular::Component)", shape =rectangle ];
  "DashboardComponent" [ label = "DashboardComponent
(Angular::Component)", shape =rectangle ];
  "PlantManagementComponent" [ label = "PlantManagementComponent
(Angular::Component)", shape =rectangle ];
  "ManageObservationComponent" [ label = "ManageObservationComponent
(Angular::Component)", shape =rectangle ];
  "OtherTaskComponent" [ label = "OtherTaskComponent
(Angular::Component)", shape =rectangle ];
  "CrisisManagementComponent" [ label = "CrisisManagementComponent
(Angular::Component)", shape =rectangle ];
  "LeaderboardComponent" [ label = "LeaderboardComponent
(Angular::Component)", shape =rectangle ];
  "AppSettingsComponent" [ label = "AppSettingsComponent
(Angular::Component)", shape =rectangle ];
  "NotificationManagementComponent" [ label = "NotificationManagementComponent
(Angular::Component)", shape =rectangle ];
  "ManageDigisafeComponent" [ label = "ManageDigisafeComponent
(Angular::Component)", shape =rectangle ];
  "UserwiseReportComponent" [ label = "UserwiseReportComponent
(Angular::Component)", shape =rectangle ];
  "PlantwiseReportComponent" [ label = "PlantwiseReportComponent
(Angular::Component)", shape =rectangle ];
  "QrCodeReportComponent" [ label = "QrCodeReportComponent
(Angular::Component)", shape =rectangle ];
  "ActiveUserComponent" [ label = "ActiveUserComponent
(Angular::Component)", shape =rectangle ];
  "InactiveUserComponent" [ label = "InactiveUserComponent
(Angular::Component)", shape =rectangle ];
  "TransferRequestComponent" [ label = "TransferRequestComponent
(Angular::Component)", shape =rectangle ];
  "DeleteUserComponent" [ label = "DeleteUserComponent
(Angular::Component)", shape =rectangle ];
  "RolesComponent" [ label = "RolesComponent
(Angular::Component)", shape =rectangle ];
  "DepartmentComponent" [ label = "DepartmentComponent
(Angular::Component)", shape =rectangle ];
  "DesignationComponent" [ label = "DesignationComponent
(Angular::Component)", shape =rectangle ];
  "ClusterComponent" [ label = "ClusterComponent
(Angular::Component)", shape =rectangle ];
  "PlantTypeComponent" [ label = "PlantTypeComponent
(Angular::Component)", shape =rectangle ];
  "SegmentComponent" [ label = "SegmentComponent
(Angular::Component)", shape =rectangle ];
  "QrTypeComponent" [ label = "QrTypeComponent
(Angular::Component)", shape =rectangle ];
  "LocationTypeComponent" [ label = "LocationTypeComponent
(Angular::Component)", shape =rectangle ];
  "LocationComponent" [ label = "LocationComponent
(Angular::Component)", shape =rectangle ];
  "OpcoComponent" [ label = "OpcoComponent
(Angular::Component)", shape =rectangle ];
  "RelatestoComponent" [ label = "RelatestoComponent
(Angular::Component)", shape =rectangle ];
  "AreaComponent" [ label = "AreaComponent
(Angular::Component)", shape =rectangle ];
  "EquipmentComponent" [ label = "EquipmentComponent
(Angular::Component)", shape =rectangle ];
  "BodyPartComponent" [ label = "BodyPartComponent
(Angular::Component)", shape =rectangle ];
  "RootCauseComponent" [ label = "RootCauseComponent
(Angular::Component)", shape =rectangle ];
  "InspectionToolComponent" [ label = "InspectionToolComponent
(Angular::Component)", shape =rectangle ];
  "RecommendedTypeComponent" [ label = "RecommendedTypeComponent
(Angular::Component)", shape =rectangle ];
  "MisDashboardComponent" [ label = "MisDashboardComponent
(Angular::Component)", shape =rectangle ];
  "IncidentMasterComponent" [ label = "IncidentMasterComponent
(Angular::Component)", shape =rectangle ];
}
